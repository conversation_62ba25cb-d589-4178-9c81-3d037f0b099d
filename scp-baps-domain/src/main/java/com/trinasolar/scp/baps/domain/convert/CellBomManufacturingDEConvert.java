package com.trinasolar.scp.baps.domain.convert;

import cn.hutool.core.util.ReflectUtil;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDTO;
import com.trinasolar.scp.baps.domain.dto.CellBomManufacturingDTO;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturing;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturingSummary;
import com.trinasolar.scp.baps.domain.entity.CellGradeCapacity;
import com.trinasolar.scp.baps.domain.excel.CellBomManufacturingExcelDTO;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import jdk.nashorn.internal.ir.annotations.Ignore;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 制造BOM表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellBomManufacturingDEConvert extends BaseDEConvert<CellBomManufacturingDTO, CellBomManufacturing> {

    CellBomManufacturingDEConvert INSTANCE = Mappers.getMapper(CellBomManufacturingDEConvert.class);

    List<CellBomManufacturingExcelDTO> toExcelDTO(List<CellBomManufacturingDTO> dtos);

    CellBomManufacturingExcelDTO toExcelDTO(CellBomManufacturingDTO dto);
    @Mappings({

            @Mapping(source = "workunit", target = "workUnit")

    })
    @Override
    CellBomManufacturingDTO toDto(CellBomManufacturing entity);
    List<CellBomManufacturingSummary> toCellBomManufacturingSummary(List<CellBomManufacturingDTO> dtos);
    @Mappings({

            @Mapping(source = "workUnit", target = "workunit"),
            @Mapping(target = "id",expression = "java(null)")

    })
    CellBomManufacturingSummary toCellBomManufacturingSummary(CellBomManufacturingDTO dto);
    @Mappings({

            @Mapping(target = "fromid", source = "id"),
            @Mapping(target = "endDate", expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getLocalDateTimeAdd(cellBaseCapacity.getEndTime(),1,7,59,59))"),
            @Mapping(target = "startDate", expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getLocalDateTimeAdd(cellBaseCapacity.getStartTime(),8,0,0))"),
            @Mapping(source = "numberLine", target = "totalLine"),
            @Mapping(target = "capacityQuantity", expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.changeUnitToHourPh(cellBaseCapacity.getSingleCapacity()))"),
            @Mapping(target = "month", expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getMonth(cellBaseCapacity.getStartTime()))")

    })
    CellBomManufacturing fromCellBaseCapacity(CellBaseCapacityDTO cellBaseCapacity);

    @Mappings({
            @Mapping(target = "fromid", source = "id")
    })
    CellBomManufacturing fromCellGradeCapacity(CellGradeCapacity cellGradeCapacity);

    /**
     * 依据爬坡数据获取多条bom数据
     *
     * @param cellGradeCapacity
     * @return
     */
    default List<CellBomManufacturing> fromCellGradeCapacityList(CellGradeCapacity cellGradeCapacity) {
        List<CellBomManufacturing> cellBomManufacturings = new ArrayList<>();
        for (int day = 1; day <= 31; day++) {
            BigDecimal value =  ReflectUtil.invoke(cellGradeCapacity,"getD" + day);
            if (Objects.nonNull(value) && value.compareTo(BigDecimal.ZERO)>0) {
                CellBomManufacturing cellBomManufacturing = fromCellGradeCapacity(cellGradeCapacity);
                cellBomManufacturing.setId(null);
                cellBomManufacturing.setIeorgrade(1);
                cellBomManufacturing.setCapacityQuantity(MapStrutUtil.changeUnitToHourPh(new BigDecimal(value.toString())));
                String monthandyear = cellGradeCapacity.getMonth();
                int year = Integer.parseInt(monthandyear.substring(0, 4));
                int month = Integer.parseInt(monthandyear.substring(4, 6));
                LocalDate localDate = LocalDate.of(year, month, day);
                LocalTime localTime = LocalTime.of(8, 0, 0);
                LocalDateTime startDate = LocalDateTime.of(localDate, localTime);
                LocalDateTime endDate = LocalDateTime.of(localDate.plusDays(1), localTime.plusSeconds(-1));
                cellBomManufacturing.setStartDate(startDate);
                cellBomManufacturing.setEndDate(endDate);
                cellBomManufacturing.setTotalLine(cellGradeCapacity.getLineNumber());
                cellBomManufacturing.setUsageLine(cellGradeCapacity.getLineNumber());
                cellBomManufacturings.add(cellBomManufacturing);
            }
        }

        return cellBomManufacturings;
    }

}
