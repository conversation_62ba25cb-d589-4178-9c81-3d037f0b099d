package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.MainGridSpacingRuleDTO;
import com.trinasolar.scp.baps.domain.entity.MainGridSpacingRule;
import com.trinasolar.scp.baps.domain.excel.MainGridSpacingRuleExcelDTO;
import com.trinasolar.scp.baps.domain.save.MainGridSpacingRuleSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池主栅间距规则 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-17 08:55:14
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MainGridSpacingRuleDEConvert extends BaseDEConvert<MainGridSpacingRuleDTO, MainGridSpacingRule> {

    MainGridSpacingRuleDEConvert INSTANCE = Mappers.getMapper(MainGridSpacingRuleDEConvert.class);

    List<MainGridSpacingRuleExcelDTO> toExcelDTO(List<MainGridSpacingRuleDTO> dtos);

    MainGridSpacingRuleExcelDTO toExcelDTO(MainGridSpacingRuleDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MainGridSpacingRule saveDTOtoEntity(MainGridSpacingRuleSaveDTO saveDTO, @MappingTarget MainGridSpacingRule entity);

    MainGridSpacingRuleSaveDTO excelDTOtoSaveDTO(MainGridSpacingRuleExcelDTO excelDto);
}
