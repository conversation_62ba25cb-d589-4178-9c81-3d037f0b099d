package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 需求计划明细（APS）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 08:30:30
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "需求计划明细（APS）DTO对象", description = "DTO对象")
public class DemandPlanLinesApsDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    /**
     * 实际覆盖日期
     */
    @ApiModelProperty(value = "实际覆盖日期")
    private LocalDate actualCoverageDate;
    /**
     * 计划锁定日期
     */
    @ApiModelProperty(value = "计划锁定日期")
    private LocalDate planLockDate;
    /**
     * 后端提供给APS的唯一ID
     */
    @ApiModelProperty(value = "后端提供给APS的唯一ID")
    private String dpId;
    /**
     * 电池需求计划号
     */
    @ApiModelProperty(value = "电池需求计划号")
    private String demandPlanCode;
    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceType;
    /**
     * 国内/海外名称
     */
    @ApiModelProperty(value = "国内/海外名称")
    private String domesticOverseaName;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 电池物料编码
     */
    @ApiModelProperty(value = "电池物料编码")
    private String batteryMaterialCode;
    /**
     * H追溯名称
     */
    @ApiModelProperty(value = "H追溯名称")
    private String hTraceName;
    /**
     * 片源种类名称
     */
    @ApiModelProperty(value = "片源种类名称")
    private String pcsSourceTypeName;
    /**
     * 透明双波名称
     */
    @ApiModelProperty(value = "透明双波名称")
    private String transparentDoubleGlassName;
    /**
     * 美学名称
     */
    @ApiModelProperty(value = "美学名称")
    private String aestheticsName;
    /**
     * 小区域国家名称
     */
    @ApiModelProperty(value = "小区域国家名称")
    private String regionalCountryName;
    /**
     * 需求地名称
     */
    @ApiModelProperty(value = "需求地名称")
    private String basePlaceName;
    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;
    /**
     * 起始效率
     */
    @ApiModelProperty(value = "起始效率")
    private BigDecimal startEfficiency;
    /**
     * 电池特殊单号
     */
    @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;
    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    private LocalDate demandDate;
    /**
     * 符合率
     */
    @ApiModelProperty(value = "符合率")
    private BigDecimal passPercent;
    /**
     * 电池生产车间
     */
    @ApiModelProperty(value = "电池生产车间")
    private String scheduleWorkshop;
    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    private String processCategoryName;
    /**
     * 需求数量(汇总)
     */
    @ApiModelProperty(value = "需求数量(汇总)")
    private BigDecimal demandQty;
    /**
     * 外部需求ID
     */
    @ApiModelProperty(value = "外部需求ID")
    private String exteriorDemandId;
    /**
     * H转换标记
     */
    @ApiModelProperty(value = "H转换标记")
    private String hChangeFlag;
    /**
     * DT转换标记
     */
    @ApiModelProperty(value = "DT转换标记")
    private String pcsSourceDtChangeFlag;
    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;
}
