package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.baps.domain.utils.CustomDateConverter;
import com.trinasolar.scp.common.api.util.LocalDateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * IE产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellBaseCapacityExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始日期",converter = CustomDateConverter.class)

    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束日期",converter = CustomDateConverter.class)

    private LocalDate endTime;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 能否生产单玻
     */
    @ExcelProperty(value = "能否生产单玻")
    private String isSingleGlass;
    /**
     * 能否生产低碳
     */
    @ExcelProperty(value = "能否生产低碳")
    private String isDt;
    /**
     * 能否生产小区域国家
     */
    @ExcelProperty(value = "能否生产小区域国家")
    private String isRegionalCountry;
    /**
     * 能否生产H兼容
     */
    @ExcelProperty(value = "能否生产H兼容")
    private String isHChangeFlag;
    /**
     * 能否生产H追溯
     */
    @ExcelProperty(value = "能否生产H追溯")
    private String isHTrace;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产线体
     */
    @ExcelProperty(value = "生产线体")
    @ExcelIgnore
    private String lineName;
    /**
     * 线体数量
     */
    @ExcelProperty(value = "线体数量")
    private BigDecimal numberLine;
    /**
     * 可用线体数量
     */
    @ExcelProperty(value = "可用线体数量")
    private BigDecimal usageLine;
    /**
     * 产能（单线）
     */
    @ExcelProperty(value = "产能（单线）")
    private BigDecimal singleCapacity;
    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String unit;
}
