package com.trinasolar.scp.baps.domain.query;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 硅片拆分规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 07:44:54
 */
@Data
@ApiModel(value = "SiliconSplitRule查询条件", description = "查询条件")
@Accessors(chain = true)
public class SiliconSplitRuleQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 电池类型Id
         */
        @ApiModelProperty(value = "电池类型Id")
    private Long cellTypeId;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellType;
        /**
         * 生产车间Id
         */
        @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshop;
        /**
         * 硅片等级
         */
        @ApiModelProperty(value = "硅片等级")
    private String waferGrade;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
        /**
         * 电池良率
         */
        @ApiModelProperty(value = "电池良率")
    private BigDecimal cellFine;
        /**
         * 开始时间
         */
        @ApiModelProperty(value = "开始时间")
    private LocalDate startDate;
        /**
         * 结束时间
         */
        @ApiModelProperty(value = "结束时间")
    private LocalDate endDate;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
        private String month;
    /**
     * 分片规则类型
     */
    @ApiModelProperty(value = "分片规则类型")
    private String ruleType;
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
    /**
     * 硅料厂家Id
     */
    @ApiModelProperty(value = "硅料厂家Id")
    private String siMfrsId;
    /**
     * 硅片等级Id
     */
    @ApiModelProperty(value = "硅片等级Id")
    private String waferGradeId;

}
