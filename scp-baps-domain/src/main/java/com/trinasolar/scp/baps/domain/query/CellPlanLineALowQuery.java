package com.trinasolar.scp.baps.domain.query;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 入库计划表A-
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@ApiModel(value = "CellPlanLineALow查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellPlanLineALowQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 国内海外Id
         */
        @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
        /**
         * 国内海外
         */
        @ApiModelProperty(value = "国内海外")
    private String isOversea;
        /**
         * 生产基地
         */
        @ApiModelProperty(value = "生产基地")
    private String basePlace;
        /**
         * 生产基地Id
         */
        @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshop;
        /**
         * 生产车间Id
         */
        @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellsType;
        /**
         * 电池类型Id
         */
        @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
        /**
         * H追溯
         */
        @ApiModelProperty(value = "H追溯")
    private String hTrace;
        /**
         * 美学
         */
        @ApiModelProperty(value = "美学")
    private String aesthetics;
        /**
         * 透明双玻
         */
        @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
        /**
         * 片源种类
         */
        @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
        /**
         * 月份
         */
        @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 万片数
     */
    @ApiModelProperty(value = "万片数")
    private BigDecimal qtyThousandPc;
        /**
         * MV
         */
        @ApiModelProperty(value = "MV")
    private BigDecimal cellMv;
        /**
         * 版本
         */
        @ApiModelProperty(value = "版本")
    private String version;
    /**
     * 来自入库计划数据的版本
     */
    @ApiModelProperty(value = "来自入库计划数据的版本")
    private String fromVersion;
        /**
         * 5A料号
         */
        @ApiModelProperty(value = "5A料号")
    private String itemCode;
        /**
         * d1
         */
        @ApiModelProperty(value = "d1")
    private BigDecimal d1;
        /**
         * d2
         */
        @ApiModelProperty(value = "d2")
    private BigDecimal d2;
        /**
         * d3
         */
        @ApiModelProperty(value = "d3")
    private BigDecimal d3;
        /**
         * d4
         */
        @ApiModelProperty(value = "d4")
    private BigDecimal d4;
        /**
         * d5
         */
        @ApiModelProperty(value = "d5")
    private BigDecimal d5;
        /**
         * d6
         */
        @ApiModelProperty(value = "d6")
    private BigDecimal d6;
        /**
         * d7
         */
        @ApiModelProperty(value = "d7")
    private BigDecimal d7;
        /**
         * d8
         */
        @ApiModelProperty(value = "d8")
    private BigDecimal d8;
        /**
         * d9
         */
        @ApiModelProperty(value = "d9")
    private BigDecimal d9;
        /**
         * d10
         */
        @ApiModelProperty(value = "d10")
    private BigDecimal d10;
        /**
         * d11
         */
        @ApiModelProperty(value = "d11")
    private BigDecimal d11;
        /**
         * d12
         */
        @ApiModelProperty(value = "d12")
    private BigDecimal d12;
        /**
         * d13
         */
        @ApiModelProperty(value = "d13")
    private BigDecimal d13;
        /**
         * d14
         */
        @ApiModelProperty(value = "d14")
    private BigDecimal d14;
        /**
         * d15
         */
        @ApiModelProperty(value = "d15")
    private BigDecimal d15;
        /**
         * d16
         */
        @ApiModelProperty(value = "d16")
    private BigDecimal d16;
        /**
         * d17
         */
        @ApiModelProperty(value = "d17")
    private BigDecimal d17;
        /**
         * d18
         */
        @ApiModelProperty(value = "d18")
    private BigDecimal d18;
        /**
         * d19
         */
        @ApiModelProperty(value = "d19")
    private BigDecimal d19;
        /**
         * d20
         */
        @ApiModelProperty(value = "d20")
    private BigDecimal d20;
        /**
         * d21
         */
        @ApiModelProperty(value = "d21")
    private BigDecimal d21;
        /**
         * d22
         */
        @ApiModelProperty(value = "d22")
    private BigDecimal d22;
        /**
         * d23
         */
        @ApiModelProperty(value = "d23")
    private BigDecimal d23;
        /**
         * d24
         */
        @ApiModelProperty(value = "d24")
    private BigDecimal d24;
        /**
         * d25
         */
        @ApiModelProperty(value = "d25")
    private BigDecimal d25;
        /**
         * d26
         */
        @ApiModelProperty(value = "d26")
    private BigDecimal d26;
        /**
         * d27
         */
        @ApiModelProperty(value = "d27")
    private BigDecimal d27;
        /**
         * d28
         */
        @ApiModelProperty(value = "d28")
    private BigDecimal d28;
        /**
         * d29
         */
        @ApiModelProperty(value = "d29")
    private BigDecimal d29;
        /**
         * d30
         */
        @ApiModelProperty(value = "d30")
    private BigDecimal d30;
        /**
         * d31
         */
        @ApiModelProperty(value = "d31")
    private BigDecimal d31;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
