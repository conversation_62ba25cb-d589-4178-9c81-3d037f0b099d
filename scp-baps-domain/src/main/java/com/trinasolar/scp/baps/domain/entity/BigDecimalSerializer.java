package com.trinasolar.scp.baps.domain.entity;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: BigDecimalSerializer
 * @date 2023/12/4 20:41
 */
public class BigDecimalSerializer implements ObjectSerializer, ObjectDeserializer {

    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) {
        SerializeWriter out = serializer.out;
        if (object == null) {
            out.writeNull();
            return;
        }

        DecimalFormat df = new DecimalFormat("0.00");
        if (object instanceof BigDecimal) {
//            out.writeString(((BigDecimal) object).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString() + "%");
            out.writeString(df.format(object));
        } else {
            String strVal = object.toString();
            out.writeString(strVal);
        }
    }

    @Override
    public BigDecimal deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        Object parse = parser.parse(fieldName);
        if (Objects.nonNull(parse)) {
            String value = parse.toString().trim();
            return new BigDecimal(value);
        }
        return null;
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}
