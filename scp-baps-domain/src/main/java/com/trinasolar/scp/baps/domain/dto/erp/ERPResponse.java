package com.trinasolar.scp.baps.domain.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Erp通用返回对象
 *
 * <AUTHOR>
 * @date 2022/6/16 10:22
 **/
@Data
@ApiModel("erpResponseDto")
@AllArgsConstructor
@NoArgsConstructor
public class ERPResponse {
    /**
     * 成功
     */
    public static String SUCCESS = "S000A000";
    /**
     * 失败
     */
    public static String FAIL = "E0002D15";

    @ApiModelProperty("返回状态码")
    private String xReturnCode;

    @ApiModelProperty("错误信息")
    private String xReturnMesg;
}
