package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 投产计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_plan_line_version")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_plan_line_version SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_plan_line_version SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellPlanLineVersion extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    @Column(name = "month")
    private String month;

    /**
     * 最終确认发布的版本
     */
    @ApiModelProperty(value = "最終确认发布的版本")
    @Column(name = "final_version")
    private String finalVersion;

    /**
     * 排产版本
     */
    @ApiModelProperty(value = "排产版本")
    @Column(name = "version")
    private String version;
    /**
     * 是否进行了投产提前期转化过
     */
    @ApiModelProperty(value = "是否进行了投产提前期转化过")
    @Column(name = "is_transform")
    private Integer isTransform;
    /**
     * 是否进行了硅料厂家拆分
     */
    @ApiModelProperty(value = "是否进行了硅料厂家拆分")
    @Column(name = "is_si_mfrs")
    private Integer isSiMfrs;

    /**
     * 是否已经进行硅片等级拆分
     */
    @ApiModelProperty(value = "是否已经进行硅片等级拆分")
    @Column(name = "is_wafer_grade")
    private Integer isWaferGrade;

    /**
     * 是否已经进行了加工类型拆分
     */
    @ApiModelProperty(value = "是否已经进行了加工类型拆分")
    @Column(name = "is_process_category")
    private Integer isProcessCategory;

    /**
     * 是否进行了计划确认
     */
    @ApiModelProperty(value = "是否进行了计划确认")
    @Column(name = "is_confirm_plan")
    private Integer isConfirmPlan;

    /**
     * 是否创建了入库计划
     */
    @ApiModelProperty(value = "是否创建了入库计划")
    @Column(name = "is_create_instock_plan")
    private Integer isCreateInstockPlan;

    /**
     * 是否发送了邮件
     */
    @ApiModelProperty(value = "是否发送了邮件")
    @Column(name = "is_send_email")
    private Integer isSendEmail;

    /**
     * 哪个月排的
     */
    @ApiModelProperty(value = "哪个月排的")
    @Column(name = "schedule_month")
    private String scheduleMonth;


}
