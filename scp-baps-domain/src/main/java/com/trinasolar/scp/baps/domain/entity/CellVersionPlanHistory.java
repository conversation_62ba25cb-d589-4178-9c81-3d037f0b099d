package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 计划与上一版本计划对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_version_plan_history")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_version_plan_history SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_version_plan_history SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellVersionPlanHistory extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外Id
     */
    @ApiModelProperty(value = "国内/海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea_name")
    private String isOverseaName;

    /**
     * 电池类型编号
     */
    @ApiModelProperty(value = "电池类型编号")
    @Column(name = "cell_type_id")
    private Long cellTypeId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cell_type_name")
    private String cellTypeName;

    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshop_id")
    private Long workshopId;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop_name")
    private String workshopName;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    @Column(name = "h_trace")
    private String hTrace;

    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    @Column(name = "aesthetics")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    @Column(name = "transparent_double_glass")
    private String transparentDoubleGlass;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    @Column(name = "cell_source")
    private String cellSource;

    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    @Column(name = "regional_country")
    private String regionalCountry;
    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    @Column(name = "item_code")
    private String itemCode;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 月份计划（上上版）
     */
    @ApiModelProperty(value = "月份计划（上上版）")
    @Column(name = "month_v0")
    private BigDecimal monthV0;

    /**
     * 月份计划（上版）
     */
    @ApiModelProperty(value = "月份计划（上版）")
    @Column(name = "month_v1")
    private BigDecimal monthV1;

    /**
     * 月份计划新版
     */
    @ApiModelProperty(value = "月份计划新版")
    @Column(name = "month_v2")
    private BigDecimal monthV2;

    /**
     * 差异
     */
    @ApiModelProperty(value = "差异")
    @Column(name = "gap")
    private BigDecimal gap;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;


}
