package com.trinasolar.scp.baps.domain.utils;

import com.google.common.collect.Lists;
import com.trinasolar.scp.common.api.util.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * @USER: MWZ
 * @DATE: 2022/6/24
 */
@Slf4j
public class MathUtils {
    public final static BigDecimal ONE_HUNDRED = new BigDecimal("100");

    public final static BigDecimal ONE_MILLION = new BigDecimal("1000000");

    public final static String ZERO_PER = "0%";

    public static Long str2Long(String value) {
        if (StringUtils.isNotBlank(value)) {
            try {
                return Long.parseLong(value);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    public static BigDecimal defaultIfNull(BigDecimal value, BigDecimal defaultValue) {
        if (Objects.isNull(value)) {
            return defaultValue;
        } else {
            return value;
        }
    }

    /**
     * 是否大于目标值
     *
     * @param sourceValue 原值
     * @param targetValue 目标值
     * @return
     */
    public static boolean integerGt(Integer sourceValue, Integer targetValue) {
        Objects.requireNonNull(targetValue, "目标值不能为空");

        if (Objects.isNull(sourceValue)) {
            return false;
        }

        return sourceValue > targetValue;
    }

    /**
     * BigDecimal转字符串
     *
     * @param val 值
     * @return 字符串
     */
    public static String bigDecimal2String(BigDecimal val) {
        if (Objects.isNull(val)) {
            return null;
        }
        return val.stripTrailingZeros().toPlainString();
    }

    /**
     * 是否大于目标值
     *
     * @param sourceValue 原值
     * @param targetValue 目标值
     * @return
     */
    public static boolean bigDecimalGt(BigDecimal sourceValue, BigDecimal targetValue) {
        Objects.requireNonNull(targetValue, "目标值不能为空");

        if (Objects.isNull(sourceValue)) {
            return false;
        }

        return sourceValue.compareTo(targetValue) > 0;
    }

    //只是单存加法，当属性值为空值时会变成0
    public static BigDecimal addBigDecimal(BigDecimal v1, BigDecimal v2) {
        if (v1 == null) {
            v1 = BigDecimal.ZERO;
        }
        if (v2 == null) {
            v2 = BigDecimal.ZERO;
        }

        return v1.add(v2);
    }

    //只是单存加法，当属性值为空值时会变成0
    public static BigDecimal addBigDecimal(BigDecimal... v1) {
        BigDecimal num = null;
        for (BigDecimal bigDecimal : v1) {
            if (bigDecimal != null) {
                if (num == null) {
                    num = BigDecimal.ZERO;
                }
                num = num.add(bigDecimal);
            }
        }

        return num;
    }

    //比较大小  去最大的值
    public static BigDecimal maxBigDecimal(BigDecimal v1, BigDecimal v2) {
        if (v1 == null) {
            v1 = BigDecimal.ZERO;
        }
        if (v2 == null) {
            v2 = BigDecimal.ZERO;
        }
        return v1.compareTo(v2) > 0 ? v1 : v2;
    }


    //只是单存减法，当属性值为空值时会变成0
    public static BigDecimal subtractBigDecimal(BigDecimal v1, BigDecimal v2) {
        if (v1 == null) {
            v1 = BigDecimal.ZERO;
        }
        if (v2 == null) {
            v2 = BigDecimal.ZERO;
        }

        return v1.subtract(v2);
    }

    //只是单存除法，当属性值为空值时会变成0
    public static BigDecimal divideBigDecimal(BigDecimal v1, BigDecimal v2) {
        if (v1 == null) {
            v1 = BigDecimal.ZERO;
        }
        if (v2 == null || MathUtils.checkIsZero(v2)) {
            return BigDecimal.ZERO;
        }

        return divideBigDecimal(v1, v2, 4);
    }

    public static BigDecimal divideBigDecimal(BigDecimal v1, BigDecimal v2, int scale) {
        if (v1 == null) {
            v1 = BigDecimal.ZERO;
        }
        if (v2 == null || MathUtils.checkIsZero(v2)) {
            return BigDecimal.ZERO;
        }

        return v1.divide(v2, scale, RoundingMode.HALF_UP);
    }

    //只是单存加法，当属性值为空值时会变成0
    public static BigDecimal addBigDecimal(String str1, String str2) {
        BigDecimal v1 = BigDecimal.ZERO;
        BigDecimal v2 = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(str1)) {
            v1 = new BigDecimal(str1);
        }
        if (StringUtils.isNotBlank(str2)) {
            v2 = new BigDecimal(str2);
        }

        return v1.add(v2);
    }

    //字符串.leng -1 转化成 BigDecimal
    public static BigDecimal strToBigDecimal(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        } else if ("无".equals(str)) {
            return BigDecimal.ZERO;
        }
        return strToBigDecimal(str, null);
    }

    public static BigDecimal strToBigDecimal(String str, String errMsg) {
        return strToBigDecimal(str, 6, errMsg);
    }

    public static BigDecimal strToBigDecimal(String str, int scale) {
        return strToBigDecimal(str, scale, null);
    }

    public static BigDecimal strToBigDecimal(String str, int scale, String errMsg) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        try {
            if (str.indexOf("%") > -1) {
                str = str.replaceAll("%", "");
                return new BigDecimal(str).divide(ONE_HUNDRED, scale, RoundingMode.HALF_UP);
            }
            return new BigDecimal(str);
        } catch (NumberFormatException e) {
            if (StringUtils.isNotBlank(errMsg)) {
                throw new BizException(errMsg);
            }
            log.warn("MathUtil.strToBigDecimal", e);
        }
        return null;
    }

    //等于0
    public static boolean checkIsZero(BigDecimal v1) {
        return Objects.isNull(v1) || v1.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 任何一个为0 都返回true
     *
     * @param arg
     * @return
     */
    public static boolean checkIsZero(BigDecimal... arg) {
        for (BigDecimal bigDecimal : Lists.newArrayList(arg)) {
            if (Objects.isNull(bigDecimal) || bigDecimal.compareTo(BigDecimal.ZERO) == 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 所有为0 才返回true
     *
     * @param arg
     * @return
     */
    public static boolean checkAllIsZero(BigDecimal... arg) {
        boolean flag = true;
        for (BigDecimal bigDecimal : Lists.newArrayList(arg)) {
            if (Objects.nonNull(bigDecimal) && bigDecimal.compareTo(BigDecimal.ZERO) != 0) {
                flag = false;
            }
        }
        return flag;
    }

    //求平均值  s1 是带% 的数值
    public static String bigDecimalAvg(String... s1) {
        int i = 0;
        BigDecimal avg = BigDecimal.ZERO;
        for (String s : s1) {
            if (StringUtils.isNotBlank(s)) {
                BigDecimal v = new BigDecimal(s.substring(0, s.length() - 1));

                avg = avg.add(v);
                i++;

            }
        }
        if (i == 0) {
            return null;
        }
        return avg.divide(BigDecimal.valueOf(i), 2, RoundingMode.UP) + "%";
    }

    //创建时不报错
    public static BigDecimal create(String num) {
        if (StringUtils.isNotBlank(num)) {
            return new BigDecimal(num);
        }
        return null;
    }


    /**
     * 小数转成百分比
     *
     * @param num
     * @return
     */
    public static String num2PerStr(BigDecimal num) {
        return num2PerStr(num, 2);
    }

    /**
     * 小数转成百分比 4位
     *
     * @param num
     * @return
     */
    public static String num2PerStr4(BigDecimal num) {
        return num2PerStr(num, 4);
    }

    public static String num2PerStr(String str, int scale) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        BigDecimal num = new BigDecimal(str);
        if (checkIsZero(num)) {
            return "0.00%";
        }
        num = num.multiply(ONE_HUNDRED).setScale(scale, RoundingMode.HALF_UP);
        if (checkIsZero(num)) {
            return "0.00%";
        }
        return num.stripTrailingZeros().toPlainString() + "%";
    }

    /**
     * 小数转成百分比
     *
     * @param num
     * @param scale
     * @return
     */
    public static String num2PerStr(BigDecimal num, int scale) {
        if (Objects.isNull(num)) {
            return ZERO_PER;
        }
        return num.multiply(ONE_HUNDRED).setScale(scale, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString() + "%";
    }

    public static int defaultIfNull(Integer value, Integer defaultValue) {
        if (Objects.isNull(value)) {
            return defaultValue;
        }
        return value;
    }

    //计算百分比数据
    public static BigDecimal percentageNum(String str) {
        if (StringUtils.isNotBlank(str)) {
            if (str.contains("%")) {
                return new BigDecimal(str.substring(0, str.length() - 1)).divide(ONE_HUNDRED, 6, RoundingMode.HALF_UP);
            } else {
                return new BigDecimal(str);
            }
        }

        return null;
    }

    public static BigDecimal calculateQuantitySumZero(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    public static String obj2DecimalFormat(Object num) {

        return obj2DecimalFormat(num, "#################.##########");
    }

    public static String obj2DecimalFormat(Object num, String format) {
        if (Objects.isNull(num)) {
            return null;
        }
        if (StringUtils.isBlank(format)) {
            format = "#################.##########";
        }
        DecimalFormat decimalFormat = new DecimalFormat(format);
        return decimalFormat.format(new BigDecimal(num.toString()));
    }

    public static String num2str(BigDecimal num, int scale) {
        if (Objects.isNull(num)) {
            return null;
        }
        return num.setScale(scale, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }

    public static String num2str(BigDecimal num) {
        return num2str(num, 2);
    }

    /**
     * 校验是否正数，非0
     *
     * @param num
     * @return
     */
    public static boolean checkPositiveNumber(BigDecimal num) {
        if (Objects.nonNull(num) && num.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        return false;
    }
}
