package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;


/**
 * 电池良率中间表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellFineMidExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ExcelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 年份
     */
    @ExcelProperty(value = "年份")
    private Integer year;
    /**
     * 满产/排产
     */
    @ExcelProperty(value = "满产/排产")
    private String parameterType;
    /**
     * 产品分类
     */
    @ExcelProperty(value = "产品分类")
    private String productType;
    /**
     * 品类
     */
    @ExcelProperty(value = "品类")
    private String productCategory;
    /**
     * 主栅
     */
    @ExcelProperty(value = "主栅")
    private String mainGrid;
    /**
     * 晶体类型
     */
    @ExcelProperty(value = "晶体类型")
    private String crystalType;
    /**
     * 单晶/多晶
     */
    @ExcelProperty(value = "单晶/多晶")
    private String crystalSpec;
    /**
     * 车间
     */
    @ExcelProperty(value = "车间")
    private String workshop;
    /**
     * 1月数
     */
    @ExcelProperty(value = "1月数")
    private BigDecimal m1;
    /**
     * 2月数
     */
    @ExcelProperty(value = "2月数")
    private BigDecimal m2;
    /**
     * 3月数
     */
    @ExcelProperty(value = "3月数")
    private BigDecimal m3;
    /**
     * 4月数
     */
    @ExcelProperty(value = "4月数")
    private BigDecimal m4;
    /**
     * 5月数
     */
    @ExcelProperty(value = "5月数")
    private BigDecimal m5;
    /**
     * 6月数
     */
    @ExcelProperty(value = "6月数")
    private BigDecimal m6;
    /**
     * 7月数
     */
    @ExcelProperty(value = "7月数")
    private BigDecimal m7;
    /**
     * 8月数
     */
    @ExcelProperty(value = "8月数")
    private BigDecimal m8;
    /**
     * 9月数
     */
    @ExcelProperty(value = "9月数")
    private BigDecimal m9;
    /**
     * 10月数
     */
    @ExcelProperty(value = "10月数")
    private BigDecimal m10;
    /**
     * 11月数
     */
    @ExcelProperty(value = "11月数")
    private BigDecimal m11;
    /**
     * 12月数
     */
    @ExcelProperty(value = "12月数")
    private BigDecimal m12;
}
