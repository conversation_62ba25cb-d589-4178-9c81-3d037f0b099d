package com.trinasolar.scp.baps.domain.dto;

import cn.hutool.core.util.ReflectUtil;
import com.google.common.collect.Lists;
import com.trinasolar.scp.baps.domain.constant.CommonConstant;
import com.trinasolar.scp.baps.domain.dto.bbom.ItemsDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.SiliconSlicePurchasePlanDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.TjOnHandDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: HTraceSupply
 * @date 2024/6/7 13:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HTraceSupply {

    /**
     * 生产基地
     */
    private String basePlace;
    /**
     * 是否通用供应
     */
    private boolean generalSupply;
    /**
     * 生产车间
     */
    private String workshop;
    /**
     * 生产车间
     */
    private List<String> workshopList;

    /**
     * 品类
     */
    private String productCategory;
    /**
     * 晶体类型
     */
    private String crystalType;
    /**
     * H追溯
     */
    private String hTrace;
    /**
     * 月份
     */
    private String month;
    /**
     * 天
     */
    private Integer day;
    /**
     * 供应数量
     */
    private BigDecimal quantity;
    /**
     * 供应日期
     */
    private LocalDate supplyDate;
    /**
     * 结余
     */
    private BigDecimal surplus;

    public HTraceSupply(String basePlace, String workshop, String productCategory, String crystalType, String hTrace, String month, Integer day) {
        this.basePlace = basePlace;
        this.workshop = workshop;
        this.productCategory = productCategory;
        this.crystalType = crystalType;
        this.hTrace = hTrace;
        this.month = month;
        this.day = day;
        this.supplyDate = DateUtil.getLocalDate(month, day);
    }

    public static HTraceSupply build(HTraceDayResult dayResult, String hTrace) {
        HTraceSupply supply = new HTraceSupply();
        supply.setBasePlace(dayResult.getBasePlace());
        supply.setGeneralSupply(dayResult.getShareWorkshop().contains(","));
        supply.setWorkshop(dayResult.getShareWorkshop());
        supply.setWorkshopList(Arrays.asList(dayResult.getShareWorkshop().split(",")));
        supply.setProductCategory(dayResult.getProductCategory());
        supply.setCrystalType(dayResult.getCrystalType());
        supply.setHTrace(hTrace);
        supply.setMonth(dayResult.getMonth());
        supply.setDay(dayResult.getDay());
        supply.setQuantity(BigDecimal.ZERO);
        return supply;
    }

    public static HTraceSupply build(TjOnHandDTO tjOnHandDTO, ItemsDTO item) {
        String hTrace = item.getSegment17();
        //当H追溯为H，且等级为A-、B时，算入非H库存中。
        if (CommonConstant.H.equals(hTrace) && StringUtils.equalsAny(item.getSegment6(), CommonConstant.BATTERY_A, CommonConstant.BATTERY_B)) {
            hTrace = CommonConstant.NON;
        }
        HTraceSupply supply = new HTraceSupply();
        supply.setBasePlace(tjOnHandDTO.getBasePlace());
        supply.setGeneralSupply(tjOnHandDTO.getWorkshop().contains(","));
        supply.setWorkshop(tjOnHandDTO.getWorkshop());
        supply.setWorkshopList(tjOnHandDTO.getWorkshopList());
        supply.setProductCategory(item.getSegment9());
        supply.setCrystalType(item.getSegment7());
        supply.setHTrace(hTrace);
        supply.setMonth(DateUtil.getMonth(tjOnHandDTO.getInventoryDate()));
        supply.setDay(tjOnHandDTO.getInventoryDate().getDayOfMonth());
        supply.setQuantity(tjOnHandDTO.getQuantity().divide(BigDecimal.valueOf(10000), 6, BigDecimal.ROUND_HALF_UP));
        return supply;
    }

    public static List<HTraceSupply> build(SiliconSliceSupplyLinesDTO siliconSliceSupply, ItemsDTO item) {
        List<HTraceSupply> result = Lists.newArrayList();
        String supplyMonth = siliconSliceSupply.getMonth();
        int daysForMonth = DateUtil.getDaysForMonth(supplyMonth);
        for (int i = 1; i <= daysForMonth; i++) {
            Object fieldValue = ReflectUtil.getFieldValue(siliconSliceSupply, String.format("d%sQuantity", i));
            BigDecimal supplyQuantity = Optional.ofNullable((BigDecimal) fieldValue).orElse(BigDecimal.ZERO);
            HTraceSupply supply = new HTraceSupply();
            supply.setBasePlace(siliconSliceSupply.getBasePlace());
            supply.setGeneralSupply(siliconSliceSupply.getWorkshop().contains(","));
            supply.setWorkshop(siliconSliceSupply.getWorkshop());
            supply.setProductCategory(item.getSegment9());
            supply.setCrystalType(item.getSegment7());
            supply.setHTrace(item.getSegment17());
            supply.setMonth(supplyMonth);
            supply.setDay(i);
            supply.setQuantity(supplyQuantity);
            result.add(supply);
        }
        return result;
    }

    public static List<HTraceSupply> build(SiliconSlicePurchasePlanDTO siliconSliceSupply, ItemsDTO item) {
        List<HTraceSupply> result = Lists.newArrayList();
        String supplyMonth = siliconSliceSupply.getMonth();
        int daysForMonth = DateUtil.getDaysForMonth(supplyMonth);
        for (int i = 1; i <= daysForMonth; i++) {
            Object fieldValue = ReflectUtil.getFieldValue(siliconSliceSupply, String.format("d%sQuantity", i));
            BigDecimal supplyQuantity = Optional.ofNullable((BigDecimal) fieldValue).orElse(BigDecimal.ZERO);
            HTraceSupply supply = new HTraceSupply();
            supply.setBasePlace(siliconSliceSupply.getBasePlace());
            supply.setGeneralSupply(siliconSliceSupply.getWorkshop().contains(","));
            supply.setWorkshop(siliconSliceSupply.getWorkshop());
            supply.setProductCategory(item.getSegment9());
            supply.setCrystalType(item.getSegment7());
            supply.setHTrace(item.getSegment17());
            supply.setMonth(supplyMonth);
            supply.setDay(i);
            supply.setQuantity(supplyQuantity);
            result.add(supply);
        }
        return result;
    }

    public static HTraceSupply groupBy(HTraceSupply supply) {
        return new HTraceSupply(supply.getBasePlace(), supply.getWorkshop(), supply.getProductCategory(), supply.getCrystalType(), supply.getHTrace(), supply.getMonth(), supply.getDay());
    }

    public void assembly() {
        this.generalSupply = this.workshop.contains(",");
        this.workshopList = Arrays.asList(this.workshop.split(","));
    }

    public String dailySign() {
        return StringUtils.join(this.basePlace, this.productCategory, this.crystalType, this.supplyDate);
    }

    public String hSurplusSign() {
        return StringUtils.join(this.basePlace, this.workshop, this.productCategory, this.crystalType);
    }
}
