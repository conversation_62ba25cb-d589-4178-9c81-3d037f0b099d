package com.trinasolar.scp.baps.domain.dto.bmrp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * 安全库存天数表
 *
 * <AUTHOR>
 * @Date 2024/1/9
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BmrpSafetyStockDaysDTO对象", description = "DTO对象")
public class BmrpSafetyStockDaysDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "国内/海外")
    private String countryFlag;

    private String countryFlagCode;

    @ApiModelProperty(value = "物料分类")
    private String itemCategory;

    private String itemCategoryCode;

    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "小区域")
    private String regionalCountry;

    private String regionalCountryCode;

    @ApiModelProperty(value = "安全库存天数")
    private String safetyStockDays;

    @ApiModelProperty(value = "基地")
    private String basePlace;

    private String basePlaceCode;

    @ApiModelProperty(value = "机位")
    private String cellMachine;

    private String cellMachineCode;

    public String sign() {
        return StringUtils.join(this.basePlace, this.month);
    }
}
