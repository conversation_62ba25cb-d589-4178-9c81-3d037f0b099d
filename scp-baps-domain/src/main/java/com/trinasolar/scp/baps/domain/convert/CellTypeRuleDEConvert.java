package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellTypeRule;
import com.trinasolar.scp.baps.domain.dto.CellTypeRuleDTO;
import com.trinasolar.scp.baps.domain.excel.CellTypeRuleExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellTypeRuleSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池类型转化规则 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-11 01:42:00
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class})
public interface CellTypeRuleDEConvert extends BaseDEConvert<CellTypeRuleDTO, CellTypeRule> {

    CellTypeRuleDEConvert INSTANCE = Mappers.getMapper(CellTypeRuleDEConvert.class);

    List<CellTypeRuleExcelDTO> toExcelDTO(List<CellTypeRuleDTO> dtos);

    CellTypeRuleExcelDTO toExcelDTO(CellTypeRuleDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellTypeRule saveDTOtoEntity(CellTypeRuleSaveDTO saveDTO, @MappingTarget CellTypeRule entity);
    @Mappings(
            {
                    @Mapping(target = "field" ,expression = "java(MapStrutUtil.getValueByName(LovHeaderCodeConstant.BAPS_CELL_TYPE_RULE_FIELD,dto.getFieldName()))"),
                    @Mapping(target = "module" ,expression = "java(MapStrutUtil.getValueByName(LovHeaderCodeConstant.BAPS_CELL_TYPE_RULE_MODULE,dto.getModuleName()))"),
            }
    )
    CellTypeRuleSaveDTO excelDtoToSaveDto(CellTypeRuleExcelDTO dto);
    List<CellTypeRuleSaveDTO> excelDtoToSaveDto(List<CellTypeRuleExcelDTO> excelDtos);

    @Override
    @Mappings(
            {
                    @Mapping(target = "fieldName" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BAPS_CELL_TYPE_RULE_FIELD,entity.getField()))"),
                    @Mapping(target = "moduleName" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BAPS_CELL_TYPE_RULE_MODULE,entity.getModule()))"),
            }
    )
    CellTypeRuleDTO toDto(CellTypeRule entity);

}
