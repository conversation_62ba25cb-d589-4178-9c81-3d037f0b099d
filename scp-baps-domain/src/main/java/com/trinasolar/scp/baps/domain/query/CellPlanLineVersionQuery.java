package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 投产计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Data
@ApiModel(value = "CellPlanLineVersion查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellPlanLineVersionQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 国内海外Id
         */
        @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
        /**
         * 国内海外
         */
        @ApiModelProperty(value = "国内海外")
    private String isOversea;
        /**
         * 排产月份
         */
        @ApiModelProperty(value = "排产月份")
    private String month;
        /**
         * 最終确认发布的版本
         */
        @ApiModelProperty(value = "最終确认发布的版本")
    private String finalVersion;
        /**
         * 排产版本
         */
        @ApiModelProperty(value = "排产版本")
    private String version;
    /**
     * 是否进行了投产提前期转化过
     */
    @ApiModelProperty(value = "是否进行了投产提前期转化过")
    private Integer isTransform;
        /**
         * 是否进行了硅料厂家拆分
         */
        @ApiModelProperty(value = "是否进行了硅料厂家拆分")
    private Integer isSiMfrs;
        /**
         * 是否已经进行硅片等级拆分
         */
        @ApiModelProperty(value = "是否已经进行硅片等级拆分")
    private Integer isWaferGrade;
        /**
         * 是否已经进行了加工类型拆分
         */
        @ApiModelProperty(value = "是否已经进行了加工类型拆分")
    private Integer isProcessCategory;
        /**
         * 是否进行了计划确认
         */
        @ApiModelProperty(value = "是否进行了计划确认")
    private Integer isConfirmPlan;
        /**
         * 是否创建了入库计划
         */
        @ApiModelProperty(value = "是否创建了入库计划")
    private Integer isCreateInstockPlan;
        /**
         * 是否发送了邮件
         */
        @ApiModelProperty(value = "是否发送了邮件")
    private Integer isSendEmail;
        /**
         * 哪个月排的
         */
        @ApiModelProperty(value = "哪个月排的")
    private String scheduleMonth;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
