package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 开立工单明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_wip_total")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_wip_total SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_wip_total SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellWipTotal extends BasePO implements Serializable{
 private static final long serialVersionUID=1L;

 /**
  * ID主键
  */
 @Id
 @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
         strategy = GenerationType.SEQUENCE)
 @GenericGenerator(
         name = "SnowflakeIdGeneratorConfig",
         strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
 @ApiModelProperty(value = "ID主键")
 @Column(name = "id")
 private Long id;

 /**
  * 订单号
  */
 @ApiModelProperty(value = "订单号")
 @Column(name = "order_code")
 private String orderCode;

 /**
  * 开单账号
  */
 @ApiModelProperty(value = "开单账号")
 @Column(name = "billing_account")
 private String billingAccount;

 /**
  * 开单日期
  */
 @ApiModelProperty(value = "开单日期")
 @Column(name = "billing_date")
 private LocalDateTime billingDate;

 /**
  * erp工单号
  */
 @ApiModelProperty(value = "erp工单号")
 @Column(name = "erp_order_code")
 private String erpOrderCode;

 /**
  * 月份
  */
 @ApiModelProperty(value = "月份")
 @Column(name = "month")
 private String month;

 /**
  * 生产基地
  */
 @ApiModelProperty(value = "生产基地")
 @Column(name = "base_place")
 private String basePlace;

 /**
  * 车间
  */
 @ApiModelProperty(value = "车间")
 @Column(name = "workshop")
 private String workshop;

 /**
  * 计划数量
  */
 @ApiModelProperty(value = "计划数量")
 @Column(name = "qty")
 private BigDecimal qty;

 /**
  * 已开单数量
  */
 @ApiModelProperty(value = "已开单数量")
 @Column(name = "qty_billing")
 private BigDecimal qtyBilling;

 /**
  * 累计已开单数量
  */
 @ApiModelProperty(value = "累计已开单数量")
 @Column(name = "qty_billing_total")
 private BigDecimal qtyBillingTotal;

 /**
  * 电池类型
  */
 @ApiModelProperty(value = "电池类型")
 @Column(name = "cell_type")
 private String cellType;

 /**
  * 5a料号
  */
 @ApiModelProperty(value = "5a料号")
 @Column(name = "item_fivea")
 private String itemFivea;

 /**
  * 国内海外
  */
 @ApiModelProperty(value = "国内海外")
 @Column(name = "is_oversea")
 private String isOversea;
 /**
  * 国内海外Id
  */
 @ApiModelProperty(value = "国内海外Id")
 @Column(name = "is_oversea_id")
 private Long isOverseaId;

 /**
  * 起始时间
  */
 @ApiModelProperty(value = "起始时间")
 @Column(name = "start_time")
 private LocalDateTime startTime;

 /**
  * 结束时间
  */
 @ApiModelProperty(value = "结束时间")
 @Column(name = "end_time")
 private LocalDateTime endTime;
 /**
  * 最終邮件确认发布的版本
  */
 @ApiModelProperty(value = "最終邮件确认发布的版本")
 @Column(name = "final_version")
 private String finalVersion;

}
