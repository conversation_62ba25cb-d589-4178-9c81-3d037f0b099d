package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;

import javax.persistence.Column;


/**
 * 产能并行损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellParallelLoss保存参数", description = "保存参数")
public class CellParallelLossSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    @ApiModelProperty(value = "并行类型-ID")
    private Long parallelTypeId;

    /**
     * 并行类型
     */
    @ApiModelProperty(value = "并行类型")
    private String parallelType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型ID")
    private Long cellsTypeId;

    /**
     * 电池类型two
     */
    @ApiModelProperty(value = "电池类型2-ID")
    private Long cellsTypeTwoId;

    /**
     * 电池类型two
     */
    @ApiModelProperty(value = "电池类型2")
    private String cellsTypeTwo;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshopid")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元Id")
    @Column(name = "workunitid")
    private Long workunitid;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    private  BigDecimal lineNumber;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 损失产能
     */
    @ApiModelProperty(value = "损失产能")
    private BigDecimal loss;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
}
