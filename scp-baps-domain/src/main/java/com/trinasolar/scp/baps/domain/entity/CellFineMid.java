package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 电池良率中间表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_fine_mid")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "delete  from baps_cell_fine_mid   WHERE id = ?")
@SQLDeleteAll(sql = "delete  from baps_cell_fine_mid  WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellFineMid extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 满产/排产
     */
    @ApiModelProperty(value = "满产/排产")
    @Column(name = "parameter_type")
    private String parameterType;

    /**
     * 产品分类
     */
    @ApiModelProperty(value = "产品分类")
    @Column(name = "product_type")
    private String productType;

    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    @Column(name = "product_category")
    private String productCategory;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    @Column(name = "main_grid")
    private String mainGrid;

    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    @Column(name = "crystal_type")
    private String crystalType;

    /**
     * 单晶/多晶
     */
    @ApiModelProperty(value = "单晶/多晶")
    @Column(name = "crystal_spec")
    private String crystalSpec;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型left")
    @Column(name = "cells_type_left")
    private String cellsTypeLeft;
    /**
     * 1月数
     */
    @ApiModelProperty(value = "1月数")
    @Column(name = "m1")
    private BigDecimal m1;

    /**
     * 2月数
     */
    @ApiModelProperty(value = "2月数")
    @Column(name = "m2")
    private BigDecimal m2;

    /**
     * 3月数
     */
    @ApiModelProperty(value = "3月数")
    @Column(name = "m3")
    private BigDecimal m3;

    /**
     * 4月数
     */
    @ApiModelProperty(value = "4月数")
    @Column(name = "m4")
    private BigDecimal m4;

    /**
     * 5月数
     */
    @ApiModelProperty(value = "5月数")
    @Column(name = "m5")
    private BigDecimal m5;

    /**
     * 6月数
     */
    @ApiModelProperty(value = "6月数")
    @Column(name = "m6")
    private BigDecimal m6;

    /**
     * 7月数
     */
    @ApiModelProperty(value = "7月数")
    @Column(name = "m7")
    private BigDecimal m7;

    /**
     * 8月数
     */
    @ApiModelProperty(value = "8月数")
    @Column(name = "m8")
    private BigDecimal m8;

    /**
     * 9月数
     */
    @ApiModelProperty(value = "9月数")
    @Column(name = "m9")
    private BigDecimal m9;

    /**
     * 10月数
     */
    @ApiModelProperty(value = "10月数")
    @Column(name = "m10")
    private BigDecimal m10;

    /**
     * 11月数
     */
    @ApiModelProperty(value = "11月数")
    @Column(name = "m11")
    private BigDecimal m11;

    /**
     * 12月数
     */
    @ApiModelProperty(value = "12月数")
    @Column(name = "m12")
    private BigDecimal m12;


}
