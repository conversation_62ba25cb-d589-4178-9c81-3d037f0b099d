package com.trinasolar.scp.baps.domain.save;

import com.trinasolar.scp.common.api.annotation.ExportConvert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 物流节假日表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DeliveryHoliday保存参数", description = "保存参数")
public class DeliveryHolidaySaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外名")
    private String isOversea;
    /**
     * 发货帐套
     */
    @ApiModelProperty(value = "发货帐套")
    private String booksFrom;
    /**
     * 发货基地
     */
    @ApiModelProperty(value = "发货基地")
    private String basePlaceFrom;
    /**
     * 到货帐套
     */
    @ApiModelProperty(value = "到货帐套")
    private String booksTo;
    /**
     * 到货基地
     */
    @ApiModelProperty(value = "到货基地")
    private String basePlaceTo;
    /**
     * 年月
     */
    @ApiModelProperty(value = "年月")
    private String month;
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private Integer dayBegin;
    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private Integer dayEnd;
    /**
     * 日期区间
     */
    @ApiModelProperty(value = "日期区间")
    private String dateBetween;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
