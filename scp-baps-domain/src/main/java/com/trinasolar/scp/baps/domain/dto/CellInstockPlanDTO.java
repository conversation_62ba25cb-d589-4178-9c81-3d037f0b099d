package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.baps.domain.entity.ActualInstockPlan;
import com.trinasolar.scp.baps.domain.enums.DataType;
import com.trinasolar.scp.baps.domain.enums.SelectedEnum;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.*;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;


/**
 * 入库计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:13:25
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "入库计划表DTO对象", description = "DTO对象")
@ToString
public class CellInstockPlanDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 订单表
     */
    @ApiModelProperty(value = "订单表")
    private String orderCode;
    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceType;
    /**
     * 需求版本号
     */
    @ApiModelProperty(value = "需求版本号")
    private String demandVersion;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workunitId;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 产线数量
     */
    @ApiModelProperty(value = "产线数量")
    private BigDecimal numberLine;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 是否进行了透明双玻拆分
     */
    @ApiModelProperty(value = "是否进行了透明双玻拆分")
    private Integer isTransparentDoubleGlass;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    private String demandBasePlace;
    /**
     * 是否电池特殊要求
     */
    @ApiModelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    private String lowResistance;
    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQty;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * MV
     */
    @ApiModelProperty(value = "MV")
    private BigDecimal cellMv;
    /**
     * 最終邮件确认发布的版本
     */
    @ApiModelProperty(value = "最終邮件确认发布的版本")
    private String finalVersion;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 数量（片）拆分前的值
     */
    @ApiModelProperty(value = "数量（片）拆分前的值")
    private BigDecimal oldQtyPc;
    /**
     * 数量（片）
     */
    @ApiModelProperty(value = "数量（片）")
    private BigDecimal qtyPc;
    /**
     * 汇总明细行id
     */
    @ApiModelProperty(value = "汇总明细行id")
    private Long demandSummaryLinesId;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 是否进行了硅料厂家拆分
     */
    @ApiModelProperty(value = "是否进行了硅料厂家拆分")
    private Integer isSiMfrs;
    /**
     * 硅片厂家
     */
    @ApiModelProperty(value = "硅片厂家")
    private String siliconMaterialManufacturer;
    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;
    /**
     * 起始效率
     */
    @ApiModelProperty(value = "起始效率")
    private BigDecimal startEfficiency;
    /**
     * 最大分布效率
     */
    @ApiModelProperty(value = "最大分布效率")
    private BigDecimal maxEfficiency;
    /**
     * 电池特殊单号
     */
    @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;
    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    private LocalDate demandDate;
    /**
     * 是否已经进行硅片等级拆分
     */
    @ApiModelProperty(value = "是否已经进行硅片等级拆分")
    private Integer isWaferGrade;
    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String waferGrade;
    /**
     * 是否进行了A-拆分
     */
    @ApiModelProperty(value = "是否进行了A-拆分")
    private Integer isASplit;

    /**
     * 是否进行了低效拆分
     */
    @ApiModelProperty(value = "是否进行了低效拆分-拆分")
    private Integer isLESplit;
    /**
     * 加工类型优先级
     */
    @ApiModelProperty(value = "加工类型优先级")
    private Integer processCategoryPriority;
    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    private String processCategory;
    /**
     * 是否已经进行了加工类型拆分
     */
    @ApiModelProperty(value = "是否已经进行了加工类型拆分")
    private Integer isProcessCategory;
    /**
     * 是否进行手动加工类型指定
     */
    @ApiModelProperty(value = "是否进行手动加工类型指定")
    private Integer isHandProcessCategory;
    /**
     * gap
     */
    @ApiModelProperty(value = "gap")
    private BigDecimal gap;
    /**
     * 需求说明
     */
    @ApiModelProperty(value = "需求说明")
    private String demandRemark;
    /**
     * 分档规则
     */
    @ApiModelProperty(value = "分档规则")
    private String gradeRule;
    /**
     * 验证标识
     */
    @ApiModelProperty(value = "验证标识")
    private String verificationMark;
    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    private String demandSource;
    /**
     * 电池物料编码
     */
    @ApiModelProperty(value = "电池物料编码")
    private String batteryMaterialCode;
    /**
     * 原来投产开始时间
     */
    @ApiModelProperty(value = "原来投产开始时间")
    private LocalDateTime oldStartTime;
    /**
     * 原来投产结束时间
     */
    @ApiModelProperty(value = "原来投产结束时间")
    private LocalDateTime oldEndTime;
    /**
     * 排产来源Id
     */
    @ApiModelProperty(value = "排产来源Id")
    private Long schedulingFromId;
    /**
     * 投产来源Id
     */
    @ApiModelProperty(value = "投产来源Id")
    private Long planLineFromId;
    /**
     * 拆前父Id
     */
    @ApiModelProperty(value = "拆前父Id")
    private Long parentId;
    /**
     * 对应bbom中的Id
     */
    @ApiModelProperty(value = "BbomId")
    private Long bbomId;
    /**
     * 出库日期
     */
    @ApiModelProperty(value = "出库日期")
    private LocalDateTime outBoundDate;

    /**
     * 出库日期
     */
    @ApiModelProperty(value = "出库日期")
    private String outBoundDateStr;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String leadType;
    /**
     * 入库计划确认
     */
    @ApiModelProperty(value = "入库计划确认")
    private Integer confirmPlan;
    /**
     * 原投产月份
     */
    @ApiModelProperty(value = "原投产月份")
    private String oldMonth;
    /**
     * 入库计划id
     */
    @ApiModelProperty(value = "入库计划id")
    private Long instockPlanId;
    /**
     * 哪个月份排的
     */
    @ApiModelProperty(value = "哪个月份排的")
    private String scheduleMonth;
    /**
     * 来源类型Id
     */
    @ApiModelProperty(value = "来源类型Id")
    private Long sourceTypeId;
    /**
     * H追溯Id
     */
    @ApiModelProperty(value = "H追溯Id")
    private Long hTraceId;

    /**
     * 美学Id
     */
    @ApiModelProperty(value = "美学Id")
    private Long aestheticsId;

    /**
     * 透明双玻Id
     */
    @ApiModelProperty(value = "透明双玻Id")
    private Long transparentDoubleGlassId;

    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    private Long cellSourceId;

    /**
     * 小区域国家Id
     */
    @ApiModelProperty(value = "小区域国家Id")
    private Long regionalCountryId;
    /**
     * 需求地Id
     */
    @ApiModelProperty(value = "需求地Id")
    private Long demandBasePlaceId;


    /**
     * 电池厂家Id
     */
    @ApiModelProperty(value = "电池厂家Id")
    private Long cellMfrsId;

    /**
     * 银浆厂家Id
     */
    @ApiModelProperty(value = "银浆厂家Id")
    private Long silverPulpMfrsId;
    /**
     * 硅料厂家Id
     */
    @ApiModelProperty(value = "硅料厂家Id")
    private Long siMfrsId;
    /**
     * 硅片等级Id
     */
    @ApiModelProperty(value = "硅片等级Id")
    private Long waferGradeId;
    /**
     * 加工类型Id
     */
    @ApiModelProperty(value = "加工类型Id")
    private Long processCategoryId;
    /**
     * 产品等级Id
     */
    @ApiModelProperty(value = "产品等级Id")
    private Long productionGradeId;
    /**
     * 是否进行了美学拆分
     */
    @ApiModelProperty(value ="是否进行了美学拆分")
    private Integer isAestheticsSplit;
    /**
     * 给可发货计划计算可发货数量上限使用（5月14日添加的业务逻辑）
     */
    @ApiModelProperty(value ="给可发货计划计算可发货数量上限使用")
    private BigDecimal maxQtyPc;
    /**
     * 片源级投产良率
     */
    @ApiModelProperty(value = "片源级投产良率")
    private BigDecimal waferYieldRatio;
    /**
     * 片源级A-比例
     */
    @ApiModelProperty(value = "片源级A-比例")
    private BigDecimal waferGradeRatio;
    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;
    /**
     * 是否小区域国家
     */
    @ApiModelProperty(value = "是否小区域国家")
    private String isRegionalCountry;
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;
    /**
     * 是否H兼容
     */
    @ApiModelProperty(value = "是否H兼容")
    private String hChangeFlag;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    /**
     * 数据来源类型
     */
    @ApiModelProperty(value = "数据来源类型")
    private Integer dataType;
    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    private String supplyMethod;
    /**
     * 供应方式Id
     */
    @ApiModelProperty(value = "供应方式Id")
    private Long supplyMethodId;

    /**
     * 背面细栅
     */
    @ApiModelProperty(value = "背面细栅")
    private String backFineGrid;
    /**
     * 正面细栅
     */
    @ApiModelProperty(value = "正面细栅")
    private String frontFineGrid;
    /**
     * 硅片厚度
     */
    @ApiModelProperty(value = "硅片厚度")
    private String siliconWaferThickness;
    /**
     * 硅片尺寸
     */
    @ApiModelProperty(value = "硅片尺寸")
    private String siliconWaferSize;

    @ApiModelProperty(value = "料号匹配类型:instock入库plan投产")
    private String planType;

    /**
     * 是否选中(0：未选中，1：已选中)
     */
    @ApiModelProperty(value ="是否选中(NO：未选中，YES：已选中)")
    private SelectedEnum isSelected;

    /**
     * 拆分比例
     */
    @ApiModelProperty(value = "拆分比例")
    private BigDecimal splitRate;

    /**
     * 拆分开始日期
     */
    @ApiModelProperty(value ="开始日期")
    private LocalDate splitStartDate;

    /**
     * 拆分结束日期
     */
    @ApiModelProperty(value ="结束日期")
    private LocalDate splitEndDate;

    @ApiModelProperty(value = "证书编号")
    private String certCode;

    @ApiModelProperty(value = "配比")
    private String ratioCode;

    @ApiModelProperty(value = "ECS-CODE")
    private String ecsCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;
    /**
     * 生命周期状态
     */
    @ApiModelProperty(value = "生命周期状态")
    private String lifecycleState;

    @ApiModelProperty(value = "临时量产标识")
    private String isTemporaryOutput;

    public ActualInstockPlan build(){
        ActualInstockPlan actualInstockPlan = new ActualInstockPlan();
        BeanUtils.copyProperties(this, actualInstockPlan, "id", "isDeleted");
        actualInstockPlan.setStartTime(this.startTime);
        actualInstockPlan.setDataType(DataType.PLAN.name());
        return actualInstockPlan;
    }

    public String group() {
        return StringTools.joinWith(",", this.getIsOversea(), this.getBasePlace(), this.getWorkshop(), this.getCellsType(),
                this.getItemCode(), this.getHTrace(), this.getSupplyMethod(), this.getHChangeFlag(), this.getCellSource(), this.getRegionalCountry(),
                this.getTransparentDoubleGlass(), this.getAesthetics(), this.getProductionGrade(), this.getMonth(),
                this.getMainGridSpace(), this.getSourceType(), this.getAesthetics(), this.getCertCode(), this.getRatioCode());
    }
}
