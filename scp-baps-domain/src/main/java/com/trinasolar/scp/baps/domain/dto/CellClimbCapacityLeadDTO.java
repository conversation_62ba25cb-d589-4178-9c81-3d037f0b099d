package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 爬坡产能可靠性验证表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-05 08:10:43
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "爬坡产能可靠性验证表DTO对象", description = "DTO对象")
public class CellClimbCapacityLeadDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型id
     */
    @ApiModelProperty(value = "电池类型id")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workShopId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workShop;
    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workUnitId;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workUnit;
    /**
     * 验证时间
     */
    @ApiModelProperty(value = "验证时间")
    private LocalDateTime validateTime;

    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    private BigDecimal lineNumber;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    public String filedGroupTwo(){
        return StringTools.joinWith("/",this.basePlace,this.workShop,this.workUnit,this.cellType,this.lineName);
    }
    public String filedGroupTwoNoWorkUnit(){
        return StringTools.joinWith("/",this.basePlace,this.workShop,null,this.cellType,null);
    }
}
