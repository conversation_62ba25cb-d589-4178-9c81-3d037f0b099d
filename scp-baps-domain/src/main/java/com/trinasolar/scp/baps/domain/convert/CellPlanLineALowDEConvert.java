package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineALowQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.ConfigCellAMinusQuery;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineALow;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineALowDTO;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineALowExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellPlanLineALowSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划表A- DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanLineALowDEConvert extends BaseDEConvert<CellPlanLineALowDTO, CellPlanLineALow> {

    CellPlanLineALowDEConvert INSTANCE = Mappers.getMapper(CellPlanLineALowDEConvert.class);

    List<CellPlanLineALowExcelDTO> toExcelDTO(List<CellPlanLineALowDTO> dtos);

    CellPlanLineALowExcelDTO toExcelDTO(CellPlanLineALowDTO dto);
    CellPlanLineALowDTO cellPlanLineALowDTOCopy(CellPlanLineALowDTO dto);
    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellPlanLineALow saveDTOtoEntity(CellPlanLineALowSaveDTO saveDTO, @MappingTarget CellPlanLineALow entity);

    CellPlanLineQuery toCellPlanLineQuery(CellPlanLineALowQuery query);
    @Mappings(
            {
                    @Mapping(target = "cellType",source = "cellsType")
            }
    )
    ConfigCellAMinusQuery toConfigCellAMinusQuery(CellPlanLineALowQuery query);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    CellPlanLineALowQuery toCellPlanLineALowQueryByName(CellPlanLineALowQuery query);

    CellInstockPlanQuery toCellInstockPlanQuery(CellPlanLineALowQuery query);
}
