package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * IE产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_base_capacity")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_base_capacity SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_base_capacity SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellBaseCapacity extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @Column(name = "start_time")
    private LocalDate startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_time")
    private LocalDate endTime;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型id")
    @Column(name = "cells_type_id")
    private Long cellsTypeId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地id")
    @Column(name = "base_place_id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshopid")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元id")
    @Column(name = "workunitid")
    private Long workunitid;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    @Column(name = "line_name")
    private String lineName;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    @Column(name = "number_line")
    private BigDecimal numberLine;

    /**
     * 可用线体数量
     */
    @ApiModelProperty(value = "可用线体数量")
    @Column(name = "usage_line")
    private BigDecimal usageLine;

    /**
     * 产能（单线）
     */
    @ApiModelProperty(value = "产能（单线）")
    @Column(name = "single_capacity")
    private BigDecimal singleCapacity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;
    /**
     * 能否生产单玻
     */
    @ApiModelProperty(value = "能否生产单玻")
    @Column(name = "is_single_glass")
    private String isSingleGlass;
    /**
     * 能否生产低碳
     */
    @ApiModelProperty(value = "能否生产低碳")
    @Column(name = "is_dt")
    private String isDt;
    /**
     * 能否生产小区域国家
     */
    @ApiModelProperty(value = "能否生产小区域国家")
    @Column(name = "is_regional_country")
    private String isRegionalCountry;
    /**
     * 能否生产H兼容
     */
    @ApiModelProperty(value = "能否生产H兼容")
    @Column(name = "is_h_change_flag")
    private String isHChangeFlag;
    /**
     * 能否生产H追溯
     */
    @ApiModelProperty(value = "能否生产H追溯")
    @Column(name = "is_h_trace")
    private String isHTrace;

}
