package com.trinasolar.scp.baps.domain.utils;

/**
 * <AUTHOR>
 * @Date 2024/1/16
 */
public interface RedisLockKey {
    //电池物料分配任务分布式锁
    String SCHEDULED_TASK = "SCHEDULED_TASK";
    /**
     * 长周期需求计算锁key
     */
    String LONG_CYCLE_MATERIALS_SERIAL_TASK = "LONG_CYCLE_MATERIALS_SERIAL_TASK";
    /**
     * BAPS流水号key
     */
    String BAPS_SERIAL_NO = "BAPS_SERIAL_NO";
    //库存趋势表计算分布式锁
    String INVENTORY_TRENDS = "INVENTORY_TRENDS";

    /**
     * 战略备货流水号key
     */
    String STRATEGY_PLAN_SERIAL_NO = "STRATEGY_PLAN_SERIAL_NO";
    /**
     * BAPS redis前缀
     */

    String BAPS_PREFIX="BAPS";
}
