package com.trinasolar.scp.baps.domain.excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;

/**
 * IE产能打折月度（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-28 03:34:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellBaseCapacityMonthDiscountsExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 国内海外id
     */
    @ExcelProperty(value = "国内海外id")
    @ExcelIgnore
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地id
     */
    @ExcelProperty(value = "生产基地id")
    @ExcelIgnore
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间id
     */
    @ExcelProperty(value = "生产车间id")
    @ExcelIgnore
    private Long workshopId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元id
     */
    @ExcelProperty(value = "生产单元id")
    @ExcelIgnore
    private Long workunitId;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * d1
     */
    @ExcelProperty(value = "d1")
    private String d1;
    /**
     * d2
     */
    @ExcelProperty(value = "d2")
    private String d2;
    /**
     * d3
     */
    @ExcelProperty(value = "d3")
    private String d3;
    /**
     * d4
     */
    @ExcelProperty(value = "d4")
    private String d4;
    /**
     * d5
     */
    @ExcelProperty(value = "d5")
    private String d5;
    /**
     * d6
     */
    @ExcelProperty(value = "d6")
    private String d6;
    /**
     * d7
     */
    @ExcelProperty(value = "d7")
    private String d7;
    /**
     * d8
     */
    @ExcelProperty(value = "d8")
    private String d8;
    /**
     * d9
     */
    @ExcelProperty(value = "d9")
    private String d9;
    /**
     * d10
     */
    @ExcelProperty(value = "d10")
    private String d10;
    /**
     * d11
     */
    @ExcelProperty(value = "d11")
    private String d11;
    /**
     * d12
     */
    @ExcelProperty(value = "d12")
    private String d12;
    /**
     * d13
     */
    @ExcelProperty(value = "d13")
    private String d13;
    /**
     * d14
     */
    @ExcelProperty(value = "d14")
    private String d14;
    /**
     * d15
     */
    @ExcelProperty(value = "d15")
    private String d15;
    /**
     * d16
     */
    @ExcelProperty(value = "d16")
    private String d16;
    /**
     * d17
     */
    @ExcelProperty(value = "d17")
    private String d17;
    /**
     * d18
     */
    @ExcelProperty(value = "d18")
    private String d18;
    /**
     * d19
     */
    @ExcelProperty(value = "d19")
    private String d19;
    /**
     * d20
     */
    @ExcelProperty(value = "d20")
    private String d20;
    /**
     * d21
     */
    @ExcelProperty(value = "d21")
    private String d21;
    /**
     * d22
     */
    @ExcelProperty(value = "d22")
    private String d22;
    /**
     * d23
     */
    @ExcelProperty(value = "d23")
    private String d23;
    /**
     * d24
     */
    @ExcelProperty(value = "d24")
    private String d24;
    /**
     * d25
     */
    @ExcelProperty(value = "d25")
    private String d25;
    /**
     * d26
     */
    @ExcelProperty(value = "d26")
    private String d26;
    /**
     * d27
     */
    @ExcelProperty(value = "d27")
    private String d27;
    /**
     * d28
     */
    @ExcelProperty(value = "d28")
    private String d28;
    /**
     * d29
     */
    @ExcelProperty(value = "d29")
    private String d29;
    /**
     * d30
     */
    @ExcelProperty(value = "d30")
    private String d30;
    /**
     * d31
     */
    @ExcelProperty(value = "d31")
    private String d31;
}
