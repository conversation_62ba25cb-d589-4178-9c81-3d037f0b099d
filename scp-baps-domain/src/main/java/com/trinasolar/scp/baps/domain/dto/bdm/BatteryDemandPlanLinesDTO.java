package com.trinasolar.scp.baps.domain.dto.bdm;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.annotation.ConvertType;
import com.trinasolar.scp.common.api.annotation.ImportConvert;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 需求计划明细（APS）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-21 11:42:06
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "需求计划明细（APS）DTO对象", description = "DTO对象")
public class BatteryDemandPlanLinesDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 后端提供给APS的唯一ID
     */
    @ApiModelProperty(value = "后端提供给APS的唯一ID")
    private String dpId;
    /**
     * 电池需求计划号
     */
    @ApiModelProperty(value = "电池需求计划号")
    private String demandPlanCode;
    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceType;
    /**
     * 国内/海外名称
     */
    @ApiModelProperty(value = "国内/海外名称")
    private String domesticOverseaName;
    /**
     * 电池类型名称
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.BATTERY_TYPE, convertType = ConvertType.ID, convertTargetFieldName = "batteryId")
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private Long batteryId;
    /**
     * 电池物料编码
     */
    @ApiModelProperty(value = "电池物料编码")
    private String batteryMaterialCode;
    /**
     * H追溯名称
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.H_TRACE, convertType = ConvertType.ID, convertTargetFieldName = "hTraceId")
    @ApiModelProperty(value = "H追溯名称")
    private String hTraceName;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private Long hTraceId;
    /**
     * 片源种类名称
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.CELL_SOURCE, convertType = ConvertType.ID, convertTargetFieldName = "pcsSourceTypeId")
    @ApiModelProperty(value = "片源种类名称")
    private String pcsSourceTypeName;
    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    private Long pcsSourceTypeId;
    /**
     * 透明双波名称
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS, convertType = ConvertType.ID, convertTargetFieldName = "transparentDoubleGlassId")
    @ApiModelProperty(value = "透明双波名称")
    private String transparentDoubleGlassName;
    /**
     * 透明双波
     */
    @ApiModelProperty(value = "透明双波")
    private Long transparentDoubleGlassId;
    /**
     * 美学名称
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.AESTHETICS, convertType = ConvertType.ID, convertTargetFieldName = "aestheticsId")
    @ApiModelProperty(value = "美学名称")
    private String aestheticsName;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private Long aestheticsId;
    /**
     * 小区域国家名称
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.REGIONAL_COUNTRY, convertType = ConvertType.ID, required = false, convertTargetFieldName = "regionalCountryId")
    @ApiModelProperty(value = "小区域国家名称")
    private String regionalCountryName;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private Long regionalCountryId;
    /**
     * 需求地名称
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.BASE_PLACE, convertType = ConvertType.ID, required = false, convertTargetFieldName = "basePlaceId")
    @ApiModelProperty(value = "需求地名称")
    private String basePlaceName;
    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    private Long basePlaceId;
    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;
    /**
     * 起始效率
     */
    @ApiModelProperty(value = "起始效率")
    private BigDecimal startEfficiency;
    /**
     * 电池特殊单号
     */
    @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;
    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    private LocalDate demandDate;
    /**
     * 符合率
     */
    @ApiModelProperty(value = "符合率")
    private BigDecimal passPercent;
    /**
     * 电池生产车间
     */
    @ApiModelProperty(value = "电池生产车间")
    private String scheduleWorkshop;
    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    private String processCategoryName;
    /**
     * 需求数量(汇总)
     */
    @ApiModelProperty(value = "需求数量(汇总)")
    private BigDecimal demandQty;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    private String supplyMethod;


    @ApiModelProperty("配比")
    private String ratioCode;

    /**
     * 外部需求Id 排产万用来回写计划库存释放日期的
     */
    @ApiModelProperty("外部需求ID")
    private String exteriorDemandId;

    public String compatibleDtAndGlass() {
        return StringUtils.join(this.batteryId, this.aestheticsId, this.transparentDoubleGlassId, this.pcsSourceTypeId, StringUtils.isBlank(this.regionalCountryName),StringUtils.isBlank(this.mainGridSpace) ? "无" : this.mainGridSpace,StringUtils.isBlank(this.supplyMethod)? "无" : this.supplyMethod,StringUtils.isBlank(this.ratioCode) ? "无" : this.ratioCode);
    }

    public String compatibleSign() {
        return StringUtils.join(this.batteryId, this.hTraceId, this.aestheticsId, this.transparentDoubleGlassId, this.pcsSourceTypeId, StringUtils.isBlank(this.regionalCountryName),StringUtils.isBlank(this.mainGridSpace) ? "无" : this.mainGridSpace,StringUtils.isBlank(this.supplyMethod)? "无" : this.supplyMethod,StringUtils.isBlank(this.ratioCode) ? "无" : this.ratioCode);
    }

    public String compatibleDoubleGlass() {
        return StringUtils.join(this.batteryId, this.aestheticsId, this.pcsSourceTypeId, StringUtils.isBlank(this.regionalCountryName),StringUtils.isBlank(this.mainGridSpace) ? "无" : this.mainGridSpace);
    }

    public String compatibleAesthetics() {
        return StringUtils.join(this.batteryId, this.transparentDoubleGlassId, this.pcsSourceTypeId, StringUtils.isBlank(this.regionalCountryName),StringUtils.isBlank(this.mainGridSpace) ? "无" : this.mainGridSpace);
    }

    public String compatibleAestheticsAndDoubleGlass() {
        return StringUtils.join(this.batteryId, this.pcsSourceTypeId, StringUtils.isBlank(this.regionalCountryName),StringUtils.isBlank(this.mainGridSpace) ? "无" : this.mainGridSpace);
    }

    public String compatibleRegionalCountry() {
        return StringUtils.join(this.batteryId, this.aestheticsId, this.transparentDoubleGlassId, this.pcsSourceTypeId,StringUtils.isBlank(this.mainGridSpace) ? "无" : this.mainGridSpace);
    }

    public String compatibleHTrace() {
        return StringUtils.join(this.batteryId, this.aestheticsId, this.transparentDoubleGlassId, this.pcsSourceTypeId, StringUtils.isBlank(this.regionalCountryName), StringUtils.isBlank(this.mainGridSpace) ? "无" : this.mainGridSpace, StringUtils.isBlank(this.supplyMethod) ? "无" : this.supplyMethod);
    }
}
