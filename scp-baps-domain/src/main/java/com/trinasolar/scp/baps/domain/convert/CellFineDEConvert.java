package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellFineDTO;
import com.trinasolar.scp.baps.domain.entity.CellFine;
import com.trinasolar.scp.baps.domain.entity.CellFineMonth;
import com.trinasolar.scp.baps.domain.excel.CellFineExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellFineQuery;
import com.trinasolar.scp.baps.domain.save.CellFineSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池良率表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Mapper(componentModel = "spring",
        imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellFineDEConvert extends BaseDEConvert<CellFineDTO, CellFine> {

    CellFineDEConvert INSTANCE = Mappers.getMapper(CellFineDEConvert.class);

    List<CellFineExcelDTO> toExcelDTO(List<CellFineDTO> dtos);
    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings(
            {
                    @Mapping(target = "cellType",source = "cellsType")
            }
    )
    CellFineMonth toCellFineMonth(CellFine cellFine);
    CellFineExcelDTO toExcelDTO(CellFineDTO dto);
    @Override
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellFine.getCellsTypeId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellFine.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellFine.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellFine.getWorkshopid()))"),
                    @Mapping(target = "m1",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM1(),2))"),
                    @Mapping(target = "m2",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM2(),2))"),
                    @Mapping(target = "m3",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM3(),2))"),
                    @Mapping(target = "m4",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM4(),2))"),
                    @Mapping(target = "m5",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM5(),2))"),
                    @Mapping(target = "m6",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM6(),2))"),
                    @Mapping(target = "m7",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM7(),2))"),
                    @Mapping(target = "m8",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM8(),2))"),
                    @Mapping(target = "m9",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM9(),2))"),
                    @Mapping(target = "m10",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM10(),2))"),
                    @Mapping(target = "m11",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM11(),2))"),
                    @Mapping(target = "m12",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellFine.getM12(),2))"),
            }
    )
    CellFineDTO toDto(CellFine cellFine);
    @Mappings(
            {
                    @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType()).getLovLineId())"),
                    @Mapping(target = "isOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOversea()).getLovLineId())"),
                    @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "workshopid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop()).getLovLineId())"),
            }
    )
    CellFineSaveDTO excelDtoToSaveDto(CellFineExcelDTO excelDTO);

    List<CellFineSaveDTO> excelDtoToSaveDto(List<CellFineExcelDTO> dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,query.getCellsType(),lang))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,query.getIsOversea(),lang))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,query.getBasePlace(),lang))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,query.getWorkshop(),lang))")
            }
    )
    CellFineQuery toCellFineQueryCn(CellFineQuery query, String lang);
}
