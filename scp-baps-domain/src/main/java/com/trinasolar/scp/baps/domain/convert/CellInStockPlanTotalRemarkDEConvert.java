package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellInStockPlanTotalRemarkDTO;
import com.trinasolar.scp.baps.domain.entity.CellInStockPlanTotalRemark;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellInStockPlanTotalRemarkDEConvert extends BaseDEConvert<CellInStockPlanTotalRemarkDTO, CellInStockPlanTotalRemark> {

    CellInStockPlanTotalRemarkDEConvert INSTANCE = Mappers.getMapper(CellInStockPlanTotalRemarkDEConvert.class);
}
