package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 货位对应车间关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 02:57:01
 */
@Data
@ApiModel(value = "CellLocatorWorkshopRelation查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellLocatorWorkshopRelationQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 货位
         */
        @ApiModelProperty(value = "货位")
    private String locator;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshop;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
