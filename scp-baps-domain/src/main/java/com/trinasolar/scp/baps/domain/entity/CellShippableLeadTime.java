package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.hibernate.annotations.GenericGenerator;

/**
 * 可发货计划提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-08 06:14:56
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_shippable_lead_time")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_shippable_lead_time SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_shippable_lead_time SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellShippableLeadTime extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cell_type_id")
    private Long cellTypeId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cell_type")
    private String cellType;

    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "work_shop_id")
    private Long workShopId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "work_shop")
    private String workShop;
    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元ID")
    @Column(name = "work_unit_id")
    private Long workUnitId;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "work_unit")
    private String workUnit;
    /**
     * 预留天数
     */
    @ApiModelProperty(value = "预留天数")
    @Column(name = "buff_days")
    private Integer buffDays;

    /**
     * 可发货浮动系数
     */
    @ApiModelProperty(value = "可发货浮动系数")
    @Column(name = "rate")
    private BigDecimal rate;
}
