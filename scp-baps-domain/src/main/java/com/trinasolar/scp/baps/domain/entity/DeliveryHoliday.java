package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 物流节假日表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
@Entity
@ToString
@Data
@Table(name = "baps_delivery_holiday")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_delivery_holiday SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_delivery_holiday SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryHoliday extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 发货基地
     */
    @ApiModelProperty(value = "发货基地")
    @Column(name = "base_place_from")
    private String basePlaceFrom;

    /**
     * 到货基地
     */
    @ApiModelProperty(value = "到货基地")
    @Column(name = "base_place_to")
    private String basePlaceTo;

    /**
     * 年月
     */
    @ApiModelProperty(value = "年月")
    @Column(name = "month")
    private String month;

    /**
     * 日期区间
     */
    @ApiModelProperty(value = "日期区间")
    @Column(name = "date_between")
    private String dateBetween;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;


}
