package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.excel.CellVersionQopIeExcelDTO;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanQop;
import com.trinasolar.scp.baps.domain.dto.CellVersionPlanQopDTO;
import com.trinasolar.scp.baps.domain.excel.CellVersionPlanQopExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellVersionPlanQopSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 计划与qop DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellVersionPlanQopDEConvert extends BaseDEConvert<CellVersionPlanQopDTO, CellVersionPlanQop> {

    CellVersionPlanQopDEConvert INSTANCE = Mappers.getMapper(CellVersionPlanQopDEConvert.class);

    List<CellVersionPlanQopExcelDTO> toExcelDTO(List<CellVersionPlanQopDTO> dtos);

    CellVersionPlanQopExcelDTO toExcelDTO(CellVersionPlanQopDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellVersionPlanQop saveDTOtoEntity(CellVersionPlanQopSaveDTO saveDTO, @MappingTarget CellVersionPlanQop entity);

    /**
     * 拼接查询qop
     * @param query
     * @return
     */
    QopDetailsQuery toQopDetailsQuery(CellVersionPlanQopQuery query);

    /**
     * 拼接查询计划
     * @param query
     * @return
     */
    CellProductionPlanTotalQuery toCellProductionPlanTotalQuery(CellVersionPlanQopQuery query);
    CellPlanLineTotalQuery toCellPlanPlanTotalQuery(CellVersionPlanQopQuery query);

    CellInstockPlanTotalQuery toCellInstockPlanTotalQuery(CellVersionPlanQopQuery query);
     CellVersionPlanQopExcelDTO toExcelDTOFromEntity(CellVersionPlanQop cellVersionPlanQop);
    List<CellVersionPlanQopExcelDTO> toExcelDTOFromEntity(List<CellVersionPlanQop> cellVersionPlanQopCollect);
}
