package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "实际入库覆盖排产DTO对象", description = "实际入库覆盖排产DTO对象")
public class CalcDemandDto {
   @NotEmpty
    @ApiModelProperty(value = "月份")
    private  String month;
   @NotEmpty
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    @NotEmpty
    @ApiModelProperty(value = "版本")
    private String version;
    @NotNull
    @ApiModelProperty(value = "覆盖日期")
    private LocalDate coverDate;
    @NotNull
    @ApiModelProperty(value = "锁定日期")
    private LocalDate lockDate;
}
