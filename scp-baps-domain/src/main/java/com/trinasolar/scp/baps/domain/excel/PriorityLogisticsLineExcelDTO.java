package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 物流线路优先级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:51:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PriorityLogisticsLineExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    @ExcelIgnore
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 需求基地Id
     */
    @ExcelProperty(value = "需求基地Id")
    @ExcelIgnore
    private Long demandBasePlaceId;
    /**
     * 需求基地
     */
    @ExcelProperty(value = "需求基地")
    private String demandBasePlace;

    /**
     * 优先级
     */
    @ExcelProperty(value = "优先级")
    private Integer priority;

    /**
     * 天数
     */
    @ExcelProperty(value = "天数")
    private Integer days;
}
