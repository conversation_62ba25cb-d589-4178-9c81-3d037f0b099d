package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;


/**
 * 可发货计划提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-08 06:14:56
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "可发货计划提前期DTO对象", description = "DTO对象")
public class CellShippableLeadTimeDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workShopId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workShop;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workUnitId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产单元")
    private String workUnit;
    /**
     * 预留天数
     */
    @ApiModelProperty(value = "预留天数")
    private Integer buffDays;
    /**
     * 可发货浮动系数
     */
    @ApiModelProperty(value = "可发货浮动系数")
    private BigDecimal rate;
    /**
     * 可发货浮动系数百分比
     */
    @ApiModelProperty(value = "可发货浮动系数百分比")
    public String getRatePercent(){
        return MapStrutUtil.addPercentageIgnoreZero(rate,2);
    }
    //单元不为空的
    public String filedGroupTwo(){
        return this.basePlace+"/"+this.workShop+"/"+this.workUnit+"/"+this.cellType;
    }
    //单元为空的
    public String filedGroupTwoNoWorkUnit(){
        return this.basePlace+"/"+this.workShop+"/null/"+this.cellType;
    }
}
