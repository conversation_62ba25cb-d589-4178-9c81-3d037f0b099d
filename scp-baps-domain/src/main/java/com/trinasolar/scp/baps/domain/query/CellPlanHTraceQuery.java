package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.annotation.ConvertType;
import com.trinasolar.scp.common.api.annotation.ImportConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: CellPlanHTraceQuery
 * @date 2024/6/5 10:10
 */
@Data
@ApiModel(value = "CellPlanHTrace查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellPlanHTraceQuery {
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String beginMonth;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String endMonth;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private List<String> monthList;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.DOMESTIC_OVERSEA, convertType = ConvertType.ID, convertTargetFieldName = "isOverseaId")
    @ApiModelProperty(value = "国内海外")
    private String isOversea;

}
