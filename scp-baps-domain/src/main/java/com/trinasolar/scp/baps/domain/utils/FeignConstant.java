package com.trinasolar.scp.baps.domain.utils;

/**
 * Feign常量
 *
 * <AUTHOR>
 * @date 2022年9月9日11:05:26
 */
public interface FeignConstant {
    public static final String FEIGN_NAME_ERP = "ERP";
    public static final String FEIGN_NAME_CUX3WIP_ERP = "CUX3WIPERP";
    public static final String FEIGN_NAME_BPM = "BPM";

    public static final String FEIGN_NAME_SRM = "SRM";

    String SCP_SYSTEM_API = "scp-system-api";

    String SCP_DP_API = "scp-dp-api";

    String SCP_BOM_API = "scp-bom-api";

    String SCP_CERT_API = "scp-cert-api";

    String SCP_APS_API = "scp-aps-api";

    String SCP_AOP_API = "scp-aop-api";

    String SCP_BATTERY_DM_API = "scp-battery-dm-api";

    String SCP_BATTERY_MRP_API = "scp-battery-mrp-api";

    String SCP_BATTERY_BOM_API = "scp-battery-bom-api";

    String SCP_BATTERY_APS_API = "scp-battery-aps-api";
   String DPA_MESSAGE_SERVICE="scp-dpa-message-api";
}
