package com.trinasolar.scp.baps.domain.dto;

import com.google.common.collect.Lists;
import com.trinasolar.scp.baps.domain.HTraceWarnType;
import com.trinasolar.scp.baps.domain.constant.CommonConstant;
import com.trinasolar.scp.baps.domain.utils.MathUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: HTraceResult
 * @date 2024/6/12 09:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HTraceDayResult implements Serializable {
    /**
     * 生产基地
     */
    private String basePlace;
    /**
     * 生产车间
     */
    private String workshop;

    /**
     * 共用车间
     */
    private String shareWorkshop;

    /**
     * 品类
     */
    private String productCategory;
    /**
     * 晶体类型
     */
    private String crystalType;
    /**
     * H追溯
     */
    private String hTrace;
    /**
     * 月份
     */
    private String month;
    /**
     * 天
     */
    private Integer day;
    /**
     * 日期
     */
    private LocalDate date;
    /**
     * 类型
     */
    private String type;
    /**
     * 数量
     */
    private BigDecimal quantity;

    public static List<HTraceDayResult> build(HTraceWarnType type, Map<String, HTraceSupply> supplyMap) {
        List<HTraceDayResult> resultList = new ArrayList<>();
        supplyMap.forEach((hTrace, supply) -> {
            HTraceDayResult result = new HTraceDayResult();
            result.setBasePlace(supply.getBasePlace());
            result.setWorkshop(supply.getWorkshop());
            result.setShareWorkshop(supply.getWorkshop());
            result.setProductCategory(supply.getProductCategory());
            result.setCrystalType(supply.getCrystalType());
            result.setHTrace(hTrace);
            result.setMonth(supply.getMonth());
            result.setDay(supply.getDay());
            result.setDate(supply.getSupplyDate());
            result.setType(type.getCode());
            result.setQuantity(supply.getQuantity().add(supply.getSurplus()));
            resultList.add(result);
        });
        return resultList;
    }

    public static List<HTraceDayResult> build(HTraceWarnType type, Map<String, HTraceSupply> supplyMap, BigDecimal balance, BigDecimal hBalance) {
        List<HTraceDayResult> resultList = new ArrayList<>();
        supplyMap.forEach((hTrace, supply) -> {
            HTraceDayResult result = new HTraceDayResult();
            result.setBasePlace(supply.getBasePlace());
            result.setWorkshop(supply.getWorkshop());
            result.setShareWorkshop(supply.getWorkshop());
            result.setProductCategory(supply.getProductCategory());
            result.setCrystalType(supply.getCrystalType());
            result.setHTrace(hTrace);
            result.setMonth(supply.getMonth());
            result.setDay(supply.getDay());
            result.setDate(supply.getSupplyDate());
            result.setType(type.getCode());
            result.setQuantity(CommonConstant.H.equals(hTrace) ? hBalance : balance);
            resultList.add(result);
        });
        return resultList;
    }

    public static HTraceDayResult build(HTraceWarnType type, HTracePlan plan) {
        HTraceDayResult result = new HTraceDayResult();
        result.setBasePlace(plan.getBasePlace());
        result.setWorkshop(plan.getWorkshop());
        result.setShareWorkshop(plan.getShareWorkshop());
        result.setProductCategory(plan.getProductCategory());
        result.setCrystalType(plan.getCrystalType());
        result.setHTrace(plan.getHTrace());
        result.setMonth(plan.getMonth());
        result.setDay(plan.getDay());
        result.setDate(plan.getPlanDate());
        result.setType(type.getCode());
        result.setQuantity(plan.getQuantity());
        return result;
    }

    public static HTraceDayResult build(HTraceWarnType type, HTracePlan plan, BigDecimal quantity) {
        HTraceDayResult result = new HTraceDayResult();
        result.setBasePlace(plan.getBasePlace());
        result.setWorkshop(plan.getWorkshop());
        result.setShareWorkshop(plan.getShareWorkshop());
        result.setProductCategory(plan.getProductCategory());
        result.setCrystalType(plan.getCrystalType());
        result.setHTrace(plan.getHTrace());
        result.setMonth(plan.getMonth());
        result.setDay(plan.getDay());
        result.setDate(plan.getPlanDate());
        result.setType(type.getCode());
        result.setQuantity(quantity);
        return result;
    }

    /**
     * 电池排产，安全库存
     *
     * @param planMap
     * @return
     */
    public static List<HTraceDayResult> build(Map<String, List<HTracePlan>> planMap) {
        String CELL_SCHEDULE = HTraceWarnType.CELL_SCHEDULE.getCode();
        String SAFETY_STOCK = HTraceWarnType.SAFETY_STOCK.getCode();

        List<HTraceDayResult> resultList = Lists.newArrayList();
        planMap.forEach((hTrace, planList) -> {
            planList.forEach(plan -> {
                //排产数量
                HTraceDayResult result = new HTraceDayResult();
                result.setBasePlace(plan.getBasePlace());
                result.setWorkshop(plan.getWorkshop());
                result.setShareWorkshop(plan.getShareWorkshop());
                result.setProductCategory(plan.getProductCategory());
                result.setCrystalType(plan.getCrystalType());
                result.setHTrace(hTrace);
                result.setMonth(plan.getMonth());
                result.setDay(plan.getDay());
                result.setDate(plan.getPlanDate());
                result.setType(CELL_SCHEDULE);
                result.setQuantity(plan.getQuantity());
                resultList.add(result);
                //安全库存
                HTraceDayResult clone = SerializationUtils.clone(result);
                clone.setType(SAFETY_STOCK);
                clone.setQuantity(plan.getSafetyStockQuantity());
                resultList.add(clone);
            });
        });
        return resultList;
    }


    public static Collection<? extends HTraceDayResult> build(HTraceWarnType type, BigDecimal hBalance, BigDecimal balance, HTraceDayResult dayResult) {
        List<HTraceDayResult> resultList = new ArrayList<>();
        //H
        HTraceDayResult hResult = new HTraceDayResult();
        hResult.setBasePlace(dayResult.getBasePlace());
        hResult.setWorkshop(dayResult.getShareWorkshop());
        hResult.setProductCategory(dayResult.getProductCategory());
        hResult.setCrystalType(dayResult.getCrystalType());
        hResult.setHTrace(CommonConstant.H);
        hResult.setMonth(dayResult.getMonth());
        hResult.setDay(dayResult.getDay());
        hResult.setDate(dayResult.getDate());
        hResult.setType(type.getCode());
        hResult.setQuantity(hBalance);
        resultList.add(hResult);
        //常规
        HTraceDayResult result = new HTraceDayResult();
        result.setBasePlace(dayResult.getBasePlace());
        result.setWorkshop(dayResult.getShareWorkshop());
        result.setProductCategory(dayResult.getProductCategory());
        result.setCrystalType(dayResult.getCrystalType());
        result.setHTrace(CommonConstant.NON);
        result.setMonth(dayResult.getMonth());
        result.setDay(dayResult.getDay());
        result.setDate(dayResult.getDate());
        result.setType(type.getCode());
        result.setQuantity(balance);
        resultList.add(result);
        return resultList;
    }

    public String compatibleSign() {
        return StringUtils.join(this.basePlace, this.workshop, this.productCategory, this.crystalType, this.hTrace, this.date);
    }

    public String hSurplusSign() {
        return StringUtils.join(this.basePlace, this.shareWorkshop, this.productCategory, this.crystalType);
    }
}
