package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "查询条件", description = "查询条件")
@Accessors(chain = true)
public class ScheduledTaskLinesQuery extends PageDTO {

    @ApiModelProperty(value = "任务名称")
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "提交时间起")
    private LocalDateTime requestDateFrom;

    @ApiModelProperty(value = "提交时间止")
    private LocalDateTime requestDateTo;

    @ApiModelProperty(value = "完成时间起")
    private LocalDateTime completeDateFrom;
    @ApiModelProperty(value = "完成时间止")
    private LocalDateTime completeDateTo;

    @ApiModelProperty(value = "提交人")
    private String requestBy;
}
