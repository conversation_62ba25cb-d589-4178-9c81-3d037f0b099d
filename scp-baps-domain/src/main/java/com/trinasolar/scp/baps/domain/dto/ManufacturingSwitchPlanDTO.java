package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2024/8/20
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "制造工艺切换计划维护表DTO对象", description = "DTO对象")
public class ManufacturingSwitchPlanDTO {

    @ApiModelProperty(value = "国内/海外")
    private String isOversea;

    @ApiModelProperty(value = "国内/海外")
    private Long isOverseaId;

    @ApiModelProperty(value = "制造工艺")
    private String manufacturing;

    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;

    @ApiModelProperty(value = "生产车间")
    private String workshop;

    @ApiModelProperty(value = "生产车间Id")
    private Long workshopid;

    @ApiModelProperty(value = "生产单元")
    private String workunit;

    @ApiModelProperty(value = "生产单元")
    private Long workunitid;

    @ApiModelProperty(value = "生产产线")
    private String productionLine;

    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;

    @ApiModelProperty(value = "产能")
    private BigDecimal capacityQuantity;

    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;
}
