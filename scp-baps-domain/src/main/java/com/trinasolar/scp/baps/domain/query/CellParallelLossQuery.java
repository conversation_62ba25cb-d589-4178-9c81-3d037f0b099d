package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

import javax.persistence.Column;

/**
 * 产能并行损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@ApiModel(value = "CellParallelLoss查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellParallelLossQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 并行类型
     */
        @ApiModelProperty(value = "并行类型")
    private String parallelType;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellsType;
        /**
         * 生产基地
         */
        @ApiModelProperty(value = "生产基地")
    private String basePlace;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshop;
        /**
         * 生产单元
         */
        @ApiModelProperty(value = "生产单元")
    private String workunit;
        /**
         * 线体数量
         */
        @ApiModelProperty(value = "线体数量")
    private  BigDecimal lineNumber;
        /**
         * 月份
         */
        @ApiModelProperty(value = "月份")
    private String month;
        /**
         * 损失产能
         */
        @ApiModelProperty(value = "损失产能")
    private BigDecimal loss;
        /**
         * 单位
         */
        @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
