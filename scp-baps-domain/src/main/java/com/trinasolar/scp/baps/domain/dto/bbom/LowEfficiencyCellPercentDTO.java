package com.trinasolar.scp.baps.domain.dto.bbom;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value = "LowEfficiencyCellPercentDTO对象", description = "DTO对象")
public class LowEfficiencyCellPercentDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外lovValue")
    private String countryFlag;


    @ApiModelProperty(value = "国内/海外lovName")
    private String countryFlagName;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间lovValue")
    private String workshop;

    @ApiModelProperty(value = "车间lovName")
    private String workshopName;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地lovValue")
    private String basePlace;

    @ApiModelProperty(value = "生产基地lovName")
    private String basePlaceName;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型lovValue")
    private String cellType;

    @ApiModelProperty(value = "电池类型lovName")
    private String cellTypeName;

    /**
     * 1月占比
     */
    @ApiModelProperty(value = "1月占比")
    private String m1Percent;

    /**
     * 2月占比
     */
    @ApiModelProperty(value = "2月占比")
    private String m2Percent;

    /**
     * 3月占比
     */
    @ApiModelProperty(value = "3月占比")
    private String m3Percent;

    /**
     * 4月占比
     */
    @ApiModelProperty(value = "4月占比")
    private String m4Percent;

    /**
     * 5月占比
     */
    @ApiModelProperty(value = "5月占比")
    private String m5Percent;

    /**
     * 6月占比
     */
    @ApiModelProperty(value = "6月占比")
    private String m6Percent;

    /**
     * 7月占比
     */
    @ApiModelProperty(value = "7月占比")
    private String m7Percent;

    /**
     * 8月占比
     */
    @ApiModelProperty(value = "8月占比")
    private String m8Percent;

    /**
     * 9月占比
     */
    @ApiModelProperty(value = "9月占比")
    private String m9Percent;

    /**
     * 10月占比
     */
    @ApiModelProperty(value = "10月占比")
    private String m10Percent;

    /**
     * 11月占比
     */
    @ApiModelProperty(value = "11月占比")
    private String m11Percent;

    /**
     * 12月占比
     */
    @ApiModelProperty(value = "12月占比")
    private String m12Percent;


}
