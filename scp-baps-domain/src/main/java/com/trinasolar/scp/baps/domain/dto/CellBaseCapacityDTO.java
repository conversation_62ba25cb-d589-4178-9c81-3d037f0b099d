package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.persistence.Column;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Map;


/**
 * IE产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "IE产能表DTO对象", description = "DTO对象")
public class CellBaseCapacityDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型id")
    private Long cellsTypeId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外id")
    private Long isOverseaId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元id")
    private Long workunitid;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    private BigDecimal numberLine;
    /**
     * 可用线体数量
     */
    @ApiModelProperty(value = "可用线体数量")
    private BigDecimal usageLine;
    /**
     * 产能（单线）
     */
    @ApiModelProperty(value = "产能（单线）")
    private BigDecimal singleCapacity;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 能否生产单玻
     */
    @ApiModelProperty(value = "能否生产单玻")
    private String isSingleGlass;
    /**
     * 能否生产低碳
     */
    @ApiModelProperty(value = "能否生产低碳")
    private String isDt;
    /**
     * 能否生产小区域国家
     */
    @ApiModelProperty(value = "能否生产小区域国家")
    private String isRegionalCountry;
    /**
     * 能否生产H兼容
     */
    @ApiModelProperty(value = "能否生产H兼容")
    private String isHChangeFlag;
    /**
     * 能否生产H追溯
     */
    @ApiModelProperty(value = "能否生产H追溯")
    private String isHTrace;

    /**
     * 处理打折后的日期
     */
    @ApiModelProperty(value = "处理打折后的日期")
    private LocalDate localDate;

    public Pair<String, String> getMonthRange(String month) {
        LocalDate monthStartDate = LocalDate.parse(month + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate monthEndDate = monthStartDate.with(TemporalAdjusters.lastDayOfMonth());
        if (startTime.compareTo(monthStartDate) <= 0 && endTime.compareTo(monthEndDate) >= 0) {
            return Pair.of(monthStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), monthEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else if (startTime.compareTo(monthStartDate) > 0 && endTime.compareTo(monthEndDate) >= 0) {
            return Pair.of(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), monthEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            return Pair.of(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
    }

    public String build(Map<Long, LovLineDTO> lovMap) {
        LovLineDTO lov = lovMap.get(this.cellsTypeId);
        String crystalType = LovUtils.getById(lov.getAttribute2()).getLovValue();
        String productCategory = LovUtils.getById(lov.getAttribute3()).getLovValue();
        return StringUtils.join(this.basePlace, this.workshop, productCategory, crystalType);
    }

    /**
     * 转化为电池型号之前的电池类型
     */
    @ApiModelProperty(value = "转化为电池型号之前的电池类型")
    private String oldCellsType;
}
