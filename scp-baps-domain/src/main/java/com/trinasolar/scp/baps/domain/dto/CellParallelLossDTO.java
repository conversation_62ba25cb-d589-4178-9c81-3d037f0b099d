package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 产能并行损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "产能并行损失表DTO对象", description = "DTO对象")
public class CellParallelLossDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型ID")
    private Long cellsTypeId;

    /**
     * 电池类型two
     */
    @ApiModelProperty(value = "电池类型2-ID")
    private Long cellsTypeTwoId;

    /**
     * 电池类型two
     */
    @ApiModelProperty(value = "电池类型2")
    private String cellsTypeTwo;

    @ApiModelProperty(value = "并行类型-ID")
    private Long parallelTypeId;

    /**
     * 并行类型
     */
    @ApiModelProperty(value = "并行类型")
    private String parallelType;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workunitid;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    private  BigDecimal lineNumber;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 损失产能
     */
    @ApiModelProperty(value = "损失产能")
    private BigDecimal loss;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
}
