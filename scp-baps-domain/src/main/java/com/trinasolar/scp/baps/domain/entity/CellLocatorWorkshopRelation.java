package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 货位对应车间关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 02:57:01
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_locator_workshop_relation")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_locator_workshop_relation SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_locator_workshop_relation SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellLocatorWorkshopRelation extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @Column(name = "locator")
    private String locator;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;


}
