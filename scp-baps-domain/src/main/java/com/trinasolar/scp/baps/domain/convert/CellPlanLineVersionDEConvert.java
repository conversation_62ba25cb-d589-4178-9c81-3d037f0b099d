package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionQueryDto;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineVersion;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionDTO;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineVersionExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellPlanLineVersionSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 投产计划版本管理表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanLineVersionDEConvert extends BaseDEConvert<CellPlanLineVersionDTO, CellPlanLineVersion> {

    CellPlanLineVersionDEConvert INSTANCE = Mappers.getMapper(CellPlanLineVersionDEConvert.class);

    List<CellPlanLineVersionExcelDTO> toExcelDTO(List<CellPlanLineVersionDTO> dtos);

    CellPlanLineVersionExcelDTO toExcelDTO(CellPlanLineVersionDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true),
            @Mapping(target = "isOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, entity.getIsOversea()).getLovLineId())")
    })
    CellPlanLineVersion saveDTOtoEntity(CellPlanLineVersionSaveDTO saveDTO, @MappingTarget CellPlanLineVersion entity);
    @Mappings(
            {
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
            }
    )
    CellPlanLineVersionQueryDto toCellPlanLineVersionQueryDtoByName(CellPlanLineVersionQueryDto query);
}
