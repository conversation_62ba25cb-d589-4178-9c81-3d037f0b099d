package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.query.PriorityLogisticsLineQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.PriorityLogisticsLine;
import com.trinasolar.scp.baps.domain.dto.PriorityLogisticsLineDTO;
import com.trinasolar.scp.baps.domain.excel.PriorityLogisticsLineExcelDTO;
import com.trinasolar.scp.baps.domain.save.PriorityLogisticsLineSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 物流线路优先级 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:51:00
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PriorityLogisticsLineDEConvert extends BaseDEConvert<PriorityLogisticsLineDTO, PriorityLogisticsLine> {

    PriorityLogisticsLineDEConvert INSTANCE = Mappers.getMapper(PriorityLogisticsLineDEConvert.class);

    List<PriorityLogisticsLineExcelDTO> toExcelDTO(List<PriorityLogisticsLineDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(dto.getBasePlaceId()).getLovName())"),
                    @Mapping(target = "demandBasePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(dto.getDemandBasePlaceId()).getLovName())")
            }
    )
    PriorityLogisticsLineExcelDTO toExcelDTO(PriorityLogisticsLineDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    PriorityLogisticsLine saveDTOtoEntity(PriorityLogisticsLineSaveDTO saveDTO, @MappingTarget PriorityLogisticsLine entity);
    @Mappings(
            {
                    @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE,excelDto.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "demandBasePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE,excelDto.getDemandBasePlace()).getLovLineId())"),
            }
    )
    PriorityLogisticsLineSaveDTO excelDtoToSaveDto(PriorityLogisticsLineExcelDTO excelDto);
    List<PriorityLogisticsLineSaveDTO> excelDtoToSaveDto(List<PriorityLogisticsLineExcelDTO> excelDtos);
    @Mappings(
            {
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(dto.getBasePlaceId()).getLovName())"),
                    @Mapping(target = "demandBasePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(dto.getDemandBasePlaceId()).getLovName())")
            }
    )
    @Override
    PriorityLogisticsLineDTO toDto(PriorityLogisticsLine dto);

    @Override
    List<PriorityLogisticsLineDTO> toDto(List<PriorityLogisticsLine> list);

    List<PriorityLogisticsLine> saveDTOListtoEntity(List<PriorityLogisticsLineSaveDTO> toList);
    @Mappings(
            {
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlace(),lang))"),
                    @Mapping(target = "demandBasePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getDemandBasePlace(),lang))")
            }
    )
    PriorityLogisticsLineQuery toPriorityLogisticsLineQueryCNName(PriorityLogisticsLineQuery dto,String lang);
}
