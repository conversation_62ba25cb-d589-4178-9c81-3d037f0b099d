package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import com.trinasolar.scp.common.api.util.DataColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.List;


/**
 * scp-aps全年效率值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerEfficiencyDTO对象", description = "DTO对象")
@Slf4j
public class PowerEfficiencyDTO extends BaseDTO {

    public PowerEfficiencyDTO(Long id, String supplier, String supplyType,String cellType,String beginMonth, String targetEfficiency, String actualEfficiency) {
        this.id = id;
        this.supplier = supplier;
        this.cellType=cellType;
        this.supplyType = supplyType;
        this.beginMonth = beginMonth;
        this.targetEfficiency = targetEfficiency;
        this.actualEfficiency = actualEfficiency;
    }

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;
    /**
     * 增/减规则
     */
    @ApiModelProperty(value = "增/减规则")
    private String rule;
    /**
     * 供应类型:自产/外购
     */
    @ApiModelProperty(value = "供应类型:自产/外购")
    private String supplyType;
    /**
     * 月度起
     */
    @ApiModelProperty(value = "月度起")
    private String beginMonth;
    /**
     * 月度止
     */
    @ApiModelProperty(value = "月度止")
    private String endMonth;
    /**
     * 目标效率
     */
    @ApiModelProperty(value = "目标效率")
    private String targetEfficiency;

    /**
     * 实际效率
     */
    @ApiModelProperty(value = "实际效率")
    private String actualEfficiency;
    /**
     * 基准档位
     */
    @ApiModelProperty(value = "基准档位")
    private String baseGear;

    /**
     * 实际档位
     */
    @ApiModelProperty(value = "实际档位")
    private String realGear;

    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1")
    private String efficiency1;

    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private String efficiency2;

    /**
     * 效率3
     */
    @ApiModelProperty(value = "效率3")
    private String efficiency3;

    /**
     * 效率4
     */
    @ApiModelProperty(value = "效率4")
    private String efficiency4;

    /**
     * 效率5
     */
    @ApiModelProperty(value = "效率5")
    private String efficiency5;

    /**
     * 效率6
     */
    @ApiModelProperty(value = "效率6")
    private String efficiency6;

    /**
     * 效率7
     */
    @ApiModelProperty(value = "效率7")
    private String efficiency7;

    /**
     * 效率8
     */
    @ApiModelProperty(value = "效率8")
    private String efficiency8;

    /**
     * 效率9
     */
    @ApiModelProperty(value = "效率9")
    private String efficiency9;

    /**
     * 效率10
     */
    @ApiModelProperty(value = "效率10")
    private String efficiency10;

    /**
     * 效率11
     */
    @ApiModelProperty(value = "效率11")
    private String efficiency11;

    /**
     * 效率12
     */
    @ApiModelProperty(value = "效率12")
    private String efficiency12;

    /**
     * 效率13
     */
    @ApiModelProperty(value = "效率13")
    private String efficiency13;

    /**
     * 效率14
     */
    @ApiModelProperty(value = "效率14")
    private String efficiency14;

    /**
     * 效率15
     */
    @ApiModelProperty(value = "效率15")
    private String efficiency15;

    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16")
    private String efficiency16;

    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17")
    private String efficiency17;

    /**
     * 效率18
     */
    @ApiModelProperty(value = "效率18")
    private String efficiency18;

    /**
     * 效率19
     */
    @ApiModelProperty(value = "效率19")
    private String efficiency19;

    /**
     * 效率20
     */
    @ApiModelProperty(value = "效率20")
    private String efficiency20;

    /**
     * 效率21
     */
    @ApiModelProperty(value = "效率21")
    private String efficiency21;

    /**
     * 效率22
     */
    @ApiModelProperty(value = "效率22")
    private String efficiency22;

    /**
     * 效率23
     */
    @ApiModelProperty(value = "效率23")
    private String efficiency23;

    /**
     * 效率24
     */
    @ApiModelProperty(value = "效率24")
    private String efficiency24;

    /**
     * 效率25
     */
    @ApiModelProperty(value = "效率25")
    private String efficiency25;

    /**
     * 效率26
     */
    @ApiModelProperty(value = "效率26")
    private String efficiency26;

    /**
     * 效率27
     */
    @ApiModelProperty(value = "效率27")
    private String efficiency27;

    /**
     * 效率28
     */
    @ApiModelProperty(value = "效率28")
    private String efficiency28;

    /**
     * 效率29
     */
    @ApiModelProperty(value = "效率29")
    private String efficiency29;

    /**
     * 效率30
     */
    @ApiModelProperty(value = "效率30")
    private String efficiency30;

    /**
     * 效率31
     */
    @ApiModelProperty(value = "效率31")
    private String efficiency31;

    /**
     * 效率32
     */
    @ApiModelProperty(value = "效率32")
    private String efficiency32;

    /**
     * 效率33
     */
    @ApiModelProperty(value = "效率33")
    private String efficiency33;

    /**
     * 效率34
     */
    @ApiModelProperty(value = "效率34")
    private String efficiency34;

    /**
     * 效率35
     */
    @ApiModelProperty(value = "效率35")
    private String efficiency35;

    /**
     * 效率36
     */
    @ApiModelProperty(value = "效率36")
    private String efficiency36;

    /**
     * 效率37
     */
    @ApiModelProperty(value = "效率37")
    private String efficiency37;

    /**
     * 效率38
     */
    @ApiModelProperty(value = "效率38")
    private String efficiency38;

    /**
     * 效率39
     */
    @ApiModelProperty(value = "效率39")
    private String efficiency39;

    /**
     * 效率40
     */
    @ApiModelProperty(value = "效率40")
    private String efficiency40;

    /**
     * 效率41
     */
    @ApiModelProperty(value = "效率41")
    private String efficiency41;

    /**
     * 效率42
     */
    @ApiModelProperty(value = "效率42")
    private String efficiency42;

    /**
     * 效率43
     */
    @ApiModelProperty(value = "效率43")
    private String efficiency43;

    /**
     * 效率44
     */
    @ApiModelProperty(value = "效率44")
    private String efficiency44;

    /**
     * 效率45
     */
    @ApiModelProperty(value = "效率45")
    private String efficiency45;

    /**
     * 效率46
     */
    @ApiModelProperty(value = "效率46")
    private String efficiency46;

    /**
     * 效率47
     */
    @ApiModelProperty(value = "效率47")
    private String efficiency47;

    /**
     * 效率48
     */
    @ApiModelProperty(value = "效率48")
    private String efficiency48;

    /**
     * 效率49
     */
    @ApiModelProperty(value = "效率49")
    private String efficiency49;

    /**
     * 效率50
     */
    @ApiModelProperty(value = "效率50")
    private String efficiency50;

    @ApiModelProperty(value = "效率51")
    private String efficiency51;
    @ApiModelProperty(value = "效率52")
    private String efficiency52;
    @ApiModelProperty(value = "效率53")
    private String efficiency53;
    @ApiModelProperty(value = "效率54")
    private String efficiency54;
    @ApiModelProperty(value = "效率55")
    private String efficiency55;
    @ApiModelProperty(value = "效率56")
    private String efficiency56;
    @ApiModelProperty(value = "效率57")
    private String efficiency57;
    @ApiModelProperty(value = "效率58")
    private String efficiency58;
    @ApiModelProperty(value = "效率59")
    private String efficiency59;
    @ApiModelProperty(value = "效率60")
    private String efficiency60;
    @ApiModelProperty(value = "效率61")
    private String efficiency61;
    @ApiModelProperty(value = "效率62")
    private String efficiency62;
    @ApiModelProperty(value = "效率63")
    private String efficiency63;
    @ApiModelProperty(value = "效率64")
    private String efficiency64;
    @ApiModelProperty(value = "效率65")
    private String efficiency65;
    @ApiModelProperty(value = "效率66")
    private String efficiency66;
    @ApiModelProperty(value = "效率67")
    private String efficiency67;
    @ApiModelProperty(value = "效率68")
    private String efficiency68;
    @ApiModelProperty(value = "效率69")
    private String efficiency69;
    @ApiModelProperty(value = "效率70")
    private String efficiency70;
    @ApiModelProperty(value = "效率71")
    private String efficiency71;
    @ApiModelProperty(value = "效率72")
    private String efficiency72;
    @ApiModelProperty(value = "效率73")
    private String efficiency73;
    @ApiModelProperty(value = "效率74")
    private String efficiency74;
    @ApiModelProperty(value = "效率75")
    private String efficiency75;
    @ApiModelProperty(value = "效率76")
    private String efficiency76;
    @ApiModelProperty(value = "效率77")
    private String efficiency77;
    @ApiModelProperty(value = "效率78")
    private String efficiency78;
    @ApiModelProperty(value = "效率79")
    private String efficiency79;
    @ApiModelProperty(value = "效率80")
    private String efficiency80;
    @ApiModelProperty(value = "效率81")
    private String efficiency81;
    @ApiModelProperty(value = "效率82")
    private String efficiency82;
    @ApiModelProperty(value = "效率83")
    private String efficiency83;
    @ApiModelProperty(value = "效率84")
    private String efficiency84;
    @ApiModelProperty(value = "效率85")
    private String efficiency85;
    @ApiModelProperty(value = "效率86")
    private String efficiency86;
    @ApiModelProperty(value = "效率87")
    private String efficiency87;
    @ApiModelProperty(value = "效率88")
    private String efficiency88;
    @ApiModelProperty(value = "效率89")
    private String efficiency89;
    @ApiModelProperty(value = "效率90")
    private String efficiency90;
    @ApiModelProperty(value = "效率91")
    private String efficiency91;
    @ApiModelProperty(value = "效率92")
    private String efficiency92;
    @ApiModelProperty(value = "效率93")
    private String efficiency93;
    @ApiModelProperty(value = "效率94")
    private String efficiency94;
    @ApiModelProperty(value = "效率95")
    private String efficiency95;
    @ApiModelProperty(value = "效率96")
    private String efficiency96;
    @ApiModelProperty(value = "效率97")
    private String efficiency97;
    @ApiModelProperty(value = "效率98")
    private String efficiency98;
    @ApiModelProperty(value = "效率99")
    private String efficiency99;
    @ApiModelProperty(value = "效率100")
    private String efficiency100;




    public void fillHead(List<DataColumn> list) {

        Class<? extends PowerEfficiencyDTO> clazz = this.getClass();
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            try {
                declaredField.setAccessible(true);
                Object value = declaredField.get(this);
                if (value != null) {
                    if ("id".equals(declaredField.getName()) || "log".equals(declaredField.getName())) {
                        continue;
                    }
                    DataColumn dataColumn = new DataColumn();
                    dataColumn.setIndex(list.size());
                    dataColumn.setName(declaredField.getName());
                    String str = value.toString();
//                    if(str.contains(".")){
//                        str = new BigDecimal(str).multiply(new BigDecimal("100"))+"%";
//                    }
                    dataColumn.setTitle(str);
                    list.add(dataColumn);
                }
            } catch (IllegalAccessException e) {
                log.warn("【PowerEfficiencyDTO.fillHead】",e);
            }
        }

    }

    public void strJudge(List<String> list, String str) {
        if (StringUtils.isNotBlank(str)) {
            list.add(str);
        }
    }

    public void strToDataColumn(String tile, String name, List<DataColumn> list) {
        DataColumn dataColumn = new DataColumn();
        if (StringUtils.isBlank(tile) || StringUtils.isBlank(name)) {
            return;
        }
        dataColumn.setTitle(tile);
        dataColumn.setName(name);
        dataColumn.setIndex(list.size());
        list.add(dataColumn);
    }
}
