package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 每日结存报表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-12 06:19:24
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_daily_balance")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_daily_balance SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_daily_balance SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellDailyBalance extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内外Id
     */
    @ApiModelProperty(value = "国内外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内外
     */
    @ApiModelProperty(value = "国内外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cell_type_id")
    private Long cellTypeId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cell_type")
    private String cellType;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    @Column(name = "project")
    private String project;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    @Column(name = "h_trace")
    private String hTrace;

    /**
     * 低碳
     */
    @ApiModelProperty(value = "低碳")
    @Column(name = "dt")
    private String dt;

    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    @Column(name = "aesthetics")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    @Column(name = "transparent_double_glass")
    private String transparentDoubleGlass;
    /**
     * 数据来源版本
     */
    @ApiModelProperty(value = "数据来源版本")
    @Column(name = "from_version")
    private String fromVersion;

    /**
     * 1号
     */
    @ApiModelProperty(value = "1号")
    @Column(name = "d1")
    private BigDecimal d1=BigDecimal.ZERO;

    /**
     * 2号
     */
    @ApiModelProperty(value = "2号")
    @Column(name = "d2")
    private BigDecimal d2=BigDecimal.ZERO;

    /**
     * 3号
     */
    @ApiModelProperty(value = "3号")
    @Column(name = "d3")
    private BigDecimal d3=BigDecimal.ZERO;

    /**
     * 4号
     */
    @ApiModelProperty(value = "4号")
    @Column(name = "d4")
    private BigDecimal d4=BigDecimal.ZERO;

    /**
     * 5号
     */
    @ApiModelProperty(value = "5号")
    @Column(name = "d5")
    private BigDecimal d5=BigDecimal.ZERO;

    /**
     * 6号
     */
    @ApiModelProperty(value = "6号")
    @Column(name = "d6")
    private BigDecimal d6=BigDecimal.ZERO;

    /**
     * 7号
     */
    @ApiModelProperty(value = "7号")
    @Column(name = "d7")
    private BigDecimal d7=BigDecimal.ZERO;

    /**
     * 8号
     */
    @ApiModelProperty(value = "8号")
    @Column(name = "d8")
    private BigDecimal d8=BigDecimal.ZERO;

    /**
     * 9号
     */
    @ApiModelProperty(value = "9号")
    @Column(name = "d9")
    private BigDecimal d9=BigDecimal.ZERO;

    /**
     * 10号
     */
    @ApiModelProperty(value = "10号")
    @Column(name = "d10")
    private BigDecimal d10=BigDecimal.ZERO;

    /**
     * 11号
     */
    @ApiModelProperty(value = "11号")
    @Column(name = "d11")
    private BigDecimal d11=BigDecimal.ZERO;

    /**
     * 12号
     */
    @ApiModelProperty(value = "12号")
    @Column(name = "d12")
    private BigDecimal d12=BigDecimal.ZERO;

    /**
     * 13号
     */
    @ApiModelProperty(value = "13号")
    @Column(name = "d13")
    private BigDecimal d13=BigDecimal.ZERO;

    /**
     * 14号
     */
    @ApiModelProperty(value = "14号")
    @Column(name = "d14")
    private BigDecimal d14=BigDecimal.ZERO;

    /**
     * 15号
     */
    @ApiModelProperty(value = "15号")
    @Column(name = "d15")
    private BigDecimal d15=BigDecimal.ZERO;

    /**
     * 16号
     */
    @ApiModelProperty(value = "16号")
    @Column(name = "d16")
    private BigDecimal d16=BigDecimal.ZERO;

    /**
     * 17号
     */
    @ApiModelProperty(value = "17号")
    @Column(name = "d17")
    private BigDecimal d17=BigDecimal.ZERO;

    /**
     * 18号
     */
    @ApiModelProperty(value = "18号")
    @Column(name = "d18")
    private BigDecimal d18=BigDecimal.ZERO;

    /**
     * 19号
     */
    @ApiModelProperty(value = "19号")
    @Column(name = "d19")
    private BigDecimal d19=BigDecimal.ZERO;

    /**
     * 20号
     */
    @ApiModelProperty(value = "20号")
    @Column(name = "d20")
    private BigDecimal d20=BigDecimal.ZERO;

    /**
     * 21号
     */
    @ApiModelProperty(value = "21号")
    @Column(name = "d21")
    private BigDecimal d21=BigDecimal.ZERO;

    /**
     * 22号
     */
    @ApiModelProperty(value = "22号")
    @Column(name = "d22")
    private BigDecimal d22=BigDecimal.ZERO;

    /**
     * 23号
     */
    @ApiModelProperty(value = "23号")
    @Column(name = "d23")
    private BigDecimal d23=BigDecimal.ZERO;

    /**
     * 24号
     */
    @ApiModelProperty(value = "24号")
    @Column(name = "d24")
    private BigDecimal d24=BigDecimal.ZERO;

    /**
     * 25号
     */
    @ApiModelProperty(value = "25号")
    @Column(name = "d25")
    private BigDecimal d25=BigDecimal.ZERO;

    /**
     * 26号
     */
    @ApiModelProperty(value = "26号")
    @Column(name = "d26")
    private BigDecimal d26=BigDecimal.ZERO;

    /**
     * 27号
     */
    @ApiModelProperty(value = "27号")
    @Column(name = "d27")
    private BigDecimal d27=BigDecimal.ZERO;

    /**
     * 28号
     */
    @ApiModelProperty(value = "28号")
    @Column(name = "d28")
    private BigDecimal d28=BigDecimal.ZERO;

    /**
     * 29号
     */
    @ApiModelProperty(value = "29号")
    @Column(name = "d29")
    private BigDecimal d29=BigDecimal.ZERO;

    /**
     * 30号
     */
    @ApiModelProperty(value = "30号")
    @Column(name = "d30")
    private BigDecimal d30=BigDecimal.ZERO;

    /**
     * 31号
     */
    @ApiModelProperty(value = "31号")
    @Column(name = "d31")
    private BigDecimal d31=BigDecimal.ZERO;


}
