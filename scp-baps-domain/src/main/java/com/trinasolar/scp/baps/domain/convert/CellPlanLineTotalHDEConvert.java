package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineTotalH;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalHDTO;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineTotalHExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellPlanLineTotalHSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 投产计划H汇总表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-31 06:53:01
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanLineTotalHDEConvert extends BaseDEConvert<CellPlanLineTotalHDTO, CellPlanLineTotalH> {

    CellPlanLineTotalHDEConvert INSTANCE = Mappers.getMapper(CellPlanLineTotalHDEConvert.class);

    List<CellPlanLineTotalHExcelDTO> toExcelDTO(List<CellPlanLineTotalHDTO> dtos);

    CellPlanLineTotalHExcelDTO toExcelDTO(CellPlanLineTotalHDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellPlanLineTotalH saveDTOtoEntity(CellPlanLineTotalHSaveDTO saveDTO, @MappingTarget CellPlanLineTotalH entity);
   @Named("dtoToEntity")
    CellPlanLineTotalH dtoToEntity(CellPlanLineTotalHDTO cellPlanLineTotalHDTO);
   @IterableMapping(qualifiedByName = "dtoToEntity")
   List< CellPlanLineTotalH> dtoToEntity(List< CellPlanLineTotalHDTO> cellPlanLineTotalHDTOs);
}
