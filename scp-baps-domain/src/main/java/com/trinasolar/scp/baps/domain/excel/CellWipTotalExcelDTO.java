package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import java.io.Serializable;


/**
 * 开立工单明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellWipTotalExcelDTO {

 /**
  * ID主键
  */
 @ExcelProperty(value = "ID主键")
 private Long id;
 /**
  * 订单号
  */
 @ExcelProperty(value = "订单号")
 private String orderCode;
 /**
  * 开单账号
  */
 @ExcelProperty(value = "开单账号")
 private String billingAccount;
 /**
  * 开单日期
  */
 @ExcelProperty(value = "开单日期")
 private LocalDateTime billingDate;
 /**
  * erp工单号
  */
 @ExcelProperty(value = "erp工单号")
 private String erpOrderCode;
 /**
  * 月份
  */
 @ExcelProperty(value = "月份")
 private String month;
 /**
  * 生产基地
  */
 @ExcelProperty(value = "生产基地")
 private String basePlace;
 /**
  * 车间
  */
 @ExcelProperty(value = "车间")
 private String workshop;
 /**
  * 计划数量
  */
 @ExcelProperty(value = "计划数量")
 private BigDecimal qty;
 /**
  * 已开单数量
  */
 @ExcelProperty(value = "已开单数量")
 private BigDecimal qtyBilling;
 /**
  * 累计已开单数量
  */
 @ExcelProperty(value = "累计已开单数量")
 private BigDecimal qtyBillingTotal;
 /**
  * 电池类型
  */
 @ExcelProperty(value = "电池类型")
 private String cellType;
 /**
  * 5a料号
  */
 @ExcelProperty(value = "5a料号")
 private String itemFivea;
 /**
  * 国内海外
  */
 @ExcelProperty(value = "国内海外")
 private String isOversea;
 /**
  * 起始时间
  */
 @ExcelProperty(value = "起始时间")
 private LocalDateTime startTime;
 /**
  * 结束时间
  */
 @ExcelProperty(value = "结束时间")
 private LocalDateTime endTime;
}
