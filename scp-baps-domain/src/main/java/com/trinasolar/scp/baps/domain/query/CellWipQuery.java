package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.baps.domain.dto.CellProductionPlanTotalDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import com.trinasolar.scp.common.api.base.PageDTO;

import javax.persistence.Column;

/**
 * 工单生成预览
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Data
@ApiModel(value = "CellWip查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellWipQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String wipCode;
    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private String category;
    /**
     * 装配件
     */
    @ApiModelProperty(value = "装配件")
    private String itemCode;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String state;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectCode;
    /**
     * 工艺路线替代项
     */
    @ApiModelProperty(value = "工艺路线替代项")
    private String alternateRoutingDesignator;
    /**
     * bom替代项
     */
    @ApiModelProperty(value = "bom替代项")
    private String alternateBomDesignator;
    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    private String organizationId;
    /**
     * 是否哈密瓜
     */
    @ApiModelProperty(value = "是否哈密瓜")
    private String isH;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal qty;
    /**
     * 保税形态
     */
    @ApiModelProperty(value = "保税形态")
    private String bondedForm;
    /**
     * 外协厂家
     */
    @ApiModelProperty(value = "外协厂家")
    private String outsourcingFactory;
    /**
     * 是否提前投产
     */
    @ApiModelProperty(value = "是否提前投产")
    private String isAdvanceProduction;
    /**
     * 是否外售
     */
    @ApiModelProperty(value = "是否外售")
    private String isSell;
    /**
     * 可靠性标识
     */
    @ApiModelProperty(value = "可靠性标识")
    private String reliability;
    /**
     * 验证日期
     */
    @ApiModelProperty(value = "验证日期")
    private LocalDateTime verificationDate;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;

    /**
     * 投产计划汇总表DTO主键
     */
    @ApiModelProperty(value = "投产计划汇总数据")
    private List<CellProductionPlanTotalDTO> planTotalDTOList;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 最終邮件确认发布的版本
     */
    @ApiModelProperty(value = "最終邮件确认发布的版本")
    private String finalVersion;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
}
