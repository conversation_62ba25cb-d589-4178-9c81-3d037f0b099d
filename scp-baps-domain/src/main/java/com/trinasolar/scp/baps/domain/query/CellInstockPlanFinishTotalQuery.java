package com.trinasolar.scp.baps.domain.query;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "CellInstockPlanFinishTotalQuery查询条件", description = "查询条件")
public class CellInstockPlanFinishTotalQuery  extends PageDTO implements Serializable {
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
