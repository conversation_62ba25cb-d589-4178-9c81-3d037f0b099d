package com.trinasolar.scp.baps.domain.dto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 手动设置硅料厂家DTO
 */
@Data
@ApiModel(value = "手动设置硅料厂家DTO", description = "DTO对象")
public class HandSetSiMfrsDto {
    /**
     * 入库计划Id
     */
    @ApiModelProperty(value = "入库计划Id")
    private  Long id;

    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
}
