package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.entity.CellVersionPlanIe;
import com.trinasolar.scp.baps.domain.excel.CellVersionPlanIeExcelDTO;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellVersionQopIe;
import com.trinasolar.scp.baps.domain.dto.CellVersionQopIeDTO;
import com.trinasolar.scp.baps.domain.excel.CellVersionQopIeExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellVersionQopIeSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * qop与ie对比 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:08
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellVersionQopIeDEConvert extends BaseDEConvert<CellVersionQopIeDTO, CellVersionQopIe> {

    CellVersionQopIeDEConvert INSTANCE = Mappers.getMapper(CellVersionQopIeDEConvert.class);

    List<CellVersionQopIeExcelDTO> toExcelDTO(List<CellVersionQopIeDTO> dtos);
    CellVersionQopIeExcelDTO toExcelDTOFromEntity(CellVersionQopIe cellVersionQopIe);
    List<CellVersionQopIeExcelDTO> toExcelDTOFromEntity(List<CellVersionQopIe> cellVersionQopIes);
    CellVersionQopIeExcelDTO toExcelDTO(CellVersionQopIeDTO dto);
    @Mappings(
            {
                    @Mapping(target = "isOversea",source = "isOverseaName"),
                    @Mapping(target = "cellsType",source = "cellTypeName"),
                    @Mapping(target = "workshop",source = "workshopName")
            }
    )
    CellBaseCapacityQuery toCellBaseCapacityQuery(CellVersionQopIeQuery query);
    @Mappings(
            {

                    @Mapping(target = "cellsType",source = "cellTypeName"),
                    @Mapping(target = "workshop",source = "workshopName")
            }
    )
    CellGradeCapacityQuery toCellGradeCapacityQuery(CellVersionQopIeQuery query);
    QopDetailsQuery toQopDetailsQuery(CellVersionQopIeQuery query);
    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellVersionQopIe saveDTOtoEntity(CellVersionQopIeSaveDTO saveDTO, @MappingTarget CellVersionQopIe entity);
}
