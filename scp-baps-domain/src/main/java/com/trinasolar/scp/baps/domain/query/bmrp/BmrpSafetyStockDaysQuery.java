package com.trinasolar.scp.baps.domain.query.bmrp;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/10
 */
@Data
@ApiModel(value = "BmrpSafetyStockDays查询条件", description = "查询条件")
@Accessors(chain = true)
public class BmrpSafetyStockDaysQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = -4496782526662041481L;

    @ApiModelProperty(value = "物料分类")
    private String itemCategory;

    @ApiModelProperty(value = "小区域")
    private String regionalCountry;

    @ApiModelProperty(value = "国内/海外")
    private String countryFlag;

    @ApiModelProperty(value = "基地")
    private String basePlace;

    @ApiModelProperty(value = "基地")
    private List<String> basePlaceList;

    @ApiModelProperty(value = "机位")
    private String cellMachine;

    @ApiModelProperty(value = "月份")
    private String month;
}
