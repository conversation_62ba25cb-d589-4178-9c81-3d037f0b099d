package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellPlanHTraceDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineDTO;
import com.trinasolar.scp.baps.domain.entity.CellPlanLine;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: CellPlanHTraceDEConvert
 * @date 2024/6/5 15:25
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class , LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanHTraceDEConvert {

    List<CellPlanHTraceDTO> toHTraceDTO(List<CellPlanLineDTO> dataList);

}
