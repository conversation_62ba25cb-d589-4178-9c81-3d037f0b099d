package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellResource;
import com.trinasolar.scp.baps.domain.dto.CellResourceDTO;
import com.trinasolar.scp.baps.domain.excel.CellResourceExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellResourceSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池资源表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-08 02:52:37
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellResourceDEConvert extends BaseDEConvert<CellResourceDTO, CellResource> {

    CellResourceDEConvert INSTANCE = Mappers.getMapper(CellResourceDEConvert.class);

    List<CellResourceExcelDTO> toExcelDTO(List<CellResourceDTO> dtos);

    CellResourceExcelDTO toExcelDTO(CellResourceDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellResource saveDTOtoEntity(CellResourceSaveDTO saveDTO, @MappingTarget CellResource entity);
}
