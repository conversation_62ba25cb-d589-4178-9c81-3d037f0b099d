package com.trinasolar.scp.baps.domain.query.bmrp;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 硅片外购计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 03:17:19
 */
@Data
@ApiModel(value = "SiliconSlicePurchasePlan查询条件", description = "查询条件")
@Accessors(chain = true)
public class SiliconSlicePurchasePlanQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String materialNo;
    /**
     * 料号属性
     */
    @ApiModelProperty(value = "料号属性")
    private String materialAttribute;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private List<String> monthList;
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
