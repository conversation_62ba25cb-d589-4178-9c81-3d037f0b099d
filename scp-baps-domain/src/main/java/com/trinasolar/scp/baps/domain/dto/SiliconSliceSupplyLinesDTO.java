package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
@ApiModel(value = "DTO对象", description = "DTO对象")
public class SiliconSliceSupplyLinesDTO {

    @ApiModelProperty(value = "主键")
    private Long supplyLineId;

    @ApiModelProperty(value = "基地")
    private String basePlace;

    @ApiModelProperty(value = "品类")
    private String category;

    @ApiModelProperty(value = "晶体类型")
    private String crystalType;

    @ApiModelProperty(value = "片源等级")
    private String siSourceLevel;

    @ApiModelProperty(value = "加工方式")
    private String processType;
    @ApiModelProperty(value = "加工方式优先级")
    private Integer processTypePrority;
    @ApiModelProperty(value = "基地优先级")
    private Integer basePlacePrority;
    @ApiModelProperty(value = "物料ID")
    private Long itemId;

    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    @ApiModelProperty(value = "物料说明")
    private String itemDesc;

    @ApiModelProperty(value = "单位")
    private String uom;
    
    @ApiModelProperty(value = "需求日期")
    private String month;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("1号数量")
    private BigDecimal d1Quantity;

    @ApiModelProperty("2号数量")
    private BigDecimal d2Quantity;

    @ApiModelProperty("3号数量")
    private BigDecimal d3Quantity;

    @ApiModelProperty("4号数量")
    private BigDecimal d4Quantity;

    @ApiModelProperty("5号数量")
    private BigDecimal d5Quantity;

    @ApiModelProperty("6号数量")
    private BigDecimal d6Quantity;

    @ApiModelProperty("7号数量")
    private BigDecimal d7Quantity;

    @ApiModelProperty("8号数量")
    private BigDecimal d8Quantity;

    @ApiModelProperty("9号数量")
    private BigDecimal d9Quantity;

    @ApiModelProperty("10号数量")
    private BigDecimal d10Quantity;

    @ApiModelProperty("11号数量")
    private BigDecimal d11Quantity;

    @ApiModelProperty("12号数量")
    private BigDecimal d12Quantity;

    @ApiModelProperty("13号数量")
    private BigDecimal d13Quantity;

    @ApiModelProperty("14号数量")
    private BigDecimal d14Quantity;

    @ApiModelProperty("15号数量")
    private BigDecimal d15Quantity;

    @ApiModelProperty("16号数量")
    private BigDecimal d16Quantity;

    @ApiModelProperty("17号数量")
    private BigDecimal d17Quantity;

    @ApiModelProperty("18号数量")
    private BigDecimal d18Quantity;

    @ApiModelProperty("19号数量")
    private BigDecimal d19Quantity;

    @ApiModelProperty("20号数量")
    private BigDecimal d20Quantity;

    @ApiModelProperty("21号数量")
    private BigDecimal d21Quantity;

    @ApiModelProperty("22号数量")
    private BigDecimal d22Quantity;

    @ApiModelProperty("23号数量")
    private BigDecimal d23Quantity;

    @ApiModelProperty("24号数量")
    private BigDecimal d24Quantity;

    @ApiModelProperty("25号数量")
    private BigDecimal d25Quantity;

    @ApiModelProperty("26号数量")
    private BigDecimal d26Quantity;

    @ApiModelProperty("27号数量")
    private BigDecimal d27Quantity;

    @ApiModelProperty("28号数量")
    private BigDecimal d28Quantity;

    @ApiModelProperty("29号数量")
    private BigDecimal d29Quantity;

    @ApiModelProperty("30号数量")
    private BigDecimal d30Quantity;

    @ApiModelProperty("31号数量")
    private BigDecimal d31Quantity;

    @ApiModelProperty(value = "车间")
    private String workshop;
}
