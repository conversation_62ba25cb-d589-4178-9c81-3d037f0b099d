package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * Erp实际入库来源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:23:58
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "Erp实际入库来源表DTO对象", description = "DTO对象")
public class ErpWipIssueDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 事务数量
     */
    @ApiModelProperty(value = "事务数量")
    private BigDecimal transactionQuantity;
    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String itemNumber;
    /**
     * 交易库存组织Id
     */
    @ApiModelProperty(value = "交易库存组织Id")
    private Long transferOrganizationId;
    /**
     * 事务单位编码
     */
    @ApiModelProperty(value = "事务单位编码")
    private String transactionUomCode;
    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;
    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDescription;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    private String locatorCode;
    /**
     * 事务来源类型名
     */
    @ApiModelProperty(value = "事务来源类型名")
    private String transactionSourceTypeName;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrder;
    /**
     * 库存组织名
     */
    @ApiModelProperty(value = "库存组织名")
    private String organizationName;
    /**
     * 事务类型Id
     */
    @ApiModelProperty(value = "事务类型Id")
    private Long transactionTypeId;
    /**
     * lot主要数据量
     */
    @ApiModelProperty(value = "lot主要数据量")
    private BigDecimal lotPrimaryQuantity;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    private String subinventoryCode;
    /**
     * lot编码
     */
    @ApiModelProperty(value = "lot编码")
    private String lotNumber;
    /**
     * 事务处理日期
     */
    @ApiModelProperty(value = "事务处理日期")
    private LocalDateTime transactionDate;
    /**
     * 事务来源Id
     */
    @ApiModelProperty(value = "事务来源Id")
    private Long transactionSourceId;
    /**
     * 事务类型名
     */
    @ApiModelProperty(value = "事务类型名")
    private String transactionTypeName;
    /**
     * 事务Id
     */
    @ApiModelProperty(value = "事务Id")
    private Long transactionId;
    /**
     * 事务来源类型Id
     */
    @ApiModelProperty(value = "事务来源类型Id")
    private Long transactionSourceTypeId;
    /**
     * 物料Id
     */
    @ApiModelProperty(value = "物料Id")
    private Long inventoryItemId;
    /**
     * 库存组织编号
     */
    @ApiModelProperty(value = "库存组织编号")
    private String organizationCode;
    /**
     * 货位Id
     */
    @ApiModelProperty(value = "货位Id")
    private Long locatorId;
    /**
     * 主要数量
     */
    @ApiModelProperty(value = "主要数量")
    private BigDecimal primaryQuantity;
    /**
     * lot事务处理数量
     */
    @ApiModelProperty(value = "lot事务处理数量")
    private BigDecimal lotTransactionQuantity;
    /**
     * erp创建时间
     */
    @ApiModelProperty(value = "erp创建时间")
    private LocalDateTime creationDate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
