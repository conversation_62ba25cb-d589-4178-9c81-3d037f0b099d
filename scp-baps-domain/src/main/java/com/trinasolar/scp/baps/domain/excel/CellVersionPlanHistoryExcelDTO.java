package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;


/**
 * 计划与上一版本计划对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellVersionPlanHistoryExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 国内/海外Id
     */
    @ExcelProperty(value = "国内/海外Id")
    @ExcelIgnore
    private Long isOverseaId;
    /**
     * 国内/海外
     */
    @ExcelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    @ExcelIgnore
    private Long workshopId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 电池类型编号
     */
    @ExcelProperty(value = "电池类型编号")
    @ExcelIgnore
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    /**
     * 小区域国家
     */
    @ExcelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 月份计划（上上版）
     */
    @ExcelProperty(value = "月份计划（上上版）")
    private BigDecimal monthV0;
    /**
     * 月份计划（上版）
     */
    @ExcelProperty(value = "月份计划（上版）")
    private BigDecimal monthV1;
    /**
     * 月份计划新版
     */
    @ExcelProperty(value = "月份计划新版")
    private BigDecimal monthV2;
    /**
     * 差异
     */
    @ExcelProperty(value = "差异")
    private BigDecimal gap;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
