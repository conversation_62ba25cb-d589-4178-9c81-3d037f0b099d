package com.trinasolar.scp.baps.domain.dto.aps;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 运输天数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Data
@ApiModel(value = "CellShippingDays查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellShippingDaysQuery implements Serializable {

    /**
     * 电池生产车间
     */
    @ApiModelProperty(value = "电池生产车间")
    private String originWorkshop;
    /**
     * 电池生产车间
     */
    @ApiModelProperty(value = "电池生产车间")
    private List<String> originWorkshopList;
    /**
     * 电池需求车间
     */
    @ApiModelProperty(value = "电池需求车间")
    private String demandWorkshop;
    /**
     * 电池需求车间
     */
    @ApiModelProperty(value = "电池需求车间")
    private List<String> demandWorkshopList;
}
