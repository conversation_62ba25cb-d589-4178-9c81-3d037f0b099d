package com.trinasolar.scp.baps.domain.convert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.entity.CellPlanLine;
import com.trinasolar.scp.baps.domain.entity.CellProductionPlan;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineSiliconTotalQuery;
import com.trinasolar.scp.baps.domain.query.SiliconSplitQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class , LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanLineDEConvert extends BaseDEConvert<CellPlanLineDTO, CellPlanLine> {

    CellPlanLineDEConvert INSTANCE = Mappers.getMapper(CellPlanLineDEConvert.class);

    List<CellPlanLineExcelDTO> toExcelDTO(List<CellPlanLineDTO> dtos);

    CellPlanLineExcelDTO toExcelDTO(CellPlanLineDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    CellPlanLine saveDTOtoEntity(CellPlanLineSaveDTO saveDTO, @MappingTarget CellPlanLine entity);

    @Override
    CellPlanLineDTO toDto(CellPlanLine entity);

    @Override
    @Mappings({
            @Mapping(target ="certCode"),
            @Mapping(target ="ratioCode"),
            @Mapping(target ="ecsCode"),
    })
    List<CellPlanLineDTO> toDto(List<CellPlanLine> entities);
    @Mappings({
            @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, entity.getCellsType()).getLovLineId())"),
            @Mapping(target = "isOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, entity.getIsOversea()).getLovLineId())"),
            @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, entity.getBasePlace()).getLovLineId())"),
            @Mapping(target = "workshopId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, entity.getWorkshop()).getLovLineId())"),
            @Mapping(target = "workunitId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(LovHeaderCodeConstant.WORK_UNIT, entity.getWorkunit()).getLovLineId())")
    })
    CellPlanLineSaveDTO toSaveDto(CellPlanLine entity);
    List<CellPlanLineSaveDTO> toSaveDto(List<CellPlanLine> entity);
    CellPlanLine  toEntityFromSaveDto(CellPlanLineSaveDTO dto);

    List<CellPlanLine>  toEntityFromSaveDto(List<CellPlanLineSaveDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    CellPlanLineQuery toCellPlanLineQueryByName(CellPlanLineQuery query);
    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellType()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop()))")
            }
    )
    CellAMinusDto toCellAMinusDtoByName(CellAMinusDto dto);
    @Mappings(
            {
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()))"),
            }
    )
    ProcessCategorySplitDto toProcessCategorySplitDtoByName(ProcessCategorySplitDto dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop()))")
            }
    )
    TransparentDoubleGlassSplitDto toTransparentDoubleGlassSplitDtoByName(TransparentDoubleGlassSplitDto dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()))"),
                    //  @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop()))")
            }
    )
    AestheticsSplitDto toAestheticsSplitDtoByName(AestheticsSplitDto dto);
    CellPlanLineQuery toCellPlanLineQueryFromSiliconSplitQuery(SiliconSplitQuery query);
    CellProductionPlan toCellProductionPlanFromCellPlanline(CellPlanLine cellPlanLine);
    CellPlanLineQuery toCellPlanLineQueryFromCellGradeRuleMateDto(CellGradeRuleMateDto query);
    List<CellProductionPlan> toCellProductionPlanFromCellPlanline(List<CellPlanLine> cellPlanLines);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop()))")
            }
    )
    CellGradeRuleMateDto toCellGradeRuleMateDtoByName(CellGradeRuleMateDto dto);
    CellGradeRuleMateTotalDTO toCellGradeRuleMateTotalDTO(CellPlanLineDTO dto);
    @Mappings({
            @Mapping(target = "cellsTypeId" ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.BATTERY_TYPE, entity.getCellsType()))"),
            @Mapping(target = "isOverseaId" ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, entity.getIsOversea()))"),
            @Mapping(target = "basePlaceId" ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.BASE_PLACE, entity.getBasePlace()))"),
            @Mapping(target = "workshopId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.WORK_SHOP, entity.getWorkshop()))"),
            @Mapping(target = "workunitId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.WORK_UNIT, entity.getWorkunit()))"),
            @Mapping(target = "HTraceId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.H_TRACE, entity.getHTrace()))"),
            @Mapping(target = "aestheticsId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.AESTHETICS, entity.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlassId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS, entity.getTransparentDoubleGlass()))"),
            @Mapping(target = "cellSourceId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.CELL_SOURCE, entity.getCellSource()))"),
            @Mapping(target = "regionalCountryId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.REGIONAL_COUNTRY, entity.getRegionalCountry()))"),
            @Mapping(target = "productionGradeId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.PRODUCTION_GRADE, entity.getProductionGrade()))"),
            @Mapping(target = "sourceTypeId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.BDM_DEMAND_SOURCE, entity.getSourceType()))"),
            @Mapping(target = "demandBasePlaceId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.DEMAND_BASE_PLACE, entity.getDemandBasePlace()))"),
            @Mapping(target = "cellMfrsId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.CELL_MFRS, entity.getCellMfrs()))"),
            @Mapping(target = "silverPulpMfrsId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.SILVER_PULP_MFRS, entity.getSilverPulpMfrs()))"),
            @Mapping(target = "siMfrsId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.SI_MFRS, entity.getSiMfrs()))"),
            @Mapping(target = "waferGradeId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.WAFER_GRADE, entity.getWaferGrade()))"),
            @Mapping(target = "processCategoryId"  ,expression = "java(MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY, entity.getProcessCategory()))")
    })
    @Override
    CellPlanLine toEntity(CellPlanLineDTO entity);
    @Named("toCopyEntityFromDto")
    CellPlanLine toCopyEntityFromDto(CellPlanLineDTO dto);
    @IterableMapping(qualifiedByName = "toCopyEntityFromDto")
    List<CellPlanLine> toCopyEntityFromDto(List<CellPlanLineDTO>   dtos);
    CellProductionPlanSummaryDTO detailsToSummary(CellPlanLineDTO cellPlanLineDTO);
    CellPlanLine toCopyCellPlanLine(CellPlanLine cellPlanLine);
    List<CellPlanLine>  toCopyCellPlanLine(List<CellPlanLine> cellPlanLines);
    CellPlanLineSaveDTO toSaveDto(CellPlanLineDTO data);
    default void test(CellPlanLineDTO entity){
        MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,entity.getCellsType());
    }
    @Mappings({
            @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,entity.getCellsType()))"),
            @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,entity.getIsOversea()))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getBasePlace()))"),
            @Mapping(target = "workshop"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,entity.getWorkshop()))"),
            @Mapping(target = "workunit"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_UNIT,entity.getWorkunit()))"),
            @Mapping(target = "HTrace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,entity.getHTrace()))"),
            @Mapping(target = "aesthetics"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,entity.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlass"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,entity.getTransparentDoubleGlass()))"),
            @Mapping(target = "cellSource"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_SOURCE,entity.getCellSource()))"),
            @Mapping(target = "regionalCountry"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.REGIONAL_COUNTRY,entity.getRegionalCountry()))"),
            @Mapping(target = "productionGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,entity.getProductionGrade()))"),
            @Mapping(target = "sourceType"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BDM_DEMAND_SOURCE,entity.getSourceType()))"),
            @Mapping(target = "demandBasePlace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getDemandBasePlace()))"),
            @Mapping(target = "cellMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_MFRS,entity.getCellMfrs()))"),
            @Mapping(target = "silverPulpMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SILVER_PULP_MFRS, entity.getSilverPulpMfrs()))"),
            @Mapping(target = "siMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SI_MFRS, entity.getSiMfrs()))"),
            @Mapping(target = "waferGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WAFER_GRADE,entity.getWaferGrade()))"),
            @Mapping(target = "processCategory"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,entity.getProcessCategory()))")
    })
    @Named("toCellPlanLineDTONameFromCNName")
    CellPlanLineDTO  toCellPlanLineDTONameFromCNName(CellPlanLineDTO entity);
    @IterableMapping(qualifiedByName = "toCellPlanLineDTONameFromCNName")
    List<CellPlanLineDTO>  toCellPlanLineDTONameFromCNName(List<CellPlanLineDTO> dtos);
    @Mappings({
            @Mapping(target = "cellType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,entity.getCellType(),lang))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getBasePlace(),lang))"),
            @Mapping(target = "workshop"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,entity.getWorkshop(),lang))"),
            @Mapping(target = "HTrace"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.H_TRACE,entity.getHTrace(),lang))"),
            @Mapping(target = "aesthetics"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.AESTHETICS,entity.getAesthetics(),lang))"),
            @Mapping(target = "transparentDoubleGlass"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,entity.getTransparentDoubleGlass(),lang))"),
            @Mapping(target = "productionGrade"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,entity.getProductionGrade(),lang))"),
            @Mapping(target = "siMfrs"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.SI_MFRS, entity.getSiMfrs(),lang))"),
            @Mapping(target = "waferGrade"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WAFER_GRADE,entity.getWaferGrade(),lang))"),
            @Mapping(target = "processCategory"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,entity.getProcessCategory(),lang))")
    })
    CellPlanLineSiliconTotalQuery toCellPlanLineSiliconTotalQueryCnName(CellPlanLineSiliconTotalQuery entity,String lang);
    @Mappings({
            @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,entity.getCellsType(),lang))"),
            @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,entity.getIsOversea(),lang))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getBasePlace(),lang))"),
            @Mapping(target = "workshop"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,entity.getWorkshop(),lang))"),
            @Mapping(target = "workunit"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_UNIT,entity.getWorkunit(),lang))"),
            @Mapping(target = "HTrace"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.H_TRACE,entity.getHTrace(),lang))"),
            @Mapping(target = "aesthetics"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.AESTHETICS,entity.getAesthetics(),lang))"),
            @Mapping(target = "transparentDoubleGlass"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,entity.getTransparentDoubleGlass(),lang))"),
            @Mapping(target = "cellSource"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.CELL_SOURCE,entity.getCellSource(),lang))"),
            @Mapping(target = "regionalCountry"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.REGIONAL_COUNTRY,entity.getRegionalCountry(),lang))"),
            @Mapping(target = "productionGrade"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,entity.getProductionGrade(),lang))"),
            @Mapping(target = "sourceType"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BDM_DEMAND_SOURCE,entity.getSourceType(),lang))"),
            @Mapping(target = "demandBasePlace"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getDemandBasePlace(),lang))"),
            @Mapping(target = "cellMfrs"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.CELL_MFRS,entity.getCellMfrs(),lang))"),
            @Mapping(target = "silverPulpMfrs"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.SILVER_PULP_MFRS, entity.getSilverPulpMfrs(),lang))"),
            @Mapping(target = "siMfrs"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.SI_MFRS, entity.getSiMfrs(),lang))"),
            @Mapping(target = "waferGrade"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WAFER_GRADE,entity.getWaferGrade(),lang))"),
            @Mapping(target = "processCategory"  ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,entity.getProcessCategory(),lang))")
    })
    CellPlanLineQuery toCellPlanLineQueryCNameByName(CellPlanLineQuery entity, String lang);
    @Mappings({
            @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,entity.getCellsType()))"),
            @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,entity.getIsOversea()))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getBasePlace()))"),
            @Mapping(target = "workshop"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,entity.getWorkshop()))"),
            @Mapping(target = "workunit"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_UNIT,entity.getWorkunit()))"),
            @Mapping(target = "HTrace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,entity.getHTrace()))"),
            @Mapping(target = "aesthetics"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,entity.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlass"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,entity.getTransparentDoubleGlass()))"),
            @Mapping(target = "cellSource"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_SOURCE,entity.getCellSource()))"),
            @Mapping(target = "regionalCountry"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.REGIONAL_COUNTRY,entity.getRegionalCountry()))"),
            @Mapping(target = "productionGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,entity.getProductionGrade()))"),
            @Mapping(target = "sourceType"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BDM_DEMAND_SOURCE,entity.getSourceType()))"),
            @Mapping(target = "demandBasePlace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getDemandBasePlace()))"),
            @Mapping(target = "cellMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_MFRS,entity.getCellMfrs()))"),
            @Mapping(target = "silverPulpMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SILVER_PULP_MFRS, entity.getSilverPulpMfrs()))"),
            @Mapping(target = "siMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SI_MFRS, entity.getSiMfrs()))"),
            @Mapping(target = "waferGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WAFER_GRADE,entity.getWaferGrade()))"),
            @Mapping(target = "processCategory"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,entity.getProcessCategory()))")
    })
    @Named("toCellProductionPlanSummaryDTONameFromCNName")
    CellProductionPlanSummaryDTO toCellProductionPlanSummaryDTONameFromCNName(CellProductionPlanSummaryDTO entity);
    @IterableMapping(qualifiedByName = "toCellProductionPlanSummaryDTONameFromCNName")
    List<CellProductionPlanSummaryDTO> toCellProductionPlanSummaryDTONameFromCNName(List<CellProductionPlanSummaryDTO> cellProductionPlanSummaryDTOS);
}
