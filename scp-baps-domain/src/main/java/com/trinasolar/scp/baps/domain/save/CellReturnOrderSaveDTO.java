package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;

import javax.persistence.Column;


/**
 * 返司
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellReturnOrder保存参数", description = "保存参数")
public class CellReturnOrderSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 是否海外
     */
    @ApiModelProperty(value = "发货方国内海外")
    private String shipperIsOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "发货方国内海外id")
    private Long shipperIsOverseaId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "发货方生产基地")
    private String shipperBasePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "发货方生产基地id")
    private Long shipperBasePlaceId;
    /**
     * 是否海外
     */
    @ApiModelProperty(value = "接收方国内海外")
    private String receiverIsOversea;
    /**
     * 是否海外
     */
    @ApiModelProperty(value = "接收方国内海外")
    private Long receiverIsOverseaId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "接收方生产基地")
    private String receiverBasePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "接收方生产基地id")
    private Long receiverBasePlaceId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型id")
    private Long cellsTypeId;
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String itemFivea;
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号Id")
    private Long itemFiveaId;
    /**
     * 效率值
     */
    @ApiModelProperty(value = "效率值")
    private BigDecimal workCell;
    /**
     * 对应标识
     */
    @ApiModelProperty(value = "对应标识")
    private String note;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDate date;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal cellQty;
    /**
     * 账套
     */
    @ApiModelProperty(value = "接收方账套")
    private String receiverAccount;
}
