package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 物流节假日表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "物流节假日表DTO对象", description = "DTO对象")
public class DeliveryHolidayDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 发货基地
     */
    @ApiModelProperty(value = "发货基地")
    private String basePlaceFrom;
    /**
     * 到货基地
     */
    @ApiModelProperty(value = "到货基地")
    private String basePlaceTo;
    /**
     * 年月
     */
    @ApiModelProperty(value = "年月")
    private String month;
    /**
     * 日期区间
     */
    @ApiModelProperty(value = "日期区间")
    private String dateBetween;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
