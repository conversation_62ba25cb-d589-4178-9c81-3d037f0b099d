package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.query.CellClimbCapacityLeadQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellClimbCapacityLead;
import com.trinasolar.scp.baps.domain.dto.CellClimbCapacityLeadDTO;
import com.trinasolar.scp.baps.domain.excel.CellClimbCapacityLeadExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellClimbCapacityLeadSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 爬坡产能可靠性验证表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-05 08:10:43
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellClimbCapacityLeadDEConvert extends BaseDEConvert<CellClimbCapacityLeadDTO, CellClimbCapacityLead> {

    CellClimbCapacityLeadDEConvert INSTANCE = Mappers.getMapper(CellClimbCapacityLeadDEConvert.class);

    List<CellClimbCapacityLeadExcelDTO> toExcelDTO(List<CellClimbCapacityLeadDTO> dtos);

    CellClimbCapacityLeadExcelDTO toExcelDTO(CellClimbCapacityLeadDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellClimbCapacityLead saveDTOtoEntity(CellClimbCapacityLeadSaveDTO saveDTO, @MappingTarget CellClimbCapacityLead entity);

    List<CellClimbCapacityLeadDTO> saveToEntityDTO(List<CellClimbCapacityLeadSaveDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellType(),lang))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace(),lang))"),
                    @Mapping(target = "workShop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP, query.getWorkShop(),lang))"),
                    @Mapping(target = "workUnit" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_UNIT, query.getWorkUnit(),lang))"),
            }
    )
    CellClimbCapacityLeadQuery toCellClimbCapacityLeadQuery(CellClimbCapacityLeadQuery query, String lang);
    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(LovUtils.getName( entity.getCellTypeId() ))"),
                    @Mapping(target = "basePlace" ,expression = "java(LovUtils.getName( entity.getBasePlaceId() ))"),
                    @Mapping(target = "workShop" ,expression = "java(LovUtils.getName(  entity.getWorkShopId() ))"),
                    @Mapping(target = "workUnit" ,expression = "java(LovUtils.getName(  entity.getWorkUnitId() ))")
            }
    )
    @Override
    CellClimbCapacityLeadDTO toDto(CellClimbCapacityLead entity);
    @Override
    List<CellClimbCapacityLeadDTO> toDto(List<CellClimbCapacityLead> entitys);
}
