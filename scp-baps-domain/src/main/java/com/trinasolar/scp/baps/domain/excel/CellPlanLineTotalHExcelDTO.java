package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.math.BigDecimal;


/**
 * 入库计划汇总表(H兼容拆分)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellPlanLineTotalHExcelDTO {

    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    @ExcelIgnore
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")

    private String basePlace;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    @ExcelIgnore
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    @ExcelIgnore
    private Long workshopId;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    @ExcelIgnore
    private String workunit;

    /**
     * 生产单元Id
     */
    @ExcelProperty(value = "生产单元Id")
    @ExcelIgnore
    private Long workunitId;
    /**
     * 生产线体
     */
    @ExcelProperty(value = "生产线体")
    @ExcelIgnore
    private String lineName;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    @ExcelIgnore
    private Long cellsTypeId;
    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 是否电池特殊要求
     */
    @ExcelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 低阻
     */
    @ExcelProperty(value = "低阻")
    @ExcelIgnore
    private String lowResistance;

    /**
     * 电池厂家
     */
    @ExcelProperty(value = "电池厂家")
    @ExcelIgnore
    private String cellMfrs;

    /**
     * 银浆厂家
     */
    @ExcelProperty(value = "银浆厂家")
    @ExcelIgnore
    private String silverPulpMfrs;
    /**
     * 硅料厂家
     */
    @ExcelProperty(value = "硅料厂家")
    private String siMfrs;

    /**
     * 网版厂家
     */
    @ExcelProperty(value = "网版厂家")
    @ExcelIgnore
    private String screenPlateMfrs;

    /**
     * 电池特殊单号
     */
    @ExcelProperty(value = "电池特殊单号")
    @ExcelIgnore
    private String specialOrderNo;
    /**
     * 验证标识
     */
    @ExcelProperty(value = "验证标识")
    @ExcelIgnore
    private  String verificationMark;
    /**
     * 片源级投产良率
     */
    @ExcelProperty(value = "片源级投产良率")
    @ExcelIgnore
    private BigDecimal  waferYieldRatio;
    /**
     * 片源级A-比例
     */
    @ExcelProperty(value = "片源级A-比例")
    @ExcelIgnore
    private BigDecimal waferGradeRatio;
    /**
     * 计划版本
     */
    @ExcelProperty(value = "计划版本")
    private String version;

    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;

    /**
     * d1
     */
    @ExcelProperty(value = "1")
    private BigDecimal d1;
    /**
     * d2
     */
    @ExcelProperty(value = "2")
    private BigDecimal d2;
    /**
     * d3
     */
    @ExcelProperty(value = "3")
    private BigDecimal d3;
    /**
     * d4
     */
    @ExcelProperty(value = "4")
    private BigDecimal d4;
    /**
     * d5
     */
    @ExcelProperty(value = "5")
    private BigDecimal d5;
    /**
     * d6
     */
    @ExcelProperty(value = "6")
    private BigDecimal d6;
    /**
     * d7
     */
    @ExcelProperty(value = "7")
    private BigDecimal d7;
    /**
     * d8
     */
    @ExcelProperty(value = "8")
    private BigDecimal d8;
    /**
     * d9
     */
    @ExcelProperty(value = "9")
    private BigDecimal d9;
    /**
     * d10
     */
    @ExcelProperty(value = "10")
    private BigDecimal d10;
    /**
     * d11
     */
    @ExcelProperty(value = "11")
    private BigDecimal d11;
    /**
     * d12
     */
    @ExcelProperty(value = "12")
    private BigDecimal d12;
    /**
     * d13
     */
    @ExcelProperty(value = "13")
    private BigDecimal d13;
    /**
     * d14
     */
    @ExcelProperty(value = "14")
    private BigDecimal d14;
    /**
     * d15
     */
    @ExcelProperty(value = "15")
    private BigDecimal d15;
    /**
     * d16
     */
    @ExcelProperty(value = "16")
    private BigDecimal d16;
    /**
     * d17
     */
    @ExcelProperty(value = "17")
    private BigDecimal d17;
    /**
     * d18
     */
    @ExcelProperty(value = "18")
    private BigDecimal d18;
    /**
     * d19
     */
    @ExcelProperty(value = "19")
    private BigDecimal d19;
    /**
     * d20
     */
    @ExcelProperty(value = "20")
    private BigDecimal d20;
    /**
     * d21
     */
    @ExcelProperty(value = "21")
    private BigDecimal d21;
    /**
     * d22
     */
    @ExcelProperty(value = "22")
    private BigDecimal d22;
    /**
     * d23
     */
    @ExcelProperty(value = "23")
    private BigDecimal d23;
    /**
     * d24
     */
    @ExcelProperty(value = "24")
    private BigDecimal d24;
    /**
     * d25
     */
    @ExcelProperty(value = "25")
    private BigDecimal d25;
    /**
     * d26
     */
    @ExcelProperty(value = "26")
    private BigDecimal d26;
    /**
     * d27
     */
    @ExcelProperty(value = "27")
    private BigDecimal d27;
    /**
     * d28
     */
    @ExcelProperty(value = "28")
    private BigDecimal d28;
    /**
     * d29
     */
    @ExcelProperty(value = "29")
    private BigDecimal d29;
    /**
     * d30
     */
    @ExcelProperty(value = "30")
    private BigDecimal d30;
    /**
     * d31
     */
    @ExcelProperty(value = "31")
    private BigDecimal d31;
}
