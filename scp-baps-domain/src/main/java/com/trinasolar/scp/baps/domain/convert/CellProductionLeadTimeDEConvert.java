package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellShippableLeadTimeDTO;
import com.trinasolar.scp.baps.domain.entity.CellShippableLeadTime;
import com.trinasolar.scp.baps.domain.query.CellProductionLeadTimeQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellProductionLeadTime;
import com.trinasolar.scp.baps.domain.dto.CellProductionLeadTimeDTO;
import com.trinasolar.scp.baps.domain.excel.CellProductionLeadTimeExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellProductionLeadTimeSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 投产提前期 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class,LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellProductionLeadTimeDEConvert extends BaseDEConvert<CellProductionLeadTimeDTO, CellProductionLeadTime> {

    CellProductionLeadTimeDEConvert INSTANCE = Mappers.getMapper(CellProductionLeadTimeDEConvert.class);

    List<CellProductionLeadTimeExcelDTO> toExcelDTO(List<CellProductionLeadTimeDTO> dtos);

    CellProductionLeadTimeExcelDTO toExcelDTO(CellProductionLeadTimeDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellProductionLeadTime saveDTOtoEntity(CellProductionLeadTimeSaveDTO saveDTO, @MappingTarget CellProductionLeadTime entity);
    @Mappings(
            {
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellProductionLeadTime.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellProductionLeadTime.getWorkshopId()))"),
                    @Mapping(target = "workunit" ,expression = "java(cellProductionLeadTime.getWorkunitId() != null ? com.trinasolar.scp.common.api.util.LovUtils.getName(cellProductionLeadTime.getWorkunitId()) : null)")
            }
    )
    CellProductionLeadTimeDTO toDto(CellProductionLeadTime cellProductionLeadTime);
    @Mappings(
            {
                    @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDto.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "workshopId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDto.getWorkshop()).getLovLineId())"),
                    @Mapping(target = "workunitId" ,expression = "java(excelDto.getWorkunit() != null ? com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT, excelDto.getWorkunit()).getLovLineId() : null)")
            }
    )
    CellProductionLeadTimeSaveDTO excelDtoToSaveDto(CellProductionLeadTimeExcelDTO excelDto);
    List<CellProductionLeadTimeSaveDTO> excelDtoToSaveDto(List<CellProductionLeadTimeExcelDTO> excelDtos);

    List<CellShippableLeadTimeDTO> entityToDto(List<CellShippableLeadTime> leadTimeList);
    @Mappings(
            {
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,query.getBasePlace(),lang))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,query.getWorkshop(),lang))"),
                    @Mapping(target = "workunit" ,expression = "java(query.getWorkunitId() != null ? MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_UNIT,query.getWorkunit(),lang) : null)")
            }
    )
    CellProductionLeadTimeQuery toCellProductionLeadTimeQueryCN(CellProductionLeadTimeQuery query, String lang);
    @Mappings(
            {
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshopId()))"),
                    @Mapping(target = "workunit" ,expression = "java(dto.getWorkunitId() != null ? MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_UNIT,dto.getWorkunitId()) : null)")
            }
    )
    @Named("toCellProductionLeadTimeSaveDTOCnName")
    CellProductionLeadTimeSaveDTO toCellProductionLeadTimeSaveDTOCnName(CellProductionLeadTimeSaveDTO dto);
    @IterableMapping(qualifiedByName = "toCellProductionLeadTimeSaveDTOCnName")
    List<CellProductionLeadTimeSaveDTO> toCellProductionLeadTimeSaveDTOCnName(List<CellProductionLeadTimeSaveDTO> dtos);

}
