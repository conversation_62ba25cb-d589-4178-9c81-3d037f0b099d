package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 可发货计划提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-08 06:14:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellShippableLeadTimeExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * ID主键
     */
   /* @ExcelProperty(value = "类型Id")
    @ExcelIgnore
    private Long leadTypeId;*/
    /**
     * 类型
     */
    /*@ExcelProperty(value = "类型")
    private String leadType;*/
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    @ExcelIgnore
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellType;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    @ExcelIgnore
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    @ExcelIgnore
    private Long workShopId;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间")
    private String workShop;
    /**
     * 生产单位Id
     */
    @ExcelProperty(value = "生产单位Id")
    @ExcelIgnore
    private Long workUnitId;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workUnit;
    /**
     * 预留天数
     */
    @ExcelProperty(value = "预留天数")
    private Integer buffDays;
    /**
     * 可发货浮动系数
     */
    @ExcelProperty(value = "可发货浮动系数")
    @ExcelIgnore
    private BigDecimal rate;
    /**
     * 可发货浮动系数百分比
     */
    @ExcelProperty(value = "可发货浮动系数")
    private String ratePercent;
    /**
     * 验证时间
     */
    /*@ExcelProperty(value = "验证时间")
    private LocalDateTime validateTime;*/
}
