package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 可发货计划提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-08 06:14:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellShippableLeadTime保存参数", description = "保存参数")
public class CellShippableLeadTimeSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 类型Id
     */
    /*@ApiModelProperty(value = "类型Id")
    private Long leadTypeId;*/
    /**
     * 类型
     */
    /*@ApiModelProperty(value = "类型")
    private String leadType;*/
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workShopId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workShop;
    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workUnitId;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workUnit;
    /**
     * 预留天数
     */
    @ApiModelProperty(value = "预留天数")
    private Integer buffDays;
    /**
     * 可发货浮动系数
     */
    @ApiModelProperty(value = "可发货浮动系数")
    private BigDecimal rate;
    /**
     * 验证时间
     */
    /*@ApiModelProperty(value = "验证时间")
    private LocalDateTime validateTime;*/
}
