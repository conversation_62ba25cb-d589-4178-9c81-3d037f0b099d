package com.trinasolar.scp.baps.domain.dto.aps;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 电池系列与料号关系查询
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-22 14:14:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellRelationDTO对象", description = "DTO对象")
public class CellRelationDTO implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String materialNo;
    /**
     * 描述
     */
    @ExcelProperty(value = "描述", index = 1)
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    private BigDecimal acreage;
    /**
     * 片源
     */
    @ApiModelProperty(value = "片源")
    private String cellSource;
    /**
     * 电池来源：外购/自产
     */
    @ApiModelProperty(value = "电池来源：外购/自产")
    private String productFrom;
    /**
     * 电池来源：外购/自产
     */
    @ApiModelProperty(value = "电池来源：外购/自产")
    private String productFromName;
    /**
     * 标记
     */
    @ApiModelProperty(value = "sign")
    private String sign;

    public CellRelationDTO(String materialNo) {
        this.materialNo = materialNo;
        this.cellType = "";
    }
}
