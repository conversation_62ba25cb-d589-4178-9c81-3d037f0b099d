package com.trinasolar.scp.baps.domain.utils;

import oshi.jna.platform.unix.solaris.LibKstat;

public final class LovHeaderCodeConstant {
    //电池类型
    public  static  final String BATTERY_TYPE="BATTERY_TYPE";
    /**
     * APS电池类型
     */
    public  static  final String APS_BATTERY_TYPE="aps_power_cell_type";
    //国内国外
    public  static  final String DOMESTIC_OVERSEA="Domestic/Oversea";
    //生产基地
    public  static  final  String BASE_PLACE="base_place";
    //生产车间
    public  static  final  String WORK_SHOP="work_shop";
    //生成单元
    public  static  final  String WORK_UNIT="work_unit";
    //车间类型
    public static  final  String WORKSHOP_TYPE="WORKSHOP_TYPE";
    //库存组织代码对照关系
    public static  final  String INVENTORY_ORGANIZATION="inventory_organization";
    //生成单元
    public  static  final  String SHIPMENT_LEADTIME_TYPE="SHIPMENT_LEADTIME_TYPE";
    //电池片_硅料厂商（厂家）
    public static  final String SI_MFRS="5A00100100121";
    //硅片等级
    public static  final  String WAFER_GRADE="5A00100100122";
    //电池片_加工类别
    public static  final String CELL_PROCESS_CATEGORY="5A00100100111";
    //硅片_加工方式
    public static  final  String SILICON_PROCESS_CATEGORY=  "4A00100100105";
    //BAPS邮件通知
    public static final String BAPS_EMAIL_REMINDER="BAPS_EMAIL_REMINDER";
    //产能打折人力通知
    public  static final String BAPS_CAPICITY_DISCOUNT_MAIL_REMINDER="BAPS_CAPICITY_DISCOUNT_MAIL_REMINDER";

    //BAPS并行损失类型
    public  static final String BAPS_PARALLET_LOSS_TYPE="BAPS_PARALLET_LOSS_TYPE";
    /**
     *     BAPS投产/入库计划邮件通知配置
     */
    public  static  final String BAPS_SCHEDULE_USER_EMAIL="BAPS_SCHEDULE_USER_EMAIL";
    public static  final String BAPS_CELLPLANLINEINSTOCK_YES="1538719273986428928";
    public static  final String BAPS_CELLPLANLINEINSTOCK_NO="1538719345075687424";
    /**
     * 电池型号
     */
    public static  final String AOP_CELL_SERIES="aop_cell_series";
    /**
     * BAPS_任务名称
     */
    public static  final  String BAPS_TASK_NAME="BAPS_TASK_NAME";
    /**
     * 计划状态
     */
    public static final String PLAN_STATUS="PLAN_STATUS";
    /**
     * 投产计划-创建投产计划
     */
    public static final String  BAPS_CREATE_CELL_PLAN="投产计划-创建投产计划";
    /**
     * 投产计划-加工类型拆分
     */
    public static final String  BAPS_PROCESS_CATEGORY_SPLIT="投产计划-加工类型拆分";
    /**
     * 投产计划-硅片等级拆分
     */
    public static final String  BAPS_WAFER_GRADE_SPLIT="投产计划-硅片等级拆分";
    /**
     * 投产计划-硅料厂家拆分
     */
    public static final String  BAPS_SIMFRS_SPLIT="投产计划-硅料厂家拆分";
    /**
     * 投产计划-计划确认
     */
    public static final String  BAPS_CELL_PLAN_CONFIRM_PLAN="投产计划-计划确认";
    /**
     * 投产计划-创建入库计划
     */
    public static final String  BAPS_CELL_PLAN_CREATE_INSTOCK_PLAN="投产计划-创建入库计划";

    /**
     * 入库计划-料号匹配
     */
    public static final String  BAPS_CELL_INSTOCK_LINE_MATCH="入库计划-料号匹配";
    /**
     * 入库计划-A-拆分
     */
    public static final String  BAPS_A_SPLIT="入库计划-A-拆分";

    /**
     * 入库计划-低效-拆分
     */
    public static final String  BAPS_LE_SPLIT="入库计划-低效-拆分";
    /**
     * 入库计划-透明双玻拆分
     */
    public static final String  BAPS_TRANSPARENTDOUBLEGLASS_SPLIT="入库计划-透明双玻拆分";
    /**
     * 入库计划-美学拆分
     */
    public static final String  BAPS_AESTHETICS_SPLIT="入库计划-美学拆分";
    /**
     * 入库计划-计划确认
     */
    public static final String  BAPS_INSTOCK_PLAN_CONFIRM_PLAN="入库计划-计划确认";
    /**
     * 入库计划-匹配分档规则
     */
    public static final String  BAPS_GRADERULE_MATE="入库计划-匹配分档规则";
    /**
     * aop模块版本类型
     */
    public static  final String AOP_VERSION_TYPE="version_type";
    /**
     * aop模块版本类型-目标版
     */
    public static  final String AOP_VERSION_TYPE_NAME="目标版";
    /**
     * 可发货计划-电池产出计划推送
     */
    public static final String  TASK_LOG_01="可发货计划-电池产出计划推送";
    /**
     * 可发货计划-同步入库计划数据
     */
    public static final String  TASK_LOG_02="可发货计划-同步入库计划数据";
    /**
     * BAPS_生产单元拆线配置
     */
    public static  final String BAPS_WORKUNIT_TO_LINE="BAPS_WORKUNIT_TO_LINE";
    /**
     * 电池需求来源
     */
    public static  final String BDM_DEMAND_SOURCE="BDM_DEMAND_SOURCE";
    /**
     * 电池片_H追溯
     */
    public static  final String H_TRACE ="5A00100100135";
    /**
     * 电池片_美学
     */
    public static  final String AESTHETICS ="5A00100100124";
    /**
     * 透明双玻
     */
    public static  final String TRANSPARENT_DOUBLE_GLASS="5A00100100123";
    /**
     * 电池片_片源种类
     */
    public static  final String CELL_SOURCE="5A00100100123";
    /**
     * 电池片_产品等级
     */
    public static  final String PRODUCTION_GRADE="5A00100100103";
    /**
     * 小区域国家
     */
    public static  final String REGIONAL_COUNTRY="country";
    /**
     * 需求基地
     */
    public static  final String DEMAND_BASE_PLACE="base_place";
    /**
     * 电池厂家
     */
    public static  final String CELL_MFRS="6A00100100110";
    /**
     * 银浆厂家
     */
    public static  final String SILVER_PULP_MFRS="7A00900100108";
    /**
     * 语言中文
     */
    public  static final String LANGUAGE_CN="zh_CN";
    public static final String LANGUAGE_EN="en_US";
    /**
     * 电池类型的PN型-P型ID
     */
    public  static final String BATTERY_TYPE_PN_P  ="1556810244208857088";
    /**
     * 电池类型的电池型号是210RN的ID
     */
    public  static final String BATTERY_TYPE_MODEL_210RN  ="1574243710923837440";
    /**
     * BAPS_电池类型解析模块
     */
    public static final String BAPS_CELL_TYPE_RULE_MODULE="BAPS_CELL_TYPE_RULE_MODULE";
    /**
     * BAPS_电池类型解析字段
     */
    public static final String BAPS_CELL_TYPE_RULE_FIELD="BAPS_CELL_TYPE_RULE_FIELD";
    /**
     * 是否Lov
     */
    public static final String YES_OR_NO="yes_or_no";

    /**
     * 子库位与车间对应关系
     */
    public static final String BDM_HTRACE_MAPPING = "BDM_HTRACE_MAPPING";

    /**
     * H追溯预警类型
     */
    public static final String BDM_HTRACE_TYPE = "BDM_HTRACE_TYPE";
    public static final String BAPS_INSTOCK_FINISH_TASK = "生产实际覆盖排产-获取ERP实际入库数据";
    /**
     * 账套类型
     */
    public static final    String BOOKS_TYPE = "books_type";
    /**
     * 账套类型电池
     */
    public static final    String BOOKS_TYPE_CELL = "电池";
    /**
     * 账套类型电池与组件
     */
    public static final    String BOOKS_TYPE_CELL_COMPONENT = "组件&电池";

    /**
     * 品类
     */
    public static final String PRODUCT_CATEGORY = "5A00200200129";

    /**
     * P型N型
     */
    public static final String CRYSTAL_TYPE = "5A00100100101";
    /**
     * 要排除的货位信息
     */
    public static final String BAPS_ERP_WIP_FINISHED_EXCEPT_LOCATOR = "BAPS_ERP_WIP_FINISHED_EXCEPT_LOCATOR";
    /**
     * 电池BOM邮件通知列表
     */
    public static final String BBOM_EMAIL_REMINDER = "BBOM_EMAIL_REMINDER";

    /**
     * 供应方式
     */
    public static final String SUPPLY_METHOD = "5A00100100140";
}
