package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 硅片拆分(针对入库)汇总
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "硅片拆分(针对入库)汇总DTO对象", description = "DTO对象")
public class CellInstockPlanSiliconTotalDTO extends BaseDTO {
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellTypeId;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
  private String processCategory;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private  String siMfrs;
    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String waferGrade;
    /**
     * MV
     */
    @ApiModelProperty(value = "MV")
    private BigDecimal cellMv;
    /**
     * 万片数
     */
    @ApiModelProperty(value = "万片数")
    private BigDecimal qtyThousandPc;
     /**
     * 对应入库计划的版本
     */
    @ApiModelProperty(value = "对应入库计划的版本")
    private String fromVersion;

    /**
     * d1
     */
    @ApiModelProperty(value = "d1")
    private BigDecimal d1;
    /**
     * d2
     */
    @ApiModelProperty(value = "d2")
    private BigDecimal d2;
    /**
     * d3
     */
    @ApiModelProperty(value = "d3")
    private BigDecimal d3;
    /**
     * d4
     */
    @ApiModelProperty(value = "d4")
    private BigDecimal d4;
    /**
     * d5
     */
    @ApiModelProperty(value = "d5")
    private BigDecimal d5;
    /**
     * d6
     */
    @ApiModelProperty(value = "d6")
    private BigDecimal d6;
    /**
     * d7
     */
    @ApiModelProperty(value = "d7")
    private BigDecimal d7;
    /**
     * d8
     */
    @ApiModelProperty(value = "d8")
    private BigDecimal d8;
    /**
     * d9
     */
    @ApiModelProperty(value = "d9")
    private BigDecimal d9;
    /**
     * d10
     */
    @ApiModelProperty(value = "d10")
    private BigDecimal d10;
    /**
     * d11
     */
    @ApiModelProperty(value = "d11")
    private BigDecimal d11;
    /**
     * d12
     */
    @ApiModelProperty(value = "d12")
    private BigDecimal d12;
    /**
     * d13
     */
    @ApiModelProperty(value = "d13")
    private BigDecimal d13;
    /**
     * d14
     */
    @ApiModelProperty(value = "d14")
    private BigDecimal d14;
    /**
     * d15
     */
    @ApiModelProperty(value = "d15")
    private BigDecimal d15;
    /**
     * d16
     */
    @ApiModelProperty(value = "d16")
    private BigDecimal d16;
    /**
     * d17
     */
    @ApiModelProperty(value = "d17")
    private BigDecimal d17;
    /**
     * d18
     */
    @ApiModelProperty(value = "d18")
    private BigDecimal d18;
    /**
     * d19
     */
    @ApiModelProperty(value = "d19")
    private BigDecimal d19;
    /**
     * d20
     */
    @ApiModelProperty(value = "d20")
    private BigDecimal d20;
    /**
     * d21
     */
    @ApiModelProperty(value = "d21")
    private BigDecimal d21;
    /**
     * d22
     */
    @ApiModelProperty(value = "d22")
    private BigDecimal d22;
    /**
     * d23
     */
    @ApiModelProperty(value = "d23")
    private BigDecimal d23;
    /**
     * d24
     */
    @ApiModelProperty(value = "d24")
    private BigDecimal d24;
    /**
     * d25
     */
    @ApiModelProperty(value = "d25")
    private BigDecimal d25;
    /**
     * d26
     */
    @ApiModelProperty(value = "d26")
    private BigDecimal d26;
    /**
     * d27
     */
    @ApiModelProperty(value = "d27")
    private BigDecimal d27;
    /**
     * d28
     */
    @ApiModelProperty(value = "d28")
    private BigDecimal d28;
    /**
     * d29
     */
    @ApiModelProperty(value = "d29")
    private BigDecimal d29;
    /**
     * d30
     */
    @ApiModelProperty(value = "d30")
    private BigDecimal d30;
    /**
     * d31
     */
    @ApiModelProperty(value = "d31")
    private BigDecimal d31;
    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    private String supplyMethod;
    /**
     * 供应方式Id
     */
    @ApiModelProperty(value = "供应方式Id")
    private Long supplyMethodId;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    private Long cellSourceId;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 小区域国家Id
     */
    @ApiModelProperty(value = "小区域国家Id")
    private Long regionalCountryId;
    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;
    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
}
