package com.trinasolar.scp.baps.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel(value = "查询条件", description = "查询条件")
@Accessors(chain = true)
public class MaxVersionSliceSupplyAndDmaterialDeliveryQuery {
    /**
     * 月份集合
     */
    @ApiModelProperty(value = "月份集合")
    private List<String> months;

    @ApiModelProperty(value = "开始时间")
    private LocalDate startDate;
}
