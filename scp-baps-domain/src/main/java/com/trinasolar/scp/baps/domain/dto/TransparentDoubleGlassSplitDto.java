package com.trinasolar.scp.baps.domain.dto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ApiModel(value = "透明双玻拆分DTO", description = "DTO对象")
public class TransparentDoubleGlassSplitDto {
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型",notes ="要进行拆分的电池类型")
    private String cellsType;

    @ApiModelProperty(value = "拆分比例",notes ="拆分比例" )
    private BigDecimal rate;
    @ApiModelProperty(value ="开始日期",notes = "开始日期")
    private LocalDate startDate;
    @ApiModelProperty(value ="结束日期",notes = "结束日期")
    private LocalDate endDate;
}
