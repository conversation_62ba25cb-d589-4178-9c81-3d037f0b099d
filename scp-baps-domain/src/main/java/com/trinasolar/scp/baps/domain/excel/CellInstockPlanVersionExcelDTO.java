package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 入库计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellInstockPlanVersionExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 排产月份
     */
    @ExcelProperty(value = "排产月份")
    private String month;
    /**
     * 最終确认发布的版本
     */
    @ExcelProperty(value = "最終确认发布的版本")
    private String version;
    /**
     * 来自哪个投产版本
     */
    @ExcelProperty(value = "来自哪个投产版本")
    private String fromVersion;
    /**
     * 是否进行了A-拆分
     */
    @ExcelProperty(value = "是否进行了A-拆分")
    private Integer isASplit;
    /**
     * 是否已经进行了透明双玻拆分
     */
    @ExcelProperty(value = "是否已经进行了透明双玻拆分")
    private Integer isTransparentDoubleGlass;
    /**
     * 是否已经进行了分档规则拆分
     */
    @ExcelProperty(value = "是否已经进行了分档规则拆分")
    private Integer isGradeRule;
    /**
     * 是否进行了计划确认
     */
    @ExcelProperty(value = "是否进行了计划确认")
    private Integer isConfirmPlan;
    /**
     * 是否进行了邮件发送
     */
    @ExcelProperty(value = "是否进行了邮件发送")
    private Integer isSendEmail;
    /**
     * 哪个月排的
     */
    @ExcelProperty(value = "哪个月排的")
    private String scheduleMonth;
}
