package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 产能并行损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_parallel_loss")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_parallel_loss SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_parallel_loss SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellParallelLoss extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型1")
    @Column(name = "cells_type")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型1ID")
    @Column(name = "cells_type_id")
    private Long cellsTypeId;

    /**
     * 电池类型two
     */
    @ApiModelProperty(value = "电池类型2-ID")
    @Column(name = "cells_type_two_id")
    private Long cellsTypeTwoId;

    /**
     * 电池类型two
     */
    @ApiModelProperty(value = "电池类型2")
    @Column(name = "cells_type_two")
    private String cellsTypeTwo;
    /**
     * 并行类型ID
     */
    @ApiModelProperty(value = "并行类型-ID")
    @Column(name = "parallel_type_id")
    private Long parallelTypeId;

    /**
     * 并行类型
     */
    @ApiModelProperty(value = "并行类型")
    @Column(name = "parallel_type")
    private String parallelType;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshopid")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元Id")
    @Column(name = "workunitid")
    private Long workunitid;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    @Column(name = "line_number")
    private  BigDecimal lineNumber;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 损失产能
     */
    @ApiModelProperty(value = "损失产能")
    @Column(name = "loss")
    private BigDecimal loss;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;


}
