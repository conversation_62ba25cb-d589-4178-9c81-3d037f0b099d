package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.DemandPlanLinesAps;
import com.trinasolar.scp.baps.domain.dto.DemandPlanLinesApsDTO;
import com.trinasolar.scp.baps.domain.excel.DemandPlanLinesApsExcelDTO;
import com.trinasolar.scp.baps.domain.save.DemandPlanLinesApsSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 需求计划明细（APS） DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 08:30:30
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DemandPlanLinesApsDEConvert extends BaseDEConvert<DemandPlanLinesApsDTO, DemandPlanLinesAps> {

    DemandPlanLinesApsDEConvert INSTANCE = Mappers.getMapper(DemandPlanLinesApsDEConvert.class);

    List<DemandPlanLinesApsExcelDTO> toExcelDTO(List<DemandPlanLinesApsDTO> dtos);

    DemandPlanLinesApsExcelDTO toExcelDTO(DemandPlanLinesApsDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    DemandPlanLinesAps saveDTOtoEntity(DemandPlanLinesApsSaveDTO saveDTO, @MappingTarget DemandPlanLinesAps entity);

    List<DemandPlanLinesAps> batteryDemandToEntity(List<BatteryDemandPlanLinesDTO> saveList);

    DemandPlanLinesAps batteryDemandDTOToEntity(BatteryDemandPlanLinesDTO batteryDemandPlanLinesDTO);
}
