package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 生产日历
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "生产日历DTO对象", description = "DTO对象")
public class CalendarDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    @ApiModelProperty(value = "ie爬坡产能标识0代表ie，1代表爬坡")
    private Integer ieorgrade;
    @ApiModelProperty(value = "数据来源id(IE或爬坡id)")
    private Long fromid;
    /**
     * 日历类型
     */
    @ApiModelProperty(value = "日历类型")
    private String type;
    /**
     * 资源单元Id
     */
    @ApiModelProperty(value = "资源单元Id")
    private Long workunitId;
    /**
     * 资源单元
     */
    @ApiModelProperty(value = "资源单元")
    private String workunit;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDate date;
    /**
     * 出勤代码
     */
    @ApiModelProperty(value = "出勤代码")
    private String shiftcode;
    /**
     * 资源量
     */
    @ApiModelProperty(value = "资源量")
    private BigDecimal defaultqty;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer sortorder;
    /**
     * 标准产线数
     */
    @ApiModelProperty(value = "标准产线数")
    private BigDecimal numLines;
}
