package com.trinasolar.scp.baps.domain.convert;
import com.trinasolar.scp.baps.domain.dto.CellGradeRuleMateTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanSiliconTotalDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanFinishTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanTotal;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanTotalDTO;
import com.trinasolar.scp.baps.domain.excel.CellInstockPlanTotalExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanTotalSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划汇总表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-26 11:20:51
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovUtils.class, LovHeaderCodeConstant.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellInstockPlanTotalDEConvert extends BaseDEConvert<CellInstockPlanTotalDTO, CellInstockPlanTotal> {

    CellInstockPlanTotalDEConvert INSTANCE = Mappers.getMapper(CellInstockPlanTotalDEConvert.class);

    List<CellInstockPlanTotalExcelDTO> toExcelDTO(List<CellInstockPlanTotalDTO> dtos);

    CellInstockPlanTotalExcelDTO toExcelDTO(CellInstockPlanTotalDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellInstockPlanTotal saveDTOtoEntity(CellInstockPlanTotalSaveDTO saveDTO, @MappingTarget CellInstockPlanTotal entity);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    CellPlanLineTotalQuery toCellPlanLineTotalQuery(CellPlanLineTotalQuery query);
    default  void test(CellInstockPlanTotalQuery query){
        MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE,query.getCellsType());
    }
    CellInstockPlanQuery toCellInstockPlanQuery(CellInstockPlanTotalQuery query);

    @Mappings(
            {
                    @Mapping(target = "ratioCode"),
                    @Mapping(target = "ecsCode"),
            }
    )
    CellInstockPlanTotalDTO toCellInstockPlanTotalDTO(CellInstockPlanDTO dto);

    CellInstockPlanQuery toCellInstockPlan(CellInstockPlanTotalQuery query);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    CellInstockPlanTotalQuery toCellInstockPlanTotalQuery(CellInstockPlanTotalQuery query);
    @Mappings({
            @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellsType()))"),
            @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOversea()))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlace()))"),
            @Mapping(target = "workshop"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshop()))"),
            @Mapping(target = "HTrace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,dto.getHTrace()))"),
            @Mapping(target = "aesthetics"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,dto.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlass"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,dto.getTransparentDoubleGlass()))"),
            @Mapping(target = "productionGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,dto.getProductionGrade()))"),
            @Mapping(target = "cellSource"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_SOURCE,dto.getCellSource()))"),
            @Mapping(target = "regionalCountry"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.REGIONAL_COUNTRY,dto.getRegionalCountry()))")
    })
    @Named("toCellInstockPlanTotalDTONameByCnName")
    CellInstockPlanTotalDTO toCellInstockPlanTotalDTONameByCnName(CellInstockPlanTotalDTO dto);
    @IterableMapping(qualifiedByName = "toCellInstockPlanTotalDTONameByCnName")
    List<CellInstockPlanTotalDTO> toCellInstockPlanTotalDTONameByCnName(List<CellInstockPlanTotalDTO> dtos);
    @Mappings({
            @Mapping(target = "cellType" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellType()))"),
            @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOversea()))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlace()))"),
            @Mapping(target = "workshop"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshop()))"),
            @Mapping(target = "HTrace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,dto.getHTrace()))"),
            @Mapping(target = "aesthetics"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,dto.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlass"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,dto.getTransparentDoubleGlass()))"),
            @Mapping(target = "productionGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,dto.getProductionGrade()))"),
            @Mapping(target = "processCategory"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,dto.getProcessCategory()))"),
            @Mapping(target = "siMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SI_MFRS,dto.getSiMfrs()))"),
            @Mapping(target = "waferGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WAFER_GRADE,dto.getWaferGrade()))")
    })
    @Named("toCellInstockPlanSiliconTotalDTONameByCNName")
    CellInstockPlanSiliconTotalDTO toCellInstockPlanSiliconTotalDTONameByCNName(CellInstockPlanSiliconTotalDTO dto);
    @IterableMapping(qualifiedByName = "toCellInstockPlanSiliconTotalDTONameByCNName")
    List<CellInstockPlanSiliconTotalDTO> toCellInstockPlanSiliconTotalDTONameByCNName(List<CellInstockPlanSiliconTotalDTO> sortList);
    @Mappings({
            @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellsType()))"),
            @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOversea()))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlace()))"),
            @Mapping(target = "workshop"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshop()))")

    })
    @Named("toCellGradeRuleMateTotalDTONameByCNName")
    CellGradeRuleMateTotalDTO toCellGradeRuleMateTotalDTONameByCNName(CellGradeRuleMateTotalDTO dto);
    @IterableMapping(qualifiedByName = "toCellGradeRuleMateTotalDTONameByCNName")
    List<CellGradeRuleMateTotalDTO> toCellGradeRuleMateTotalDTONameByCNName(List<CellGradeRuleMateTotalDTO> cellGradeRuleMateTotals);
    @Mappings(
            {
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))")
            }
    )
    CellInstockPlanFinishTotalQuery toCellInstockPlanFinishTotalQuery(CellInstockPlanFinishTotalQuery query);
}
