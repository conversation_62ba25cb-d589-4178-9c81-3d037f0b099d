package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 车间优先度效率目标值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellWorkshopPriorityTargetExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 车间Id
     */
    @ExcelProperty(value = "车间Id")
    @ExcelIgnore
    private Long workshopId;
    /**
     * 车间
     */
    @ExcelProperty(value = "车间")
    private String workshop;
    /**
     * 效率分布
     */
    @ExcelProperty(value = "效率分布")
    private String efficiency;
}
