package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 货位对应车间关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 02:57:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellLocatorWorkshopRelationExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 货位
     */
    @ExcelProperty(value = "货位")
    private String locator;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    @ExcelIgnore
    private String workshop;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
}
