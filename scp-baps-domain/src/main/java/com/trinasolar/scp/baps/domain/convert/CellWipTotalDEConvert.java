package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellWipTotal;
import com.trinasolar.scp.baps.domain.dto.CellWipTotalDTO;
import com.trinasolar.scp.baps.domain.excel.CellWipTotalExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellWipTotalSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 开立工单明细表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellWipTotalDEConvert extends BaseDEConvert<CellWipTotalDTO, CellWipTotal> {

    CellWipTotalDEConvert INSTANCE = Mappers.getMapper(CellWipTotalDEConvert.class);

    List<CellWipTotalExcelDTO> toExcelDTO(List<CellWipTotalDTO> dtos);

    CellWipTotalExcelDTO toExcelDTO(CellWipTotalDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    CellWipTotal saveDTOtoEntity(CellWipTotalSaveDTO saveDTO, @MappingTarget CellWipTotal entity);
}
