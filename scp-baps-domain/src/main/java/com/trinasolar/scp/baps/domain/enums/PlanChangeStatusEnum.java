package com.trinasolar.scp.baps.domain.enums;

import com.trinasolar.scp.baps.domain.dto.CellInstockPlanTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalDTO;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

public enum PlanChangeStatusEnum {
    UNCHANGE("无变更", "UNCHANGE"),
    CHANGEED("有变更", "CHANGEED"),
    ADD("新增", "ADD"),
    DELETE("已删除", "DELETE");

    private final String desc;
    private final String value;

    PlanChangeStatusEnum(String desc, String value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return this.desc;
    }

    public String getValue() {
        return this.value;
    }

    public static String getDescForCompare(CellPlanLineTotalDTO newDTO, CellPlanLineTotalDTO oldDTO) {
        if (Objects.isNull(newDTO) && Objects.nonNull(oldDTO)) {
            return PlanChangeStatusEnum.DELETE.desc;
        } else if (Objects.nonNull(newDTO) && Objects.isNull(oldDTO)) {
            return PlanChangeStatusEnum.ADD.desc;
        } else {
            return PlanChangeStatusEnum.UNCHANGE.desc;
        }
    }

//    public static String getDescForCompare(CellInstockPlanTotalDTO newDTO, CellInstockPlanTotalDTO oldDTO) {
//
//    }

    public static String getDescForCompare(CellInstockPlanTotalDTO newDTO, CellInstockPlanTotalDTO oldDTO) {
        if (Objects.isNull(newDTO) && Objects.nonNull(oldDTO)) {
            return PlanChangeStatusEnum.DELETE.desc;
        } else if (Objects.nonNull(newDTO) && Objects.isNull(oldDTO)) {
            return PlanChangeStatusEnum.ADD.desc;
        } else {
            return PlanChangeStatusEnum.UNCHANGE.desc;
        }
    }
}
