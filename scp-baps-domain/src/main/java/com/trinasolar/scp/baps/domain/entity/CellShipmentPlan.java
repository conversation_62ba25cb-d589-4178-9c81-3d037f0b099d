package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 可发货计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-28 11:52:39
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_shipment_plan")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_shipment_plan SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_shipment_plan SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellShipmentPlan extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;
    /**
     * 入库计划id
     */
    @ApiModelProperty(value = "入库计划id")
    @Column(name = "instock_plan_id")
    private Long instockPlanId;
    /**
     * 订单表
     */
    @ApiModelProperty(value = "订单表")
    @Column(name = "order_code")
    private String orderCode;

    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    @Column(name = "source_type")
    private String sourceType;

    /**
     * 需求版本号
     */
    @ApiModelProperty(value = "需求版本号")
    @Column(name = "demand_version")
    private String demandVersion;

    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshop_id")
    private Long workshopId;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;

    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元Id")
    @Column(name = "workunit_id")
    private Long workunitId;

    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    @Column(name = "line_name")
    private String lineName;

    /**
     * 产线数量
     */
    @ApiModelProperty(value = "产线数量")
    @Column(name = "number_line")
    private BigDecimal numberLine;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cells_type_id")
    private Long cellsTypeId;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    @Column(name = "h_trace")
    private String hTrace;

    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    @Column(name = "aesthetics")
    private String aesthetics;

    /**
     * 是否进行了透明双玻拆分
     */
    @ApiModelProperty(value = "是否进行了透明双玻拆分")
    @Column(name = "is_transparent_double_glass")
    private Integer isTransparentDoubleGlass;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    @Column(name = "transparent_double_glass")
    private String transparentDoubleGlass;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    @Column(name = "cell_source")
    private String cellSource;

    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    @Column(name = "production_grade")
    private String productionGrade;

    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    @Column(name = "regional_country")
    private String regionalCountry;

    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    @Column(name = "demand_base_place")
    private String demandBasePlace;

    /**
     * 是否电池特殊要求
     */
    @ApiModelProperty(value = "是否电池特殊要求")
    @Column(name = "is_special_requirement")
    private String isSpecialRequirement;

    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    @Column(name = "low_resistance")
    private String lowResistance;

    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    @Column(name = "cell_mfrs")
    private String cellMfrs;

    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    @Column(name = "silver_pulp_mfrs")
    private String silverPulpMfrs;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @Column(name = "demand_qty")
    private BigDecimal demandQty;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 入库排产月份
     */
    @ApiModelProperty(value = "入库排产月份")
    @Column(name = "month")
    private String month;

    /**
     * MV
     */
    @ApiModelProperty(value = "MV")
    @Column(name = "cell_mv")
    private BigDecimal cellMv;

    /**
     * 最終邮件确认发布的版本
     */
    @ApiModelProperty(value = "最終邮件确认发布的版本")
    @Column(name = "final_version")
    private String finalVersion;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "version")
    private String version;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 数量（片）拆分前的值
     */
    @ApiModelProperty(value = "数量（片）拆分前的值")
    @Column(name = "old_qty_pc")
    private BigDecimal oldQtyPc;

    /**
     * 数量（片）
     */
    @ApiModelProperty(value = "数量（片）")
    @Column(name = "qty_pc")
    private BigDecimal qtyPc;

    /**
     * 汇总明细行id
     */
    @ApiModelProperty(value = "汇总明细行id")
    @Column(name = "demand_summary_lines_id")
    private Long demandSummaryLinesId;

    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    @Column(name = "si_mfrs")
    private String siMfrs;

    /**
     * 是否进行了硅料厂家拆分
     */
    @ApiModelProperty(value = "是否进行了硅料厂家拆分")
    @Column(name = "is_si_mfrs")
    private Integer isSiMfrs;

    /**
     * 硅片厂家
     */
    @ApiModelProperty(value = "硅片厂家")
    @Column(name = "silicon_material_manufacturer")
    private String siliconMaterialManufacturer;

    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    @Column(name = "screen_plate_mfrs")
    private String screenPlateMfrs;

    /**
     * 起始效率
     */
    @ApiModelProperty(value = "起始效率")
    @Column(name = "start_efficiency")
    private BigDecimal startEfficiency;

    /**
     * 最大分布效率
     */
    @ApiModelProperty(value = "最大分布效率")
    @Column(name = "max_efficiency")
    private BigDecimal maxEfficiency;

    /**
     * 电池特殊单号
     */
    @ApiModelProperty(value = "电池特殊单号")
    @Column(name = "special_order_no")
    private String specialOrderNo;

    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    @Column(name = "demand_date")
    private LocalDate demandDate;

    /**
     * 是否已经进行硅片等级拆分
     */
    @ApiModelProperty(value = "是否已经进行硅片等级拆分")
    @Column(name = "is_wafer_grade")
    private Integer isWaferGrade;

    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    @Column(name = "wafer_grade")
    private String waferGrade;

    /**
     * 是否进行了A-拆分
     */
    @ApiModelProperty(value = "是否进行了A-拆分")
    @Column(name = "is_a_split")
    private Integer isASplit;

    /**
     * 加工类型优先级
     */
    @ApiModelProperty(value = "加工类型优先级")
    @Column(name = "process_category_priority")
    private Integer processCategoryPriority;

    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    @Column(name = "process_category")
    private String processCategory;

    /**
     * 是否已经进行了加工类型拆分
     */
    @ApiModelProperty(value = "是否已经进行了加工类型拆分")
    @Column(name = "is_process_category")
    private Integer isProcessCategory;

    /**
     * 是否进行手动加工类型指定
     */
    @ApiModelProperty(value = "是否进行手动加工类型指定")
    @Column(name = "is_hand_process_category")
    private Integer isHandProcessCategory;

    /**
     * gap
     */
    @ApiModelProperty(value = "gap")
    @Column(name = "gap")
    private BigDecimal gap;

    /**
     * 需求说明
     */
    @ApiModelProperty(value = "需求说明")
    @Column(name = "demand_remark")
    private String demandRemark;

    /**
     * 分档规则
     */
    @ApiModelProperty(value = "分档规则")
    @Column(name = "grade_rule")
    private String gradeRule;

    /**
     * 验证标识
     */
    @ApiModelProperty(value = "验证标识")
    @Column(name = "verification_mark")
    private String verificationMark;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    @Column(name = "demand_source")
    private String demandSource;

    /**
     * 电池物料编码
     */
    @ApiModelProperty(value = "电池物料编码")
    @Column(name = "battery_material_code")
    private String batteryMaterialCode;

    /**
     * 原来投产开始时间
     */
    @ApiModelProperty(value = "原来投产开始时间")
    @Column(name = "old_start_time")
    private LocalDateTime oldStartTime;

    /**
     * 原来投产结束时间
     */
    @ApiModelProperty(value = "原来投产结束时间")
    @Column(name = "old_end_time")
    private LocalDateTime oldEndTime;

    /**
     * 排产来源Id
     */
    @ApiModelProperty(value = "排产来源Id")
    @Column(name = "scheduling_from_id")
    private Long schedulingFromId;

    /**
     * 投产来源Id
     */
    @ApiModelProperty(value = "投产来源Id")
    @Column(name = "plan_line_from_id")
    private Long planLineFromId;

    /**
     * bbom中的Id
     */
    @ApiModelProperty(value = "bbom中的Id")
    @Column(name = "bbom_id")
    private Long bbomId;

    /**
     * 拆前父Id
     */
    @ApiModelProperty(value = "拆前父Id")
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 计划确认
     */
    @ApiModelProperty(value = "计划确认")
    @Column(name = "confirm_plan")
    private Integer confirmPlan;

    /**
     * 投产月份
     */
    @ApiModelProperty(value = "投产月份")
    @Column(name = "old_month")
    private String oldMonth;

    /**
     * 发货(出库)日期
     */
    @ApiModelProperty(value = "发货(出库)日期")
    @Column(name = "out_bound_date")
    private LocalDateTime outBoundDate;
    /**
     * 来源类型Id
     */
    @ApiModelProperty(value = "来源类型Id")
    @Column(name = "source_type_id")
    private Long sourceTypeId;
    /**
     * H追溯Id
     */
    @ApiModelProperty(value = "H追溯Id")
    @Column(name = "h_trace_id")
    private Long hTraceId;

    /**
     * 美学Id
     */
    @ApiModelProperty(value = "美学Id")
    @Column(name = "aesthetics_id")
    private Long aestheticsId;

    /**
     * 透明双玻Id
     */
    @ApiModelProperty(value = "透明双玻Id")
    @Column(name = "transparent_double_glass_id")
    private Long transparentDoubleGlassId;

    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    @Column(name = "cell_source_id")
    private Long cellSourceId;

    /**
     * 小区域国家Id
     */
    @ApiModelProperty(value = "小区域国家Id")
    @Column(name = "regional_country_id")
    private Long regionalCountryId;
    /**
     * 需求地Id
     */
    @ApiModelProperty(value = "需求地Id")
    @Column(name = "demand_base_place_id")
    private Long demandBasePlaceId;


    /**
     * 电池厂家Id
     */
    @ApiModelProperty(value = "电池厂家Id")
    @Column(name = "cell_mfrs_id")
    private Long cellMfrsId;

    /**
     * 银浆厂家Id
     */
    @ApiModelProperty(value = "银浆厂家Id")
    @Column(name = "silver_pulp_mfrs_id")
    private Long silverPulpMfrsId;
    /**
     * 硅料厂家Id
     */
    @ApiModelProperty(value = "硅料厂家Id")
    @Column(name = "si_mfrs_id")
    private Long siMfrsId;
    /**
     * 硅片等级Id
     */
    @ApiModelProperty(value = "硅片等级Id")
    @Column(name = "wafer_grade_id")
    private Long waferGradeId;
    /**
     * 加工类型Id
     */
    @ApiModelProperty(value = "加工类型Id")
    @Column(name = "process_category_id")
    private Long processCategoryId;
    /**
     * 产品等级Id
     */
    @ApiModelProperty(value = "产品等级Id")
    @Column(name = "production_grade_id")
    private Long productionGradeId;
    /**
     * 上限数量（片）
     */
    @ApiModelProperty(value = "上限数量（片）")
    @Column(name = "max_qty_pc")
    private BigDecimal maxQtyPc;

    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_space")
    private String mainGridSpace;
    /**
     * 背面细栅
     */
    @ApiModelProperty(value = "背面细栅")
    @Column(name = "back_fine_grid")
    private String backFineGrid;
    /**
     * 正面细栅
     */
    @ApiModelProperty(value = "正面细栅")
    @Column(name = "front_fine_grid")
    private String frontFineGrid;
    /**
     * 硅片厚度
     */
    @ApiModelProperty(value = "硅片厚度")
    @Column(name = "silicon_wafer_thickness")
    private String siliconWaferThickness;
    /**
     * 硅片尺寸
     */
    @ApiModelProperty(value = "硅片尺寸")
    @Column(name = "silicon_wafer_size")
    private String siliconWaferSize;

    @ApiModelProperty(value = "证书编号")
    @Column(name = "cert_code")
    private String certCode;

    @ApiModelProperty(value = "配比")
    @Column(name = "ratio_code")
    private String ratioCode;
}
