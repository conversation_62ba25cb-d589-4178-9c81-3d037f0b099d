package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.hibernate.annotations.GenericGenerator;

/**
 * 电池良率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_fine")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "delete  from baps_cell_fine WHERE id = ?")
@SQLDeleteAll(sql = "delete from  baps_cell_fine   WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellFine extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    public CellFine(String isOversea, String cellsType, String workshop, Integer year, BigDecimal m1, BigDecimal m2, BigDecimal m3, BigDecimal m4, BigDecimal m5, BigDecimal m6, BigDecimal m7, BigDecimal m8, BigDecimal m9, BigDecimal m10, BigDecimal m11, BigDecimal m12) {

        this.isOversea = isOversea;
        this.cellsType = cellsType;
        this.workshop = workshop;
        this.year = year;
        this.m1 = m1;
        this.m2 = m2;
        this.m3 = m3;
        this.m4 = m4;
        this.m5 = m5;
        this.m6 = m6;
        this.m7 = m7;
        this.m8 = m8;
        this.m9 = m9;
        this.m10 = m10;
        this.m11 = m11;
        this.m12 = m12;
    }


    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")

    private Long id;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型id")
    @Column(name = "cells_type_id")
    private Long cellsTypeId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地id")
    @Column(name = "base_place_id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshopid")
    private Long workshopid;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月
     */
    @ApiModelProperty(value = "1月")
    @Column(name = "m1")
    private BigDecimal m1;

    /**
     * 2月
     */
    @ApiModelProperty(value = "2月")
    @Column(name = "m2")
    private BigDecimal m2;

    /**
     * 3月
     */
    @ApiModelProperty(value = "3月")
    @Column(name = "m3")
    private BigDecimal m3;

    /**
     * 4月
     */
    @ApiModelProperty(value = "4月")
    @Column(name = "m4")
    private BigDecimal m4;

    /**
     * 5月
     */
    @ApiModelProperty(value = "5月")
    @Column(name = "m5")
    private BigDecimal m5;

    /**
     * 6月
     */
    @ApiModelProperty(value = "6月")
    @Column(name = "m6")
    private BigDecimal m6;

    /**
     * 7月
     */
    @ApiModelProperty(value = "7月")
    @Column(name = "m7")
    private BigDecimal m7;

    /**
     * 8月
     */
    @ApiModelProperty(value = "8月")
    @Column(name = "m8")
    private BigDecimal m8;

    /**
     * 9月
     */
    @ApiModelProperty(value = "9月")
    @Column(name = "m9")
    private BigDecimal m9;

    /**
     * 10月
     */
    @ApiModelProperty(value = "10月")
    @Column(name = "m10")
    private BigDecimal m10;

    /**
     * 11月
     */
    @ApiModelProperty(value = "11月")
    @Column(name = "m11")
    private BigDecimal m11;

    /**
     * 12月
     */
    @ApiModelProperty(value = "12月")
    @Column(name = "m12")
    private BigDecimal m12;


}
