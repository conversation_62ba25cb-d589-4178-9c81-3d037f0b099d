package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 电池良率中间表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellFineMid保存参数", description = "保存参数")
public class CellFineMidSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private Integer year;
    /**
     * 满产/排产
     */
    @ApiModelProperty(value = "满产/排产")
    private String parameterType;
    /**
     * 产品分类
     */
    @ApiModelProperty(value = "产品分类")
    private String productType;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String productCategory;
    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGrid;
    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    private String crystalType;
    /**
     * 单晶/多晶
     */
    @ApiModelProperty(value = "单晶/多晶")
    private String crystalSpec;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 1月数
     */
    @ApiModelProperty(value = "1月数")
    private BigDecimal m1;
    /**
     * 2月数
     */
    @ApiModelProperty(value = "2月数")
    private BigDecimal m2;
    /**
     * 3月数
     */
    @ApiModelProperty(value = "3月数")
    private BigDecimal m3;
    /**
     * 4月数
     */
    @ApiModelProperty(value = "4月数")
    private BigDecimal m4;
    /**
     * 5月数
     */
    @ApiModelProperty(value = "5月数")
    private BigDecimal m5;
    /**
     * 6月数
     */
    @ApiModelProperty(value = "6月数")
    private BigDecimal m6;
    /**
     * 7月数
     */
    @ApiModelProperty(value = "7月数")
    private BigDecimal m7;
    /**
     * 8月数
     */
    @ApiModelProperty(value = "8月数")
    private BigDecimal m8;
    /**
     * 9月数
     */
    @ApiModelProperty(value = "9月数")
    private BigDecimal m9;
    /**
     * 10月数
     */
    @ApiModelProperty(value = "10月数")
    private BigDecimal m10;
    /**
     * 11月数
     */
    @ApiModelProperty(value = "11月数")
    private BigDecimal m11;
    /**
     * 12月数
     */
    @ApiModelProperty(value = "12月数")
    private BigDecimal m12;
}
