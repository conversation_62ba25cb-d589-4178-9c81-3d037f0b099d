package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 可发货计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-28 11:52:39
 */
@Data
@ApiModel(value = "CellShipmentPlan查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellShipmentPlanQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 订单表
         */
        @ApiModelProperty(value = "订单表")
    private String orderCode;
        /**
         * 来源类型
         */
        @ApiModelProperty(value = "来源类型")
    private String sourceType;
        /**
         * 需求版本号
         */
        @ApiModelProperty(value = "需求版本号")
    private String demandVersion;
        /**
         * 国内海外Id
         */
        @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
        /**
         * 国内海外
         */
        @ApiModelProperty(value = "国内海外")
    private String isOversea;
        /**
         * 生产基地
         */
        @ApiModelProperty(value = "生产基地")
    private String basePlace;
        /**
         * 生产基地Id
         */
        @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshop;
        /**
         * 生产车间Id
         */
        @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
        /**
         * 生产单元
         */
        @ApiModelProperty(value = "生产单元")
    private String workunit;
        /**
         * 生产单元Id
         */
        @ApiModelProperty(value = "生产单元Id")
    private Long workunitId;
        /**
         * 生产线体
         */
        @ApiModelProperty(value = "生产线体")
    private String lineName;
        /**
         * 产线数量
         */
        @ApiModelProperty(value = "产线数量")
    private BigDecimal numberLine;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellsType;
        /**
         * 电池类型Id
         */
        @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
        /**
         * H追溯
         */
        @ApiModelProperty(value = "H追溯")
    private String hTrace;
        /**
         * 美学
         */
        @ApiModelProperty(value = "美学")
    private String aesthetics;
        /**
         * 是否进行了透明双玻拆分
         */
        @ApiModelProperty(value = "是否进行了透明双玻拆分")
    private Integer isTransparentDoubleGlass;
        /**
         * 透明双玻
         */
        @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
        /**
         * 片源种类
         */
        @ApiModelProperty(value = "片源种类")
    private String cellSource;
        /**
         * 产品等级
         */
        @ApiModelProperty(value = "产品等级")
    private String productionGrade;
        /**
         * 小区域国家
         */
        @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
        /**
         * 5A料号
         */
        @ApiModelProperty(value = "5A料号")
    private String itemCode;
        /**
         * 需求地
         */
        @ApiModelProperty(value = "需求地")
    private String demandBasePlace;
        /**
         * 是否电池特殊要求
         */
        @ApiModelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
        /**
         * 低阻
         */
        @ApiModelProperty(value = "低阻")
    private String lowResistance;
        /**
         * 电池厂家
         */
        @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
        /**
         * 银浆厂家
         */
        @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
        /**
         * 需求数量
         */
        @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQty;
        /**
         * 结束时间
         */
        @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
        /**
         * 开始时间
         */
        @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
        /**
         * 入库排产月份
         */
        @ApiModelProperty(value = "入库排产月份")
    private String month;
        /**
         * MV
         */
        @ApiModelProperty(value = "MV")
    private BigDecimal cellMv;
        /**
         * 最終邮件确认发布的版本
         */
        @ApiModelProperty(value = "最終邮件确认发布的版本")
    private String finalVersion;
        /**
         * 版本
         */
        @ApiModelProperty(value = "版本")
    private String version;
        /**
         * 备注
         */
        @ApiModelProperty(value = "备注")
    private String remark;
        /**
         * 数量（片）拆分前的值
         */
        @ApiModelProperty(value = "数量（片）拆分前的值")
    private BigDecimal oldQtyPc;
        /**
         * 数量（片）
         */
        @ApiModelProperty(value = "数量（片）")
    private BigDecimal qtyPc;
        /**
         * 汇总明细行id
         */
        @ApiModelProperty(value = "汇总明细行id")
    private Long demandSummaryLinesId;
        /**
         * 硅料厂家
         */
        @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
        /**
         * 是否进行了硅料厂家拆分
         */
        @ApiModelProperty(value = "是否进行了硅料厂家拆分")
    private Integer isSiMfrs;
        /**
         * 硅片厂家
         */
        @ApiModelProperty(value = "硅片厂家")
    private String siliconMaterialManufacturer;
        /**
         * 网版厂家
         */
        @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;
        /**
         * 起始效率
         */
        @ApiModelProperty(value = "起始效率")
    private BigDecimal startEfficiency;
        /**
         * 最大分布效率
         */
        @ApiModelProperty(value = "最大分布效率")
    private BigDecimal maxEfficiency;
        /**
         * 电池特殊单号
         */
        @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;
        /**
         * 需求日期
         */
        @ApiModelProperty(value = "需求日期")
    private LocalDate demandDate;
        /**
         * 是否已经进行硅片等级拆分
         */
        @ApiModelProperty(value = "是否已经进行硅片等级拆分")
    private Integer isWaferGrade;
        /**
         * 硅片等级
         */
        @ApiModelProperty(value = "硅片等级")
    private String waferGrade;
        /**
         * 是否进行了A-拆分
         */
        @ApiModelProperty(value = "是否进行了A-拆分")
    private Integer isASplit;
        /**
         * 加工类型优先级
         */
        @ApiModelProperty(value = "加工类型优先级")
    private Integer processCategoryPriority;
        /**
         * 加工类型
         */
        @ApiModelProperty(value = "加工类型")
    private String processCategory;
        /**
         * 是否已经进行了加工类型拆分
         */
        @ApiModelProperty(value = "是否已经进行了加工类型拆分")
    private Integer isProcessCategory;
        /**
         * 是否进行手动加工类型指定
         */
        @ApiModelProperty(value = "是否进行手动加工类型指定")
    private Integer isHandProcessCategory;
        /**
         * gap
         */
        @ApiModelProperty(value = "gap")
    private BigDecimal gap;
        /**
         * 需求说明
         */
        @ApiModelProperty(value = "需求说明")
    private String demandRemark;
        /**
         * 分档规则
         */
        @ApiModelProperty(value = "分档规则")
    private String gradeRule;
        /**
         * 验证标识
         */
        @ApiModelProperty(value = "验证标识")
    private String verificationMark;
        /**
         * 需求来源
         */
        @ApiModelProperty(value = "需求来源")
    private String demandSource;
        /**
         * 电池物料编码
         */
        @ApiModelProperty(value = "电池物料编码")
    private String batteryMaterialCode;
        /**
         * 原来投产开始时间
         */
        @ApiModelProperty(value = "原来投产开始时间")
    private LocalDateTime oldStartTime;
        /**
         * 原来投产结束时间
         */
        @ApiModelProperty(value = "原来投产结束时间")
    private LocalDateTime oldEndTime;
        /**
         * 排产来源Id
         */
        @ApiModelProperty(value = "排产来源Id")
    private Long schedulingFromId;
        /**
         * 投产来源Id
         */
        @ApiModelProperty(value = "投产来源Id")
    private Long planLineFromId;
        /**
         * bbom中的Id
         */
        @ApiModelProperty(value = "bbom中的Id")
    private Long bbomId;
        /**
         * 拆前父Id
         */
        @ApiModelProperty(value = "拆前父Id")
    private Long parentId;
        /**
         * 计划确认
         */
        @ApiModelProperty(value = "计划确认")
    private Integer confirmPlan;
        /**
         * 投产月份
         */
        @ApiModelProperty(value = "投产月份")
    private String oldMonth;
        /**
         * 发货(出库)日期
         */
        @ApiModelProperty(value = "发货(出库)日期")
    private LocalDateTime outBoundDate;
    /**
     * 上个月
     */
    @ApiModelProperty(value = "上个月")
    private String lastMonth;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
    /**
     * 来源类型Id
     */
    @ApiModelProperty(value = "来源类型Id")
    private Long sourceTypeId;
    /**
     * H追溯Id
     */
    @ApiModelProperty(value = "H追溯Id")
    private Long hTraceId;

    /**
     * 美学Id
     */
    @ApiModelProperty(value = "美学Id")
    private Long aestheticsId;

    /**
     * 透明双玻Id
     */
    @ApiModelProperty(value = "透明双玻Id")
    private Long transparentDoubleGlassId;

    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    private Long cellSourceId;

    /**
     * 小区域国家Id
     */
    @ApiModelProperty(value = "小区域国家Id")
    private Long regionalCountryId;
    /**
     * 需求地Id
     */
    @ApiModelProperty(value = "需求地Id")
    private Long demandBasePlaceId;


    /**
     * 电池厂家Id
     */
    @ApiModelProperty(value = "电池厂家Id")
    private Long cellMfrsId;

    /**
     * 银浆厂家Id
     */
    @ApiModelProperty(value = "银浆厂家Id")
    private Long silverPulpMfrsId;
    /**
     * 硅料厂家Id
     */
    @ApiModelProperty(value = "硅料厂家Id")
    private Long siMfrsId;
    /**
     * 硅片等级Id
     */
    @ApiModelProperty(value = "硅片等级Id")
    private Long waferGradeId;
    /**
     * 加工类型Id
     */
    @ApiModelProperty(value = "加工类型Id")
    private Long processCategoryId;
    /**
     * 产品等级Id
     */
    @ApiModelProperty(value = "产品等级Id")
    private Long productionGradeId;
    /**
     * 上限数量（片）
     */
    @ApiModelProperty(value = "上限数量（片）")
    private BigDecimal maxQtyPc;
}
