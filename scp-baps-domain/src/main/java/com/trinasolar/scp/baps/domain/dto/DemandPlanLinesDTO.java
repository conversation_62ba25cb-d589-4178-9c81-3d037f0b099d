package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;

import java.util.Date;
/**
 * 电池需求计划明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-21 08:58:37
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池需求计划明细表DTO对象", description = "DTO对象")
public class DemandPlanLinesDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 需求计划头表
     */
    @ApiModelProperty(value = "需求计划头表")
    private Long demandPlanHeaderId;
    /**
     * 来源行id  来源行根据这个关联明细行
     */
    @ApiModelProperty(value = "电池需求计划来源行表ID")
    private Long demandPlanSourceId;
    /**
     * 汇总行id  明细行根据这个关联汇总行 summaryLinesId
     */
    @ApiModelProperty(value = "汇总明细行id")
    private Long demandSummaryLinesId;
    /**
     * 电池需求计划号
     */
    @ApiModelProperty(value = "电池需求计划号")
    private String demandPlanCode;
    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceType;


    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceTypeName;


    /**
     * 需求来源 与sourceType 重复废弃
     */
    /*@ApiModelProperty(value = "需求来源")
    private String demandSource;*/

    /**
     * 需求来源
     */
    /*@ApiModelProperty(value = "需求来源")
    private String demandSourceName;*/

    /**
     * 需求版本号
     */
    @ApiModelProperty(value = "需求版本号")
    private String demandVersion;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String domesticOversea;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String domesticOverseaName;

    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String pcsSourceType;

    /**
     * H追溯名称
     */
    @ApiModelProperty(value = "H追溯名称")
    private String hTraceName;

    /**
     * 片源种类名称
     */
    @ApiModelProperty(value = "片源种类名称")
    private String pcsSourceTypeName;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;

    /**
     * 美学名称
     */
    @ApiModelProperty(value = "美学名称")
    private String aestheticsName;


    /**
     * 透明双玻名称
     */
    @ApiModelProperty(value = "透明双玻名称")
    private String transparentDoubleGlassName;



    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountryName;


    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    private String basePlace;

    @ApiModelProperty(value = "需求地名称")
    private String basePlaceName;
    /**
     * 是否电池特殊要求
     */
    @ApiModelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    private String lowResistance;
    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQty;
    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    private LocalDate demandDate;
    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal scheduleQty;
    /**
     * 是否sop
     */
    @ApiModelProperty(value = "是否sop")
    private String isSop;
    /**
     * 说明
     */
    @ApiModelProperty(value = "说明")
    private String remark;
    /**
     * '硅料厂家'
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;

    /**
     * '硅料厂家'
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;

    /**
     * 起始效率
     */
    @ApiModelProperty(value = "起始效率")
    private BigDecimal startEfficiency;

    /**
     * 最大分布效率
     */
    @ApiModelProperty(value = "最大分布效率")
    private String maxEfficiency;

    /**
     * 最大分布效率
     */
    @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;

    /**
     * 指定车间 废弃
     */
//    @ApiModelProperty(value = "指定车间")
//    private String scheduleWorkshop;

    /**
     * 指定车间 废弃
     */
//    @ApiModelProperty(value = "指定车间")
//    private String scheduleWorkshopName;

    /**
     * 电池物料编码
     */
    @ApiModelProperty(value = "电池物料编码")
    private String batteryMaterialCode;

    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    private Long supplyMode;

    /**
     * s&op数量
     */
    @ApiModelProperty(value = "s&op数量")
    private BigDecimal sopAndDpQuantity;
    /**
     * 差异量
     */
    @ApiModelProperty(value = "差异量")
    private BigDecimal disparity;

    /**
     * 备货量
     */
    @ApiModelProperty(value = "备货量")
    private BigDecimal stockQuantity;
    /**
     * 符合率
     */
    @ApiModelProperty("符合率")
    private BigDecimal passPercent;

    /**
     * 加工类别
     */
    @ApiModelProperty(value = "加工类别")
    private Long processCategory;

    /**
     * 加工类别
     */
    @ApiModelProperty(value = "加工类别")
    private String processCategoryName;

    /**
     * 电池生产车间
     */
    @ApiModelProperty(value = "电池生产车间")
    private String scheduleWorkshop;

    @ApiModelProperty(value = "电池生产车间")
    private String scheduleWorkshopName;

    /**
     * 组件指定车间（暂时没用）
     */
    @ApiModelProperty(value = "组件指定车间")
    private String assignComponentWorkshop;
}
