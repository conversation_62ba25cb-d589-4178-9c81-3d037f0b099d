package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.entity.CellBomManufacturing;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.Calendar;
import com.trinasolar.scp.baps.domain.dto.CalendarDTO;
import com.trinasolar.scp.baps.domain.excel.CalendarExcelDTO;
import com.trinasolar.scp.baps.domain.save.CalendarSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * 生产日历 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalendarDEConvert extends BaseDEConvert<CalendarDTO, Calendar> {

    CalendarDEConvert INSTANCE = Mappers.getMapper(CalendarDEConvert.class);

    List<CalendarExcelDTO> toExcelDTO(List<CalendarDTO> dtos);

    CalendarExcelDTO toExcelDTO(CalendarDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })

    Calendar saveDTOtoEntity(CalendarSaveDTO saveDTO, @MappingTarget Calendar entity);
    @Mappings(
            {
                    @Mapping(target = "workunitId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT,bom.getWorkunit()).getLovLineId())"),
                    @Mapping(target = "date",expression ="java(bom.getStartDate().toLocalDate())" ),
                    @Mapping(target = "defaultqty",source ="capacityQuantity" ),
                    @Mapping(target = "sortorder",source = "rate"),
                    @Mapping(target = "numLines",source = "usageLine")
            }
    )
    Calendar fromCellBomManufacturingToCalendar(CellBomManufacturing bom);
    default List<Calendar> fromCellBomManufacturingList(CellBomManufacturing bom){
        List<Calendar> calendars=new ArrayList<>();
        if ( ChronoUnit.DAYS.between(bom.getStartDate(), bom.getEndDate())==0){
            //bom中对应一天数据的情况（如爬坡产能的情况）
            Calendar calendar=fromCellBomManufacturingToCalendar(bom);
            calendar.setId(null);
            calendars.add(calendar);
        }else{
            //bom中对应一个时间段数据的情况（如IE产能的情况）
            LocalDate startDate=bom.getStartDate().toLocalDate();
            LocalDate endDate=bom.getEndDate().toLocalDate();
            Calendar calendar = null;
            for (LocalDate date = startDate; !date.equals(endDate); date = date.plusDays(1)) {
                calendar = fromCellBomManufacturingToCalendar(bom);
                calendar.setDate(date);
                calendars.add(calendar);
            }
            if (calendar != null) {
                calendar.setId(null);
                calendars.add(calendar);
            }
        }
        return  calendars;
    }


}
