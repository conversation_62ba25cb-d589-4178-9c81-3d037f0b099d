package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellPlanLineDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanShippableDTO;
import com.trinasolar.scp.baps.domain.dto.aps.CellPlanShippableSaveListDTO;
import com.trinasolar.scp.baps.domain.entity.CellPlanShippable;
import com.trinasolar.scp.baps.domain.excel.CellPlanShippableExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanShippableQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanShippableSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 可发货计划表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-09 10:05:37
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanShippableDEConvert extends BaseDEConvert<CellPlanShippableDTO, CellPlanShippable> {

    CellPlanShippableDEConvert INSTANCE = Mappers.getMapper(CellPlanShippableDEConvert.class);

    List<CellPlanShippableExcelDTO> toExcelDTO(List<CellPlanShippableDTO> dtos);

    CellPlanShippableExcelDTO toExcelDTO(CellPlanShippableDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    CellPlanShippable saveDTOtoEntity(CellPlanShippableSaveDTO saveDTO, @MappingTarget CellPlanShippable entity);

    @Mappings(
            {
                    @Mapping(target = "cellsTypeId", expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType()).getLovLineId())"),
                    @Mapping(target = "basePlaceId", expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "workShopId", expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkShop()).getLovLineId())"),
                    @Mapping(target = "workUnitId", expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkUnit()).getLovLineId())"),
            }
    )
    CellPlanShippableSaveDTO excelDtoToSaveDto(CellPlanShippableExcelDTO excelDTO);

    List<CellPlanShippableSaveDTO> excelDtoToSaveDto(List<CellPlanShippableExcelDTO> dto);

    CellPlanLineQuery toCellPlanLineQuery(CellPlanShippableQuery query);

    CellPlanShippableDTO toCellShippable(CellPlanLineDTO cellPlanLineDTO);

    List<CellPlanShippableSaveListDTO> toSaveListDto(List<CellPlanShippableDTO> dtos);
}
