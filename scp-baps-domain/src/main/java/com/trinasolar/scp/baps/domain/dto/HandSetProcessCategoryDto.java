package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 手动设置加工类型DTO
 */
@ApiModel(value = "手动设置加工类型DTO", description = "DTO对象")
@Data
public class HandSetProcessCategoryDto {
    /**
     * 入库计划Id
     */
    @ApiModelProperty(value = "入库计划Id")
    private  Long id;

    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    private String processCategory;

}
