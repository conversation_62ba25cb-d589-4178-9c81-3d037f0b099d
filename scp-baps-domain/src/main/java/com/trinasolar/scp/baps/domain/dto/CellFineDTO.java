package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 电池良率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池良率表DTO对象", description = "DTO对象")
public class CellFineDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外id")
    private Long isOverseaId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型id")
    private Long cellsTypeId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private Long workshopid;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private Integer year;
    /**
     * 1月
     */
    @ApiModelProperty(value = "1月")
    private String m1;
    /**
     * 2月
     */
    @ApiModelProperty(value = "2月")
    private String m2;
    /**
     * 3月
     */
    @ApiModelProperty(value = "3月")
    private String m3;
    /**
     * 4月
     */
    @ApiModelProperty(value = "4月")
    private String m4;
    /**
     * 5月
     */
    @ApiModelProperty(value = "5月")
    private String m5;
    /**
     * 6月
     */
    @ApiModelProperty(value = "6月")
    private String m6;
    /**
     * 7月
     */
    @ApiModelProperty(value = "7月")
    private String m7;
    /**
     * 8月
     */
    @ApiModelProperty(value = "8月")
    private String m8;
    /**
     * 9月
     */
    @ApiModelProperty(value = "9月")
    private String m9;
    /**
     * 10月
     */
    @ApiModelProperty(value = "10月")
    private String m10;
    /**
     * 11月
     */
    @ApiModelProperty(value = "11月")
    private String m11;
    /**
     * 12月
     */
    @ApiModelProperty(value = "12月")
    private String m12;
}
