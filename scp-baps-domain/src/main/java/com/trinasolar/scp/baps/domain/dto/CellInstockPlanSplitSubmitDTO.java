package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 入库计划 拆分 确认
 *
 * <AUTHOR>
 * @email
 * @date 2025-01-20
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "入库计划表拆分确认DTO对象", description = "DTO对象")
@ToString
public class CellInstockPlanSplitSubmitDTO {

    @ApiModelProperty(value = "入库计划-拆分-查询条件")
    private CellInstockPlanQuery cellInstockPlanQuery;

    @ApiModelProperty(value = "入库计划-拆分-确认")
    private List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOS;

}
