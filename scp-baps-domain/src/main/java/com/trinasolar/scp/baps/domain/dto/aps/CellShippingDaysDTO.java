package com.trinasolar.scp.baps.domain.dto.aps;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 运输天数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "运输天数DTO对象", description = "DTO对象")
public class CellShippingDaysDTO extends BaseDTO {

    /**
     * 电池生产车间
     */
    @ApiModelProperty(value = "电池生产车间")
    private String originWorkshop;
    /**
     * 电池需求车间
     */
    @ApiModelProperty(value = "电池需求车间")
    private String demandWorkshop;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer rate;
    /**
     * 运输天数
     */
    @ApiModelProperty(value = "运输天数")
    private Integer days;
}
