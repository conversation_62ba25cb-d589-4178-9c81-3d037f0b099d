package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 返司
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_return_order")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_return_order SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_return_order SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellReturnOrder extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 发货方是否海外
     */
    @ApiModelProperty(value = "发货方国内海外")
    @Column(name = "shipper_is_oversea")
    private String shipperIsOversea;
    /**
     * 发货方国内海外
     */
    @ApiModelProperty(value = "发货方国内海外id")
    @Column(name = "shipper_is_oversea_id")
    private Long shipperIsOverseaId;
    /**
     * 发货方生产基地
     */
    @ApiModelProperty(value = "发货方生产基地")
    @Column(name = "shipper_base_place")
    private String shipperBasePlace;
    /**
     * 发货方生产基地
     */
    @ApiModelProperty(value = "发货方生产基地id")
    @Column(name = "shipper_base_place_id")
    private Long shipperBasePlaceId;
    /**
     * 接收方是否海外
     */
    @ApiModelProperty(value = "接收方国内海外")
    @Column(name = "receiver_is_oversea")
    private String receiverIsOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "接收方国内海外id")
    @Column(name = "receiver_is_oversea_id")
    private Long receiverIsOverseaId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "接收方生产基地")
    @Column(name = "receiver_base_place")
    private String receiverBasePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "接收方生产基地id")
    @Column(name = "receiver_base_place_id")
    private Long receiverBasePlaceId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型id")
    @Column(name = "cells_type_id")
    private Long cellsTypeId;
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    @Column(name = "item_fivea")
    private String itemFivea;
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号Id")
    @Column(name = "item_fivea_id")
    private Long itemFiveaId;
    /**
     * 效率值
     */
    @ApiModelProperty(value = "效率值")
    @Column(name = "work_cell")
    private BigDecimal workCell;

    /**
     * 对应标识
     */
    @ApiModelProperty(value = "对应标识")
    @Column(name = "note")
    private String note;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @Column(name = "date")
    private LocalDate date;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @Column(name = "cell_qty")
    private BigDecimal cellQty;
    /**
     * 账套
     */
    @ApiModelProperty(value = "接收方账套")
    @Column(name = "receiver_account")
    private String receiverAccount;

}
