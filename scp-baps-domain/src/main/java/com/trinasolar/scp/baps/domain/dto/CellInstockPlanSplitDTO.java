package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.baps.domain.enums.SelectedEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 入库计划 拆分
 *
 * <AUTHOR>
 * @email
 * @date 2025-01-20
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "入库计划表拆分DTO对象", description = "DTO对象")
@ToString
public class CellInstockPlanSplitDTO{
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * H追溯Id
     */
    @ApiModelProperty(value = "H追溯Id")
    private Long hTraceId;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 美学Id
     */
    @ApiModelProperty(value = "美学Id")
    private Long aestheticsId;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 透明双玻Id
     */
    @ApiModelProperty(value = "透明双玻Id")
    private Long transparentDoubleGlassId;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    private Long cellSourceId;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 产品等级Id
     */
    @ApiModelProperty(value = "产品等级Id")
    private Long productionGradeId;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 小区域国家Id
     */
    @ApiModelProperty(value = "小区域国家Id")
    private Long regionalCountryId;
    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;
    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    private String supplyMethod;
    /**
     * 供应方式Id
     */
    @ApiModelProperty(value = "供应方式Id")
    private Long supplyMethodId;

    /**
     * 拆分比例
     */
    @ApiModelProperty(value = "拆分比例")
    private BigDecimal splitRate;

    /**
     * 拆分开始日期
     */
    @ApiModelProperty(value ="开始日期")
    private LocalDate splitStartDate;

    /**
     * 拆分结束日期
     */
    @ApiModelProperty(value ="结束日期")
    private LocalDate splitEndDate;

    /**
     * 是否选中(0：未选中，1：已选中)
     */
    @ApiModelProperty(value ="是否选中(NO：未选中，YES：已选中)")
    private SelectedEnum isSelected;

    /**
     * 入库计划原始数据id集合
     */
    @ApiModelProperty(value ="入库计划原始数据id集合")
    private List<Long> ids;
}
