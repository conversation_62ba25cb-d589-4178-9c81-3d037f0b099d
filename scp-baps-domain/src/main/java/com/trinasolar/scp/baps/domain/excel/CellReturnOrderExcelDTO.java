package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.baps.domain.utils.CustomDateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * 返司
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellReturnOrderExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 是否海外
     */
    @ExcelProperty(value = "发货方国内海外")
    private String shipperIsOversea;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "发货方生产基地")
    private String shipperBasePlace;
    /**
     * 是否海外
     */
    @ExcelProperty(value = "接收方国内海外")
    private String receiverIsOversea;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "接收方生产基地")
    private String receiverBasePlace;
    /**
     * 账套
     */
    @ExcelProperty(value = "接收方账套")
    private String receiverAccount;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池料号
     */
    @ExcelProperty(value = "电池料号")
    private String itemFivea;
    /**
     * 电池料号Id
     */
    @ExcelProperty(value = "电池料号Id")
    @ExcelIgnore
    private Long itemFiveaId;
    /**
     * 效率值
     */
    @ExcelProperty(value = "效率值")
    private BigDecimal workCell;
    /**
     * 对应标识
     */
    @ExcelProperty(value = "对应标识")
    private String note;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 日期
     */
    @ExcelProperty(value = "日期",converter = CustomDateConverter.class)
    private LocalDate date;
    /**
     * 数量
     */
    @ExcelProperty(value = "数量(单位：万片)")
    private BigDecimal cellQty;

}
