package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 爬坡产能可靠性验证表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-05 08:10:43
 */
@Data
@ApiModel(value = "CellClimbCapacityLead查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellClimbCapacityLeadQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 电池类型id
         */
        @ApiModelProperty(value = "电池类型id")
    private Long cellTypeId;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellType;
        /**
         * 生产基地Id
         */
        @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
        /**
         * 生产基地
         */
        @ApiModelProperty(value = "生产基地")
    private String basePlace;
        /**
         * 生产车间Id
         */
        @ApiModelProperty(value = "生产车间Id")
    private Long workShopId;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workShop;
        /**
         * 生产单元Id
         */
        @ApiModelProperty(value = "生产单元Id")
    private Long workUnitId;
        /**
         * 生产单元
         */
        @ApiModelProperty(value = "生产单元")
    private String workUnit;
        /**
         * 验证时间
         */
        @ApiModelProperty(value = "验证时间")
    private LocalDateTime validateTime;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    private BigDecimal lineNumber;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
