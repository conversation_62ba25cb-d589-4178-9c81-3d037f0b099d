package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import java.io.Serializable;


/**
 * Erp实际入库来源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:23:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpWipIssueExcelDTO {

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 事务数量
     */
    @ExcelProperty(value = "事务数量")
    private BigDecimal transactionQuantity;
    /**
     * 料号
     */
    @ExcelProperty(value = "料号")
    private String itemNumber;
    /**
     * 交易库存组织Id
     */
    @ExcelProperty(value = "交易库存组织Id")
    private Long transferOrganizationId;
    /**
     * 事务单位编码
     */
    @ExcelProperty(value = "事务单位编码")
    private String transactionUomCode;
    /**
     * 库存组织ID
     */
    @ExcelProperty(value = "库存组织ID")
    private Long organizationId;
    /**
     * 物料描述
     */
    @ExcelProperty(value = "物料描述")
    private String itemDescription;
    /**
     * 货位
     */
    @ExcelProperty(value = "货位")
    private String locatorCode;
    /**
     * 事务来源类型名
     */
    @ExcelProperty(value = "事务来源类型名")
    private String transactionSourceTypeName;
    /**
     * 工单号
     */
    @ExcelProperty(value = "工单号")
    private String workOrder;
    /**
     * 库存组织名
     */
    @ExcelProperty(value = "库存组织名")
    private String organizationName;
    /**
     * 事务类型Id
     */
    @ExcelProperty(value = "事务类型Id")
    private Long transactionTypeId;
    /**
     * lot主要数据量
     */
    @ExcelProperty(value = "lot主要数据量")
    private BigDecimal lotPrimaryQuantity;
    /**
     * 子库存
     */
    @ExcelProperty(value = "子库存")
    private String subinventoryCode;
    /**
     * lot编码
     */
    @ExcelProperty(value = "lot编码")
    private String lotNumber;
    /**
     * 事务处理日期
     */
    @ExcelProperty(value = "事务处理日期")
    private LocalDateTime transactionDate;
    /**
     * 事务来源Id
     */
    @ExcelProperty(value = "事务来源Id")
    private Long transactionSourceId;
    /**
     * 事务类型名
     */
    @ExcelProperty(value = "事务类型名")
    private String transactionTypeName;
    /**
     * 事务Id
     */
    @ExcelProperty(value = "事务Id")
    private Long transactionId;
    /**
     * 事务来源类型Id
     */
    @ExcelProperty(value = "事务来源类型Id")
    private Long transactionSourceTypeId;
    /**
     * 物料Id
     */
    @ExcelProperty(value = "物料Id")
    private Long inventoryItemId;
    /**
     * 库存组织编号
     */
    @ExcelProperty(value = "库存组织编号")
    private String organizationCode;
    /**
     * 货位Id
     */
    @ExcelProperty(value = "货位Id")
    private Long locatorId;
    /**
     * 主要数量
     */
    @ExcelProperty(value = "主要数量")
    private BigDecimal primaryQuantity;
    /**
     * lot事务处理数量
     */
    @ExcelProperty(value = "lot事务处理数量")
    private BigDecimal lotTransactionQuantity;
    /**
     * erp创建时间
     */
    @ExcelProperty(value = "erp创建时间")
    private LocalDateTime creationDate;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
