package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 可发货计划表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellShipmentPlanMainExcelDTO {
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;

    @ExcelProperty(value = "需求来源")
    private String sourceType;

    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 车间
     */
    @ExcelProperty(value = "生产车间")
    private String workShop;
    /**
     * 单元
     */
    @ExcelProperty(value = "生产单元")
    private String workUnit;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;

    @ExcelProperty(value = "证书编号")
    private String certCode;

    @ExcelProperty(value = "配比")
    private String ratioCode;

    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 月份
     */
    @ExcelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 背面细栅
     */
    @ExcelProperty(value = "背面细栅")
    private String backFineGrid;
    /**
     * 正面细栅
     */
    @ExcelProperty(value = "正面细栅")
    private String frontFineGrid;
    /**
     * 硅片厚度
     */
    @ExcelProperty(value = "硅片厚度")
    private String siliconWaferThickness;
    /**
     * 硅片尺寸
     */
    @ExcelProperty(value = "硅片尺寸")
    private String siliconWaferSize;

    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;
    /**
     * d1
     */
    @ExcelProperty(value = "1号")
    private BigDecimal d1;
    /**
     * d2
     */
    @ExcelProperty(value = "2号")
    private BigDecimal d2;
    /**
     * d3
     */
    @ExcelProperty(value = "3号")
    private BigDecimal d3;
    /**
     * d4
     */
    @ExcelProperty(value = "4号")
    private BigDecimal d4;
    /**
     * d5
     */
    @ExcelProperty(value = "5号")
    private BigDecimal d5;
    /**
     * d6
     */
    @ExcelProperty(value = "6号")
    private BigDecimal d6;
    /**
     * d7
     */
    @ExcelProperty(value = "7号")
    private BigDecimal d7;
    /**
     * d8
     */
    @ExcelProperty(value = "8号")
    private BigDecimal d8;
    /**
     * d9
     */
    @ExcelProperty(value = "9号")
    private BigDecimal d9;
    /**
     * d10
     */
    @ExcelProperty(value = "10号")
    private BigDecimal d10;
    /**
     * d11
     */
    @ExcelProperty(value = "11号")
    private BigDecimal d11;
    /**
     * d12
     */
    @ExcelProperty(value = "12号")
    private BigDecimal d12;
    /**
     * d13
     */
    @ExcelProperty(value = "13号")
    private BigDecimal d13;
    /**
     * d14
     */
    @ExcelProperty(value = "14号")
    private BigDecimal d14;
    /**
     * d15
     */
    @ExcelProperty(value = "15号")
    private BigDecimal d15;
    /**
     * d16
     */
    @ExcelProperty(value = "16号")
    private BigDecimal d16;
    /**
     * d17
     */
    @ExcelProperty(value = "17号")
    private BigDecimal d17;
    /**
     * d18
     */
    @ExcelProperty(value = "18号")
    private BigDecimal d18;
    /**
     * d19
     */
    @ExcelProperty(value = "19号")
    private BigDecimal d19;
    /**
     * d20
     */
    @ExcelProperty(value = "20号")
    private BigDecimal d20;
    /**
     * d21
     */
    @ExcelProperty(value = "21号")
    private BigDecimal d21;
    /**
     * d22
     */
    @ExcelProperty(value = "22号")
    private BigDecimal d22;
    /**
     * d23
     */
    @ExcelProperty(value = "23号")
    private BigDecimal d23;
    /**
     * d24
     */
    @ExcelProperty(value = "24号")
    private BigDecimal d24;
    /**
     * d25
     */
    @ExcelProperty(value = "25号")
    private BigDecimal d25;
    /**
     * d26
     */
    @ExcelProperty(value = "26号")
    private BigDecimal d26;
    /**
     * d27
     */
    @ExcelProperty(value = "27号")
    private BigDecimal d27;
    /**
     * d28
     */
    @ExcelProperty(value = "28号")
    private BigDecimal d28;
    /**
     * d29
     */
    @ExcelProperty(value = "29号")
    private BigDecimal d29;
    /**
     * d30
     */
    @ExcelProperty(value = "30号")
    private BigDecimal d30;
    /**
     * d31
     */
    @ExcelProperty(value = "31号")
    private BigDecimal d31;
}
