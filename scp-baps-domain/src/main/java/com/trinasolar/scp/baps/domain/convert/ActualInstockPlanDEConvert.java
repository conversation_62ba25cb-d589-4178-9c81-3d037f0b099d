package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.ActualInstockPlan;
import com.trinasolar.scp.baps.domain.dto.ActualInstockPlanDTO;
import com.trinasolar.scp.baps.domain.excel.ActualInstockPlanExcelDTO;
import com.trinasolar.scp.baps.domain.save.ActualInstockPlanSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库数据（ERP实际入库、入库计划） DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 02:47:50
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActualInstockPlanDEConvert extends BaseDEConvert<ActualInstockPlanDTO, ActualInstockPlan> {

    ActualInstockPlanDEConvert INSTANCE = Mappers.getMapper(ActualInstockPlanDEConvert.class);

    List<ActualInstockPlanExcelDTO> toExcelDTO(List<ActualInstockPlanDTO> dtos);

    ActualInstockPlanExcelDTO toExcelDTO(ActualInstockPlanDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    ActualInstockPlan saveDTOtoEntity(ActualInstockPlanSaveDTO saveDTO, @MappingTarget ActualInstockPlan entity);
}
