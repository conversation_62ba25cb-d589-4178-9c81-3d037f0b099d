package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.baps.domain.utils.CustomDateConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.time.LocalDate;


/**
 * 投产计划浮动系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-30 01:49:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellPlanQtyFluctuationCoefficientExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间",converter = CustomDateConverter.class)
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间",converter = CustomDateConverter.class)
    private LocalDate endTime;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间名")
    private String workshopName;
    /**
     * 计划数量浮动系数
     */
    @ExcelProperty(value = "计划数量浮动系数")
    private BigDecimal fluctuationCoefficient;
    /**
     * 计划数量浮动系数百分比
     */
    @ExcelProperty(value = "计划数量浮动系数百分比")
    private String fluctuationCoefficientPercent;
}
