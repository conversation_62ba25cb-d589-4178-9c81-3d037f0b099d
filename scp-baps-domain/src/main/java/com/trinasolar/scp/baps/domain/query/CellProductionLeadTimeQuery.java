package com.trinasolar.scp.baps.domain.query;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 投产提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@ApiModel(value = "CellProductionLeadTime查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellProductionLeadTimeQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 生产基地
         */
        @ApiModelProperty(value = "生产基地")
    private String basePlace;
        /**
         * 生产基地
         */
        @ApiModelProperty(value = "生产基地")
    private Long basePlaceId;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshop;
        /**
         * 生产车间Id
         */
        @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;

    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workunitId;
        /**
         * 提前期
         */
        @ApiModelProperty(value = "提前期")
    private BigDecimal leadTime;
    @ExcelIgnore
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
