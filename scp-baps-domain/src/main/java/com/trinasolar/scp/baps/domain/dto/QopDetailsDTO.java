package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;


/**
 * SOP数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-30 03:38:41
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SOP数据DTO对象", description = "DTO对象")
public class QopDetailsDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外名
     */
    @ApiModelProperty(value = "国内海外名")
    private String isOverseaName;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long workshopId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String workshopName;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal sopQty;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;
}
