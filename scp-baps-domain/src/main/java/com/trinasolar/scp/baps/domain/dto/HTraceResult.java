package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.annotation.ExportConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: HTraceResult
 * @date 2024/6/12 15:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HTraceResult {
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String productCategory;
    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    private String crystalType;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 类型
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.BDM_HTRACE_TYPE)
    private String type;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String typeName;
    /**
     * 动态头
     */
    @ApiModelProperty(value = "动态头")
    private List<String> subList;
    /**
     * 动态值
     */
    @ApiModelProperty(value = "动态值")
    private Map<String, BigDecimal> subMap;

    public HTraceResult(String basePlace, String workshop, String productCategory, String crystalType, String hTrace, String month, String type) {
        this.basePlace = basePlace;
        this.workshop = workshop;
        this.productCategory = productCategory;
        this.crystalType = crystalType;
        this.hTrace = hTrace;
        this.month = month;
        this.type = type;
    }

    public static HTraceResult groupBy(HTraceDayResult dayResult) {
        return new HTraceResult(dayResult.getBasePlace(), dayResult.getWorkshop(), dayResult.getProductCategory(), dayResult.getCrystalType(), dayResult.getHTrace(), dayResult.getMonth(), dayResult.getType());
    }


}
