package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.base.BaseDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Optional;


/**
 * 投产计划浮动系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-30 01:49:53
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "投产计划浮动系数DTO对象", description = "DTO对象")
public class CellPlanQtyFluctuationCoefficientDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间名
     */
    @ApiModelProperty(value = "生产车间名")
    private String workshopName;
    /**
     * 计划数量浮动系数
     */
    @ApiModelProperty(value = "计划数量浮动系数")
    private BigDecimal fluctuationCoefficient;
    /**
     * 计划数量浮动系数百分比
     */
    @ApiModelProperty(value = "计划数量浮动系数百分比")
    private String fluctuationCoefficientPercent;

}
