package com.trinasolar.scp.baps.domain.dto.aps;
import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
/**
 * AOP供应能力维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerSupplyAopDTO对象", description = "DTO对象")
public class PowerSupplyAopDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;

    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    @ApiModelProperty(value = "电池类型")
    private String cellTypeName;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份")
    private String year;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @ExcelProperty(value = "月份")
    private String month;

    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;

    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    @ExcelProperty(value = "供应方")
    private String supplierName;

    /**
     * 供应类型:自产/外购
     */
    @ApiModelProperty(value = "供应类型:自产/外购")
    private String supplyType;

    /**
     * 供应类型:自产/外购
     */
    @ApiModelProperty(value = "供应类型:自产/外购")
    @ExcelProperty(value = "供应类型:自产/外购")
    private String supplyTypeName;

    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    @ExcelProperty(value = "供应量")
    private BigDecimal supplyQuantity;

    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m1SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m2SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m3SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m4SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m5SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m6SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m7SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m8SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m9SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m10SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m11SupplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal m12SupplyQuantity;

    /**
     * 数据版本
     */
    @ApiModelProperty(value = "数据版本")
    private String dataVersion;
    /**
     * 数据版本
     */
    @ApiModelProperty(value = "数据版本")
    private String dataVersionName;




}
