package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.query.CellBaseCapacityDiscountsQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacityDiscountsSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellBaseCapacityDiscounts;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDiscountsDTO;
import com.trinasolar.scp.baps.domain.excel.CellBaseCapacityDiscountsExcelDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * IE产能打折（人力）表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovUtils.class, LovHeaderCodeConstant.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellBaseCapacityDiscountsDEConvert extends BaseDEConvert<CellBaseCapacityDiscountsDTO, CellBaseCapacityDiscounts> {

    CellBaseCapacityDiscountsDEConvert INSTANCE = Mappers.getMapper(CellBaseCapacityDiscountsDEConvert.class);

    List<CellBaseCapacityDiscountsExcelDTO> toExcelDTO(List<CellBaseCapacityDiscountsDTO> dtos);

    CellBaseCapacityDiscountsExcelDTO toExcelDTO(CellBaseCapacityDiscountsDTO dto);
    @Mappings(
            {
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getWorkshopid()))"),
                    //  @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getWorkunitid()))"),
                    @Mapping(target = "ratio" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(cellBaseCapacity.getRatio(),2))"),
                    //  @Mapping(target = "ieConfirm" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.changeIntToYesNo(cellBaseCapacity.getIeConfirm()))"),
                    //  @Mapping(target = "planConfirm" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.changeIntToYesNo(cellBaseCapacity.getPlanConfirm()))")
            }
    )
    CellBaseCapacityDiscountsDTO toDto(CellBaseCapacityDiscounts cellBaseCapacity);
    @Override
    List<CellBaseCapacityDiscountsDTO> toDto(List<CellBaseCapacityDiscounts> list);
    @Mappings(
            {
                    @Mapping(target = "isOverseaId" ,expression = "java(LovUtils.get(LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()).getLovLineId())"),
                    @Mapping(target = "basePlaceId" ,expression = "java(LovUtils.get(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "workshopid" ,expression = "java(LovUtils.get(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop()).getLovLineId())"),
                    @Mapping(target = "ratio" ,expression = "java(MapStrutUtil.removePercentage(dto.getRatio(),4))"),
            }
    )
    CellBaseCapacityDiscountsSaveDTO excelDtoToSaveDto(CellBaseCapacityDiscountsExcelDTO dto);
    List<CellBaseCapacityDiscountsSaveDTO> excelDtoToSaveDto(List<CellBaseCapacityDiscountsExcelDTO> dto);

    @Mappings(
            {
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,query.getWorkshop(),language))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,query.getIsOversea(),language))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,query.getBasePlace(),language))")
            }
    )
    CellBaseCapacityDiscountsQuery toCellBaseCapacityDiscountsQueryCNNameByName(CellBaseCapacityDiscountsQuery query,String language);
    @Mappings(
            {
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkshopid()))"),
            }
    )
    @Named("toCellBaseCapacityDiscountsDTOSNameById")
    CellBaseCapacityDiscountsDTO toCellBaseCapacityDiscountsDTOSNameById(CellBaseCapacityDiscountsDTO dto);
    @IterableMapping(qualifiedByName = "toCellBaseCapacityDiscountsDTOSNameById")
    List<CellBaseCapacityDiscountsDTO> toCellBaseCapacityDiscountsDTOSNameById(List<CellBaseCapacityDiscountsDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshopid()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlaceId()))")
            }
    )
    @Named("cellBaseCapacityDiscountsSaveDTOCNNameById")
    CellBaseCapacityDiscountsSaveDTO cellBaseCapacityDiscountsSaveDTOCNNameById(CellBaseCapacityDiscountsSaveDTO dto);
    @IterableMapping(qualifiedByName = "cellBaseCapacityDiscountsSaveDTOCNNameById")
    List<CellBaseCapacityDiscountsSaveDTO> cellBaseCapacityDiscountsSaveDTOCNNameById(List<CellBaseCapacityDiscountsSaveDTO> saveDTOS);
}
