package com.trinasolar.scp.baps.domain.dto.aps;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;


/**
 * 电池档位对应关系
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池档位对应关系DTO对象", description = "DTO对象")
public class RecordTransitionDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 是否海外
     */
    @ApiModelProperty(value = "是否海外")
    private String isOversea;
    /**
     * 是否海外
     */
    @ApiModelProperty(value = "是否海外")
    private String isOverseaName;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * 电池等级
     */
    @ApiModelProperty(value = "电池等级")
    private String cellGrade;
    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;
    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplierName;
    /**
     * 分档规则
     */
    @ApiModelProperty(value = "分档规则")
    private String rule;
    /**
     * 有效起始时间
     */
    @ApiModelProperty(value = "有效起始时间")
    private LocalDate startTime;
    /**
     * 有效截至时间
     */
    @ApiModelProperty(value = "有效截至时间")
    private LocalDate endTime;
    /**
     * 替换版本
     */
    @ApiModelProperty(value = "替换版本")
    private String replaceRule;
    /**
     * 替换原因
     */
    @ApiModelProperty(value = "替换原因")
    private String reason;
    /**
     * 降档规则
     */
    @ApiModelProperty(value = "降档规则")
    private String downshiftRule;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
