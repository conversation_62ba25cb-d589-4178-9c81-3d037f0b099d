package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * qop与ie对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:08
 */
@Data
@ApiModel(value = "CellVersionQopIe查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellVersionQopIeQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 国内/海外Id
         */
        @ApiModelProperty(value = "国内/海外Id")
    private Long isOverseaId;
        /**
         * 国内/海外
         */
        @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
        /**
         * 电池类型编号
         */
        @ApiModelProperty(value = "电池类型编号")
    private Long cellTypeId;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellTypeName;
        /**
         * 生产车间Id
         */
        @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshopName;
        /**
         * 月份
         */
        @ApiModelProperty(value = "月份")
    private String month;
        /**
         * qop利用率
         */
        @ApiModelProperty(value = "qop利用率")
    private BigDecimal rate;
        /**
         * qop目标
         */
        @ApiModelProperty(value = "qop目标")
    private BigDecimal target;
        /**
         * IE产能
         */
        @ApiModelProperty(value = "IE产能")
    private BigDecimal capacity;
        /**
         * 差异
         */
        @ApiModelProperty(value = "差异")
    private BigDecimal gap;
        /**
         * 备注
         */
        @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
