package com.trinasolar.scp.baps.domain.utils;

import com.sun.jna.platform.unix.solaris.LibKstat;
import com.trinasolar.scp.baps.domain.dto.ConfigCellGoodDTO;
import com.trinasolar.scp.baps.domain.query.CellFineMidQuery;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class MapStrutUtil {
    /**
     * 日产能转为小时产能+ph
     * @param valueOfDay
     * @return
     */
    public static  final BigDecimal changeUnitToHourPh(BigDecimal valueOfDay){
        if (valueOfDay==null) {
            return null;
        }
        return valueOfDay.divide(BigDecimal.valueOf(24),6, RoundingMode.HALF_UP);

    }
    /**
     * 依据报价系统电池良率dto属性拼接电池类型left
     * @param dto
     * @return
     */
    public static final String getCellsTypeLeftByConfigCellGoodDTO(ConfigCellGoodDTO dto){
        StringBuilder sb= new StringBuilder();
        sb.append(dto.getCrystalSpecName());
        sb.append("_");
        sb.append(dto.getCrystalTypeName());
        sb.append("_");
        sb.append(dto.getProductCategoryName());
        sb.append("_双面_");
        sb.append(dto.getMainGridName());
        return sb.toString();

    }

    /**
     * 依据电池类型获取电池类型左
     * @param cellsType
     * @return
     */
    public static final  String getCellsTypeLeft(String cellsType){
        return  cellsType.substring(0,cellsType.lastIndexOf("_"));
    }
    public static final  String getMonth(LocalDate date){
        String result="";
        int year= date.getYear();
        int month=date.getMonthValue();
        if (month<10){
            return year+"0"+month;
        }else
        {
            return  year+""+month;
        }

    }
    public  static final LocalDateTime getLocalDateTimeAdd(LocalDate localDate,int hour,int minute,int second){
        LocalTime localTime=LocalTime.of(hour,minute,second);
        LocalDateTime localDateTime=LocalDateTime.of(localDate,localTime);
        return localDateTime;
    }
    public  static final LocalDateTime getLocalDateTimeAdd(LocalDate localDate,int day,int hour,int minute,int second){
        LocalTime localTime=LocalTime.of(hour,minute,second);
        localDate= localDate.plusDays(day);
        LocalDateTime localDateTime=LocalDateTime.of(localDate,localTime);
        return localDateTime;
    }
    /**
     * 去掉百分号
     * @param value
     * @return
     */
    public static final BigDecimal removePercentage(String value ){
        if (StringUtils.isNotEmpty(value)){
            return  new BigDecimal(value.substring(0,value.indexOf("%"))).divide(new BigDecimal(100),6,RoundingMode.HALF_UP);
        }
        return null;

    }
    /**
     * 直接去掉百分号
     * @param value
     * @return
     */
    public static final BigDecimal removePercentageIgnore(String value ){
        if (StringUtils.isNotEmpty(value)){
            return  new BigDecimal(value.substring(0,value.indexOf("%")));
        }
        return null;

    }
    public static final BigDecimal removePercentage(String value ,int scale){
        if (StringUtils.isNotEmpty(value)){
            return  new BigDecimal(value.substring(0,value.indexOf("%"))).divide(new BigDecimal(100),scale,RoundingMode.HALF_UP);
        }
        return null;

    }
    /**
     * 添加百分号
     * @param value
     * @return
     */
    public static final String addPercentage(BigDecimal value ){
        if (value==null) return  null;
       return    value.multiply(new BigDecimal("100")).toString()+"%";


    }
    /**
     * 添加百分号
     * @param value
     * @return
     */
    public static final String addPercentage(BigDecimal value,int scale ){
        if (value==null) return  null;
        return    value.multiply(new BigDecimal("100")).setScale(scale,RoundingMode.HALF_UP).toString()+"%";


    }
    public static final String addPercentageIgnoreZero(BigDecimal value, int scale) {
        if (value == null) return null;
        // 添加百分比并四舍五入
        BigDecimal result = value.multiply(new BigDecimal("100")).setScale(scale, RoundingMode.HALF_UP);
        // 去除尾部的零
        return result.stripTrailingZeros().toPlainString()+"%";

    }

    /**
     * yes -》 1 no->0
     * @param value
     * @return
     */
    public static   Integer changeYesNoToInt(String value){
        if (StringUtils.isEmpty(value)) {
            return 0;
        }else {
            return value.equalsIgnoreCase("yes")?1:0;
        }
    }
    /**
     * 1 -yes  0->no
     * @param value
     * @return
     */
    public  static String changeIntToYesNo(Integer value){
        if (value==null) {
            return "no";
        }else {
            if (value.equals(1)){
                return "yes";
            }else{
                return "no";
            }

        }
    }



    /**
     * 构建电池类型到电池型号的map集合
     * @return
     */
    public static Map<String,String> getMapCelltypeToCellModel(){
        // 准备电池类型--》电池型号的map集合开始
        //电池类型
        Map<String, LovLineDTO> allBatteryType = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BATTERY_TYPE);
        //电池型号Lov
        Map<String, LovLineDTO> allAopCellSeries = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AOP_CELL_SERIES);
        //构建电池类型对应的型号map
        //电池类型名--》型号
        Map<String,String> mapTypeToSeries=new HashMap<>();
        for (LovLineDTO dto:allBatteryType.values()
        ) {
            if (!mapTypeToSeries.containsKey(dto.getLovName())){
                String typeName=dto.getLovName();
                if (StringUtils.isNotEmpty(dto.getAttribute7())){
                    String series=allAopCellSeries.get(dto.getAttribute7()).getLovValue();
                    mapTypeToSeries.put(typeName,series);
                }

            }
        }
        // 准备电池类型--》电池型号的map集合结束
        return mapTypeToSeries;
    }
    /**
     * 构建电池类型到电池型号的map集合
     * @return
     */
    public static Map<String,LovLineDTO> getMapCelltypeToCellModelLov(){
        // 准备电池类型--》电池型号的map集合开始
        //电池类型
        Map<String, LovLineDTO> allBatteryType = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BATTERY_TYPE);
        //电池型号Lov
        Map<String, LovLineDTO> allAopCellSeries = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AOP_CELL_SERIES);
        //构建电池类型对应的型号map
        //电池类型名--》型号
        Map<String,LovLineDTO> mapTypeToSeries=new HashMap<>();
        for (LovLineDTO dto:allBatteryType.values()
        ) {
            if (!mapTypeToSeries.containsKey(dto.getLovName())){
                String typeName=dto.getLovName();
                if (StringUtils.isNotEmpty(dto.getAttribute7())){
                    LovLineDTO series=allAopCellSeries.get(dto.getAttribute7());
                    mapTypeToSeries.put(typeName,series);
                }

            }
        }
        // 准备电池类型--》电池型号的map集合结束
        return mapTypeToSeries;
    }
    /**
     * 根据value获取lv的name
     * @param headcode
     * @param value
     * @return
     */
    public static String getNameByValue(String headcode,String value){
        String name=null;
        if (StringUtils.isNotEmpty(value)){
            LovLineDTO lovLineDTO = LovUtils.get(headcode, value);
            if (lovLineDTO!=null){
                name=lovLineDTO.getLovName();
            }
        }
        return name;
    }
    public static String getValueByName(String headcode,String name){
        String value=null;
        if (StringUtils.isNotEmpty(name)){
            LovLineDTO lovLineDTO = LovUtils.getByName(headcode,name);
            if (lovLineDTO!=null){
                value=lovLineDTO.getLovValue();
            }
        }
        return value;
    }

    /**
     * 根据中文名得到value
     * @param headcode
     * @param name
     * @return
     */
    public static String getValueByCnName(String headcode,String name){
        String value=null;
       String lang= MyThreadLocal.get().getLang();
        if (StringUtils.isNotEmpty(name)){
            MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
            LovLineDTO lovLineDTO = LovUtils.getByName(headcode,name );
            if (lovLineDTO!=null){
                value=lovLineDTO.getLovValue();
            }
        }
        MyThreadLocal.get().setLang(lang);
        return value;
    }

    /**
     * 根据中文名获取Id
     * @param headcode
     * @param name
     * @return
     */
    public static  Long getIdByCNName(String headcode, String name){
       String lang=  MyThreadLocal.get().getLang();
        Long id=null;
        if (StringUtils.isNotEmpty(name)){
            MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
            LovLineDTO lovLineDTO = LovUtils.getByName(headcode,name );
            if (lovLineDTO!=null){
                id=lovLineDTO.getLovLineId();
            }
        }
        MyThreadLocal.get().setLang(lang);
        return id;
    }
    /**
     * 根据名获取Id
     * @param headcode
     * @param name
     * @return
     */
    public static  Long getIdByName(String headcode, String name){
        Long id=null;
        if (StringUtils.isNotEmpty(name)){
            LovLineDTO lovLineDTO = LovUtils.getByName(headcode,name );
            if (lovLineDTO!=null){
                id=lovLineDTO.getLovLineId();
            }
        }
        return id;
    }
    /**
     * 根据value获取lv的中文name
     * @param headcode
     * @param value
     * @return
     */
    public static String getCNNameByValue(String headcode,String value){
        String name=null;
        if (StringUtils.isNotEmpty(value)){
            LovLineDTO lovLineDTO = LovUtils.getByLang(headcode,value,LovHeaderCodeConstant.LANGUAGE_CN);
            if (lovLineDTO!=null){
                name=lovLineDTO.getLovName();

            }
        }
        return name;
    }

    /**
     * 根据Id获得中文名
     * @param headcode
     * @param id
     * @return
     */
    public static String getCNNameById(String headcode,Long id){
        String name=null;
        if (id!=null){
            LovLineDTO lovLineDTO = LovUtils.getByLang(headcode,id,LovHeaderCodeConstant.LANGUAGE_CN);
            if (lovLineDTO!=null){
                name=lovLineDTO.getLovName();
            }
        }
        return name;
    }
    /**
     * 根据特定语言的name转中文名
     * @param headerCode
     * @param name
     * @param language
     * @return
     */
    public static String getCNNameByNameLang(String headerCode,String name,String language){
        if (LovHeaderCodeConstant.LANGUAGE_CN.equals(language)){
            return name;
        }
        String cnName=null;
        String oldLang=MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(language);
       LovLineDTO lovLineDTO=  LovUtils.getByName(headerCode,name );
       if (lovLineDTO!=null){
          Long lovLineId=lovLineDTO.getLovLineId();

                lovLineDTO=  LovUtils.getByLang(headerCode,lovLineId,LovHeaderCodeConstant.LANGUAGE_CN);
              if (lovLineDTO!=null){
                  cnName=lovLineDTO.getLovName();
              }

       }
       MyThreadLocal.get().setLang(oldLang);
        return  Optional.ofNullable(cnName).orElse(name);

    }
    /**
     * 根据中文名转成特定名
     * @param headerCode
     * @param cNname
     * @return
     */
    public static String getNameByCnNameLang(String headerCode,String cNname){
        String language= MyThreadLocal.get().getLang();
        if (LovHeaderCodeConstant.LANGUAGE_CN.equals(language)){
            return cNname;
        }
        String name=null;
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        LovLineDTO lovLineDTO=  LovUtils.getByName(headerCode,cNname);
        if (lovLineDTO!=null){
            Long lovLineId=lovLineDTO.getLovLineId();

            lovLineDTO=  LovUtils.getByLang(headerCode,lovLineId,language);
            if (lovLineDTO!=null){
                name=lovLineDTO.getLovName();
            }

        }
        MyThreadLocal.get().setLang(language);
        return  Optional.ofNullable(name).orElse(cNname);

    }

    public static void main(String[] args) {
        BigDecimal bd = new BigDecimal("12300");
        String strippedString = bd.stripTrailingZeros().toPlainString();
        System.out.println(strippedString);  // 输出 "123"
    }
}
