package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 投产计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellPlanLineVersionExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 排产月份
     */
    @ExcelProperty(value = "排产月份")
    private String month;
    /**
     * 最終确认发布的版本
     */
    @ExcelProperty(value = "最終确认发布的版本")
    private String finalVersion;
    /**
     * 排产版本
     */
    @ExcelProperty(value = "排产版本")
    private String version;
    /**
     * 是否进行了投产提前期转化过
     */
    @ApiModelProperty(value = "是否进行了投产提前期转化过")
    private Integer isTransform;
    /**
     * 是否进行了硅料厂家拆分
     */
    @ExcelProperty(value = "是否进行了硅料厂家拆分")
    private Integer isSiMfrs;
    /**
     * 是否已经进行硅片等级拆分
     */
    @ExcelProperty(value = "是否已经进行硅片等级拆分")
    private Integer isWaferGrade;
    /**
     * 是否已经进行了加工类型拆分
     */
    @ExcelProperty(value = "是否已经进行了加工类型拆分")
    private Integer isProcessCategory;
    /**
     * 是否进行了计划确认
     */
    @ExcelProperty(value = "是否进行了计划确认")
    private Integer isConfirmPlan;
    /**
     * 是否创建了入库计划
     */
    @ExcelProperty(value = "是否创建了入库计划")
    private Integer isCreateInstockPlan;
    /**
     * 是否发送了邮件
     */
    @ExcelProperty(value = "是否发送了邮件")
    private Integer isSendEmail;
    /**
     * 哪个月排的
     */
    @ExcelProperty(value = "哪个月排的")
    private String scheduleMonth;
}
