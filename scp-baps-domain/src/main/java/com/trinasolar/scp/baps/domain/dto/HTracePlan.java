package com.trinasolar.scp.baps.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: HTracePlan
 * @date 2024/6/7 13:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HTracePlan {

    /**
     * 生产基地
     */
    private String basePlace;
    /**
     * 生产车间
     */
    private String workshop;

    /**
     * 共用车间
     */
    private String shareWorkshop;

    /**
     * 品类
     */
    private String productCategory;
    /**
     * 晶体类型
     */
    private String crystalType;
    /**
     * H追溯
     */
    private String hTrace;
    /**
     * 月份
     */
    private String month;
    /**
     * 月份
     */
    private String oldMonth;
    /**
     * 天
     */
    private Integer day;

    /**
     * 投产计划日期
     */
    private LocalDate planDate;

    /**
     * 排产数量
     */
    private BigDecimal quantity;

    /**
     * 安全库存数量
     */
    private BigDecimal safetyStockQuantity;

    public HTracePlan(String basePlace, String workshop, String productCategory, String crystalType, String hTrace, String month, String oldMonth, Integer day, LocalDate planDate) {
        this.basePlace = basePlace;
        this.workshop = workshop;
        this.productCategory = productCategory;
        this.crystalType = crystalType;
        this.hTrace = hTrace;
        this.month = month;
        this.oldMonth = oldMonth;
        this.day = day;
        this.planDate = planDate;
    }

    public static HTracePlan build(CellPlanLineDTO cellPlanLine) {
        return new HTracePlan(cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getProductCategory(), cellPlanLine.getCrystalType(), cellPlanLine.getHTrace(), cellPlanLine.getMonth(), cellPlanLine.getOldMonth(), cellPlanLine.getStartTime().getDayOfMonth(), cellPlanLine.getStartTime().toLocalDate());
    }

    public String sign() {
        return StringUtils.join(this.basePlace, this.workshop, this.productCategory, this.crystalType, this.hTrace);
    }

    public String dailySign() {
        return StringUtils.join(this.basePlace, this.productCategory, this.crystalType, this.planDate);
    }
}
