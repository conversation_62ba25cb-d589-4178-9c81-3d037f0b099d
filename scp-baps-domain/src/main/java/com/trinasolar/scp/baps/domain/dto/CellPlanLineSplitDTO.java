package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.baps.domain.enums.SelectedEnum;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 投产计划 拆分列表对象
 *
 * <AUTHOR>
 * @email
 * @date 2025-03-27
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "投产计划表拆分DTO对象", description = "DTO对象")
@ToString
public class CellPlanLineSplitDTO {
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;

    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    private Long cellSourceId;

    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    private String supplyMethod;

    /**
     * 供应方式Id
     */
    @ApiModelProperty(value = "供应方式Id")
    private Long supplyMethodId;

    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 拆分开始日期
     */
    @ApiModelProperty(value ="拆分开始日期")
    private LocalDate splitStartDate;

    /**
     * 拆分结束日期
     */
    @ApiModelProperty(value ="拆分结束日期")
    private LocalDate splitEndDate;

    @ApiModelProperty(value ="加工类型")
    private String processCategory;

    @ApiModelProperty(value ="证书编号")
    private String certCode;

    // 供校验是否在有效时间范围的提示行用
    @ApiModelProperty(value ="序列号")
    private String orderNo;

    /**
     * 是否选中(0：未选中，1：已选中)
     */
    @ApiModelProperty(value ="是否选中(NO：未选中，YES：已选中)")
    private SelectedEnum isSelected;

    // 按生产车间，电池类型，H追溯，供应方式，片源种类，法碳证书的维度汇总投产计划数据，给拆分页面显示
    public String splitGroup() {
        return StringTools.joinWith(",",this.getWorkshop(), this.getCellsType(), this.getHTrace(),
                this.getSupplyMethod(), this.getCellSource(), this.getCertCode());
    }
}
