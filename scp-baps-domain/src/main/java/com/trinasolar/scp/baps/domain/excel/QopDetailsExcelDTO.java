package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;


/**
 * SOP数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-30 03:38:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QopDetailsExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外名
     */
    @ExcelProperty(value = "国内海外名")
    private String isOverseaName;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    private Long workshopId;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String workshopName;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal sopQty;
    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String version;
}
