package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import com.trinasolar.scp.common.api.util.DataColumn;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.MapUtils;

import javax.persistence.Column;
import java.lang.reflect.Field;
import java.math.BigDecimal;

import java.util.List;
import java.util.TreeMap;


/**
 * 入库计划汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellPlanLineTotalExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;

    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    @ExcelIgnore
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;

    @ExcelProperty(value = "需求来源")
    private String sourceType;

    @ExcelProperty(value = "更改状态")
    private String changeStatusDesc;

    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    @ExcelIgnore
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    @ExcelIgnore
    private Long workshopId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;

    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    @ExcelIgnore
    private Long cellsTypeId;
    /**
     * 5A料号
     */
    @ExcelProperty(value = "5A料号")
    // @ExcelIgnore
    private String itemCode;
    /**
     * 背面细栅
     */
    @ExcelProperty(value = "背面细栅")
    private String backFineGrid;
    /**
     * 正面细栅
     */
    @ExcelProperty(value = "正面细栅")
    private String frontFineGrid;
    /**
     * 硅片厚度
     */
    @ExcelProperty(value = "硅片厚度")
    private String siliconWaferThickness;
    /**
     * 硅片尺寸
     */
    @ExcelProperty(value = "硅片尺寸")
    private String siliconWaferSize;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;

    /**
     * 供应方式
     */
    @ExcelProperty(value = "供应方式")
    private String supplyMethod;

    /**
     * 是否H兼容
     */
    @ExcelProperty(value = "是否H兼容")
    private String   hChangeFlag;

    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 小区域国家
     */
    @ExcelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    @ExcelIgnore
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;


    /**
     * 产品等级
     */
    @ExcelProperty(value = "产品等级")
    @ExcelIgnore
    private String productionGrade;

    /**
     * MV
     */
    @ExcelProperty(value = "MW")
    private BigDecimal cellMv;
    /**
     * 万片数
     */
    @ExcelProperty(value = "万片")
    private BigDecimal qtyThousandPc;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;

    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    @ExcelIgnore
    private String version;
    /**
     * 对应入库计划的版本
     */
    @ExcelProperty(value = "对应入库计划的版本")
    @ExcelIgnore
    private String fromVersion;

    @ExcelProperty(value = "料号描述")
    private String itemDesc;
    /**
     * 生命周期状态
     */
    @ExcelProperty(value = "生命周期状态")
    private String lifecycleState;

    @ExcelProperty(value = "临时量产标识")
    private String isTemporaryOutput;

    @ApiModelProperty(value = "color")
    private String color;

    @ExcelProperty(value = "证书编号")
    private String certCode;

    @ExcelProperty(value = "配比")
    private String ratioCode;

//    /**
//     * d1
//     */
//    @ExcelProperty(value = "1")
//    private BigDecimal d1;
//    /**
//     * d2
//     */
//    @ExcelProperty(value = "2")
//    private BigDecimal d2;
//    /**
//     * d3
//     */
//    @ExcelProperty(value = "3")
//    private BigDecimal d3;
//    /**
//     * d4
//     */
//    @ExcelProperty(value = "4")
//    private BigDecimal d4;
//    /**
//     * d5
//     */
//    @ExcelProperty(value = "5")
//    private BigDecimal d5;
//    /**
//     * d6
//     */
//    @ExcelProperty(value = "6")
//    private BigDecimal d6;
//    /**
//     * d7
//     */
//    @ExcelProperty(value = "7")
//    private BigDecimal d7;
//    /**
//     * d8
//     */
//    @ExcelProperty(value = "8")
//    private BigDecimal d8;
//    /**
//     * d9
//     */
//    @ExcelProperty(value = "9")
//    private BigDecimal d9;
//    /**
//     * d10
//     */
//    @ExcelProperty(value = "10")
//    private BigDecimal d10;
//    /**
//     * d11
//     */
//    @ExcelProperty(value = "11")
//    private BigDecimal d11;
//    /**
//     * d12
//     */
//    @ExcelProperty(value = "12")
//    private BigDecimal d12;
//    /**
//     * d13
//     */
//    @ExcelProperty(value = "13")
//    private BigDecimal d13;
//    /**
//     * d14
//     */
//    @ExcelProperty(value = "14")
//    private BigDecimal d14;
//    /**
//     * d15
//     */
//    @ExcelProperty(value = "15")
//    private BigDecimal d15;
//    /**
//     * d16
//     */
//    @ExcelProperty(value = "16")
//    private BigDecimal d16;
//    /**
//     * d17
//     */
//    @ExcelProperty(value = "17")
//    private BigDecimal d17;
//    /**
//     * d18
//     */
//    @ExcelProperty(value = "18")
//    private BigDecimal d18;
//    /**
//     * d19
//     */
//    @ExcelProperty(value = "19")
//    private BigDecimal d19;
//    /**
//     * d20
//     */
//    @ExcelProperty(value = "20")
//    private BigDecimal d20;
//    /**
//     * d21
//     */
//    @ExcelProperty(value = "21")
//    private BigDecimal d21;
//    /**
//     * d22
//     */
//    @ExcelProperty(value = "22")
//    private BigDecimal d22;
//    /**
//     * d23
//     */
//    @ExcelProperty(value = "23")
//    private BigDecimal d23;
//    /**
//     * d24
//     */
//    @ExcelProperty(value = "24")
//    private BigDecimal d24;
//    /**
//     * d25
//     */
//    @ExcelProperty(value = "25")
//    private BigDecimal d25;
//    /**
//     * d26
//     */
//    @ExcelProperty(value = "26")
//    private BigDecimal d26;
//    /**
//     * d27
//     */
//    @ExcelProperty(value = "27")
//    private BigDecimal d27;
//    /**
//     * d28
//     */
//    @ExcelProperty(value = "28")
//    private BigDecimal d28;
//    /**
//     * d29
//     */
//    @ExcelProperty(value = "29")
//    private BigDecimal d29;
//    /**
//     * d30
//     */
//    @ExcelProperty(value = "30")
//    private BigDecimal d30;
//    /**
//     * d31
//     */
//    @ExcelProperty(value = "31")
//    private BigDecimal d31;

    /**
     * dataStructures
     */
    @ExcelProperty(value = "dataStructuresMap")
    private TreeMap<String, BigDecimal> dataStructuresMap;

    public static ExcelPara buildExcelPara(TreeMap<String, BigDecimal> dataStructures) {
        List<DataColumn> columns = Lists.newLinkedList();
        Field[] fields = CellInstockPlanTotalExcelDTO.class.getDeclaredFields();
        final int[] index = {0};
        for (Field field : fields) {
            ExcelIgnore excelIgnore = field.getAnnotation(ExcelIgnore.class);
            if (excelIgnore != null) {
                continue;
            }
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                DataColumn reflectDataColumn = new DataColumn();
                reflectDataColumn.setIndex(index[0]);
                reflectDataColumn.setName(field.getName());
                reflectDataColumn.setTitle(excelProperty.value()[0]);
                reflectDataColumn.setWidth(400);
                columns.add(reflectDataColumn);
            }
            index[0]++;
        }
        if (!MapUtils.isEmpty(dataStructures)) {
            dataStructures.forEach((k, v) -> {
                DataColumn dataColumn = new DataColumn();
                dataColumn.setIndex(index[0]);
                dataColumn.setName(k);
                dataColumn.setTitle(k);
                dataColumn.setWidth(400);
                columns.add(dataColumn);
                index[0]++;
            });
        }
        ExcelPara excelPara = new ExcelPara();
        excelPara.setColumns(columns);
        return excelPara;
    }
}
