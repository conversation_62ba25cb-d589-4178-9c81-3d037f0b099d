package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 制造BOM表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellBomManufacturing保存参数", description = "保存参数")
public class CellBomManufacturingSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 产线总数
     */
    @ApiModelProperty(value = "产线总数")
    private BigDecimal totalLine;
    /**
     * 可用产线数
     */
    @ApiModelProperty(value = "可用产线数")
    private BigDecimal usageLine;
    /**
     * 产能（单元）
     */
    @ApiModelProperty(value = "产能（单元）")
    private BigDecimal capacityQuantity;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 能否生产单玻
     */
    @ApiModelProperty(value = "能否生产单玻")
    private String isSingleGlass;
    /**
     * 能否生产低碳
     */
    @ApiModelProperty(value = "能否生产低碳")
    private String isDt;
    /**
     * 能否生产小区域国家
     */
    @ApiModelProperty(value = "能否生产小区域国家")
    private String isRegionalCountry;
    /**
     * 能否生产H兼容
     */
    @ApiModelProperty(value = "能否生产H兼容")
    private String isHChangeFlag;
    /**
     * 能否生产H追溯
     */
    @ApiModelProperty(value = "能否生产H追溯")
    private String isHTrace;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 工序代码
     */
    @ApiModelProperty(value = "工序代码")
    private String processCode;
    /**
     * 工序编号
     */
    @ApiModelProperty(value = "工序编号")
    private Integer processId;
    /**
     * 使用类型
     */
    @ApiModelProperty(value = "使用类型")
    private String instructionType;
    /**
     * 使用代码
     */
    @ApiModelProperty(value = "使用代码")
    private String instructionCode;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;
    /**
     * 国内国外
     */
    @ApiModelProperty(value = "国内国外")
    private String isOversea;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 制造
     */
    @ApiModelProperty(value = "制造")
    private String manufacturing;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer rate;
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private LocalDateTime startDate;
    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private LocalDateTime endDate;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    private Integer flag;
    /**
     * 符合率
     */
    @ApiModelProperty(value = "符合率")
    private BigDecimal finePercent;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String gradeWorkDate;
    /**
     * 可靠性验证
     */
    @ApiModelProperty(value = "可靠性验证")
    private String reliabilityCheck;
}
