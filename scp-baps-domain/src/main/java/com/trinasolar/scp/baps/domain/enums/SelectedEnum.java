package com.trinasolar.scp.baps.domain.enums;

import cn.hutool.core.util.EnumUtil;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.Optional;

public enum SelectedEnum {
    @ApiModelProperty("已选中")
    YES(1, "已选中"),
    @ApiModelProperty("未选中")
    NO(0, "未选中");

    private Integer code;
    private String desc;

    public static String getDesc(String code) {
        return (String) Optional.ofNullable(code).map((c) -> (SelectedEnum) EnumUtil.likeValueOf(SelectedEnum.class, c)).map(SelectedEnum::getDesc).orElse(String.valueOf((Object)null));
    }

    public static Integer getCode(String desc) {
        return (Integer) Arrays.stream(values()).filter((e) -> e.getDesc().equals(desc)).findFirst().map(SelectedEnum::getCode).orElse((Integer) null);
    }

    private SelectedEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
