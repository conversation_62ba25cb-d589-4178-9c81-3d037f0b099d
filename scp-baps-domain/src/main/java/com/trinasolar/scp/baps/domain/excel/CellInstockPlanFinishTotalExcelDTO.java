package com.trinasolar.scp.baps.domain.excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
@Data
@EqualsAndHashCode(callSuper = false)
public class CellInstockPlanFinishTotalExcelDTO {
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 是否小区域国家
     */
    @ExcelProperty(value = "是否小区域国家")
    private String isRegionalCountry;
    /**
     * 产品等级
     */
    @ExcelProperty(value = "产品等级")
    private String productionGrade;

    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 类别
     */
    @ExcelProperty(value = "类别")
    private String dataType;
    /**
     * 万片
     */
    @ExcelProperty(value = "万片")
    @ExcelIgnore
    private BigDecimal qtyThousandPc;
    /**
     * d1
     */
    @ExcelProperty(value = "d1")
    private BigDecimal d1;
    /**
     * d2
     */
    @ExcelProperty(value = "d2")
    private BigDecimal d2;
    /**
     * d3
     */
    @ExcelProperty(value = "d3")
    private BigDecimal d3;
    /**
     * d4
     */
    @ExcelProperty(value = "d4")
    private BigDecimal d4;
    /**
     * d5
     */
    @ExcelProperty(value = "d5")
    private BigDecimal d5;
    /**
     * d6
     */
    @ExcelProperty(value = "d6")
    private BigDecimal d6;
    /**
     * d7
     */
    @ExcelProperty(value = "d7")
    private BigDecimal d7;
    /**
     * d8
     */
    @ExcelProperty(value = "d8")
    private BigDecimal d8;
    /**
     * d9
     */
    @ExcelProperty(value = "d9")
    private BigDecimal d9;
    /**
     * d10
     */
    @ExcelProperty(value = "d10")
    private BigDecimal d10;
    /**
     * d11
     */
    @ExcelProperty(value = "d11")
    private BigDecimal d11;
    /**
     * d12
     */
    @ExcelProperty(value = "d12")
    private BigDecimal d12;
    /**
     * d13
     */
    @ExcelProperty(value = "d13")
    private BigDecimal d13;
    /**
     * d14
     */
    @ExcelProperty(value = "d14")
    private BigDecimal d14;
    /**
     * d15
     */
    @ExcelProperty(value = "d15")
    private BigDecimal d15;
    /**
     * d16
     */
    @ExcelProperty(value = "d16")
    private BigDecimal d16;
    /**
     * d17
     */
    @ExcelProperty(value = "d17")
    private BigDecimal d17;
    /**
     * d18
     */
    @ExcelProperty(value = "d18")
    private BigDecimal d18;
    /**
     * d19
     */
    @ExcelProperty(value = "d19")
    private BigDecimal d19;
    /**
     * d20
     */
    @ExcelProperty(value = "d20")
    private BigDecimal d20;
    /**
     * d21
     */
    @ExcelProperty(value = "d21")
    private BigDecimal d21;
    /**
     * d22
     */
    @ExcelProperty(value = "d22")
    private BigDecimal d22;
    /**
     * d23
     */
    @ExcelProperty(value = "d23")
    private BigDecimal d23;
    /**
     * d24
     */
    @ExcelProperty(value = "d24")
    private BigDecimal d24;
    /**
     * d25
     */
    @ExcelProperty(value = "d25")
    private BigDecimal d25;
    /**
     * d26
     */
    @ExcelProperty(value = "d26")
    private BigDecimal d26;
    /**
     * d27
     */
    @ExcelProperty(value = "d27")
    private BigDecimal d27;
    /**
     * d28
     */
    @ExcelProperty(value = "d28")
    private BigDecimal d28;
    /**
     * d29
     */
    @ExcelProperty(value = "d29")
    private BigDecimal d29;
    /**
     * d30
     */
    @ExcelProperty(value = "d30")
    private BigDecimal d30;
    /**
     * d31
     */
    @ExcelProperty(value = "d31")
    private BigDecimal d31;
}
