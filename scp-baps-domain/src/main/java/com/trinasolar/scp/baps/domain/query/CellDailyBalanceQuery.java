package com.trinasolar.scp.baps.domain.query;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 每日结存报表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-12 06:19:24
 */
@Data
@ApiModel(value = "CellDailyBalance查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellDailyBalanceQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 国内外Id
         */
        @ApiModelProperty(value = "国内外Id")
    private Long isOverseaId;
        /**
         * 国内外
         */
        @ApiModelProperty(value = "国内外")
    private String isOversea;
        /**
         * 电池类型Id
         */
        @ApiModelProperty(value = "电池类型Id")
    private Long cellTypeId;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellsType;
        /**
         * 月份
         */
        @ApiModelProperty(value = "月份")
    private String month;
        /**
         * 类别
         */
        @ApiModelProperty(value = "类别")
    private String project;
        /**
         * H追溯
         */
        @ApiModelProperty(value = "H追溯")
    private String hTrace;
        /**
         * 低碳
         */
        @ApiModelProperty(value = "低碳")
    private String dt;
        /**
         * 美学
         */
        @ApiModelProperty(value = "美学")
    private String aesthetics;
        /**
         * 透明双玻
         */
        @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 数据来源版本
     */
    @ApiModelProperty(value = "数据来源版本")
    private String fromVersion;
        /**
         * 1号
         */
        @ApiModelProperty(value = "1号")
    private BigDecimal d1;
        /**
         * 2号
         */
        @ApiModelProperty(value = "2号")
    private BigDecimal d2;
        /**
         * 3号
         */
        @ApiModelProperty(value = "3号")
    private BigDecimal d3;
        /**
         * 4号
         */
        @ApiModelProperty(value = "4号")
    private BigDecimal d4;
        /**
         * 5号
         */
        @ApiModelProperty(value = "5号")
    private BigDecimal d5;
        /**
         * 6号
         */
        @ApiModelProperty(value = "6号")
    private BigDecimal d6;
        /**
         * 7号
         */
        @ApiModelProperty(value = "7号")
    private BigDecimal d7;
        /**
         * 8号
         */
        @ApiModelProperty(value = "8号")
    private BigDecimal d8;
        /**
         * 9号
         */
        @ApiModelProperty(value = "9号")
    private BigDecimal d9;
        /**
         * 10号
         */
        @ApiModelProperty(value = "10号")
    private BigDecimal d10;
        /**
         * 11号
         */
        @ApiModelProperty(value = "11号")
    private BigDecimal d11;
        /**
         * 12号
         */
        @ApiModelProperty(value = "12号")
    private BigDecimal d12;
        /**
         * 13号
         */
        @ApiModelProperty(value = "13号")
    private BigDecimal d13;
        /**
         * 14号
         */
        @ApiModelProperty(value = "14号")
    private BigDecimal d14;
        /**
         * 15号
         */
        @ApiModelProperty(value = "15号")
    private BigDecimal d15;
        /**
         * 16号
         */
        @ApiModelProperty(value = "16号")
    private BigDecimal d16;
        /**
         * 17号
         */
        @ApiModelProperty(value = "17号")
    private BigDecimal d17;
        /**
         * 18号
         */
        @ApiModelProperty(value = "18号")
    private BigDecimal d18;
        /**
         * 19号
         */
        @ApiModelProperty(value = "19号")
    private BigDecimal d19;
        /**
         * 20号
         */
        @ApiModelProperty(value = "20号")
    private BigDecimal d20;
        /**
         * 21号
         */
        @ApiModelProperty(value = "21号")
    private BigDecimal d21;
        /**
         * 22号
         */
        @ApiModelProperty(value = "22号")
    private BigDecimal d22;
        /**
         * 23号
         */
        @ApiModelProperty(value = "23号")
    private BigDecimal d23;
        /**
         * 24号
         */
        @ApiModelProperty(value = "24号")
    private BigDecimal d24;
        /**
         * 25号
         */
        @ApiModelProperty(value = "25号")
    private BigDecimal d25;
        /**
         * 26号
         */
        @ApiModelProperty(value = "26号")
    private BigDecimal d26;
        /**
         * 27号
         */
        @ApiModelProperty(value = "27号")
    private BigDecimal d27;
        /**
         * 28号
         */
        @ApiModelProperty(value = "28号")
    private BigDecimal d28;
        /**
         * 29号
         */
        @ApiModelProperty(value = "29号")
    private BigDecimal d29;
        /**
         * 30号
         */
        @ApiModelProperty(value = "30号")
    private BigDecimal d30;
        /**
         * 31号
         */
        @ApiModelProperty(value = "31号")
    private BigDecimal d31;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
