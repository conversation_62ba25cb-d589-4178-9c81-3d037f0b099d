package com.trinasolar.scp.baps.domain.dto;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.scp.baps.domain.utils.excel.ExcelHeader;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * 投产计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "入库计划表DTO对象", description = "DTO对象")
@ToString
public class HTraceLineDTO {


    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 是否电池特殊要求
     */
    @ApiModelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 计划版本
     */
    @ApiModelProperty(value = "计划版本")
    private String version;
    /**
     * 实际月份
     */
    @ApiModelProperty(value = "实际月份")
    private String month;
    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    private String oldMonth;

    /**
     * 是否H兼容
     */
    @ApiModelProperty(value = "是否H兼容")
    private String hChangeFlag;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String productCategory;
    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    private String crystalType;
    /**
     * 动态头
     */
    @ApiModelProperty(value = "动态头")
    private List<String> subList;
    /**
     * 动态值
     */
    @ApiModelProperty(value = "动态值")
    private Map<String, BigDecimal> subMap;

    public Map<String, Object> convertMap() {
        Map<String, Object> objectMap = BeanUtil.beanToMap(this);
        if (MapUtils.isNotEmpty(this.subMap)) {
            objectMap.putAll(this.subMap);
        }
        return objectMap;
    }


    public static HTraceLineDTO groupBy(CellPlanLineDTO line) {
        HTraceLineDTO hTraceLine = new HTraceLineDTO();
        BeanUtils.copyProperties(line, hTraceLine,  "productCategory", "crystalType");
        return hTraceLine;
    }
}
