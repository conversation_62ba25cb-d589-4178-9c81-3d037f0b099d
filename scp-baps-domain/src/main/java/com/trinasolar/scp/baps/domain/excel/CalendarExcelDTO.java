package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * 生产日历
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalendarExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 日历类型
     */
    @ExcelProperty(value = "日历类型")
    private String type;
    /**
     * 资源单元Id
     */
    @ExcelProperty(value = "资源单元Id")
    private Long workunitId;
    /**
     * 资源单元
     */
    @ExcelProperty(value = "资源单元")
    private String workunit;
    /**
     * 生产线体
     */
    @ExcelProperty(value = "生产线体")
    private String lineName;
    /**
     * 日期
     */
    @ExcelProperty(value = "日期")
    private LocalDate date;
    /**
     * 出勤代码
     */
    @ExcelProperty(value = "出勤代码")
    private String shiftcode;
    /**
     * 资源量
     */
    @ExcelProperty(value = "资源量")
    private BigDecimal defaultqty;
    /**
     * 优先级
     */
    @ExcelProperty(value = "优先级")
    private Integer sortorder;
    /**
     * 标准产线数
     */
    @ExcelProperty(value = "标准产线数")
    private BigDecimal numLines;
}
