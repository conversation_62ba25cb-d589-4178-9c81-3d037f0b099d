package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;


/**
 * 爬坡产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellGradeCapacityExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 国内/海外
     */
    @ExcelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 能否生产单玻
     */
    @ExcelProperty(value = "能否生产单玻")
    private String isSingleGlass;
    /**
     * 能否生产低碳
     */
    @ExcelProperty(value = "能否生产低碳")
    private String isDt;
    /**
     * 能否生产小区域国家
     */
    @ExcelProperty(value = "能否生产小区域国家")
    private String isRegionalCountry;
    /**
     * 能否生产H兼容
     */
    @ExcelProperty(value = "能否生产H兼容")
    private String isHChangeFlag;
    /**
     * 能否生产H追溯
     */
    @ExcelProperty(value = "能否生产H追溯")
    private String isHTrace;

    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产线体
     */
    @ExcelProperty(value = "生产线体")
    private String lineName;
    /**
     * 线体数量
     */
    @ExcelProperty(value = "线体数量")
    private  BigDecimal lineNumber;
    /**
     * 可靠性验证
     */
    @ExcelProperty(value = "是否可靠性验证(Y/N)")
    private String reliabilityCheck;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 产能
     */
    @ExcelProperty(value = "产能")
    @ExcelIgnore
    private BigDecimal capacityQuantity;
    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String unit;
    /**
     * 1号
     */
    @ExcelProperty(value = "1号")
    private BigDecimal d1;
    /**
     * 2号
     */
    @ExcelProperty(value = "2号")
    private BigDecimal d2;
    /**
     * 3号
     */
    @ExcelProperty(value = "3号")
    private BigDecimal d3;
    /**
     * 4号
     */
    @ExcelProperty(value = "4号")
    private BigDecimal d4;
    /**
     * 5号
     */
    @ExcelProperty(value = "5号")
    private BigDecimal d5;
    /**
     * 6号
     */
    @ExcelProperty(value = "6号")
    private BigDecimal d6;
    /**
     * 7号
     */
    @ExcelProperty(value = "7号")
    private BigDecimal d7;
    /**
     * 8号
     */
    @ExcelProperty(value = "8号")
    private BigDecimal d8;
    /**
     * 9号
     */
    @ExcelProperty(value = "9号")
    private BigDecimal d9;
    /**
     * 10号
     */
    @ExcelProperty(value = "10号")
    private BigDecimal d10;
    /**
     * 11号
     */
    @ExcelProperty(value = "11号")
    private BigDecimal d11;
    /**
     * 12号
     */
    @ExcelProperty(value = "12号")
    private BigDecimal d12;
    /**
     * 13号
     */
    @ExcelProperty(value = "13号")
    private BigDecimal d13;
    /**
     * 14号
     */
    @ExcelProperty(value = "14号")
    private BigDecimal d14;
    /**
     * 15号
     */
    @ExcelProperty(value = "15号")
    private BigDecimal d15;
    /**
     * 16号
     */
    @ExcelProperty(value = "16号")
    private BigDecimal d16;
    /**
     * 17号
     */
    @ExcelProperty(value = "17号")
    private BigDecimal d17;
    /**
     * 18号
     */
    @ExcelProperty(value = "18号")
    private BigDecimal d18;
    /**
     * 19号
     */
    @ExcelProperty(value = "19号")
    private BigDecimal d19;
    /**
     * 20号
     */
    @ExcelProperty(value = "20号")
    private BigDecimal d20;
    /**
     * 21号
     */
    @ExcelProperty(value = "21号")
    private BigDecimal d21;
    /**
     * 22号
     */
    @ExcelProperty(value = "22号")
    private BigDecimal d22;
    /**
     * 23号
     */
    @ExcelProperty(value = "23号")
    private BigDecimal d23;
    /**
     * 24号
     */
    @ExcelProperty(value = "24号")
    private BigDecimal d24;
    /**
     * 25号
     */
    @ExcelProperty(value = "25号")
    private BigDecimal d25;
    /**
     * 26号
     */
    @ExcelProperty(value = "26号")
    private BigDecimal d26;
    /**
     * 27号
     */
    @ExcelProperty(value = "27号")
    private BigDecimal d27;
    /**
     * 28号
     */
    @ExcelProperty(value = "28号")
    private BigDecimal d28;
    /**
     * 29号
     */
    @ExcelProperty(value = "29号")
    private BigDecimal d29;
    /**
     * 30号
     */
    @ExcelProperty(value = "30号")
    private BigDecimal d30;
    /**
     * 31号
     */
    @ExcelProperty(value = "31号")
    private BigDecimal d31;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

}
