package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellInstockPlanTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalDTO;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineTotal;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityQuery;
import com.trinasolar.scp.baps.domain.query.CellGradeCapacityQuery;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellVersionPlanIeQuery;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanIe;
import com.trinasolar.scp.baps.domain.dto.CellVersionPlanIeDTO;
import com.trinasolar.scp.baps.domain.excel.CellVersionPlanIeExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellVersionPlanIeSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 计划与ie产能对比 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellVersionPlanIeDEConvert extends BaseDEConvert<CellVersionPlanIeDTO, CellVersionPlanIe> {

    CellVersionPlanIeDEConvert INSTANCE = Mappers.getMapper(CellVersionPlanIeDEConvert.class);

    List<CellVersionPlanIeExcelDTO> toExcelDTO(List<CellVersionPlanIeDTO> dtos);

    CellVersionPlanIeExcelDTO toExcelDTO(CellVersionPlanIeDTO dto);
    CellVersionPlanIeExcelDTO toExcelDTOFromEntity(CellVersionPlanIe cellVersionPlanIe);
    List<CellVersionPlanIeExcelDTO> toExcelDTOFromEntity(List<CellVersionPlanIe> cellVersionPlanIes);
    @Mappings(
            {
                    @Mapping(target = "isOversea",source = "isOverseaName"),
                    @Mapping(target = "cellsType",source = "cellTypeName"),
                    @Mapping(target = "workshop",source = "workshopName")
            }
    )
    CellBaseCapacityQuery toCellBaseCapacityQuery(CellVersionPlanIeQuery query);
    @Mappings(
            {
                    @Mapping(target = "cellsType",source = "cellTypeName"),
                    @Mapping(target = "workshop",source = "workshopName")
            }
    )
    CellGradeCapacityQuery toCellGradeCapacityQuery(CellVersionPlanIeQuery query);
    @Mappings(
            {
                    @Mapping( source= "isOversea", target= "isOverseaName"),
                    @Mapping(  source= "cellsType", target = "cellTypeName"),
                    @Mapping(  source= "cellsTypeId", target = "cellTypeId"),
                    @Mapping(  source= "workshop", target = "workshopName")
            }
    )
    CellVersionPlanIe toCellVersionPlanIeFromCellPlanLineTotal(CellPlanLineTotalDTO total);
    @Mappings(
            {
                    @Mapping( source= "isOversea", target= "isOverseaName"),
                    @Mapping(  source= "cellsType", target = "cellTypeName"),
                    @Mapping(  source= "cellsTypeId", target = "cellTypeId"),
                    @Mapping(  source= "workshop", target = "workshopName")
            }
    )
    CellVersionPlanIe toCellVersionPlanIeFromCellInstockPlanTotal(CellInstockPlanTotalDTO total);
    @Mappings(
            {
                    @Mapping(target = "isOversea",source = "isOverseaName"),
                    @Mapping(target = "cellsType",source = "cellTypeName"),
                    @Mapping(target = "workshop",source = "workshopName"),
                    @Mapping(target = "cellsTypeId",source = "cellTypeId")
            }
    )
    CellPlanLineTotalQuery toCellPlanLineTotalQuery(CellVersionPlanIeQuery query);

    CellInstockPlanTotalQuery toCellInstockPlanTotalQuery(CellVersionPlanIeQuery query);
    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellVersionPlanIe saveDTOtoEntity(CellVersionPlanIeSaveDTO saveDTO, @MappingTarget CellVersionPlanIe entity);

}
