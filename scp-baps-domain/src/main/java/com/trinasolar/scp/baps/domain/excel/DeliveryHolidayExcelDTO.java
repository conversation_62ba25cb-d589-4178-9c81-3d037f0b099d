package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 物流节假日表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DeliveryHolidayExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ExcelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 发货基地
     */
    @ExcelProperty(value = "发货基地")
    private String basePlaceFrom;
    /**
     * 到货基地
     */
    @ExcelProperty(value = "到货基地")
    private String basePlaceTo;
    /**
     * 年月
     */
    @ExcelProperty(value = "年月")
    private String month;
    /**
     * 日期区间
     */
    @ExcelProperty(value = "日期区间")
    private String dateBetween;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
