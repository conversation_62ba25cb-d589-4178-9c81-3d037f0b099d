package com.trinasolar.scp.baps.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 实际入库表(待转化)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_instock_plan_no_finish")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_instock_plan_no_finish SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_instock_plan_no_finish SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellInstockPlanNoFinish extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshop_id")
    private Long workshopId;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @Column(name = "locator_code")
    private String locatorCode;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cells_type_id")
    private Long cellsTypeId;

    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 产品等级Id
     */
    @ApiModelProperty(value = "产品等级Id")
    @Column(name = "production_grade_id")
    private Long productionGradeId;

    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    @Column(name = "production_grade")
    private String productionGrade;

    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    @Column(name = "cell_source_id")
    private Long cellSourceId;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    @Column(name = "cell_source")
    private String cellSource;

    /**
     * 透明双玻Id
     */
    @ApiModelProperty(value = "透明双玻Id")
    @Column(name = "transparent_double_glass_id")
    private Long transparentDoubleGlassId;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    @Column(name = "transparent_double_glass")
    private String transparentDoubleGlass;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    @Column(name = "h_trace_id")
    private Long hTraceId;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    @Column(name = "h_trace")
    private String hTrace;

    /**
     * 是否小区域国家
     */
    @ApiModelProperty(value = "是否小区域国家")
    @Column(name = "is_regional_country")
    private String isRegionalCountry;

    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    @Column(name = "regional_country_id")
    private Long regionalCountryId;

    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    @Column(name = "regional_country")
    private String regionalCountry;

    /**
     * 实际入库时间
     */
    @ApiModelProperty(value = "实际入库时间")
    @Column(name = "finished_date")
    private LocalDateTime finishedDate;

    /**
     * 实际入库数量（片）
     */
    @ApiModelProperty(value = "实际入库数量（片）")
    @Column(name = "finished_qty")
    private BigDecimal finishedQty;
    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    @Column(name = "organization_id")
    private Long organizationId;
    /**
     * erp原始创建时间
     */
    @ApiModelProperty(value = "erp原始创建时间")
    @Column(name = "creation_date")
    private LocalDateTime creationDate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    @Column(name = "aesthetics")
    private String aesthetics;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学Id")
    @Column(name = "aesthetics_id")
    private Long aestheticsId;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_space")
    private String mainGridSpace;

    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    @Column(name = "supply_method")
    private String supplyMethod;

    /**
     * 配比
     */
    @ApiModelProperty(value = "配比")
    @Column(name = "ratio_code")
    private String ratioCode;

}
