package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.entity.CellResource;
import com.trinasolar.scp.baps.domain.query.CellGradeCapacityQuery;
import com.trinasolar.scp.baps.domain.save.CellGradeCapacitySaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellGradeCapacity;
import com.trinasolar.scp.baps.domain.dto.CellGradeCapacityDTO;
import com.trinasolar.scp.baps.domain.excel.CellGradeCapacityExcelDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.checkerframework.checker.units.qual.C;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 爬坡产能表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovUtils.class, LovHeaderCodeConstant.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellGradeCapacityDEConvert extends BaseDEConvert<CellGradeCapacityDTO, CellGradeCapacity> {

    CellGradeCapacityDEConvert INSTANCE = Mappers.getMapper(CellGradeCapacityDEConvert.class);

    List<CellGradeCapacityExcelDTO> toExcelDTO(List<CellGradeCapacityDTO> dtos);

    CellGradeCapacityExcelDTO toExcelDTO(CellGradeCapacityDTO dto);
    @Override
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellGradeCapacity.getCellsTypeId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellGradeCapacity.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellGradeCapacity.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellGradeCapacity.getWorkshopid()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellGradeCapacity.getWorkunitid()))")
            }
    )
    CellGradeCapacityDTO toDto(CellGradeCapacity cellGradeCapacity);

    @Mappings(
            {
                    @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType()).getLovLineId())"),
                    @Mapping(target = "isOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOversea()).getLovLineId())"),
                    @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "workshopid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop()).getLovLineId())"),
                    @Mapping(target = "workunitid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit()).getLovLineId())")
            }
    )
    CellGradeCapacitySaveDTO excelDtoToSaveDto(CellGradeCapacityExcelDTO excelDTO);

    List<CellGradeCapacitySaveDTO> excelDtoToSaveDto(List<CellGradeCapacityExcelDTO> dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellGradeCapacity.getCellsTypeId()).getLovValue())"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellGradeCapacity.getIsOverseaId()).getLovValue())"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellGradeCapacity.getBasePlaceId()).getLovValue())"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellGradeCapacity.getWorkshopid()).getLovValue())"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellGradeCapacity.getWorkunitid()).getLovValue())")
            }
    )
    @Named("copyEntityToValueDto")
    CellGradeCapacityDTO copyEntityToValueDto(CellGradeCapacity cellGradeCapacity);
    @IterableMapping( qualifiedByName = "copyEntityToValueDto")
    List<CellGradeCapacityDTO> copyEntityToValueDto(List<CellGradeCapacity> datas);

    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getCellsTypeId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkshopid()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkunitid()))")
            }
    )
    @Named("copyDtoNameFromDto")
    CellGradeCapacityDTO copyDtoNameFromDto(CellGradeCapacityDTO dto);
    @IterableMapping( qualifiedByName = "copyDtoNameFromDto")
    List<CellGradeCapacityDTO> copyDtoNameFromDto(List<CellGradeCapacityDTO> preData);
    CellResource toCellResourceEntity(CellGradeCapacityDTO dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellsType(),language))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOversea(),language))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlace(),language))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshop(),language))")
            }
    )
    CellGradeCapacityQuery toCellGradeCapacityQueryCNNameByName(CellGradeCapacityQuery dto,String language);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellsTypeId() ))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOverseaId() ))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlaceId() ))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshopid() ))"),
                    @Mapping(target = "workunit" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_UNIT,dto.getWorkunitid()))")
            }
    )
    @Named("toCellGradeCapacitySaveCnNameDTOById")
    CellGradeCapacitySaveDTO toCellGradeCapacitySaveCnNameDTOById(CellGradeCapacitySaveDTO dto);
    @IterableMapping(qualifiedByName = "toCellGradeCapacitySaveCnNameDTOById")
    List<CellGradeCapacitySaveDTO> toCellGradeCapacitySaveCnNameDTOById(List<CellGradeCapacitySaveDTO> datas);
    @Named("toEntityFromSaveDTO")
    CellGradeCapacity toEntityFromSaveDTO(CellGradeCapacitySaveDTO dto);
    @IterableMapping(qualifiedByName = "toEntityFromSaveDTO")
    List<CellGradeCapacity> toEntityFromSaveDTO(List<CellGradeCapacitySaveDTO> dto);
}
