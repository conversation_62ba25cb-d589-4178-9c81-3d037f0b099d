package com.trinasolar.scp.baps.domain.dto.system;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "法碳产地表", description = "DTO对象")
public class CarbonOriginAddress extends BasePO implements Serializable, Cloneable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 证书头ID
     */
    @ApiModelProperty(value = "证书头ID", notes = "证书头ID")
    private Long certHeaderId;

    /**
     * 材料类别ID
     */
    @ApiModelProperty(value = "材料类别ID", notes = "材料类别ID")
    private String mtlType;

    /**
     * 材料类别名称
     */
    @ApiModelProperty(value = "材料类别名称")
    private String mtlTypeName;

    /**
     * 产地/供应商
     */
    @ApiModelProperty(value = "产地/供应商")
    private String origin;

    /**
     * 来源地
     */
    @ApiModelProperty(value = "来源地")
    private String originEn;

    /**
     * 生产地址
     */
    @ApiModelProperty(value = "生产地址")
    private String originAddress;

    /**
     * 生产地址en
     */
    @ApiModelProperty(value = "生产地址en")
    private String originAddressEn;

    /**
     * ECS代码
     */
    @ApiModelProperty(value = "ECS代码")
    private String originEcsCode;


}