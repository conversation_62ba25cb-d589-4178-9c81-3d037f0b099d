package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.baps.domain.utils.CustomDateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.math.BigDecimal;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * 硅片拆分规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 07:44:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SiliconSplitRuleExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 分片规则类型
     */
    @ExcelProperty(value = "分片规则类型")
    private String ruleType;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    @ExcelIgnore
    private Long cellTypeId;

    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellType;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    @ExcelIgnore
    private Long workshopId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 硅片等级
     */
    @ExcelProperty(value = "硅片等级")
    private String waferGrade;
    /**
     * 硅料厂家
     */
    @ExcelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 电池良率
     */
    @ExcelProperty(value = "电池良率")
    private String cellFine;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间",converter = CustomDateConverter.class)
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间",converter = CustomDateConverter.class)
    private LocalDate endDate;

}
