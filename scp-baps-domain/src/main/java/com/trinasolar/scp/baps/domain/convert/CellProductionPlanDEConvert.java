package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellPlanLineDTO;
import com.trinasolar.scp.baps.domain.dto.CellProductionPlanSummaryDTO;
import com.trinasolar.scp.baps.domain.entity.CellPlanLine;
import com.trinasolar.scp.baps.domain.save.CellPlanLineSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellProductionPlan;
import com.trinasolar.scp.baps.domain.dto.CellProductionPlanDTO;
import com.trinasolar.scp.baps.domain.excel.CellProductionPlanExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellProductionPlanSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 投产计划 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellProductionPlanDEConvert extends BaseDEConvert<CellProductionPlanDTO, CellProductionPlan> {

    CellProductionPlanDEConvert INSTANCE = Mappers.getMapper(CellProductionPlanDEConvert.class);

    List<CellProductionPlanExcelDTO> toExcelDTO(List<CellProductionPlanDTO> dtos);

    CellProductionPlanExcelDTO toExcelDTO(CellProductionPlanDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })

    CellProductionPlan saveDTOtoEntity(CellProductionPlanSaveDTO saveDTO, @MappingTarget CellProductionPlan entity);
    @Override
    @Mappings({
            @Mapping(target = "HTrace", source = "HTrace", defaultValue = ""),
            @Mapping(target = "aesthetics", source = "aesthetics", defaultValue = ""),
            @Mapping(target = "transparentDoubleGlass", source = "transparentDoubleGlass", defaultValue = ""),
            @Mapping(target = "cellSource", source = "cellSource", defaultValue = "")
    }
    )
    CellProductionPlanDTO toDto(CellProductionPlan entity);
    @Override
    List<CellProductionPlanDTO>  toDto(List<CellProductionPlan> entities);

    @Mappings({
            @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, entity.getCellsType()).getLovLineId())"),
            @Mapping(target = "isOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, entity.getIsOversea()).getLovLineId())"),
            @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, entity.getBasePlace()).getLovLineId())"),
            @Mapping(target = "workshopId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, entity.getWorkshop()).getLovLineId())"),
            @Mapping(target = "workunitId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT, entity.getWorkunit()).getLovLineId())")
    })
    CellProductionPlanSaveDTO toSaveDto(CellProductionPlan entity);
    CellProductionPlan  toEntityFromSaveDto(CellProductionPlanSaveDTO dto);
    CellProductionPlanSummaryDTO detailsToSummary(CellPlanLineDTO cellPlanLineDTO);

}
