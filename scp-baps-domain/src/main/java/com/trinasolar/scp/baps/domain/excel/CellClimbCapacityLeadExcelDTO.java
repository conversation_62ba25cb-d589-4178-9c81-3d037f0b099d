package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import java.io.Serializable;


/**
 * 爬坡产能可靠性验证表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-05 08:10:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellClimbCapacityLeadExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型id
     */
    @ExcelProperty(value = "电池类型id")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellType;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    private Long workShopId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workShop;
    /**
     * 生产单元Id
     */
    @ExcelProperty(value = "生产单元Id")
    private Long workUnitId;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workUnit;
    /**
     * 验证时间
     */
    @ExcelProperty(value = "验证时间")
    private LocalDateTime validateTime;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    private BigDecimal lineNumber;
}
