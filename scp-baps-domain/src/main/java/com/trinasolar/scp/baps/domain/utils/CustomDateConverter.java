package com.trinasolar.scp.baps.domain.utils;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.regex.Pattern;

/**
 * easyExcel日期转化器
 */
public class CustomDateConverter implements Converter<LocalDate> {

    private static final Pattern DATE_PATTERN_1 = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");
    private static final Pattern DATE_PATTERN_2 = Pattern.compile("^\\d{4}/\\d{2}/\\d{2}$");
    private static final DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    @Override
    public Class supportJavaTypeKey() {
        return LocalDate.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(LocalDate value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String dateValue = formatter1.format(value);
        return new WriteCellData<>(dateValue);
    }

    @Override
    public LocalDate convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (cellData.getType().equals(CellDataTypeEnum.NUMBER)) {
            LocalDate localDate = LocalDate.of(1900, 1, 1);
            //excel 有些奇怪的bug, 导致日期数差2
            localDate = localDate.plusDays(cellData.getNumberValue().longValue() - 2);
            return localDate;
        } else if (cellData.getType().equals(CellDataTypeEnum.STRING)) {
            String dateStr = cellData.getStringValue();
            if (dateStr == null) {
                return null;
            }

            if (DATE_PATTERN_1.matcher(dateStr).matches()) {
                return LocalDate.parse(dateStr, formatter1);
            } else if (DATE_PATTERN_2.matcher(dateStr).matches()) {
                return LocalDate.parse(dateStr, formatter2);
            }
        }
        return null;


    }
}