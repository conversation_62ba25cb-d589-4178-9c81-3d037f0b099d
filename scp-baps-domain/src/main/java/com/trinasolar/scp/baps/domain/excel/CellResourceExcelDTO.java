package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 电池资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-08 02:52:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellResourceExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外id
     */
    @ExcelProperty(value = "国内海外id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地id
     */
    @ExcelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间id
     */
    @ExcelProperty(value = "生产车间id")
    private Long workshopId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产单元id
     */
    @ExcelProperty(value = "生产单元id")
    private Long workunitId;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 拆分标识
     */
    @ExcelProperty(value = "拆分标识")
    private Integer isSplited;
    /**
     * 生产线体
     */
    @ExcelProperty(value = "生产线体")
    private String lineName;
    /**
     * 资源组
     */
    @ExcelProperty(value = "资源组")
    private String resourceGroup;
    /**
     * 线体数量
     */
    @ExcelProperty(value = "线体数量")
    private Integer numberLine;
}
