package com.trinasolar.scp.baps.domain.query;
import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;
@Data
@ApiModel(value = "供应能力查询条件", description = "供应能力查询条件")
@Accessors(chain = true)
public class SiliconSliceSupplyLinesQuery extends PageDTO {
    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "月份")
    private List<String> monthList;

}
