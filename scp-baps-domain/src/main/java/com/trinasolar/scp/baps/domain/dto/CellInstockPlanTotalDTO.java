package com.trinasolar.scp.baps.domain.dto;


import com.alibaba.fastjson.annotation.JSONField;
import com.trinasolar.scp.baps.domain.entity.BigDecimalSerializer;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import javax.persistence.Column;
import java.util.List;
import java.util.TreeMap;

/**
 * 入库计划汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-26 11:20:51
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "入库计划汇总表DTO对象", description = "DTO对象")
public class CellInstockPlanTotalDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "组件电池类型")
    private String componentCellsType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 是否H兼容
     */
    @ApiModelProperty(value = "是否H兼容")
    private String hChangeFlag;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 万片
     */
    @ApiModelProperty(value = "万片")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal qtyThousandPc;
    /**
     * MV
     */
    @ApiModelProperty(value = "MV")
    private BigDecimal cellMv;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;
    /**
     * 投产表数据版本
     */
    @ApiModelProperty(value = "投产表数据版本")
    private String fromVersion;
    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * d1
     */
    @ApiModelProperty(value = "d1")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d1;
    /**
     * d2
     */
    @ApiModelProperty(value = "d2")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d2;
    /**
     * d3
     */
    @ApiModelProperty(value = "d3")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d3;
    /**
     * d4
     */
    @ApiModelProperty(value = "d4")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d4;
    /**
     * d5
     */
    @ApiModelProperty(value = "d5")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d5;
    /**
     * d6
     */
    @ApiModelProperty(value = "d6")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d6;
    /**
     * d7
     */
    @ApiModelProperty(value = "d7")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d7;
    /**
     * d8
     */
    @ApiModelProperty(value = "d8")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d8;
    /**
     * d9
     */
    @ApiModelProperty(value = "d9")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d9;
    /**
     * d10
     */
    @ApiModelProperty(value = "d10")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d10;
    /**
     * d11
     */
    @ApiModelProperty(value = "d11")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d11;
    /**
     * d12
     */
    @ApiModelProperty(value = "d12")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d12;
    /**
     * d13
     */
    @ApiModelProperty(value = "d13")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d13;
    /**
     * d14
     */
    @ApiModelProperty(value = "d14")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d14;
    /**
     * d15
     */
    @ApiModelProperty(value = "d15")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d15;
    /**
     * d16
     */
    @ApiModelProperty(value = "d16")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d16;
    /**
     * d17
     */
    @ApiModelProperty(value = "d17")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d17;
    /**
     * d18
     */
    @ApiModelProperty(value = "d18")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d18;
    /**
     * d19
     */
    @ApiModelProperty(value = "d19")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d19;
    /**
     * d20
     */
    @ApiModelProperty(value = "d20")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d20;
    /**
     * d21
     */
    @ApiModelProperty(value = "d21")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d21;
    /**
     * d22
     */
    @ApiModelProperty(value = "d22")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d22;
    /**
     * d23
     */
    @ApiModelProperty(value = "d23")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d23;
    /**
     * d24
     */
    @ApiModelProperty(value = "d24")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d24;
    /**
     * d25
     */
    @ApiModelProperty(value = "d25")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d25;
    /**
     * d26
     */
    @ApiModelProperty(value = "d26")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d26;
    /**
     * d27
     */
    @ApiModelProperty(value = "d27")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d27;
    /**
     * d28
     */
    @ApiModelProperty(value = "d28")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d28;
    /**
     * d29
     */
    @ApiModelProperty(value = "d29")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d29;
    /**
     * d30
     */
    @ApiModelProperty(value = "d30")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d30;
    /**
     * d31
     */
    @ApiModelProperty(value = "d31")
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    private BigDecimal d31;

    /**
     * 每日数据
     */
    private TreeMap<String, String> dataStructures;

    private TreeMap<String, Pair<String, Boolean>> dataStructurePairs;

    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ApiModelProperty(value = "需求来源")
    private String sourceType;

    @ApiModelProperty(value = "更改状态")
    private String changeStatusDesc;

    @ApiModelProperty(value = "含有变更状态的day数值")
    private List<CellPlanLineContrastDTO> changeContrastList;

    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    private String supplyMethod;

    /**
     * 供应方式Id
     */
    @ApiModelProperty(value = "供应方式Id")
    private Long supplyMethodId;

    /**
     * 背面细栅
     */
    @ApiModelProperty(value = "背面细栅")
    private String backFineGrid;
    /**
     * 正面细栅
     */
    @ApiModelProperty(value = "正面细栅")
    private String frontFineGrid;
    /**
     * 硅片厚度
     */
    @ApiModelProperty(value = "硅片厚度")
    private String siliconWaferThickness;
    /**
     * 硅片尺寸
     */
    @ApiModelProperty(value = "硅片尺寸")
    private String siliconWaferSize;

    @ApiModelProperty(value = "证书编号")
    private String certCode;

    @ApiModelProperty(value = "配比")
    private String ratioCode;

    @ApiModelProperty(value = "ECS-CODE")
    private String ecsCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 生命周期状态
     */
    @ApiModelProperty(value = "生命周期状态")
    private String lifecycleState;

    @ApiModelProperty(value = "临时量产标识")
    private String isTemporaryOutput;

    public String group() {
        return StringTools.joinWith(",", this.getIsOversea(), this.getBasePlace(), this.getWorkshop(), this.getCellsType(),
                this.getItemCode(), this.getHTrace(), this.getSupplyMethod(),this.getCertCode(),  this.getRatioCode(), this.getEcsCode(), this.getHChangeFlag(), this.getCellSource(), this.getRegionalCountry(),
                this.getTransparentDoubleGlass(), this.getMainGridSpace(), this.getSourceType(), this.getAesthetics());
    }

}
