package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * IE产能打折月度（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-28 03:34:54
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "IE产能打折月度（人力）表DTO对象", description = "DTO对象")
public class CellBaseCapacityMonthDiscountsDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外id
     */
    @ApiModelProperty(value = "国内海外id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地id
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间id
     */
    @ApiModelProperty(value = "生产车间id")
    private Long workshopId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;
    /**
     * 生产单元id
     */
    @ApiModelProperty(value = "生产单元id")
    @Column(name = "workunit_id")
    private Long workunitId;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * d1
     */
    @ApiModelProperty(value = "d1")
    private BigDecimal d1;
    public String getD1Percent(){
        return MapStrutUtil.addPercentage(getD1(),2);
    }
    /**
     * d2
     */
    @ApiModelProperty(value = "d2")
    private BigDecimal d2;
    public String getD2Percent(){
        return MapStrutUtil.addPercentage(getD2(),2);
    }
    /**
     * d3
     */
    @ApiModelProperty(value = "d3")
    private BigDecimal d3;
    public  String getD3Percent(){
        return MapStrutUtil.addPercentage(getD3(),2);
    }
    /**
     * d4
     */
    @ApiModelProperty(value = "d4")
    private BigDecimal d4;
    public String getD4Percent(){
        return MapStrutUtil.addPercentage(getD4(),2);
    }
    /**
     * d5
     */
    @ApiModelProperty(value = "d5")
    private BigDecimal d5;
    public String getD5Percent(){
        return MapStrutUtil.addPercentage(getD5(),2);
    }
    /**
     * d6
     */
    @ApiModelProperty(value = "d6")
    private BigDecimal d6;
    public String getD6Percent(){
        return MapStrutUtil.addPercentage(getD6(),2);
    }
    /**
     * d7
     */
    @ApiModelProperty(value = "d7")
    private BigDecimal d7;
    public String getD7Percent(){
        return MapStrutUtil.addPercentage(getD7(),2);
    }
    /**
     * d8
     */
    @ApiModelProperty(value = "d8")
    private BigDecimal d8;
    public String getD8Percent(){
        return MapStrutUtil.addPercentage(getD8(),2);
    }
    /**
     * d9
     */
    @ApiModelProperty(value = "d9")
    private BigDecimal d9;
    public String getD9Percent(){
        return MapStrutUtil.addPercentage(getD9(),2);
    }
    /**
     * d10
     */
    @ApiModelProperty(value = "d10")
    private BigDecimal d10;
    public  String getD10Percent(){
        return MapStrutUtil.addPercentage(getD10(),2);
    }
    /**
     * d11
     */
    @ApiModelProperty(value = "d11")
    private BigDecimal d11;
    public  String getD11Percent(){
        return MapStrutUtil.addPercentage(getD11(),2);
    }
    /**
     * d12
     */
    @ApiModelProperty(value = "d12")
    private BigDecimal d12;
    public String  getD12Percent(){
        return MapStrutUtil.addPercentage(getD12(),2);
    }
    /**
     * d13
     */
    @ApiModelProperty(value = "d13")
    private BigDecimal d13;
    public String getD13Percent(){
        return MapStrutUtil.addPercentage(getD13(),2);
    }
    /**
     * d14
     */
    @ApiModelProperty(value = "d14")
    private BigDecimal d14;
    public String getD14Percent(){
        return MapStrutUtil.addPercentage(getD14(),2);
    }
    /**
     * d15
     */
    @ApiModelProperty(value = "d15")
    private BigDecimal d15;
    public String getD15Percent(){
        return MapStrutUtil.addPercentage(getD15(),2);
    }
    /**
     * d16
     */
    @ApiModelProperty(value = "d16")
    private BigDecimal d16;
    public String getD16Percent(){
        return MapStrutUtil.addPercentage(getD16(),2);
    }
    /**
     * d17
     */
    @ApiModelProperty(value = "d17")
    private BigDecimal d17;
    public String getD17Percent(){
        return MapStrutUtil.addPercentage(getD17(),2);
    }

    /**
     * d18
     */
    @ApiModelProperty(value = "d18")
    private BigDecimal d18;
    public String getD18Percent(){
        return MapStrutUtil.addPercentage(getD18(),2);
    }
    /**
     * d19
     */
    @ApiModelProperty(value = "d19")
    private BigDecimal d19;
    public String getD19Percent(){
        return MapStrutUtil.addPercentage(getD19(),2);
    }
    /**
     * d20
     */
    @ApiModelProperty(value = "d20")
    private BigDecimal d20;
    public String getD20Percent(){
        return MapStrutUtil.addPercentage(getD20(),2);
    }
    /**
     * d21
     */
    @ApiModelProperty(value = "d21")
    private BigDecimal d21;
    public String getD21Percent(){
        return MapStrutUtil.addPercentage(getD21(),2);
    }
    /**
     * d22
     */
    @ApiModelProperty(value = "d22")
    private BigDecimal d22;
    public String getD22Percent(){
        return MapStrutUtil.addPercentage(getD22(),2);
    }
    /**
     * d23
     */
    @ApiModelProperty(value = "d23")
    private BigDecimal d23;
    public String getD23Percent(){
        return MapStrutUtil.addPercentage(getD23(),2);
    }
    /**
     * d24
     */
    @ApiModelProperty(value = "d24")
    private BigDecimal d24;
    public String getD24Percent(){
        return MapStrutUtil.addPercentage(getD24(),2);
    }
    /**
     * d25
     */
    @ApiModelProperty(value = "d25")
    private BigDecimal d25;
    public String getD25Percent(){
        return MapStrutUtil.addPercentage(getD25(),2);
    }
    /**
     * d26
     */
    @ApiModelProperty(value = "d26")
    private BigDecimal d26;
    public String getD26Percent(){
        return MapStrutUtil.addPercentage(getD26(),2);
    }
    /**
     * d27
     */
    @ApiModelProperty(value = "d27")
    private BigDecimal d27;
    public String getD27Percent(){
        return MapStrutUtil.addPercentage(getD27(),2);
    }
    /**
     * d28
     */
    @ApiModelProperty(value = "d28")
    private BigDecimal d28;
    public String getD28Percent(){
        return MapStrutUtil.addPercentage(getD28(),2);
    }
    /**
     * d29
     */
    @ApiModelProperty(value = "d29")
    private BigDecimal d29;
    public String getD29Percent(){
        return MapStrutUtil.addPercentage(getD29(),2);
    }
    /**
     * d30
     */
    @ApiModelProperty(value = "d30")
    private BigDecimal d30;
    public String getD30Percent(){
        return MapStrutUtil.addPercentage(getD30(),2);
    }
    /**
     * d31
     */
    @ApiModelProperty(value = "d31")
    private BigDecimal d31;
    public String getD31Percent(){
        return MapStrutUtil.addPercentage(getD31(),2);
    }
}
