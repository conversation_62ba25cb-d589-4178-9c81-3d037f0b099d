package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 爬坡产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_grade_capacity")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_grade_capacity SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_grade_capacity SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellGradeCapacity extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cells_type_id")
    private Long cellsTypeId;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshopid")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunitid")
    private Long workunitid;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    @Column(name = "line_name")
    private String lineName;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    @Column(name = "line_number")
    private BigDecimal lineNumber;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 产能
     */
    @ApiModelProperty(value = "产能")
    @Column(name = "capacity_quantity")
    private BigDecimal capacityQuantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;
    /**
     * 能否生产单玻
     */
    @ApiModelProperty(value = "能否生产单玻")
    @Column(name = "is_single_glass")
    private String isSingleGlass;
    /**
     * 能否生产低碳
     */
    @ApiModelProperty(value = "能否生产低碳")
    @Column(name = "is_dt")
    private String isDt;
    /**
     * 能否生产小区域国家
     */
    @ApiModelProperty(value = "能否生产小区域国家")
    @Column(name = "is_regional_country")
    private String isRegionalCountry;
    /**
     * 能否生产H兼容
     */
    @ApiModelProperty(value = "能否生产H兼容")
    @Column(name = "is_h_change_flag")
    private String isHChangeFlag;
    /**
     * 能否生产H追溯
     */
    @ApiModelProperty(value = "能否生产H追溯")
    @Column(name = "is_h_trace")
    private String isHTrace;
    /**
     * 1号
     */
    @ApiModelProperty(value = "1号")
    @Column(name = "d1")
    private BigDecimal d1;

    /**
     * 2号
     */
    @ApiModelProperty(value = "2号")
    @Column(name = "d2")
    private BigDecimal d2;

    /**
     * 3号
     */
    @ApiModelProperty(value = "3号")
    @Column(name = "d3")
    private BigDecimal d3;

    /**
     * 4号
     */
    @ApiModelProperty(value = "4号")
    @Column(name = "d4")
    private BigDecimal d4;

    /**
     * 5号
     */
    @ApiModelProperty(value = "5号")
    @Column(name = "d5")
    private BigDecimal d5;

    /**
     * 6号
     */
    @ApiModelProperty(value = "6号")
    @Column(name = "d6")
    private BigDecimal d6;

    /**
     * 7号
     */
    @ApiModelProperty(value = "7号")
    @Column(name = "d7")
    private BigDecimal d7;

    /**
     * 8号
     */
    @ApiModelProperty(value = "8号")
    @Column(name = "d8")
    private BigDecimal d8;

    /**
     * 9号
     */
    @ApiModelProperty(value = "9号")
    @Column(name = "d9")
    private BigDecimal d9;

    /**
     * 10号
     */
    @ApiModelProperty(value = "10号")
    @Column(name = "d10")
    private BigDecimal d10;

    /**
     * 11号
     */
    @ApiModelProperty(value = "11号")
    @Column(name = "d11")
    private BigDecimal d11;

    /**
     * 12号
     */
    @ApiModelProperty(value = "12号")
    @Column(name = "d12")
    private BigDecimal d12;

    /**
     * 13号
     */
    @ApiModelProperty(value = "13号")
    @Column(name = "d13")
    private BigDecimal d13;

    /**
     * 14号
     */
    @ApiModelProperty(value = "14号")
    @Column(name = "d14")
    private BigDecimal d14;

    /**
     * 15号
     */
    @ApiModelProperty(value = "15号")
    @Column(name = "d15")
    private BigDecimal d15;

    /**
     * 16号
     */
    @ApiModelProperty(value = "16号")
    @Column(name = "d16")
    private BigDecimal d16;

    /**
     * 17号
     */
    @ApiModelProperty(value = "17号")
    @Column(name = "d17")
    private BigDecimal d17;

    /**
     * 18号
     */
    @ApiModelProperty(value = "18号")
    @Column(name = "d18")
    private BigDecimal d18;

    /**
     * 19号
     */
    @ApiModelProperty(value = "19号")
    @Column(name = "d19")
    private BigDecimal d19;

    /**
     * 20号
     */
    @ApiModelProperty(value = "20号")
    @Column(name = "d20")
    private BigDecimal d20;

    /**
     * 21号
     */
    @ApiModelProperty(value = "21号")
    @Column(name = "d21")
    private BigDecimal d21;

    /**
     * 22号
     */
    @ApiModelProperty(value = "22号")
    @Column(name = "d22")
    private BigDecimal d22;

    /**
     * 23号
     */
    @ApiModelProperty(value = "23号")
    @Column(name = "d23")
    private BigDecimal d23;

    /**
     * 24号
     */
    @ApiModelProperty(value = "24号")
    @Column(name = "d24")
    private BigDecimal d24;

    /**
     * 25号
     */
    @ApiModelProperty(value = "25号")
    @Column(name = "d25")
    private BigDecimal d25;

    /**
     * 26号
     */
    @ApiModelProperty(value = "26号")
    @Column(name = "d26")
    private BigDecimal d26;

    /**
     * 27号
     */
    @ApiModelProperty(value = "27号")
    @Column(name = "d27")
    private BigDecimal d27;

    /**
     * 28号
     */
    @ApiModelProperty(value = "28号")
    @Column(name = "d28")
    private BigDecimal d28;

    /**
     * 29号
     */
    @ApiModelProperty(value = "29号")
    @Column(name = "d29")
    private BigDecimal d29;

    /**
     * 30号
     */
    @ApiModelProperty(value = "30号")
    @Column(name = "d30")
    private BigDecimal d30;

    /**
     * 31号
     */
    @ApiModelProperty(value = "31号")
    @Column(name = "d31")
    private BigDecimal d31;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 可靠性验证
     */
    @ApiModelProperty(value = "可靠性验证")
    @Column(name = "reliability_check")
    private String reliabilityCheck;

}
