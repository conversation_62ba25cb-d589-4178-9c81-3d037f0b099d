package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.ManufacturingSwitchPlanDTO;
import com.trinasolar.scp.baps.domain.entity.ManufacturingSwitchPlan;
import com.trinasolar.scp.baps.domain.excel.ManufacturingSwitchPlanExcelDTO;
import com.trinasolar.scp.baps.domain.query.ManufacturingSwitchPlanQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 制造工艺切换计划维护 DTO与实体转换器
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovUtils.class, LovHeaderCodeConstant.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ManufacturingSwitchPlanDEConvert extends BaseDEConvert<ManufacturingSwitchPlanDTO, ManufacturingSwitchPlan> {

    ManufacturingSwitchPlanDEConvert INSTANCE = Mappers.getMapper(ManufacturingSwitchPlanDEConvert.class);

    List<ManufacturingSwitchPlanExcelDTO> toExcelDTO(List<ManufacturingSwitchPlanDTO> dtos);

    ManufacturingSwitchPlanExcelDTO toExcelDTO(ManufacturingSwitchPlanDTO dto);
    @Override
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(manufacturingSwitchPlan.getCellsTypeId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(manufacturingSwitchPlan.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(manufacturingSwitchPlan.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(manufacturingSwitchPlan.getWorkshopid()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(manufacturingSwitchPlan.getWorkunitid()))")
            }
    )
    ManufacturingSwitchPlanDTO toDto(ManufacturingSwitchPlan manufacturingSwitchPlan);

    @Mappings(
            {
                    @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType()).getLovLineId())"),
                    @Mapping(target = "isOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOversea()).getLovLineId())"),
                    @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "workshopid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop()).getLovLineId())"),
                    @Mapping(target = "workunitid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit()).getLovLineId())")
            }
    )
    ManufacturingSwitchPlan excelDtoToSaveDto(ManufacturingSwitchPlanExcelDTO excelDTO);

    List<ManufacturingSwitchPlan> excelDtoToSaveDto(List<ManufacturingSwitchPlanExcelDTO> dto);

    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellsType(),language))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOversea(),language))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshop(),language))")
            }
    )
    ManufacturingSwitchPlanQuery toQueryCNNameByName(ManufacturingSwitchPlanQuery dto, String language);
}
