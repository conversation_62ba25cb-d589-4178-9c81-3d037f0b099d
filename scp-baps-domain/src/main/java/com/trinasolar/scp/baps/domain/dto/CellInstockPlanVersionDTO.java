package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;


import java.util.Objects;


/**
 * 入库计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "入库计划版本管理表DTO对象", description = "DTO对象")
public class CellInstockPlanVersionDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    private String month;
    /**
     * 最終确认发布的版本
     */
    @ApiModelProperty(value = "最終确认发布的版本")
    private String version;
    /**
     * 来自哪个投产版本
     */
    @ApiModelProperty(value = "来自哪个投产版本")
    private String fromVersion;
    /**
     * 是否进行了A-拆分
     */
    @ApiModelProperty(value = "是否进行了A-拆分")
    private Integer isASplit;


    /**
     * 是否进行了低效拆分
     */
    @ApiModelProperty(value = "是否进行了低效拆分")
    private Integer isLESplit;

    @ApiModelProperty(value = "是否进行了A-拆分")
    public String getIsASplitName(){
        if (Objects.equals(isASplit,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否已经进行了透明双玻拆分
     */
    @ApiModelProperty(value = "是否已经进行了透明双玻拆分")
    private Integer isTransparentDoubleGlass;
    @ApiModelProperty(value = "是否已经进行了透明双玻拆分")
    public String getIsTransparentDoubleGlassName(){
        if (Objects.equals(isTransparentDoubleGlass,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否已经进行了美学拆分
     */
    @ApiModelProperty(value = "是否已经进行了美学拆分")
    private Integer isAesthetics;
    @ApiModelProperty(value = "是否已经进行了美学拆分")
    public String getIsAestheticsName(){
        if (Objects.equals(isAesthetics,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否已经进行了分档规则拆分
     */
    @ApiModelProperty(value = "是否已经进行了分档规则拆分")
    private Integer isGradeRule;
    @ApiModelProperty(value = "是否已经进行了分档规则拆分")
    public String getIsGradeRuleName(){
        if (Objects.equals(isGradeRule,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否进行了计划确认
     */
    @ApiModelProperty(value = "是否进行了计划确认")
    private Integer isConfirmPlan;
    @ApiModelProperty(value = "是否进行了计划确认")
    public String getIsConfirmPlanName(){
        if (Objects.equals(isConfirmPlan,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否进行了邮件发送
     */
    @ApiModelProperty(value = "是否进行了邮件发送")
    private Integer isSendEmail;
    @ApiModelProperty(value = "是否进行了邮件发送")
    public String getIsSendEmailName(){
        if (Objects.equals(isSendEmail,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 哪个月排的
     */
    @ApiModelProperty(value = "哪个月排的")
    private String scheduleMonth;
}
