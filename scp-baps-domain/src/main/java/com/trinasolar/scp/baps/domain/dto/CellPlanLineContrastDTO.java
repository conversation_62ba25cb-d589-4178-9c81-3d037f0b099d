package com.trinasolar.scp.baps.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.TreeMap;
import java.util.stream.Collectors;


/**
 * 用于对比每日是否有变化的dto
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "变化结果dto", description = "DTO对象")
public class CellPlanLineContrastDTO extends BaseDTO {

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private BigDecimal value;
    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String name;

    /**
     * 变化标识
     */
    @ApiModelProperty(value = "变化标识")
    private Integer changeFlag;

}
