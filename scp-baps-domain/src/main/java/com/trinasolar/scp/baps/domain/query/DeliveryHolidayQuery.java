package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 物流节假日表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
@Data
@ApiModel(value = "DeliveryHoliday查询条件", description = "查询条件")
@Accessors(chain = true)
public class DeliveryHolidayQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 国内/海外
         */
        @ApiModelProperty(value = "国内/海外")
    private String isOversea;
        /**
         * 发货基地
         */
        @ApiModelProperty(value = "发货基地")
    private String basePlaceFrom;
        /**
         * 到货基地
         */
        @ApiModelProperty(value = "到货基地")
    private String basePlaceTo;
        /**
         * 年月
         */
        @ApiModelProperty(value = "年月")
    private String month;
        /**
         * 日期区间
         */
        @ApiModelProperty(value = "日期区间")
    private String dateBetween;
        /**
         * 备注
         */
        @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
