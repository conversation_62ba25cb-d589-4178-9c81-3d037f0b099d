package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import java.io.Serializable;


/**
 * 实际入库表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellInstockPlanFinishExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * 5A料号
     */
    @ExcelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 产品等级Id
     */
    @ExcelProperty(value = "产品等级Id")
    private Long productionGradeId;
    /**
     * 产品等级
     */
    @ExcelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 片源种类Id
     */
    @ExcelProperty(value = "片源种类Id")
    private Long cellSourceId;
    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学Id")
    @ExcelIgnore
    private Long aestheticsId;
    /**
     * 透明双玻Id
     */
    @ExcelProperty(value = "透明双玻Id")
    private Long transparentDoubleGlassId;
    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private Long hTraceId;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 是否小区域国家
     */
    @ExcelProperty(value = "是否小区域国家")
    private String isRegionalCountry;
    /**
     * 小区域国家
     */
    @ExcelProperty(value = "小区域国家")
    private Long regionalCountryId;
    /**
     * 小区域国家
     */
    @ExcelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 实际入库时间
     */
    @ExcelProperty(value = "实际入库时间")
    private LocalDateTime finishedDate;
    /**
     * 实际入库数量（片）
     */
    @ExcelProperty(value = "实际入库数量（片）")
    private BigDecimal finishedQty;
    /**
     * erp原始创建时间
     */
    @ExcelProperty(value = "erp原始创建时间")
    private LocalDateTime creationDate;
    /**
     * 库存组织ID
     */
    @ExcelProperty(value = "库存组织ID")
    private Long organizationId;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
