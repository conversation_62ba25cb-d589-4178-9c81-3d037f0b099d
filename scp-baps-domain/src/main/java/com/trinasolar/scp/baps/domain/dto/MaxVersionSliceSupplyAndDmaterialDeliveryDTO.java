package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "最新的硅片供应能力和最新物料到货计划信息DTO对象", description = "DTO对象")
public class MaxVersionSliceSupplyAndDmaterialDeliveryDTO {
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    @ApiModelProperty(value = "物料Code")
    private String itemCode;

    @ApiModelProperty(value = "加工方式")
    private String processType;

    @ApiModelProperty(value = "电池品类")
    private String batteryCategory;

    @ApiModelProperty(value = "晶体类型")
    private String crystalType;

    //需要根据料号去查 Segment17
    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    //片源种类Segment2
    @ApiModelProperty(value = "片源种类")
    private String pcsSourceType;

    @ApiModelProperty(value = "日期")
    private LocalDate demandDate;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

}
