package com.trinasolar.scp.baps.domain.utils;

import com.trinasolar.scp.common.api.util.Message;
import com.trinasolar.scp.common.api.util.MessageHelper;

import java.util.Locale;

/**
 * 获取国际化信息的帮助类
 */
public class BapsMessgeHelper {
    /**
     * 获取国际化信息
     * @param code
     * @return
     */
    public static String getMessage(String code) {
        Message message= MessageHelper.getMessage(code,code);
        return message.getDesc();
    }

    /**
     * 获取国际化信息
     * @param code
     * @param args 支持参数
     * @return
     */
    public static String getMessage(String code,Object[] args) {
        Message message= MessageHelper.getMessage(code,args,code);
        return message.getDesc();
    }

    /**
     * 获取国际化信息
     * @param code
     * @param locale 语言
     * @return
     */
    public static String getMessage(String code, Locale locale) {
        Message message= MessageHelper.getMessage(code,code,locale);
        return message.getDesc();
    }

    /**
     * 获取国际化信息
     * @param code
     * @param args
     * @param locale
     * @return
     */
    public static String getMessage(String code,Object[] args, Locale locale) {
        Message message= MessageHelper.getMessage(code,args,code,locale);
        return message.getDesc();
    }
}
