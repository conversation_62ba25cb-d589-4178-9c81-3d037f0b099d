package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.excel.CellBaseCapacityExcelDTO;
import com.trinasolar.scp.baps.domain.query.SiliconSplitRuleQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacitySaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.SiliconSplitRule;
import com.trinasolar.scp.baps.domain.dto.SiliconSplitRuleDTO;
import com.trinasolar.scp.baps.domain.excel.SiliconSplitRuleExcelDTO;
import com.trinasolar.scp.baps.domain.save.SiliconSplitRuleSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 硅片拆分规则 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 07:44:54
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SiliconSplitRuleDEConvert extends BaseDEConvert<SiliconSplitRuleDTO, SiliconSplitRule> {

    SiliconSplitRuleDEConvert INSTANCE = Mappers.getMapper(SiliconSplitRuleDEConvert.class);

    List<SiliconSplitRuleExcelDTO> toExcelDTO(List<SiliconSplitRuleDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getCellTypeId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkshopId()))")
            }
    )
    SiliconSplitRuleExcelDTO toExcelDTO(SiliconSplitRuleDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    SiliconSplitRule saveDTOtoEntity(SiliconSplitRuleSaveDTO saveDTO, @MappingTarget SiliconSplitRule entity);
    @Mappings(
            {
                    @Mapping(target = "cellTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellType()).getLovLineId())"),
                    @Mapping(target = "workshopId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop()).getLovLineId())"),
                    @Mapping(target = "cellFine",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(excelDTO.getCellFine(),4))"),
                    @Mapping(target = "siMfrs",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getValueByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.SI_MFRS,excelDTO.getSiMfrs()))"),
                    @Mapping(target = "waferGrade",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getValueByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WAFER_GRADE,excelDTO.getWaferGrade()))")
            }
    )
    SiliconSplitRuleSaveDTO excelDtoToSaveDto(SiliconSplitRuleExcelDTO excelDTO);
    List<SiliconSplitRuleSaveDTO> excelDtoToSaveDto(List<SiliconSplitRuleExcelDTO> excelDtos);

    @Override
    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(entity.getCellTypeId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(entity.getWorkshopId()))"),
                    @Mapping(target = "cellFine",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(entity.getCellFine(),2))"),
                    @Mapping(target = "siMfrs",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.SI_MFRS,entity.getSiMfrs()))"),
                    @Mapping(target = "waferGrade",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WAFER_GRADE,entity.getWaferGrade()))")
            }
    )
    SiliconSplitRuleDTO toDto(SiliconSplitRule entity);
    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellType()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    SiliconSplitRuleQuery toSiliconSplitRuleQueryCNName(SiliconSplitRuleQuery query, String lang);

    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellTypeId()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshopId()))")
            }
    )
    @Named("toSiliconSplitRuleSaveDTOCNName")
    SiliconSplitRuleSaveDTO toSiliconSplitRuleSaveDTOCNName(SiliconSplitRuleSaveDTO dto);
    @IterableMapping(qualifiedByName = "toSiliconSplitRuleSaveDTOCNName")
    List<SiliconSplitRuleSaveDTO> toSiliconSplitRuleSaveDTOCNName(List<SiliconSplitRuleSaveDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BATTERY_TYPE,rule.getCellTypeId()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_SHOP,rule.getWorkshopId()))"),
                    @Mapping(target = "siMfrs",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.SI_MFRS,rule.getSiMfrs()))"),
                    @Mapping(target = "waferGrade",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WAFER_GRADE,rule.getWaferGrade()))")
            }
    )
    @Named("toSiliconSplitRuleCNName")
    SiliconSplitRule toSiliconSplitRuleCNName(SiliconSplitRule rule);
    @IterableMapping(qualifiedByName = "toSiliconSplitRuleCNName")
    List<SiliconSplitRule> toSiliconSplitRuleCNName(List<SiliconSplitRule> rules);
}
