package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 生产日历
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Entity
@ToString
@Data
@Table(name = "baps_calendar")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_calendar SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_calendar SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class Calendar extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;
    @ApiModelProperty(value = "ie爬坡产能标识0代表ie，1代表爬坡")
    @Column(name = "ieorgrade")
    private Integer ieorgrade;
    @ApiModelProperty(value = "数据来源id(IE或爬坡id)")
    @Column(name = "fromid")
    private Long fromid;
    /**
     * 日历类型
     */
    @ApiModelProperty(value = "日历类型")
    @Column(name = "type")
    private String type;

    /**
     * 资源单元Id
     */
    @ApiModelProperty(value = "资源单元Id")
    @Column(name = "workunit_id")
    private Long workunitId;

    /**
     * 资源单元
     */
    @ApiModelProperty(value = "资源单元")
    @Column(name = "workunit")
    private String workunit;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    @Column(name = "line_name")
    private String lineName;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @Column(name = "date")
    private LocalDate date;

    /**
     * 出勤代码
     */
    @ApiModelProperty(value = "出勤代码")
    @Column(name = "shiftcode")
    private String shiftcode;

    /**
     * 资源量
     */
    @ApiModelProperty(value = "资源量")
    @Column(name = "defaultqty")
    private BigDecimal defaultqty;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @Column(name = "sortorder")
    private Integer sortorder;

    /**
     * 标准产线数
     */
    @ApiModelProperty(value = "标准产线数")
    @Column(name = "num_lines")
    private BigDecimal numLines;


}
