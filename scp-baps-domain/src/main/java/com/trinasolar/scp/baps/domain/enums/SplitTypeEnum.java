package com.trinasolar.scp.baps.domain.enums;

import cn.hutool.core.util.EnumUtil;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.Optional;

public enum SplitTypeEnum {

    @ApiModelProperty("A-")
    A("A", "A-"),

    @ApiModelProperty("低效电池")
    LOW_EFFICIENCY("LOW_EFFICIENCY", "低效电池"),

    @ApiModelProperty("透明双玻")
    TRANSPARENT_DOUBLE_GLASS("TRANSPARENT_DOUBLE_GLASS", "透明双玻"),

    @ApiModelProperty("美学")
    AESTHETICS("AESTHETICS", "美学");

    private String code;
    private String desc;

    public static String getDesc(String code) {
        return (String) Optional.ofNullable(code).map((c) -> (SplitTypeEnum) EnumUtil.likeValueOf(SplitTypeEnum.class, c)).map(SplitTypeEnum::getDesc).orElse(null);
    }

    public static String getCode(String desc) {
        return (String) Arrays.stream(values()).filter((e) -> e.getDesc().equals(desc)).findFirst().map(SplitTypeEnum::getCode).orElse(null);
    }

    private SplitTypeEnum(String code, String name) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
