package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.aps.PowerSupplyAopDTO;
import com.trinasolar.scp.baps.domain.dto.aps.PowerSupplyAopQuery;
import com.trinasolar.scp.baps.domain.entity.QopDetails;
import com.trinasolar.scp.baps.domain.excel.QopDetailsExcelDTO;
import com.trinasolar.scp.baps.domain.query.QopDetailsQuery;
import com.trinasolar.scp.baps.domain.save.QopDetailsSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.dto.QopDetailsDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * SOP数据 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-30 03:38:41
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface QopDetailsDEConvert extends BaseDEConvert<QopDetailsDTO, QopDetails> {

    QopDetailsDEConvert INSTANCE = Mappers.getMapper(QopDetailsDEConvert.class);

    List<QopDetailsExcelDTO> toExcelDTO(List<QopDetailsDTO> dtos);

    QopDetailsExcelDTO toExcelDTO(QopDetailsDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    QopDetails saveDTOtoEntity(QopDetailsSaveDTO saveDTO, @MappingTarget QopDetails entity);

    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "cellTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, aopDTO.getCellTypeName()).getLovLineId())"),
            @Mapping(target = "isOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, aopDTO.getIsOverseaName()).getLovLineId())"),
            @Mapping(target = "workshopId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, aopDTO.getSupplierName()).getLovLineId())"),
    })
    QopDetailsDTO toDtoFromPowerSupplyAopDTO(PowerSupplyAopDTO aopDTO);
}
