package com.trinasolar.scp.baps.domain.dto.system;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;


/**
 * 法碳证书
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CarbonCertHeaderDTO对象", description = "DTO对象")
public class CarbonCertHeaderDTO extends BaseDTO {


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 证书编号
     */
    @ApiModelProperty(value = "证书编号", notes = "证书编号")
    private String certCode;

    /**
     * 临时证书编号
     */
    @ApiModelProperty(value = "临时证书编号", notes = "临时证书编号")
    private String tempCertCode;

    /**
     * 标段
     */
    @ApiModelProperty(value = "标段")
    private String biaSection;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;

    /**
     * 硅片厚度
     */
    @ApiModelProperty(value = "硅片厚度")
    private String waferThickness;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;

    /**
     * 来源系统证书ID
     */
    @ApiModelProperty(value = "来源系统证书ID")
    private String sourceSystemId;

    /**
     * 配比
     */
    @ApiModelProperty(value = "配比")
    private String ratio;

    /**
     * 配比code
     */
    @ApiModelProperty(value = "配比code")
    private String ratioCode;

    /**
     * 证书状态
     */
    @ApiModelProperty(value = "证书状态")
    private String statusCode;


    /**
     * 法碳产地列表
     */
    @ApiModelProperty(value = "法碳产地列表")
    private List<CarbonOriginAddress> carbonOriginAddressList;

    /**
     * 法碳值 列表
     */
    @ApiModelProperty(value = "法碳值列表")
    private List<CarbonValue> carbonValueList;
}
