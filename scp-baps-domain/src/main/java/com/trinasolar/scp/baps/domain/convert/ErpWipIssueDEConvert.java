package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.ErpWipIssue;
import com.trinasolar.scp.baps.domain.dto.ErpWipIssueDTO;
import com.trinasolar.scp.baps.domain.excel.ErpWipIssueExcelDTO;
import com.trinasolar.scp.baps.domain.save.ErpWipIssueSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Erp实际入库来源表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:23:58
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ErpWipIssueDEConvert extends BaseDEConvert<ErpWipIssueDTO, ErpWipIssue> {

    ErpWipIssueDEConvert INSTANCE = Mappers.getMapper(ErpWipIssueDEConvert.class);

    List<ErpWipIssueExcelDTO> toExcelDTO(List<ErpWipIssueDTO> dtos);

    ErpWipIssueExcelDTO toExcelDTO(ErpWipIssueDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    ErpWipIssue saveDTOtoEntity(ErpWipIssueSaveDTO saveDTO, @MappingTarget ErpWipIssue entity);
}
