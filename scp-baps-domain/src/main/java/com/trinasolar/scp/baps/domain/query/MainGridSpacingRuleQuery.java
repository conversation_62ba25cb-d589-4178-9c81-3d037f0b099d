package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 电池主栅间距规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-17 08:55:14
 */
@Data
@ApiModel(value = "MainGridSpacingRule查询条件", description = "查询条件")
@Accessors(chain = true)
public class MainGridSpacingRuleQuery extends PageDTO implements Serializable {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;

    @ApiModelProperty(value = "电池类型名")
    private String batteryTypeName;

    /**
     * 组件车间
     */
    @ApiModelProperty(value = "组件车间")
    private String itemWorkshop;



    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    private String batteryWorkshop;



    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpacing;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;

    @ApiModelProperty(value = "校验日期开始")
    private LocalDate effectiveStartDate;

    @ApiModelProperty(value = "校验日期结束")
    private LocalDate effectiveStartEnd;

}
