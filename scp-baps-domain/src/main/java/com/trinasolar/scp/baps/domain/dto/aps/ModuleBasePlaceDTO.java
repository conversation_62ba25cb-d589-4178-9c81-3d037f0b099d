package com.trinasolar.scp.baps.domain.dto.aps;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 基础产地
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-08 14:33:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ModuleBasePlaceDTO对象", description = "DTO对象")
public class ModuleBasePlaceDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 是否国外
     */
    @ApiModelProperty(value = "是否国外")
    private String isOversea;
    /**
     * 产地信息
     */
    @ApiModelProperty(value = "产地信息")
    private String placeInfo;
    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private String inventoryOrganization;
    /**
     * ERP代码
     */
    @ApiModelProperty(value = "ERP代码")
    private String erpCode;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    @ApiModelProperty(value = "生产基地名称")
    private String basePlaceName;
    /**
     * 仓库
     */
    @ApiModelProperty(value = "仓库")
    private String warehouse;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 产线总数
     */
    @ApiModelProperty(value = "产线总数")
    private Integer totalLine;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
