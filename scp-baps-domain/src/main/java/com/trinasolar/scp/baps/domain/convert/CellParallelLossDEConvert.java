package com.trinasolar.scp.baps.domain.convert;
import com.trinasolar.scp.baps.domain.query.CellParallelLossQuery;
import com.trinasolar.scp.baps.domain.save.CellParallelLossSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellParallelLoss;
import com.trinasolar.scp.baps.domain.dto.CellParallelLossDTO;
import com.trinasolar.scp.baps.domain.excel.CellParallelLossExcelDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 产能并行损失表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellParallelLossDEConvert extends BaseDEConvert<CellParallelLossDTO, CellParallelLoss> {

    CellParallelLossDEConvert INSTANCE = Mappers.getMapper(CellParallelLossDEConvert.class);

    List<CellParallelLossExcelDTO> toExcelDTO(List<CellParallelLossDTO> dtos);

    CellParallelLossExcelDTO toExcelDTO(CellParallelLossDTO dto);
    @Override
    @Mappings(
            {
                    @Mapping(target = "parallelType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getParallelTypeId()))"),
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getCellsTypeId()))"),
                    @Mapping(target = "cellsTypeTwo" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getCellsTypeTwoId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getWorkshopid()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getWorkunitid()))")
            }
    )
    CellParallelLossDTO toDto(CellParallelLoss cellBaseCapacity);


    @Mappings(
            {
                    @Mapping(target = "parallelTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BAPS_PARALLET_LOSS_TYPE, excelDTO.getParallelType()).getLovLineId())"),
                    @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType()).getLovLineId())"),
                    @Mapping(target = "cellsTypeTwoId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsTypeTwo()).getLovLineId())"),
                    @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "workshopid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop()).getLovLineId())"),
                    @Mapping(target = "workunitid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit()).getLovLineId())")
            }
    )
    CellParallelLossSaveDTO excelDtoToSaveDto(CellParallelLossExcelDTO excelDTO);

    List<CellParallelLossSaveDTO> excelDtoToSaveDto(List<CellParallelLossExcelDTO> dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,query.getCellsType(),lang))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,query.getBasePlace(),lang))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,query.getWorkshop(),lang))"),
                    @Mapping(target = "parallelType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BAPS_PARALLET_LOSS_TYPE,query.getParallelType(),lang))")
            }
    )
    CellParallelLossQuery toCellParallelLossCNNameQuery(CellParallelLossQuery query, String lang);
    @Mappings(
            {
                    @Mapping(target = "parallelType" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BAPS_PARALLET_LOSS_TYPE, dto.getParallelTypeId()))"),
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsTypeId()))"),
                    @Mapping(target = "cellsTypeTwo" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsTypeTwoId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshopid()))"),
                    @Mapping(target = "workunit" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_UNIT, dto.getWorkunitid()))")
            }
    )
    @Named("toCnNameSaveDtoById")
    CellParallelLossSaveDTO toCnNameSaveDtoById(CellParallelLossSaveDTO dto);
    @IterableMapping(qualifiedByName = "toCnNameSaveDtoById")
    List<CellParallelLossSaveDTO> toCnNameSaveDtoById(List<CellParallelLossSaveDTO> dtos);
}
