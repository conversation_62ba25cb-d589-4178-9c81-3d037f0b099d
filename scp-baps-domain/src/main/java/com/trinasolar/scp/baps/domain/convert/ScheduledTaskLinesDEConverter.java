package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.ScheduledTaskLinesDTO;
import com.trinasolar.scp.baps.domain.entity.ScheduledTaskLines;
import com.trinasolar.scp.baps.domain.query.ScheduledTaskLinesQuery;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ScheduledTaskLinesDEConverter extends BaseDEConvert<ScheduledTaskLinesDTO, ScheduledTaskLines> {
    ScheduledTaskLinesDEConverter INSTANCE = Mappers.getMapper(ScheduledTaskLinesDEConverter.class);

    @Override
    ScheduledTaskLines toEntity(ScheduledTaskLinesDTO dto);

    @Override
    List<ScheduledTaskLines> toEntity(List<ScheduledTaskLinesDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "taskName" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BAPS_TASK_NAME,query.getTaskName()))"),
                    @Mapping(target = "status" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.PLAN_STATUS,query.getStatus()))"),
            }
    )
    ScheduledTaskLinesQuery toCNNameScheduledTaskLinesQuery(ScheduledTaskLinesQuery query);

    @Mappings(
            {
                    @Mapping(target = "taskName" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByCnNameLang(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BAPS_TASK_NAME,entity.getTaskName()))"),
                    @Mapping(target = "status" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByCnNameLang(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.PLAN_STATUS,entity.getStatus()))"),
            }
    )
   @Named("toDtoNameByCName")
    ScheduledTaskLinesDTO toDtoNameByCName(ScheduledTaskLines entity);
@IterableMapping(qualifiedByName = "toDtoNameByCName")
    List<ScheduledTaskLinesDTO> toDtoNameByCName(List<ScheduledTaskLines> entity);
}
