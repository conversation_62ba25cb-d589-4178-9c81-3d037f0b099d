package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.query.CellShippableLeadTimeQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellShippableLeadTime;
import com.trinasolar.scp.baps.domain.dto.CellShippableLeadTimeDTO;
import com.trinasolar.scp.baps.domain.excel.CellShippableLeadTimeExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellShippableLeadTimeSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 可发货计划提前期 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-08 06:14:56
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface
CellShippableLeadTimeDEConvert extends BaseDEConvert<CellShippableLeadTimeDTO, CellShippableLeadTime> {

    CellShippableLeadTimeDEConvert INSTANCE = Mappers.getMapper(CellShippableLeadTimeDEConvert.class);

    List<CellShippableLeadTimeExcelDTO> toExcelDTO(List<CellShippableLeadTimeDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "ratePercent" ,expression = "java(MapStrutUtil.addPercentageIgnoreZero(dto.getRate(),2))")
            }
    )
    CellShippableLeadTimeExcelDTO toExcelDTO(CellShippableLeadTimeDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    CellShippableLeadTime saveDTOtoEntity(CellShippableLeadTimeSaveDTO saveDTO, @MappingTarget CellShippableLeadTime entity);

    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(LovUtils.getName( entity.getCellTypeId() ))"),
                    @Mapping(target = "basePlace" ,expression = "java(LovUtils.getName( entity.getBasePlaceId() ))"),
                    @Mapping(target = "workShop" ,expression = "java(LovUtils.getName(  entity.getWorkShopId() ))"),
                    @Mapping(target = "workUnit" ,expression = "java(LovUtils.getName(  entity.getWorkUnitId() ))")
            }
    )
    @Override
    CellShippableLeadTimeDTO   toDto(CellShippableLeadTime entity);
    @Override
    List<CellShippableLeadTimeDTO>   toDto(List<CellShippableLeadTime> entitys);

    @Mappings(
            {
                    @Mapping(target = "cellTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellType()).getLovLineId())"),
                    @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "workShopId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkShop()).getLovLineId())"),
                    @Mapping(target = "workUnitId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkUnit()).getLovLineId())"),
                    @Mapping(target = "rate" ,expression = "java(MapStrutUtil.removePercentage(excelDTO.getRatePercent(),4))")
            }
    )
    CellShippableLeadTimeSaveDTO excelDtoToSaveDto(CellShippableLeadTimeExcelDTO excelDTO);
    List<CellShippableLeadTimeSaveDTO> excelDtoToSaveDto(List<CellShippableLeadTimeExcelDTO> dto);

    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellType(),lang))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace(),lang))"),
                    @Mapping(target = "workShop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP, query.getWorkShop(),lang))"),
                    @Mapping(target = "workUnit" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_UNIT, query.getWorkUnit(),lang))"),
            }
    )
    CellShippableLeadTimeQuery toCellShippableLeadTimeQuery(CellShippableLeadTimeQuery query, String lang);
    @Mappings(
            {
                    @Mapping(target = "cellType" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellTypeId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlaceId()))"),
                    @Mapping(target = "workShop" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkShopId()))"),
                    @Mapping(target = "workUnit" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_UNIT, dto.getWorkUnitId()))"),
            }
    )
    @Named("toCellShippableLeadTimeSaveDTOCnName")
     CellShippableLeadTimeSaveDTO toCellShippableLeadTimeSaveDTOCnName(CellShippableLeadTimeSaveDTO dto);
    @IterableMapping(qualifiedByName = "toCellShippableLeadTimeSaveDTOCnName")
    List<CellShippableLeadTimeSaveDTO> toCellShippableLeadTimeSaveDTOCnName(List<CellShippableLeadTimeSaveDTO> dtos);


}
