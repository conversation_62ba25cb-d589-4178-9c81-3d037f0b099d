package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;


/**
 * 电池良率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellFineExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 年份
     */
    @ExcelProperty(value = "年份")
    private Integer year;
    /**
     * 1月
     */
    @ExcelProperty(value = "1月")
    private String m1;
    /**
     * 2月
     */
    @ExcelProperty(value = "2月")
    private String m2;
    /**
     * 3月
     */
    @ExcelProperty(value = "3月")
    private String m3;
    /**
     * 4月
     */
    @ExcelProperty(value = "4月")
    private String m4;
    /**
     * 5月
     */
    @ExcelProperty(value = "5月")
    private String m5;
    /**
     * 6月
     */
    @ExcelProperty(value = "6月")
    private String m6;
    /**
     * 7月
     */
    @ExcelProperty(value = "7月")
    private String m7;
    /**
     * 8月
     */
    @ExcelProperty(value = "8月")
    private String m8;
    /**
     * 9月
     */
    @ExcelProperty(value = "9月")
    private String m9;
    /**
     * 10月
     */
    @ExcelProperty(value = "10月")
    private String m10;
    /**
     * 11月
     */
    @ExcelProperty(value = "11月")
    private String m11;
    /**
     * 12月
     */
    @ExcelProperty(value = "12月")
    private String m12;
}
