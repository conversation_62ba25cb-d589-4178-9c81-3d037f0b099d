package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 产能切换损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "产能切换损失表DTO对象", description = "DTO对象")
public class CellLossDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间id")
    private Long workshopid;
    /**
     * 起始月份
     */
    @ApiModelProperty(value = "起始月份")
    private String startMonth;
    /**
     * 结束月份
     */
    @ApiModelProperty(value = "结束月份")
    private String endMonth;
    /**
     * 电池类型1
     */
    @ApiModelProperty(value = "电池类型1")
    private String oldProduct;
    /**
     * 电池类型1
     */
    @ApiModelProperty(value = "电池类型1Id")
    private Long oldProductId;
    /**
     * 电池类型2
     */
    @ApiModelProperty(value = "电池类型2")
    private String newProduct;
    /**
     * 电池类型2
     */
    @ApiModelProperty(value = "电池类型2Id")
    private Long newProductId;
    /**
     * 损失时间(小时)
     */
    @ApiModelProperty(value = "损失时间(小时)")
    private BigDecimal lossTime;
    /**
     * 是否单玻1
     */
    @ApiModelProperty(value = "是否单玻1")
    private String oldIsSingleGlass;
    /**
     * 是否单玻2
     */
    @ApiModelProperty(value = "是否单玻2")
    private String newIsSingleGlass;
    /**
     * 是否小区域国家1
     */
    @ApiModelProperty(value = "是否小区域国家1")
    private String oldIsRegionalCountry;
    /**
     * 是否小区域国家2
     */
    @ApiModelProperty(value = "是否小区域国家2")
    private String newIsRegionalCountry;
    /**
     * 规格1
     */
    @ApiModelProperty(value = "规格1")
    private String specOne;
    /**
     * 规格2
     */
    @ApiModelProperty(value = "规格2")
    private String specTwo;

    @ApiModelProperty(value = "是否10.8_1")
    private String is1081;

    @ApiModelProperty(value = "是否10.8_2")
    private String is1082;

}
