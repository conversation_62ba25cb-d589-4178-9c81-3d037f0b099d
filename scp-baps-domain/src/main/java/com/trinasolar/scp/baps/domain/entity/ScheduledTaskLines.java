package com.trinasolar.scp.baps.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * *计划任务日志
 */
@Entity
@ToString
@Data
@Table(name = "baps_scheduled_task_lines")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_scheduled_task_lines SET is_deleted = 1 WHERE task_line_id = ?")
@SQLDeleteAll(sql = "UPDATE baps_scheduled_task_lines SET is_deleted = 1 WHERE task_line_id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ScheduledTaskLines extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "SnowflakeIdGeneratorConfig", strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "task_line_id")
    private Long taskLineId;

    @ApiModelProperty(value = "任务编号")
    @Column(name = "task_number")
    private String taskNumber;

    @ApiModelProperty(value = "任务名称")
    @Column(name = "task_name")
    private String taskName;

    @ApiModelProperty(value = "版本号")
    @Column(name = "versions")
    private String versions;

    @ApiModelProperty(value = "状态")
    @Column(name = "status")
    private String status;

    @ApiModelProperty(value = "提交时间")
    @Column(name = "request_date")
    private LocalDateTime requestDate;

    @ApiModelProperty(value = "完成时间")
    @Column(name = "complete_date")
    private LocalDateTime completeDate;

    @ApiModelProperty(value = "提交人")
    @Column(name = "request_by")
    private String requestBy;

    @ApiModelProperty(value = "运行日志")
    @Column(name = "log")
    private String log;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

}
