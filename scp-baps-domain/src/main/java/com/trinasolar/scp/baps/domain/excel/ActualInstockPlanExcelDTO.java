package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import java.io.Serializable;


/**
 * 入库数据（ERP实际入库、入库计划）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 02:47:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ActualInstockPlanExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 批次号
     */
    @ExcelProperty(value = "批次号")
    private String batchNo;
    /**
     * 数据类型
     */
    @ExcelProperty(value = "数据类型")
    private String dataType;
    /**
     * 订单表
     */
    @ExcelProperty(value = "订单表")
    private String orderCode;
    /**
     * 来源类型
     */
    @ExcelProperty(value = "来源类型")
    private String sourceType;
    /**
     * sourceTypeId
     */
    @ExcelProperty(value = "sourceTypeId")
    private Long sourceTypeId;
    /**
     * 需求版本号
     */
    @ExcelProperty(value = "需求版本号")
    private String demandVersion;
    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元Id
     */
    @ExcelProperty(value = "生产单元Id")
    private Long workunitId;
    /**
     * 生产线体
     */
    @ExcelProperty(value = "生产线体")
    private String lineName;
    /**
     * 产线数量
     */
    @ExcelProperty(value = "产线数量")
    private BigDecimal numberLine;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;
    /**
     * hTraceId
     */
    @ExcelProperty(value = "hTraceId")
    private Long hTraceId;
    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    private String aesthetics;
    /**
     * aestheticsId
     */
    @ExcelProperty(value = "aestheticsId")
    private Long aestheticsId;
    /**
     * 是否进行了透明双玻拆分
     */
    @ExcelProperty(value = "是否进行了透明双玻拆分")
    private Boolean isTransparentDoubleGlass;
    /**
     * transparentDoubleGlassId
     */
    @ExcelProperty(value = "transparentDoubleGlassId")
    private Long transparentDoubleGlassId;
    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String cellSource;
    /**
     * cellSourceId
     */
    @ExcelProperty(value = "cellSourceId")
    private Long cellSourceId;
    /**
     * 产品等级
     */
    @ExcelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * productionGradeId
     */
    @ExcelProperty(value = "productionGradeId")
    private Long productionGradeId;
    /**
     * 小区域国家
     */
    @ExcelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * regionalCountryId
     */
    @ExcelProperty(value = "regionalCountryId")
    private Long regionalCountryId;
    /**
     * 5A料号
     */
    @ExcelProperty(value = "5A料号")
    private String itemCode;
    /**
     * demandBasePlaceId
     */
    @ExcelProperty(value = "demandBasePlaceId")
    private Long demandBasePlaceId;
    /**
     * 需求地
     */
    @ExcelProperty(value = "需求地")
    private String demandBasePlace;
    /**
     * 是否电池特殊要求
     */
    @ExcelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 低阻
     */
    @ExcelProperty(value = "低阻")
    private String lowResistance;
    /**
     * cellMfrsId
     */
    @ExcelProperty(value = "cellMfrsId")
    private Long cellMfrsId;
    /**
     * 电池厂家
     */
    @ExcelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * silverPulpMfrsId
     */
    @ExcelProperty(value = "silverPulpMfrsId")
    private Long silverPulpMfrsId;
    /**
     * 银浆厂家
     */
    @ExcelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 需求数量
     */
    @ExcelProperty(value = "需求数量")
    private BigDecimal demandQty;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 投产月份
     */
    @ExcelProperty(value = "投产月份")
    private String month;
    /**
     * MV
     */
    @ExcelProperty(value = "MV")
    private BigDecimal cellMv;
    /**
     * 最終邮件确认发布的版本
     */
    @ExcelProperty(value = "最終邮件确认发布的版本")
    private String finalVersion;
    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String version;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 数量（片）拆分前的值
     */
    @ExcelProperty(value = "数量（片）拆分前的值")
    private BigDecimal oldQtyPc;
    /**
     * 数量（片）
     */
    @ExcelProperty(value = "数量（片）")
    private BigDecimal qtyPc;
    /**
     * 汇总明细行id
     */
    @ExcelProperty(value = "汇总明细行id")
    private Long demandSummaryLinesId;
    /**
     * siMfrsId
     */
    @ExcelProperty(value = "siMfrsId")
    private Long siMfrsId;
    /**
     * 硅料厂家
     */
    @ExcelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 是否进行了硅料厂家拆分
     */
    @ExcelProperty(value = "是否进行了硅料厂家拆分")
    private Boolean isSiMfrs;
    /**
     * 硅片厂家
     */
    @ExcelProperty(value = "硅片厂家")
    private String siliconMaterialManufacturer;
    /**
     * 网版厂家
     */
    @ExcelProperty(value = "网版厂家")
    private String screenPlateMfrs;
    /**
     * 起始效率
     */
    @ExcelProperty(value = "起始效率")
    private BigDecimal startEfficiency;
    /**
     * 最大分布效率
     */
    @ExcelProperty(value = "最大分布效率")
    private BigDecimal maxEfficiency;
    /**
     * 电池特殊单号
     */
    @ExcelProperty(value = "电池特殊单号")
    private String specialOrderNo;
    /**
     * 需求日期
     */
    @ExcelProperty(value = "需求日期")
    private LocalDate demandDate;
    /**
     * 是否已经进行硅片等级拆分
     */
    @ExcelProperty(value = "是否已经进行硅片等级拆分")
    private Boolean isWaferGrade;
    /**
     * waferGradeId
     */
    @ExcelProperty(value = "waferGradeId")
    private Long waferGradeId;
    /**
     * 硅片等级对应工单开立片源等级
     */
    @ExcelProperty(value = "硅片等级对应工单开立片源等级")
    private String waferGrade;
    /**
     * 是否进行了A-拆分
     */
    @ExcelProperty(value = "是否进行了A-拆分")
    private Boolean isASplit;
    /**
     * 加工类型优先级
     */
    @ExcelProperty(value = "加工类型优先级")
    private Integer processCategoryPriority;
    /**
     * processCategoryId
     */
    @ExcelProperty(value = "processCategoryId")
    private Long processCategoryId;
    /**
     * 加工类型
     */
    @ExcelProperty(value = "加工类型")
    private String processCategory;
    /**
     * 是否已经进行了加工类型拆分
     */
    @ExcelProperty(value = "是否已经进行了加工类型拆分")
    private Boolean isProcessCategory;
    /**
     * 是否进行手动加工类型指定
     */
    @ExcelProperty(value = "是否进行手动加工类型指定")
    private Boolean isHandProcessCategory;
    /**
     * gap
     */
    @ExcelProperty(value = "gap")
    private BigDecimal gap;
    /**
     * 需求说明
     */
    @ExcelProperty(value = "需求说明")
    private String demandRemark;
    /**
     * 分档规则
     */
    @ExcelProperty(value = "分档规则")
    private String gradeRule;
    /**
     * 验证标识
     */
    @ExcelProperty(value = "验证标识")
    private String verificationMark;
    /**
     * 需求来源
     */
    @ExcelProperty(value = "需求来源")
    private String demandSource;
    /**
     * 电池物料编码
     */
    @ExcelProperty(value = "电池物料编码")
    private String batteryMaterialCode;
    /**
     * 原来入库开始时间
     */
    @ExcelProperty(value = "原来入库开始时间")
    private LocalDateTime oldStartTime;
    /**
     * 原来入库结束时间
     */
    @ExcelProperty(value = "原来入库结束时间")
    private LocalDateTime oldEndTime;
    /**
     * 原排产ID
     */
    @ExcelProperty(value = "原排产ID")
    private Long fromId;
    /**
     * 拆前父Id
     */
    @ExcelProperty(value = "拆前父Id")
    private Long parentId;
    /**
     * 对应bbom中的Id
     */
    @ExcelProperty(value = "对应bbom中的Id")
    private Long bbomId;
    /**
     * 计划确认
     */
    @ExcelProperty(value = "计划确认")
    private Integer confirmPlan;
    /**
     * 原入库月份
     */
    @ExcelProperty(value = "原入库月份")
    private String oldMonth;
    /**
     * 哪个月排的
     */
    @ExcelProperty(value = "哪个月排的")
    private String scheduleMonth;
    /**
     * 片源级投产良率
     */
    @ExcelProperty(value = "片源级投产良率")
    private BigDecimal waferYieldRatio;
    /**
     * 片源级A-比例
     */
    @ExcelProperty(value = "片源级A-比例")
    private BigDecimal waferGradeRatio;
    /**
     * 主栅间距
     */
    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;
    /**
     * 是否H兼容
     */
    @ExcelProperty(value = "是否H兼容")
    private String hChangeFlag;
    /**
     * 记录良率
     */
    @ExcelProperty(value = "记录良率")
    private BigDecimal cellFine;
}
