package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 电池类型转化规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-11 01:42:00
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_type_rule")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_type_rule SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_type_rule SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellTypeRule extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 解析顺序
     */
    @ApiModelProperty(value = "解析顺序")
    @Column(name = "sort_no")
    private Integer sortNo;

    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    @Column(name = "module")
    private String module;

    /**
     * 说明
     */
    @ApiModelProperty(value = "说明")
    @Column(name = "illustrate")
    private String illustrate;

    /**
     * 解析字段
     */
    @ApiModelProperty(value = "解析字段")
    @Column(name = "field")
    private String field;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    @Column(name = "rule")
    private String rule;

    /**
     * 结果
     */
    @ApiModelProperty(value = "结果")
    @Column(name = "result")
    private String result;


}
