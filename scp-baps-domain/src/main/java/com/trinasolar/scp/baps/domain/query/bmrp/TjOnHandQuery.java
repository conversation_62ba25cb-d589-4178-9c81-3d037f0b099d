package com.trinasolar.scp.baps.domain.query.bmrp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(value = "TjOnHand查询条件", description = "TjOnHand查询条件")
public class TjOnHandQuery {
    private LocalDateTime inventoryDateFrom;
    private LocalDateTime inventoryDateTo;
    private Long itemId;
    private String status;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;
    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private List<Long> organizationIds;
    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private String organizationCode;
    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private List<String> organizationCodes;
    /**
     * 料号类型 4A或7A
     */
    @ApiModelProperty(value = "料号类型 4A或7A")
    private String itemType;
    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemCode;
    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private List<String> itemCodes;
    /**
     * 子库存集合
     */
    @ApiModelProperty(value = "子库存集合")
    private List<String> subInventoryCodeList;
    /**
     * 库存日期
     */
    @ApiModelProperty(value = "库存日期")
    private LocalDate localDate;


    private LocalDateTime inventoryDate;

    private LocalDateTime inventoryDateStart;

    private LocalDateTime inventoryDateEnd;
}
