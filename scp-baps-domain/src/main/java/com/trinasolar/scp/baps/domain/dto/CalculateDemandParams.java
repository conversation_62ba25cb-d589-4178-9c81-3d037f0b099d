package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "ERP实际入库、入库计划冲减需求计划参数", description = "ERP实际入库、入库计划冲减需求计划参数")
public class CalculateDemandParams {

    /**
     * 国内海外Id
     */
    @NotNull
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 月份
     */
    @NotEmpty
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 计划版本
     */
    @NotEmpty
    @ApiModelProperty(value = "计划版本")
    private String planVersion;

    /**
     * 实际覆盖日期
     */
    @NotNull
    @ApiModelProperty(value = "实际覆盖日期")
    private LocalDate actualCoverageDate;

    /**
     * 计划锁定日期
     */
    @NotNull
    @ApiModelProperty(value = "计划锁定日期")
    private LocalDate planLockDate;

    public String batchNo(){
        return String.valueOf(this.hashCode()&Integer.MAX_VALUE);
    }
}
