package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 车间优先度效率目标值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@ApiModel(value = "CellWorkshopPriorityTarget查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellWorkshopPriorityTargetQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 车间Id
         */
        @ApiModelProperty(value = "车间Id")
    private Long workshopId;
        /**
         * 车间
         */
        @ApiModelProperty(value = "车间")
    private String workshop;
        /**
         * 效率分布
         */
        @ApiModelProperty(value = "效率分布")
    private String efficiency;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
