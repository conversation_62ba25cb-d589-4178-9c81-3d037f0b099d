package com.trinasolar.scp.baps.domain.utils;
import com.ibm.dpf.base.core.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;

/**
 * 文件工具类
 *
 * <AUTHOR>
 * @date 2022年6月21日19:24:50
 */
@Slf4j
public class FileUtil {

    public static MultipartFile getMultipartFile(File file) {
        FileItem item = new DiskFileItemFactory().createItem("file"
                , MediaType.MULTIPART_FORM_DATA_VALUE
                , true
                , file.getName());
        try (InputStream input = new FileInputStream(file);
             OutputStream os = item.getOutputStream()) {
            // 流转移
            IOUtils.copy(input, os);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid file: " + e, e);
        }
        return new CommonsMultipartFile(item);
    }

    /**
     * 生成有日期的文件名
     * @param fileName
     * @return
     */
    public static String makeFileName(String fileName){
         return fileName + "_" + DateUtils.formatDate(new Date(), "yyyy-MM-dd+HH:mm:ss");
    }
}
