package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;


/**
 * 每日结存报表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-12 06:19:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellDailyBalanceExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 国内外Id
     */
    @ExcelProperty(value = "国内外Id")
    @ExcelIgnore
    private Long isOverseaId;
    /**
     * 国内外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    @ExcelIgnore
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellType;


    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 低碳
     */
    @ExcelProperty(value = "低碳")
    private String dt;
    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 数据来源版本
     */
    @ExcelProperty(value = "数据来源版本")
    @ExcelIgnore
    private String fromVersion;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 类别
     */
    @ExcelProperty(value = "类别")
    private String project;
    /**
     * 1号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "1号")
    private BigDecimal d1;
    /**
     * 2号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "2号")
    private BigDecimal d2;
    /**
     * 3号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "3号")
    private BigDecimal d3;
    /**
     * 4号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "4号")
    private BigDecimal d4;
    /**
     * 5号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "5号")
    private BigDecimal d5;
    /**
     * 6号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "6号")
    private BigDecimal d6;
    /**
     * 7号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "7号")
    private BigDecimal d7;
    /**
     * 8号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "8号")
    private BigDecimal d8;
    /**
     * 9号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "9号")
    private BigDecimal d9;
    /**
     * 10号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "10号")
    private BigDecimal d10;
    /**
     * 11号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "11号")
    private BigDecimal d11;
    /**
     * 12号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "12号")
    private BigDecimal d12;
    /**
     * 13号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "13号")
    private BigDecimal d13;
    /**
     * 14号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "14号")
    private BigDecimal d14;
    /**
     * 15号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "15号")
    private BigDecimal d15;
    /**
     * 16号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "16号")
    private BigDecimal d16;
    /**
     * 17号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "17号")
    private BigDecimal d17;
    /**
     * 18号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "18号")
    private BigDecimal d18;
    /**
     * 19号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "19号")
    private BigDecimal d19;
    /**
     * 20号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "20号")
    private BigDecimal d20;
    /**
     * 21号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "21号")
    private BigDecimal d21;
    /**
     * 22号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "22号")
    private BigDecimal d22;
    /**
     * 23号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "23号")
    private BigDecimal d23;
    /**
     * 24号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "24号")
    private BigDecimal d24;
    /**
     * 25号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "25号")
    private BigDecimal d25;
    /**
     * 26号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "26号")
    private BigDecimal d26;
    /**
     * 27号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "27号")
    private BigDecimal d27;
    /**
     * 28号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "28号")
    private BigDecimal d28;
    /**
     * 29号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "29号")
    private BigDecimal d29;
    /**
     * 30号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "30号")
    private BigDecimal d30;
    /**
     * 31号
     */
    @ContentStyle(dataFormat = 2)
    @ExcelProperty(value = "31号")
    private BigDecimal d31;
}
