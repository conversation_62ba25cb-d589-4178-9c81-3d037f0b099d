package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 电池外购计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:34
 */
@Data
@ApiModel(value = "OverseaPurchasePlan查询条件", description = "查询条件")
@Accessors(chain = true)
public class OverseaPurchasePlanQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 是否国内
     */
    @ApiModelProperty(value = "是否国内")
    private String isOversea;
    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;
    /**
     * 发货城市
     */
    @ApiModelProperty(value = "发货城市")
    private String city;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private List<String> cellTypeList;
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String cellNo;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
