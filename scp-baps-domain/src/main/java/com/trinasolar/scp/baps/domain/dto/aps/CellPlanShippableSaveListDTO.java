package com.trinasolar.scp.baps.domain.dto.aps;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 电池产出计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-22 09:59:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellProductionPlan保存参数", description = "保存参数")
public class CellPlanShippableSaveListDTO extends TokenDTO implements Serializable {

    /**
     * 是否海外
     */
    @ApiModelProperty(value = "是否海外")
    private String isOversea;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;

    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String cellNo;

    /**
     * 分档规则
     */
    @ApiModelProperty(value = "分档规则")
    private String rule;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 日期
     */
    @NotEmpty(message = "day 不可为空！")
    @ApiModelProperty(value = "日期")
    private Integer day;

    /**
     * 数量
     */
    @NotNull(message = "quantity 不可为空！")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 上线数量限制
     */
    @ApiModelProperty(value = "上线数量限制")
    private BigDecimal limitQuantity;
    /**
     * 确认的版本
     */
    @ApiModelProperty(value = "确认的版本")
    private String finalVersion;

}
