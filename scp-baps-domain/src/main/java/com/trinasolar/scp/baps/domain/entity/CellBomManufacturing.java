package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.hibernate.annotations.GenericGenerator;

/**
 * 制造BOM表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_bom_manufacturing")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_bom_manufacturing SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_bom_manufacturing SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellBomManufacturing extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;
    @ApiModelProperty(value = "ie爬坡产能标识0代表ie，1代表爬坡")
    @Column(name = "ieorgrade")
    private Integer ieorgrade;
    @ApiModelProperty(value = "数据来源id(IE或爬坡id)")
    @Column(name = "fromid")
    private Long fromid;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 产线总数
     */
    @ApiModelProperty(value = "产线总数")
    @Column(name = "total_line")
    private BigDecimal totalLine;

    /**
     * 可用产线数
     */
    @ApiModelProperty(value = "可用产线数")
    @Column(name = "usage_line")
    private BigDecimal usageLine;

    /**
     * 产能（单元）
     */
    @ApiModelProperty(value = "产能（单元）")
    @Column(name = "capacity_quantity")
    private BigDecimal capacityQuantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;
    /**
     * 能否生产单玻
     */
    @ApiModelProperty(value = "能否生产单玻")
    @Column(name = "is_single_glass")
    private String isSingleGlass;
    /**
     * 能否生产低碳
     */
    @ApiModelProperty(value = "能否生产低碳")
    @Column(name = "is_dt")
    private String isDt;
    /**
     * 能否生产小区域国家
     */
    @ApiModelProperty(value = "能否生产小区域国家")
    @Column(name = "is_regional_country")
    private String isRegionalCountry;
    /**
     * 能否生产H兼容
     */
    @ApiModelProperty(value = "能否生产H兼容")
    @Column(name = "is_h_change_flag")
    private String isHChangeFlag;
    /**
     * 能否生产H追溯
     */
    @ApiModelProperty(value = "能否生产H追溯")
    @Column(name = "is_h_trace")
    private String isHTrace;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 工序代码
     */
    @ApiModelProperty(value = "工序代码")
    @Column(name = "process_code")
    private String processCode;

    /**
     * 工序编号
     */
    @ApiModelProperty(value = "工序编号")
    @Column(name = "process_id")
    private Integer processId;

    /**
     * 使用类型
     */
    @ApiModelProperty(value = "使用类型")
    @Column(name = "instruction_type")
    private String instructionType;

    /**
     * 使用代码
     */
    @ApiModelProperty(value = "使用代码")
    @Column(name = "instruction_code")
    private String instructionCode;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "version")
    private String version;

    /**
     * 国内国外
     */
    @ApiModelProperty(value = "国内国外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    @Column(name = "line_name")
    private String lineName;
    /**
     * 制造
     */
    @ApiModelProperty(value = "制造")
    @Column(name = "manufacturing")
    private String manufacturing;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @Column(name = "rate")
    private Integer rate;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @Column(name = "start_date")
    private LocalDateTime startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @Column(name = "end_date")
    private LocalDateTime endDate;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @Column(name = "quantity")
    private BigDecimal quantity;

    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @Column(name = "flag")
    private Integer flag;

    /**
     * 符合率
     */
    @ApiModelProperty(value = "符合率")
    @Column(name = "fine_percent")
    private BigDecimal finePercent;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "grade_work_date")
    private String gradeWorkDate;
    /**
     * 可靠性验证
     */
    @ApiModelProperty(value = "可靠性验证")
    @Column(name = "reliability_check")
    private String reliabilityCheck;

}
