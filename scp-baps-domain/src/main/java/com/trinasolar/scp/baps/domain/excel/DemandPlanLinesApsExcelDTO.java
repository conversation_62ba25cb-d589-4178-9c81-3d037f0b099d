package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * 需求计划明细（APS）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 08:30:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DemandPlanLinesApsExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 批次号
     */
    @ExcelProperty(value = "批次号")
    private String batchNo;
    /**
     * 实际覆盖日期
     */
    @ExcelProperty(value = "实际覆盖日期")
    private LocalDate actualCoverageDate;
    /**
     * 计划锁定日期
     */
    @ExcelProperty(value = "计划锁定日期")
    private LocalDate planLockDate;
    /**
     * 后端提供给APS的唯一ID
     */
    @ExcelProperty(value = "后端提供给APS的唯一ID")
    private String dpId;
    /**
     * 电池需求计划号
     */
    @ExcelProperty(value = "电池需求计划号")
    private String demandPlanCode;
    /**
     * 来源类型
     */
    @ExcelProperty(value = "来源类型")
    private String sourceType;
    /**
     * 国内/海外名称
     */
    @ExcelProperty(value = "国内/海外名称")
    private String domesticOverseaName;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 电池物料编码
     */
    @ExcelProperty(value = "电池物料编码")
    private String batteryMaterialCode;
    /**
     * H追溯名称
     */
    @ExcelProperty(value = "H追溯名称")
    private String hTraceName;
    /**
     * 片源种类名称
     */
    @ExcelProperty(value = "片源种类名称")
    private String pcsSourceTypeName;
    /**
     * 透明双波名称
     */
    @ExcelProperty(value = "透明双波名称")
    private String transparentDoubleGlassName;
    /**
     * 美学名称
     */
    @ExcelProperty(value = "美学名称")
    private String aestheticsName;
    /**
     * 小区域国家名称
     */
    @ExcelProperty(value = "小区域国家名称")
    private String regionalCountryName;
    /**
     * 需求地名称
     */
    @ExcelProperty(value = "需求地名称")
    private String basePlaceName;
    /**
     * 电池厂家
     */
    @ExcelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * 银浆厂家
     */
    @ExcelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 硅料厂家
     */
    @ExcelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 网版厂家
     */
    @ExcelProperty(value = "网版厂家")
    private String screenPlateMfrs;
    /**
     * 起始效率
     */
    @ExcelProperty(value = "起始效率")
    private BigDecimal startEfficiency;
    /**
     * 电池特殊单号
     */
    @ExcelProperty(value = "电池特殊单号")
    private String specialOrderNo;
    /**
     * 需求日期
     */
    @ExcelProperty(value = "需求日期")
    private LocalDate demandDate;
    /**
     * 符合率
     */
    @ExcelProperty(value = "符合率")
    private BigDecimal passPercent;
    /**
     * 电池生产车间
     */
    @ExcelProperty(value = "电池生产车间")
    private String scheduleWorkshop;
    /**
     * 加工类型
     */
    @ExcelProperty(value = "加工类型")
    private String processCategoryName;
    /**
     * 需求数量(汇总)
     */
    @ExcelProperty(value = "需求数量(汇总)")
    private BigDecimal demandQty;
    /**
     * 外部需求ID
     */
    @ExcelProperty(value = "外部需求ID")
    private String exteriorDemandId;
    /**
     * H转换标记
     */
    @ExcelProperty(value = "H转换标记")
    private String hChangeFlag;
    /**
     * DT转换标记
     */
    @ExcelProperty(value = "DT转换标记")
    private String pcsSourceDtChangeFlag;
    /**
     * 主栅间距
     */
    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;
}
