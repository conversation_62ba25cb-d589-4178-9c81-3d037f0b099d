package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;

import org.hibernate.annotations.GenericGenerator;

/**
 * SOP数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-30 03:38:41
 */
@Entity
@ToString
@Data
@Table(name = "baps_qop_details")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_qop_details SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_qop_details SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class QopDetails extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内海外名
     */
    @ApiModelProperty(value = "国内海外名")
    @Column(name = "is_oversea_name")
    private String isOverseaName;

    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "workshop_id")
    private Long workshopId;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "workshop_name")
    private String workshopName;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cell_type_id")
    private Long cellTypeId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cell_type_name")
    private String cellTypeName;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @Column(name = "sop_qty")
    private BigDecimal sopQty;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "version")
    private String version;


}
