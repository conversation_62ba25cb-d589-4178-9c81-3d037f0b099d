package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;


/**
 * 工单生成预览
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "工单生成预览DTO对象", description = "DTO对象")
public class CellWipDTO extends BaseDTO {

 /**
  * ID主键
  */
 @ApiModelProperty(value = "ID主键")
 private Long id;
 /**
  * 工单号
  */
 @ApiModelProperty(value = "工单号")
 private String wipCode;
 /**
  * 分类
  */
 @ApiModelProperty(value = "分类")
 private String category;
 /**
  * 装配件
  */
 @ApiModelProperty(value = "装配件")
 private String itemCode;
 /**
  * 状态
  */
 @ApiModelProperty(value = "状态")
 private String state;

 /**
  * 状态名称
  */
 @ApiModelProperty(value = "状态名称")
 private String stateName;
 /**
  * 车间
  */
 @ApiModelProperty(value = "车间")
 private String workshop;
 /**
  * 起始时间
  */
 @ApiModelProperty(value = "起始时间")
 private LocalDateTime startTime;
 /**
  * 结束时间
  */
 @ApiModelProperty(value = "结束时间")
 private LocalDateTime endTime;
 /**
  * 项目编码
  */
 @ApiModelProperty(value = "项目编码")
 private String projectCode;
 /**
  * 工艺路线替代项
  */
 @ApiModelProperty(value = "工艺路线替代项")
 private String alternateRoutingDesignator;
 /**
  * bom替代项
  */
 @ApiModelProperty(value = "bom替代项")
 private String alternateBomDesignator;
 /**
  * 组织id
  */
 @ApiModelProperty(value = "组织id")
 private String organizationId;
 /**
  * 是否哈密瓜
  */
 @ApiModelProperty(value = "是否哈密瓜")
 private String isH;
 /**
  * 数量
  */
 @ApiModelProperty(value = "数量")
 private BigDecimal qty;
 /**
  * 保税形态
  */
 @ApiModelProperty(value = "保税形态")
 private String bondedForm;
 /**
  * 外协厂家
  */
 @ApiModelProperty(value = "外协厂家")
 private String outsourcingFactory;
 /**
  * 是否提前投产
  */
 @ApiModelProperty(value = "是否提前投产")
 private String isAdvanceProduction;
 /**
  * 是否外售
  */
 @ApiModelProperty(value = "是否外售")
 private String isSell;
 /**
  * 可靠性标识
  */
 @ApiModelProperty(value = "可靠性标识")
 private String reliability;
 /**
  * 验证日期
  */
 @ApiModelProperty(value = "验证日期")
 private LocalDateTime verificationDate;
 /**
  * 投产明细汇总
  */
 @ApiModelProperty(value = "投产明细汇总")
 private List<CellPlanLineDTO>cellPlanLineDTOList;
 /**
  * 月份
  */
 @ApiModelProperty(value = "月份")
 private String month;
 @ApiModelProperty(value = "最終邮件确认发布的版本")
 private String finalVersion;
 /**
  * 国内海外Id
  */
 @ApiModelProperty(value = "国内海外Id")
 private Long isOverseaId;
 /**
  * 国内海外
  */
 @ApiModelProperty(value = "国内海外")
 private String isOversea;
 /**
  * H追溯
  */
 @ApiModelProperty(value = "H追溯")
 private String hTrace;
 /**
  * 返工标识
  */
 @ApiModelProperty(value = "返工标识")
 private String reworkFlag;
 private String userId;
}
