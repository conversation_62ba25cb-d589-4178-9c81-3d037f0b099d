package com.trinasolar.scp.baps.domain.dto.bdm;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 需求计划明细（APS）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-21 11:42:06
 */
@Data
@ApiModel(value = "DemandPlanLinesAps查询条件", description = "查询条件")
@Accessors(chain = true)
public class DemandPlanLinesApsQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 后端提供给APS的唯一ID
     */
        @ApiModelProperty(value = "后端提供给APS的唯一ID")
    private String dpId;

        @ApiModelProperty(value = "后端提供给APS的唯一ID集合")
    private List<String> dpIds;

        @ApiModelProperty(value = "外部需求Id是是否为空的标记")
    private String exteriorFlag;
        /**
         * 电池需求计划号
         */
        @ApiModelProperty(value = "电池需求计划号")
    private String demandPlanCode;
        /**
         * 来源类型
         */
        @ApiModelProperty(value = "来源类型")
    private String sourceType;
        /**
         * 国内/海外名称
         */
        @ApiModelProperty(value = "国内/海外名称")
    private String domesticOverseaName;
        /**
         * 电池类型名称
         */
        @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
        /**
         * 电池物料编码
         */
        @ApiModelProperty(value = "电池物料编码")
    private String batteryMaterialCode;
        /**
         * H追溯名称
         */
        @ApiModelProperty(value = "H追溯名称")
    private String hTraceName;
        /**
         * 片源种类名称
         */
        @ApiModelProperty(value = "片源种类名称")
    private String pcsSourceTypeName;
        /**
         * 透明双波名称
         */
        @ApiModelProperty(value = "透明双波名称")
    private String transparentDoubleGlassName;
        /**
         * 美学名称
         */
        @ApiModelProperty(value = "美学名称")
    private String aestheticsName;
        /**
         * 小区域国家名称
         */
        @ApiModelProperty(value = "小区域国家名称")
    private String regionalCountryName;
        /**
         * 需求地名称
         */
        @ApiModelProperty(value = "需求地名称")
    private String basePlaceName;
        /**
         * 电池厂家
         */
        @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
        /**
         * 银浆厂家
         */
        @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
        /**
         * 硅料厂家
         */
        @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
        /**
         * 网版厂家
         */
        @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;
        /**
         * 起始效率
         */
        @ApiModelProperty(value = "起始效率")
    private BigDecimal startEfficiency;
        /**
         * 电池特殊单号
         */
        @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;
        /**
         * 需求日期
         */
        @ApiModelProperty(value = "需求日期")
    private LocalDate demandDate;
        /**
         * 符合率
         */
        @ApiModelProperty(value = "符合率")
    private BigDecimal passPercent;
        /**
         * 电池生产车间
         */
        @ApiModelProperty(value = "电池生产车间")
    private String scheduleWorkshop;
    /**
     * 加工类型
     */
        @ApiModelProperty(value = "加工类型")
    private String processCategoryName;
        /**
         * 需求数量(汇总)
         */
        @ApiModelProperty(value = "需求数量(汇总)")
    private BigDecimal demandQty;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endDate;
}
