package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 产能切换损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellLossExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 起始月份
     */
    @ExcelProperty(value = "起始月份")
    private String startMonth;
    /**
     * 结束月份
     */
    @ExcelProperty(value = "结束月份")
    private String endMonth;
    /**
     * 电池类型1
     */
    @ExcelProperty(value = "电池类型1")
    private String oldProduct;
    /**
     * 电池类型2
     */
    @ExcelProperty(value = "电池类型2")
    private String newProduct;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    @ExcelIgnore
    private String unit;
    /**
     * 是否单玻1
     */
    @ExcelProperty(value = "是否单玻1")
    private String oldIsSingleGlass;
    /**
     * 是否单玻2
     */
    @ExcelProperty(value = "是否单玻2")
    private String newIsSingleGlass;
    /**
     * 是否小区域国家1
     */
    @ExcelProperty(value = "是否小区域国家1")
    private String oldIsRegionalCountry;
    /**
     * 是否小区域国家2
     */
    @ExcelProperty(value = "是否小区域国家2")
    private String newIsRegionalCountry;
    /**
     * 损失时间(小时)
     */
    @ExcelProperty(value = "损失时间(小时)")
    private BigDecimal lossTime;

    @ExcelProperty(value = "是否10.8_1")
    private String is1081;

    @ExcelProperty(value = "是否10.8_2")
    private String is1082;
}
