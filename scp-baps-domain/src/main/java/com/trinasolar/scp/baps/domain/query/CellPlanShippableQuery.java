package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 可发货计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-09 10:05:37
 */
@Data
@ApiModel(value = "CellPlanShippable查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellPlanShippableQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 基地Id
         */
        @ApiModelProperty(value = "基地Id")
    private Long basePlaceId;
        /**
         * 基地
         */
        @ApiModelProperty(value = "基地")
    private String basePlace;
        /**
         * 车间Id
         */
        @ApiModelProperty(value = "车间Id")
    private Long workShopId;
        /**
         * 车间
         */
        @ApiModelProperty(value = "车间")
    private String workShop;
        /**
         * 单元Id
         */
        @ApiModelProperty(value = "单元Id")
    private Long workUnitId;
        /**
         * 单元
         */
        @ApiModelProperty(value = "单元")
    private String workUnit;
        /**
         * 电池类型Id
         */
        @ApiModelProperty(value = "电池类型Id")
    private Long cellTypeId;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellType;
        /**
         * 月份
         */
        @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
