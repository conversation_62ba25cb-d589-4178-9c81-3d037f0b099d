package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 计划与上一版本计划对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "计划与上一版本计划对比DTO对象", description = "DTO对象")
public class CellVersionPlanHistoryDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内/海外Id
     */
    @ApiModelProperty(value = "国内/海外Id")
    private Long isOverseaId;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 电池类型编号
     */
    @ApiModelProperty(value = "电池类型编号")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshopName;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 月份计划（上上版）
     */
    @ApiModelProperty(value = "月份计划（上上版）")
    private BigDecimal monthV0;
    /**
     * 月份计划（上版）
     */
    @ApiModelProperty(value = "月份计划（上版）")
    private BigDecimal monthV1;
    /**
     * 月份计划新版
     */
    @ApiModelProperty(value = "月份计划新版")
    private BigDecimal monthV2;
    /**
     * 差异
     */
    @ApiModelProperty(value = "差异")
    private BigDecimal gap;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    public CellVersionPlanHistoryDTO(Long isOverseaId, String isOverseaName, Long cellTypeId, String cellTypeName, Long workshopId, String workshopName, String hTrace, String aesthetics, String transparentDoubleGlass, String cellSource, String regionalCountry, String month) {
        this.isOverseaId = isOverseaId;
        this.isOverseaName = isOverseaName;
        this.cellTypeId = cellTypeId;
        this.cellTypeName = cellTypeName;
        this.workshopId = workshopId;
        this.workshopName = workshopName;
        this.hTrace = hTrace;
        this.aesthetics = aesthetics;
        this.transparentDoubleGlass = transparentDoubleGlass;
        this.cellSource = cellSource;
        this.regionalCountry = regionalCountry;
        this.month = month;
    }

    public CellVersionPlanHistoryDTO groupDetail(){
        return new CellVersionPlanHistoryDTO(this.isOverseaId, this.isOverseaName, this.cellTypeId, this.cellTypeName, this.workshopId, this.workshopName, this.hTrace, this.aesthetics, this.transparentDoubleGlass, this.cellSource, this.regionalCountry, this.month);
    }

    public CellVersionPlanHistoryDTO groupCellType(){
        CellVersionPlanHistoryDTO history = new CellVersionPlanHistoryDTO();
        history.setCellTypeId(this.cellTypeId);
        history.setCellTypeName(this.cellTypeName);
        history.setMonth(this.month);
        return history;
    }

    public CellVersionPlanHistoryDTO groupIsOversea(){
        CellVersionPlanHistoryDTO history = new CellVersionPlanHistoryDTO();
        history.setWorkshopId(this.isOverseaId);
        history.setWorkshopName(this.isOverseaName);
        history.setMonth(this.month);
        return history;
    }

}
