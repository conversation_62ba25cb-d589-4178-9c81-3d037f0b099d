package com.trinasolar.scp.baps.domain.query.aps;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 电池档位对应关系
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Data
@ApiModel(value = "RecordTransition查询条件", description = "查询条件")
@Accessors(chain = true)
public class RecordTransitionQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 是否海外
     */
    @ApiModelProperty(value = "是否海外")
    private String isOversea;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private List<String> cellTypeList=new ArrayList<>();
    /**
     * 电池等级
     */
    @ApiModelProperty(value = "电池等级")
    private String cellGrade;
    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;
    /**
     * 分档规则
     */
    @ApiModelProperty(value = "分档规则")
    private String rule;
    /**
     * 有效起始时间
     */
    @ApiModelProperty(value = "有效起始时间")
    private LocalDate startTime;
    /**
     * 有效截至时间
     */
    @ApiModelProperty(value = "有效截至时间")
    private LocalDate endTime;
    /**
     * 替换版本
     */
    @ApiModelProperty(value = "替换版本")
    private String replaceRule;
    /**
     * 替换原因
     */
    @ApiModelProperty(value = "替换原因")
    private String reason;
    /**
     * 降档规则
     */
    @ApiModelProperty(value = "降档规则")
    private String downshiftRule;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
