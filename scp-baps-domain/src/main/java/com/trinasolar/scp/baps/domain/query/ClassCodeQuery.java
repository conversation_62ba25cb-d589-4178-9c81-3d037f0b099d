package com.trinasolar.scp.baps.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * erp工单分类查询对象
 *
 * <AUTHOR>
 * @date 2022年9月18日17:20:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ClassCodeQuery查询条件", description = "DP实际排产 查询条件")
@Accessors(chain = true)
public class ClassCodeQuery implements Serializable {
    /**
     * 车间
     */
    @NotBlank
    @ApiModelProperty(value = "workshop")
    private String workshop;
    /**
     * 工单类型
     */
    @ApiModelProperty(value = "工单类型")
    private String classType;
    /**
     * organizationId
     */
    @ApiModelProperty(value = "organizationId")
    private Long organizationId;
}
