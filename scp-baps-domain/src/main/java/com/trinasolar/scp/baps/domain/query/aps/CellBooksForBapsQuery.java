package com.trinasolar.scp.baps.domain.query.aps;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 帐套信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Data
@ApiModel(value = "CellBooks查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellBooksForBapsQuery extends PageDTO implements Serializable {

        /**
         * 主键
         */
        @ApiModelProperty(value = "主键")
    private Long id;
        /**
         * 国内/海外
         */
        @ApiModelProperty(value = "国内/海外")
    private String isOversea;
        /**
         * 车间
         */
        @ApiModelProperty(value = "车间")
    private String workshop;
        /**
         * 帐套类型:电池/组件/组件&电池
         */
        @ApiModelProperty(value = "帐套类型:电池/组件/组件&电池")
    private List<String> booksTypeList;
        /**
         * 账套
         */
        @ApiModelProperty(value = "账套")
    private String books;
        /**
         * SO
         */
        @ApiModelProperty(value = "SO")
    private String so;
        /**
         * ERP代码
         */
        @ApiModelProperty(value = "ERP代码")
    private List<String> erpCodeList;
        /**
         * 组件货位
         */
        @ApiModelProperty(value = "组件货位")
    private String moduleSlotting;
        /**
         * 电池货位
         */
        @ApiModelProperty(value = "电池货位")
    private String cellSlotting;
        /**
         * 厂区
         */
        @ApiModelProperty(value = "厂区")
    private String factory;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
