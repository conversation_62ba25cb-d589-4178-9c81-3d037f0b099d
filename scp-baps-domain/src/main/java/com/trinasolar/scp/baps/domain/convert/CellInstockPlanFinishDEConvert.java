package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellInstockPlanFinishTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanFinish;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanFinishDTO;
import com.trinasolar.scp.baps.domain.excel.CellInstockPlanFinishExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanFinishSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 实际入库表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,imports = {
        LovHeaderCodeConstant.class,
        MapStrutUtil.class
}
)
public interface CellInstockPlanFinishDEConvert extends BaseDEConvert<CellInstockPlanFinishDTO, CellInstockPlanFinish> {

    CellInstockPlanFinishDEConvert INSTANCE = Mappers.getMapper(CellInstockPlanFinishDEConvert.class);

    List<CellInstockPlanFinishExcelDTO> toExcelDTO(List<CellInstockPlanFinishDTO> dtos);

    CellInstockPlanFinishExcelDTO toExcelDTO(CellInstockPlanFinishDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellInstockPlanFinish saveDTOtoEntity(CellInstockPlanFinishSaveDTO saveDTO, @MappingTarget CellInstockPlanFinish entity);
    @Mappings({
            @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellsType()))"),
            @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOversea()))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlace()))"),
            @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshop()))"),
            @Mapping(target = "HTrace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,dto.getHTrace()))"),
            @Mapping(target = "cellSource" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_SOURCE,dto.getCellSource()))"),
            @Mapping(target = "aesthetics" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,dto.getAesthetics()))"),
            @Mapping(target = "productionGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,dto.getProductionGrade()))"),
            @Mapping(target = "transparentDoubleGlass" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,dto.getTransparentDoubleGlass()))")

    })
    @Named("toCellInstockPlanFinishTotalNameByCnNameDTO")
    CellInstockPlanFinishTotalDTO toCellInstockPlanFinishTotalNameByCnNameDTO(CellInstockPlanFinishTotalDTO dto);

    @IterableMapping(qualifiedByName = "toCellInstockPlanFinishTotalNameByCnNameDTO")
    List<CellInstockPlanFinishTotalDTO> toCellInstockPlanFinishTotalNameByCnNameDTO(List<CellInstockPlanFinishTotalDTO> dtos);
}
