package com.trinasolar.scp.baps.domain.dto;

import com.google.common.collect.Lists;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.excel.ExcelHeader;
import com.trinasolar.scp.common.api.annotation.ConvertType;
import com.trinasolar.scp.common.api.annotation.ImportConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;


/**
 * 投产计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class HTraceLineInputDTO {


    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ExcelHeader(index = 0)
    @ImportConvert(lovCode = LovHeaderCodeConstant.DOMESTIC_OVERSEA, convertType = ConvertType.ID, convertTargetFieldName = "isOverseaId")
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ExcelHeader(index = 1)
    @ImportConvert(lovCode = LovHeaderCodeConstant.BASE_PLACE, convertType = ConvertType.ID, convertTargetFieldName = "basePlaceId")
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ExcelHeader(index = 2)
    @ImportConvert(lovCode = LovHeaderCodeConstant.WORK_SHOP, convertType = ConvertType.ID, convertTargetFieldName = "workshopId")
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 电池类型
     */
    @ExcelHeader(index = 3)
    @ImportConvert(lovCode = LovHeaderCodeConstant.BATTERY_TYPE, convertType = ConvertType.ID, convertTargetFieldName = "cellsTypeId")
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * H追溯
     */
    @ExcelHeader(index = 10)
    @ImportConvert(lovCode = LovHeaderCodeConstant.H_TRACE, convertType = ConvertType.ID, convertTargetFieldName = "hTraceId")
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * H追溯Id
     */
    @ApiModelProperty(value = "H追溯Id")
    private Long hTraceId;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 片源种类
     */
    @ExcelHeader(index = 4)
    @ImportConvert(lovCode = LovHeaderCodeConstant.CELL_SOURCE, convertType = ConvertType.ID, convertTargetFieldName = "cellSourceId")
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 片源种类ID
     */
    @ApiModelProperty(value = "片源种类ID")
    private Long cellSourceId;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 是否电池特殊要求
     */
    @ExcelHeader(index = 5)
    @ApiModelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 硅料厂家
     */
    @ExcelHeader(index = 6)
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 硅料厂家ID
     */
    @ApiModelProperty(value = "硅料厂家ID")
    private Long siMfrsId;
    /**
     * 计划版本
     */
    @ExcelHeader(index = 7)
    @ApiModelProperty(value = "计划版本")
    private String version;
    /**
     * 实际月份
     */
    @ExcelHeader(index = 8)
    @ApiModelProperty(value = "实际月份")
    private String month;
    /**
     * 排产月份
     */
    @ExcelHeader(index = 9)
    @ApiModelProperty(value = "排产月份")
    private String oldMonth;
    /**
     * MV
     */
    @ApiModelProperty(value = "MV")
    private BigDecimal cellMv;
    /**
     * 万片数
     */
    @ApiModelProperty(value = "万片数")
    private BigDecimal qtyThousandPc;

    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    private String itemCode;

    /**
     * 是否H兼容
     */
    @ExcelHeader(index = 11)
    @ApiModelProperty(value = "是否H兼容")
    private String hChangeFlag;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String productCategory;
    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    private String crystalType;
    /**
     * 排产日期
     */
    @ApiModelProperty(value = "排产日期")
    private LocalDate planDate;
    /**
     * 日
     */
    @ApiModelProperty(value = "日")
    private Integer day;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    public String sign(){
        return StringUtils.join(this.isOverseaId, this.basePlaceId, this.workshopId, this.cellsTypeId, this.cellSourceId, this.isSpecialRequirement, this.siMfrs, this.day);
    }

    public void convertDate() {
        LocalDate localDate = DateUtil.getLocalDate(month, day);
        this.planDate = localDate;
    }

    public HTraceDimension dimension() {
        return new HTraceDimension(this.isOverseaId, this.getOldMonth(), this.version);
    }


    public static List<HTraceLineInputDTO> build(HTraceLineDTO line) {
        List<HTraceLineInputDTO> resultList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(line.getSubMap())) {
            line.getSubMap().forEach((day, quantity) -> {
                HTraceLineInputDTO input = new HTraceLineInputDTO();
                BeanUtils.copyProperties(line, input);
                input.setDay(Integer.valueOf(day));
                input.setQuantity(quantity);
                resultList.add(input);
            });
        }
        return resultList;
    }


}
