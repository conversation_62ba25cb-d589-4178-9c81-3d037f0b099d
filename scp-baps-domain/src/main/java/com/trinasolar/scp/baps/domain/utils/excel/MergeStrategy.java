package com.trinasolar.scp.baps.domain.utils.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 导出合并策略
 *
 * <AUTHOR>
 * @date 2022年9月21日15:08:41
 */
@Slf4j
public class MergeStrategy extends AbstractMergeStrategy {
    //合并坐标集合
    private List<CellRangeAddress> cellRangeAddresses;

    public MergeStrategy(ExcelMain excelMain) {
        List<CellRangeAddress> list = new ArrayList<>();

        // 判断是否需要合并，需要合并的再构造合并的地址
        boolean needMerge = false;
        for (ExcelHead headVO : excelMain.getExcelHeads()) {
            if (CollectionUtils.isNotEmpty(headVO.getChildren())) {
                needMerge = true;
            }
        }
        if (needMerge) {
            // 重新定义head
            List<ExcelHead> heads = new ArrayList<>();
            for (ExcelHead headVO : excelMain.getExcelHeads()) {
                if (CollectionUtils.isNotEmpty(headVO.getChildren())) {
                    for (ExcelHead child : headVO.getChildren()) {
                        heads.add(child);
                    }
                } else {
                    heads.add(headVO);
                }
            }
            if (CollectionUtils.isNotEmpty(excelMain.getData())) {
                for (int i = 0; i < excelMain.getData().size(); i++) {
                    Map<String, Object> data = excelMain.getData().get(i);
                    System.out.println("第" + i + "行数据" + data);

                    for (int j = 0; j < heads.size(); j++) {
                        ExcelHead head = heads.get(j);

                        int cols = (int) data.getOrDefault("cols", 1);
                        int rows = (int) data.getOrDefault("rows", 1);
                        // 判断有没有孩子，有孩子的不用合并，没孩子的都要合并，根据所占列数合并
                        if (rows > 1 && head.isMerge()) {
                            System.out.print(head.getLabel());
                            log.info("{}, {} , {}, {}", i + 1 + 1, i + 1 + rows, j, j);
                            // row 行，col列
                            // int firstRow, int lastRow, int firstCol, int lastCol
                            CellRangeAddress item = new CellRangeAddress(i + 1 + 1, i + 1 + rows, j, j);
                            list.add(item);
                        }
                    }
                    System.out.println();
                }
            }
        }

        this.cellRangeAddresses = list;
    }

    /**
     * 合并操作：对每个单元格执行！！！
     *
     * @param sheet            sheet对象
     * @param cell             当前单元格
     * @param head             表头对象
     * @param relativeRowIndex 相关行索引
     */
    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        /*
         * 合并单元格
         *
         * 由于merge()方法会在写每个单元格(cell)时执行，因此需要保证合并策略只被添加一次。否则如果每个单元格都添加一次
         * 合并策略，则会造成重复合并。例如合并A2:A3,当cell为A2时，合并A2:A3，但是当cell为A3时，又要合并A2:A3，而此时
         * 的A2已经是之前的A2和A3合并后的结果了。
         * 由于此处的表头占了两行，因此数据单元格是从(2, 0)开始的，所以就对这个单元格(cell.getRowIndex() == 2 && cell.getColumnIndex() == 0)
         * 添加一次合并策略就可以了。如果表头只有一行，则判断条件改为「cell.getRowIndex() == 1 && cell.getColumnIndex() == 0」就可以了。
         */
        if (cell.getRowIndex() == 2 && cell.getColumnIndex() == 0) {
            for (CellRangeAddress item : cellRangeAddresses) {
                sheet.addMergedRegion(item);
            }
        }

        /*
         * 如果不作判断，可以使用addMergedRegionUnsafe()方法，
         * 这样生成的Excel文件可以打开，只是打开时会提示内容有问题，修复后可以打开
         */
        // for (CellRangeAddress item : cellRangeAddresses) {
        //     sheet.addMergedRegionUnsafe(item);
        // }
    }
}

