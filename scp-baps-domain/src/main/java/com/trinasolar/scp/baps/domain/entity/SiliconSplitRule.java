package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 硅片拆分规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 07:44:54
 */
@Entity
@ToString
@Data
@Table(name = "baps_silicon_split_rule")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_silicon_split_rule SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_silicon_split_rule SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class SiliconSplitRule extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cell_type_id")
    private Long cellTypeId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cell_type")
    private String cellType;

    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshop_id")
    private Long workshopId;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    @Column(name = "wafer_grade")
    private String waferGrade;
    /**
     * 硅片等级Id
     */
    @ApiModelProperty(value = "硅片等级Id")
    @Column(name = "wafer_grade_id")
    private String waferGradeId;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    @Column(name = "si_mfrs")
    private String siMfrs;
    /**
     * 硅料厂家Id
     */
    @ApiModelProperty(value = "硅料厂家Id")
    @Column(name = "si_mfrs_id")
    private String siMfrsId;
    /**
     * 电池良率
     */
    @ApiModelProperty(value = "电池良率")
    @Column(name = "cell_fine")
    private BigDecimal cellFine;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @Column(name = "start_date")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_date")
    private LocalDate endDate;
    /**
     * 分片规则类型
     */
    @ApiModelProperty(value = "分片规则类型")
    @Column(name = "rule_type")
    private String ruleType;


}
