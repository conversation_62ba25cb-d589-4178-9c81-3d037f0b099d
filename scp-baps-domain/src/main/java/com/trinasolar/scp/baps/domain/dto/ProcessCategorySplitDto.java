package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value = "加工类型拆分DTO", description = "DTO对象")
public class ProcessCategorySplitDto {
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
   // @ApiModelProperty(value = "生产基地")
  //  private String basePlace;

    /**
     * 生产车间
     */
   // @ApiModelProperty(value = "生产车间")
   // private String workshop;

    /**
     * 电池类型
     */
  //  @ApiModelProperty(value = "电池类型",notes ="要进行拆分的电池类型")
 //   private String cellsType;

}
