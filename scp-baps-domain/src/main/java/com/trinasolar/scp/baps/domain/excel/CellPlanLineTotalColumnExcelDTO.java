package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 投产计划表头
 *
 * <AUTHOR>
 * @email
 * @date 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellPlanLineTotalColumnExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    @ExcelIgnore
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内/海外")
    private String isOversea;

    @ExcelProperty(value = "需求来源")
    private String sourceType;

    @ExcelProperty(value = "更改状态")
    private String changeStatusDesc;

    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    @ExcelIgnore
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    @ExcelIgnore
    private Long workshopId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "组件电池类型")
    private String componentCellsType;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    @ExcelIgnore
    private Long cellsTypeId;
    /**
     * 5A料号
     */
    @ExcelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 背面细栅
     */
    @ExcelProperty(value = "背面细栅")
    private String backFineGrid;
    /**
     * 正面细栅
     */
    @ExcelProperty(value = "正面细栅")
    private String frontFineGrid;
    /**
     * 硅片厚度
     */
    @ExcelProperty(value = "硅片厚度")
    private String siliconWaferThickness;
    /**
     * 硅片尺寸
     */
    @ExcelProperty(value = "硅片尺寸")
    private String siliconWaferSize;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 是否H兼容
     */
    @ExcelProperty(value = "是否H兼容")
    private String hChangeFlag;
    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 小区域国家
     */
    @ExcelProperty(value = "小区域国家")
    private String regionalCountry;

    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    private String aesthetics;

    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;
    /**
     * 产品等级
     */
    @ExcelProperty(value = "产品等级")
    private String productionGrade;


    /**
     * MV
     */
    @ExcelProperty(value = "MW")
    private BigDecimal cellMv;
    /**
     * 万片
     */
    @ExcelProperty(value = "万片")
    @ContentStyle(dataFormat = 2)
    private BigDecimal qtyThousandPc;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    @ExcelIgnore
    private String version;
    /**
     * 投产表数据版本
     */
    @ExcelProperty(value = "投产表数据版本")
    @ExcelIgnore
    private String fromVersion;
}
