package com.trinasolar.scp.baps.domain.dto.aps;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * AOP供应能力维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:28
 */
@Data
@ApiModel(value = "PowerSupplyAop查询条件", description = "查询条件")
@Accessors(chain = true)
public class PowerSupplyAopQuery extends PageDTO implements Serializable {
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private List<String> cellTypeList;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private List<String> monthList;

    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;

    /**
     * 供应类型:自产/外购
     */
    @ApiModelProperty(value = "供应类型:自产/外购")
    private String supplyType;

    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal supplyQuantity;


    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private List<String> yearList;

    /**
     * 是否合并A-数据
     */
    @ApiModelProperty(value = "是否合并A-数据")
    private String combine;

    /**
     * 数据版本
     */
    @ApiModelProperty(value = "数据版本")
    private String dataVersion;



    private ExcelPara excelPara;
}
