package com.trinasolar.scp.baps.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 可发货计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-09 10:05:37
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_plan_shippable")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_plan_shippable SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_plan_shippable SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellPlanShippable extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 基地Id
     */
    @ApiModelProperty(value = "基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 车间Id
     */
    @ApiModelProperty(value = "车间Id")
    @Column(name = "work_shop_id")
    private Long workShopId;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "work_shop")
    private String workShop;

    /**
     * 单元Id
     */
    @ApiModelProperty(value = "单元Id")
    @Column(name = "work_unit_id")
    private Long workUnitId;

    /**
     * 单元
     */
    @ApiModelProperty(value = "单元")
    @Column(name = "work_unit")
    private String workUnit;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cell_type_id")
    private Long cellTypeId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cell_type")
    private String cellType;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * d1
     */
    @ApiModelProperty(value = "d1")
    @Column(name = "d1")
    private BigDecimal d1;

    /**
     * d2
     */
    @ApiModelProperty(value = "d2")
    @Column(name = "d2")
    private BigDecimal d2;

    /**
     * d3
     */
    @ApiModelProperty(value = "d3")
    @Column(name = "d3")
    private BigDecimal d3;

    /**
     * d4
     */
    @ApiModelProperty(value = "d4")
    @Column(name = "d4")
    private BigDecimal d4;

    /**
     * d5
     */
    @ApiModelProperty(value = "d5")
    @Column(name = "d5")
    private BigDecimal d5;

    /**
     * d6
     */
    @ApiModelProperty(value = "d6")
    @Column(name = "d6")
    private BigDecimal d6;

    /**
     * d7
     */
    @ApiModelProperty(value = "d7")
    @Column(name = "d7")
    private BigDecimal d7;

    /**
     * d8
     */
    @ApiModelProperty(value = "d8")
    @Column(name = "d8")
    private BigDecimal d8;

    /**
     * d9
     */
    @ApiModelProperty(value = "d9")
    @Column(name = "d9")
    private BigDecimal d9;

    /**
     * d10
     */
    @ApiModelProperty(value = "d10")
    @Column(name = "d10")
    private BigDecimal d10;

    /**
     * d11
     */
    @ApiModelProperty(value = "d11")
    @Column(name = "d11")
    private BigDecimal d11;

    /**
     * d12
     */
    @ApiModelProperty(value = "d12")
    @Column(name = "d12")
    private BigDecimal d12;

    /**
     * d13
     */
    @ApiModelProperty(value = "d13")
    @Column(name = "d13")
    private BigDecimal d13;

    /**
     * d14
     */
    @ApiModelProperty(value = "d14")
    @Column(name = "d14")
    private BigDecimal d14;

    /**
     * d15
     */
    @ApiModelProperty(value = "d15")
    @Column(name = "d15")
    private BigDecimal d15;

    /**
     * d16
     */
    @ApiModelProperty(value = "d16")
    @Column(name = "d16")
    private BigDecimal d16;

    /**
     * d17
     */
    @ApiModelProperty(value = "d17")
    @Column(name = "d17")
    private BigDecimal d17;

    /**
     * d18
     */
    @ApiModelProperty(value = "d18")
    @Column(name = "d18")
    private BigDecimal d18;

    /**
     * d19
     */
    @ApiModelProperty(value = "d19")
    @Column(name = "d19")
    private BigDecimal d19;

    /**
     * d20
     */
    @ApiModelProperty(value = "d20")
    @Column(name = "d20")
    private BigDecimal d20;

    /**
     * d21
     */
    @ApiModelProperty(value = "d21")
    @Column(name = "d21")
    private BigDecimal d21;

    /**
     * d22
     */
    @ApiModelProperty(value = "d22")
    @Column(name = "d22")
    private BigDecimal d22;

    /**
     * d23
     */
    @ApiModelProperty(value = "d23")
    @Column(name = "d23")
    private BigDecimal d23;

    /**
     * d24
     */
    @ApiModelProperty(value = "d24")
    @Column(name = "d24")
    private BigDecimal d24;

    /**
     * d25
     */
    @ApiModelProperty(value = "d25")
    @Column(name = "d25")
    private BigDecimal d25;

    /**
     * d26
     */
    @ApiModelProperty(value = "d26")
    @Column(name = "d26")
    private BigDecimal d26;

    /**
     * d27
     */
    @ApiModelProperty(value = "d27")
    @Column(name = "d27")
    private BigDecimal d27;

    /**
     * d28
     */
    @ApiModelProperty(value = "d28")
    @Column(name = "d28")
    private BigDecimal d28;

    /**
     * d29
     */
    @ApiModelProperty(value = "d29")
    @Column(name = "d29")
    private BigDecimal d29;

    /**
     * d30
     */
    @ApiModelProperty(value = "d30")
    @Column(name = "d30")
    private BigDecimal d30;

    /**
     * d31
     */
    @ApiModelProperty(value = "d31")
    @Column(name = "d31")
    private BigDecimal d31;


}
