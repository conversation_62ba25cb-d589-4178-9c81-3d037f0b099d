package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 手动设置加工类型DTO集合
 */
@ApiModel(value = "手动设置加工类型DTO集合", description = "DTO对象集合")
@Data
public class HandSetProcessCategoryListDto {

     private ProcessCategorySplitDto processCategorySplitDto;
    private List<HandSetProcessCategoryDto> dtos=new ArrayList<>();
}
