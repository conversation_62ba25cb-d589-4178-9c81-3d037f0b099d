package com.trinasolar.scp.baps.domain.query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@ApiModel(value = "硅片拆分条件", description = "硅片拆分条件")
@Accessors(chain = true)
public class SiliconSplitQuery {

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 硅片拆分类型
     */
    @ApiModelProperty(value = "硅片拆分类型")
    private String type;
    /**
     * 是否覆盖
     */
    @ApiModelProperty(value = "是否覆盖（只有硅料厂商和硅片等级此参数需要传递，其它情况不用）")
    private boolean isCover=false;


}
