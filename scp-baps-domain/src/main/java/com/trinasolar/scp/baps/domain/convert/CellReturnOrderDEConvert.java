package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellReturnOrderDTO;
import com.trinasolar.scp.baps.domain.dto.aps.ReturnDataSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellReturnOrder;
import com.trinasolar.scp.baps.domain.excel.CellReturnOrderExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellReturnOrderQuery;
import com.trinasolar.scp.baps.domain.save.CellReturnOrderSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 返司 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Mapper(componentModel = "spring",imports = {LovHeaderCodeConstant.class, MapStrutUtil.class, LovUtils.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellReturnOrderDEConvert extends BaseDEConvert<CellReturnOrderDTO, CellReturnOrder> {

    CellReturnOrderDEConvert INSTANCE = Mappers.getMapper(CellReturnOrderDEConvert.class);

    List<CellReturnOrderExcelDTO> toExcelDTO(List<CellReturnOrderDTO> dtos);

    CellReturnOrderExcelDTO toExcelDTO(CellReturnOrderDTO dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellReturnOrder.getCellsTypeId()))"),
                    @Mapping(target = "shipperIsOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellReturnOrder.getShipperIsOverseaId()))"),
                    @Mapping(target = "receiverIsOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellReturnOrder.getReceiverIsOverseaId()))"),
                    @Mapping(target = "shipperBasePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellReturnOrder.getShipperBasePlaceId()))"),
                    @Mapping(target = "receiverBasePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellReturnOrder.getReceiverBasePlaceId()))"),
                    @Mapping(target = "receiverAccount" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.INVENTORY_ORGANIZATION,cellReturnOrder.getReceiverAccount()))")
            }
    )
    CellReturnOrderDTO toDto(CellReturnOrder cellReturnOrder);


    @Mappings(
            {
                    @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.APS_BATTERY_TYPE, excelDTO.getCellsType()).getLovLineId())"),
                    @Mapping(target = "shipperIsOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getShipperIsOversea()).getLovLineId())"),
                    @Mapping(target = "receiverIsOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getReceiverIsOversea()).getLovLineId())"),
                    @Mapping(target = "shipperBasePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDTO.getShipperBasePlace()).getLovLineId())"),
                    @Mapping(target = "receiverBasePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDTO.getReceiverBasePlace()).getLovLineId())"),
                    @Mapping(target = "receiverAccount" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getValueByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.INVENTORY_ORGANIZATION,excelDTO.getReceiverAccount()))")
            }
    )
    CellReturnOrderSaveDTO excelDtoToSaveDto(CellReturnOrderExcelDTO excelDTO);

    List<CellReturnOrderSaveDTO> excelDtoToSaveDto(List<CellReturnOrderExcelDTO> dto);

    List<ReturnDataSaveDTO> toReturnDataDto(List<CellReturnOrderDTO> oriDTOS);
    @Mappings(
            {
                    @Mapping(target = "isOversea" , expression = "java(MapStrutUtil.getValueByName( LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getReceiverIsOversea()) )"),
                    @Mapping(target = "basePlace" , expression = "java(MapStrutUtil.getValueByName( LovHeaderCodeConstant.BASE_PLACE, dto.getReceiverBasePlace()) )"),
                    @Mapping(target = "cellType" , expression = "java(MapStrutUtil.getValueByName( LovHeaderCodeConstant.APS_BATTERY_TYPE, dto.getCellsType()) )"),
                    @Mapping(target = "cellNo" , source = "itemFivea"),
                    @Mapping(target = "efficiency" , source = "workCell"),
                    @Mapping(target = "characteristic",source = "note"),
                    @Mapping(target = "quantity",source = "cellQty"),
                    @Mapping(target = "books" , source = "receiverAccount"),
                    @Mapping(target = "day",expression = "java(dto.getDate().getDayOfMonth())")
            }
    )
    ReturnDataSaveDTO toReturnDataDto( CellReturnOrderDTO dto);
    @Mappings(
            {
                    @Mapping(target = "shipperIsOversea" , expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getShipperIsOversea(),lang))"),
                    @Mapping(target = "cellsType" , expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.APS_BATTERY_TYPE,dto.getCellsType(),lang))")
            }
    )
    CellReturnOrderQuery toCellReturnOrderQuery(CellReturnOrderQuery dto, String lang);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.APS_BATTERY_TYPE,dto.getCellsTypeId()))"),
                    @Mapping(target = "shipperIsOversea" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getShipperIsOverseaId()))"),
                    @Mapping(target = "receiverIsOversea" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getReceiverIsOverseaId()))"),
                    @Mapping(target = "shipperBasePlace" ,expression = "java( MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BASE_PLACE,dto.getShipperBasePlaceId()))"),
                    @Mapping(target = "receiverBasePlace" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BASE_PLACE,dto.getReceiverBasePlaceId()))")
            }
    )
    @Named("toCellReturnOrderSaveDTOCnName")
    CellReturnOrderSaveDTO toCellReturnOrderSaveDTOCnName(CellReturnOrderSaveDTO dto);
    @IterableMapping(qualifiedByName = "toCellReturnOrderSaveDTOCnName")
    List<CellReturnOrderSaveDTO> toCellReturnOrderSaveDTOCnName(List<CellReturnOrderSaveDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.APS_BATTERY_TYPE,dto.getCellsType()))"),
                    @Mapping(target = "shipperIsOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getShipperIsOversea()))"),
                    @Mapping(target = "receiverIsOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getReceiverIsOversea()))"),
                    @Mapping(target = "shipperBasePlace" ,expression = "java( MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getShipperBasePlace()))"),
                    @Mapping(target = "receiverBasePlace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getReceiverBasePlace()))"),
                    @Mapping(target = "receiverAccount" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.INVENTORY_ORGANIZATION,dto.getReceiverAccount()))")
            }
    )
    @Named("toCellReturnOrderDTONameByCn")
    CellReturnOrderDTO toCellReturnOrderDTONameByCn(CellReturnOrderDTO dto);
    @IterableMapping(qualifiedByName = "toCellReturnOrderDTONameByCn")
    List<CellReturnOrderDTO> toCellReturnOrderDTONameByCn(List<CellReturnOrderDTO> cellReturnOrderDTOS);



}
