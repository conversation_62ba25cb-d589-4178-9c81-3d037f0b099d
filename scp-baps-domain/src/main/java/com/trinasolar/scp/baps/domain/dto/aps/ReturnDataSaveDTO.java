package com.trinasolar.scp.baps.domain.dto.aps;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 电池返司数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ReturnData保存参数", description = "保存参数")
public class ReturnDataSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 是否海外
     */
    @NotEmpty(message = "isOversea 不可为空！")
    @ApiModelProperty(value = "是否海外")
    private String isOversea;
    /**
     * 基地
     */
    @NotEmpty(message = "basePlace 不可为空！")
    @ApiModelProperty(value = "基地")
    private String basePlace;
    /**
     * 帐套
     */
    @NotEmpty(message = "books 不可为空！")
    @ApiModelProperty(value = "帐套")
    private String books;
    /**
     * 车间
     */
    @NotEmpty(message = "workshop 不可为空！")
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 电池类型
     */
    @NotEmpty(message = "cellType 不可为空！")
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String cellNo;
    /**
     * 效率值
     */
    @ApiModelProperty(value = "效率值")
    private BigDecimal efficiency;
    /**
     * 对应标识
     */
    @ApiModelProperty(value = "对应标识")
    private String characteristic;
    /**
     * 月份
     */
    @NotEmpty(message = "month 不可为空！")
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 日期
     */
    @NotNull(message = "day 不可为空！")
    @ApiModelProperty(value = "日期")
    private Integer day;
    /**
     * 数量
     */
    @NotNull(message = "quantity 不可为空！")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    public ReturnDataSaveDTO(String isOversea, String basePlace, String books, String workshop, String cellType, String cellNo, BigDecimal efficiency, String characteristic, String month) {
        this.isOversea = isOversea;
        this.basePlace = basePlace;
        this.books = books;
        this.workshop = workshop;
        this.cellType = cellType;
        this.cellNo = cellNo;
        this.efficiency = efficiency;
        this.characteristic = characteristic;
        this.month = month;
    }

    public ReturnDataSaveDTO groupBy(){
        return new ReturnDataSaveDTO(this.isOversea, this.basePlace, this.books, this.workshop, this.cellType, this.cellNo, this.efficiency, this.characteristic, this.month);
    }
}
