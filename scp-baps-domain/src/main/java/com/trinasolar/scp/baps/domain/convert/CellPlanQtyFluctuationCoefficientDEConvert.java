package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellPlanQtyFluctuationCoefficient;
import com.trinasolar.scp.baps.domain.dto.CellPlanQtyFluctuationCoefficientDTO;
import com.trinasolar.scp.baps.domain.excel.CellPlanQtyFluctuationCoefficientExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellPlanQtyFluctuationCoefficientSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 投产计划浮动系数 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-30 01:49:53
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class})
public interface CellPlanQtyFluctuationCoefficientDEConvert extends BaseDEConvert<CellPlanQtyFluctuationCoefficientDTO, CellPlanQtyFluctuationCoefficient> {

    CellPlanQtyFluctuationCoefficientDEConvert INSTANCE = Mappers.getMapper(CellPlanQtyFluctuationCoefficientDEConvert.class);

    List<CellPlanQtyFluctuationCoefficientExcelDTO> toExcelDTO(List<CellPlanQtyFluctuationCoefficientDTO> dtos);

    CellPlanQtyFluctuationCoefficientExcelDTO toExcelDTO(CellPlanQtyFluctuationCoefficientDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellPlanQtyFluctuationCoefficient saveDTOtoEntity(CellPlanQtyFluctuationCoefficientSaveDTO saveDTO, @MappingTarget CellPlanQtyFluctuationCoefficient entity);
    @Mappings(
            {
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getValueByName(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshopName()))"),
                    @Mapping(target = "fluctuationCoefficient" ,expression = "java(MapStrutUtil.removePercentage(dto.getFluctuationCoefficientPercent(),4))"),
            }
    )
    @Named("excelDtoToSaveDto")
    CellPlanQtyFluctuationCoefficientSaveDTO excelDtoToSaveDto( CellPlanQtyFluctuationCoefficientExcelDTO dto);
    @IterableMapping(qualifiedByName = "excelDtoToSaveDto")
    List<CellPlanQtyFluctuationCoefficientSaveDTO> excelDtoToSaveDto(List<CellPlanQtyFluctuationCoefficientExcelDTO> excelDtos);

    @Override
    @Mappings(
            {
                    @Mapping(target = "workshopName" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.WORK_SHOP,entity.getWorkshop()))"),
                    @Mapping(target = "fluctuationCoefficientPercent" ,expression = "java(MapStrutUtil.addPercentageIgnoreZero(entity.getFluctuationCoefficient(),2))"),
            }
    )
    @Named("toDto")
    CellPlanQtyFluctuationCoefficientDTO toDto(CellPlanQtyFluctuationCoefficient entity);
    @IterableMapping(qualifiedByName = "toDto")
    List<CellPlanQtyFluctuationCoefficientDTO> toDto(List<CellPlanQtyFluctuationCoefficient> entitys);

}
