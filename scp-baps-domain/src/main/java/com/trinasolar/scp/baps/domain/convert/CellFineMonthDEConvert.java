package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellFineMonth;
import com.trinasolar.scp.baps.domain.dto.CellFineMonthDTO;
import com.trinasolar.scp.baps.domain.excel.CellFineMonthExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellFineMonthSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池良率月拆分表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-05 05:51:04
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellFineMonthDEConvert extends BaseDEConvert<CellFineMonthDTO, CellFineMonth> {

    CellFineMonthDEConvert INSTANCE = Mappers.getMapper(CellFineMonthDEConvert.class);

    List<CellFineMonthExcelDTO> toExcelDTO(List<CellFineMonthDTO> dtos);

    CellFineMonthExcelDTO toExcelDTO(CellFineMonthDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellFineMonth saveDTOtoEntity(CellFineMonthSaveDTO saveDTO, @MappingTarget CellFineMonth entity);
}
