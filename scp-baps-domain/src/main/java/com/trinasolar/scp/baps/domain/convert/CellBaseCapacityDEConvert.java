package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.entity.CellResource;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacitySaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellBaseCapacity;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDTO;
import com.trinasolar.scp.baps.domain.excel.CellBaseCapacityExcelDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * IE产能表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
imports = {LovHeaderCodeConstant.class, LovUtils.class, MapStrutUtil.class})
public interface CellBaseCapacityDEConvert extends BaseDEConvert<CellBaseCapacityDTO, CellBaseCapacity> {

    CellBaseCapacityDEConvert INSTANCE = Mappers.getMapper(CellBaseCapacityDEConvert.class);

    List<CellBaseCapacityExcelDTO> toExcelDTO(List<CellBaseCapacityDTO> dtos);

    CellBaseCapacityExcelDTO toExcelDTO(CellBaseCapacityDTO dto);
    @Override
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getCellsTypeId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getWorkshopid()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellBaseCapacity.getWorkunitid()))")
            }
    )
    CellBaseCapacityDTO toDto(CellBaseCapacity cellBaseCapacity);
    @Mappings(
            {
                    @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType()).getLovLineId())"),
                    @Mapping(target = "isOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOversea()).getLovLineId())"),
                    @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace()).getLovLineId())"),
                    @Mapping(target = "workshopid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop()).getLovLineId())"),
                    @Mapping(target = "workunitid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit()).getLovLineId())")

            }
    )
    CellBaseCapacitySaveDTO excelDtoToSaveDto(CellBaseCapacityExcelDTO excelDTO);

    List<CellBaseCapacitySaveDTO> excelDtoToSaveDto(List<CellBaseCapacityExcelDTO> dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellBaseCapacity.getCellsTypeId()).getLovValue())"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellBaseCapacity.getIsOverseaId()).getLovValue())"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellBaseCapacity.getBasePlaceId()).getLovValue())"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellBaseCapacity.getWorkshopid()).getLovValue())"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.get(cellBaseCapacity.getWorkunitid()).getLovValue())")
            }
    )
    @Named("copyEntityToValueDto")
    CellBaseCapacityDTO copyEntityToValueDto(CellBaseCapacity cellBaseCapacity);
    @IterableMapping( qualifiedByName = "copyEntityToValueDto")
    List<CellBaseCapacityDTO> copyEntityToValueDto(List<CellBaseCapacity> datas);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getCellsTypeId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkshopid()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkunitid()))")
            }
    )
    @Named("copyDtoNameFromDto")
    CellBaseCapacityDTO copyDtoNameFromDto(CellBaseCapacityDTO dto);
    @IterableMapping( qualifiedByName = "copyDtoNameFromDto")
    List<CellBaseCapacityDTO> copyDtoNameFromDto(List<CellBaseCapacityDTO> preData);
    CellResource toCellResourceEntity(CellBaseCapacityDTO dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByNameLang(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE,query.getCellsType(),language))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByNameLang(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA,query.getIsOversea(),language))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByNameLang(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE,query.getBasePlace(),language))")
            }
    )
    CellBaseCapacityQuery toCNNameByName(CellBaseCapacityQuery query, String language);

    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getCellsTypeId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkshopid()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkunitid()))")
            }
    )
    @Named("toDtoNameById")
    CellBaseCapacityDTO toDtoNameById(CellBaseCapacityDTO dto);

    @IterableMapping(qualifiedByName = "toDtoNameById")
    List<CellBaseCapacityDTO> toDtoNameById(List<CellBaseCapacityDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameById(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellsTypeId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameById(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameById(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameById(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshopid()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameById(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_UNIT,dto.getWorkunitid()))")
            }
    )
    @Named("toCellBaseCapacitySaveDTOCnNameById")
    CellBaseCapacitySaveDTO toCellBaseCapacitySaveDTOCnNameById(CellBaseCapacitySaveDTO dto);
    @IterableMapping(qualifiedByName = "toCellBaseCapacitySaveDTOCnNameById")
    List<CellBaseCapacitySaveDTO> toCellBaseCapacitySaveDTOCnNameById(List<CellBaseCapacitySaveDTO> saveDTOS);
    @IterableMapping(qualifiedByName = "toEntityFromSaveDTO")
    List<CellBaseCapacity> toEntityFromSaveDTO(List<CellBaseCapacitySaveDTO> saveDTOS);
    @Named("toEntityFromSaveDTO")
    CellBaseCapacity toEntityFromSaveDTO(CellBaseCapacitySaveDTO saveDTOS);
}
