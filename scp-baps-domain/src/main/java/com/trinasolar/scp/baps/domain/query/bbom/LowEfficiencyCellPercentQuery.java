package com.trinasolar.scp.baps.domain.query.bbom;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@ApiModel(value = "自产低效电池比例查询条件", description = "查询条件")
@Accessors(chain = true)
public class LowEfficiencyCellPercentQuery extends PageDTO implements Serializable {

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "国内/海外")
    private String countryFlag;

    @ApiModelProperty(value = "车间")
    private String workshop;

    @ApiModelProperty(value = "电池型号")
    private String cellModel;

    @ApiModelProperty(value = "生产基地")
    private String basePlace;


}
