package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.math.BigDecimal;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 投产提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellProductionLeadTimeExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    @ExcelIgnore
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    @ExcelIgnore
    private Long workshopId;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @ExcelProperty(value = "生产单元")
    private String workunit;

    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元Id")
    @ExcelIgnore
    private Long workunitId;
    /**
     * 提前期
     */
    @ExcelProperty(value = "提前期")
    private BigDecimal leadTime;

}
