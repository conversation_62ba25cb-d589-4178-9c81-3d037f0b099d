package com.trinasolar.scp.baps.domain.query.erp;

import com.trinasolar.scp.common.api.base.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SyncWipIssueQuery extends BaseQuery {
    private List<Long> organizationIds;
    private List<Long> transactionTypeIds;
    private String transactionDateFrom;
    private String transactionDateTo;
    private Integer pageNum = 1;
}