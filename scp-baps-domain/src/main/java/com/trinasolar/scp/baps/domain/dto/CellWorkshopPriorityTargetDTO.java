package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 车间优先度效率目标值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "车间优先度效率目标值DTO对象", description = "DTO对象")
public class CellWorkshopPriorityTargetDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 车间Id
     */
    @ApiModelProperty(value = "车间Id")
    private Long workshopId;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 效率分布
     */
    @ApiModelProperty(value = "效率分布")
    private String efficiency;
}
