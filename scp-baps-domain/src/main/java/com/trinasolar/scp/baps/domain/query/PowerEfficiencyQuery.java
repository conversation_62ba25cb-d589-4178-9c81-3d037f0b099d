package com.trinasolar.scp.baps.domain.query;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * scp-aps全年效率值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:13
 */
@Data
@ApiModel(value = "PowerEfficiency查询条件", description = "查询条件")
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PowerEfficiencyQuery extends PageDTO implements Serializable {
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;

    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;


    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;
    /**
     * 供应类型:自产/外购
     */
    @ApiModelProperty(value = "供应类型:自产/外购")
    private String supplyType;

    /**
     * 月度起
     */
    @ApiModelProperty(value = "月度起")
    private String beginMonth;

    /**
     * 月度止
     */
    @ApiModelProperty(value = "月度止")
    private String endMonth;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String rule;

    /**
     * 目标效率
     */
    @ApiModelProperty(value = "目标效率")
    private BigDecimal targetEfficiency;

    private List<String> cellTypes;

    private List<String> suppliers;

    private ExcelPara excelPara;

}
