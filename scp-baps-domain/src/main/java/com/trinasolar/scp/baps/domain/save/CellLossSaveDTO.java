package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 产能切换损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellLoss保存参数", description = "保存参数")
public class CellLossSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopid;
    /**
     * 起始月份
     */
    @ApiModelProperty(value = "起始月份")
    private String startMonth;
    /**
     * 结束月份
     */
    @ApiModelProperty(value = "结束月份")
    private String endMonth;
    /**
     * 电池类型1
     */
    @ApiModelProperty(value = "电池类型1")
    private String oldProduct;
    /**
     * 电池类型1
     */
    @ApiModelProperty(value = "电池类型1Id")
    private Long oldProductId;
    /**
     * 电池类型2
     */
    @ApiModelProperty(value = "电池类型2")
    private String newProduct;
    /**
     * 电池类型2
     */
    @ApiModelProperty(value = "电池类型2Id")
    private Long newProductId;
    /**
     * 损失时间(小时)
     */
    @ApiModelProperty(value = "损失时间(小时)")
    private BigDecimal lossTime;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 是否单玻1
     */
    @ApiModelProperty(value = "是否单玻1")
    private String oldIsSingleGlass;
    /**
     * 是否单玻2
     */
    @ApiModelProperty(value = "是否单玻2")
    private String newIsSingleGlass;
    /**
     * 是否小区域国家1
     */
    @ApiModelProperty(value = "是否小区域国家1")
    private String oldIsRegionalCountry;
    /**
     * 是否小区域国家2
     */
    @ApiModelProperty(value = "是否小区域国家2")
    private String newIsRegionalCountry;

    @ApiModelProperty(value = "是否10.8_1")
    private String is1081;

    @ApiModelProperty(value = "是否10.8_2")
    private String is1082;

}
