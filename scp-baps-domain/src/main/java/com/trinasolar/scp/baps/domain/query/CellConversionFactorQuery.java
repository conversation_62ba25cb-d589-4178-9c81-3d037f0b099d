package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 万片与兆瓦折算系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-31 10:33:29
 */
@Data
@ApiModel(value = "CellConversionFactor查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellConversionFactorQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 国内海外名称
     */
    @ApiModelProperty(value = "国内海外名称")
    private String isOversea;
        /**
         * 电池类型Id
         */
        @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellsType;
        /**
         * 折算系数
         */
        @ApiModelProperty(value = "折算系数")
    private BigDecimal conversionFactor;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
