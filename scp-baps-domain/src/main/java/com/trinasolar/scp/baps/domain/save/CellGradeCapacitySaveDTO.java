package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 爬坡产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellGradeCapacity保存参数", description = "保存参数")
public class CellGradeCapacitySaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private Long isOverseaId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workunitid;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    private  BigDecimal lineNumber;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 产能
     */
    @ApiModelProperty(value = "产能")
    private BigDecimal capacityQuantity;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 能否生产单玻
     */
    @ApiModelProperty(value = "能否生产单玻")
    private String isSingleGlass;
    /**
     * 能否生产低碳
     */
    @ApiModelProperty(value = "能否生产低碳")
    private String isDt;
    /**
     * 能否生产小区域国家
     */
    @ApiModelProperty(value = "能否生产小区域国家")
    private String isRegionalCountry;
    /**
     * 能否生产H兼容
     */
    @ApiModelProperty(value = "能否生产H兼容")
    private String isHChangeFlag;
    /**
     * 能否生产H追溯
     */
    @ApiModelProperty(value = "能否生产H追溯")
    private String isHTrace;
    /**
     * 1号
     */
    @ApiModelProperty(value = "1号")
    private BigDecimal d1;
    /**
     * 2号
     */
    @ApiModelProperty(value = "2号")
    private BigDecimal d2;
    /**
     * 3号
     */
    @ApiModelProperty(value = "3号")
    private BigDecimal d3;
    /**
     * 4号
     */
    @ApiModelProperty(value = "4号")
    private BigDecimal d4;
    /**
     * 5号
     */
    @ApiModelProperty(value = "5号")
    private BigDecimal d5;
    /**
     * 6号
     */
    @ApiModelProperty(value = "6号")
    private BigDecimal d6;
    /**
     * 7号
     */
    @ApiModelProperty(value = "7号")
    private BigDecimal d7;
    /**
     * 8号
     */
    @ApiModelProperty(value = "8号")
    private BigDecimal d8;
    /**
     * 9号
     */
    @ApiModelProperty(value = "9号")
    private BigDecimal d9;
    /**
     * 10号
     */
    @ApiModelProperty(value = "10号")
    private BigDecimal d10;
    /**
     * 11号
     */
    @ApiModelProperty(value = "11号")
    private BigDecimal d11;
    /**
     * 12号
     */
    @ApiModelProperty(value = "12号")
    private BigDecimal d12;
    /**
     * 13号
     */
    @ApiModelProperty(value = "13号")
    private BigDecimal d13;
    /**
     * 14号
     */
    @ApiModelProperty(value = "14号")
    private BigDecimal d14;
    /**
     * 15号
     */
    @ApiModelProperty(value = "15号")
    private BigDecimal d15;
    /**
     * 16号
     */
    @ApiModelProperty(value = "16号")
    private BigDecimal d16;
    /**
     * 17号
     */
    @ApiModelProperty(value = "17号")
    private BigDecimal d17;
    /**
     * 18号
     */
    @ApiModelProperty(value = "18号")
    private BigDecimal d18;
    /**
     * 19号
     */
    @ApiModelProperty(value = "19号")
    private BigDecimal d19;
    /**
     * 20号
     */
    @ApiModelProperty(value = "20号")
    private BigDecimal d20;
    /**
     * 21号
     */
    @ApiModelProperty(value = "21号")
    private BigDecimal d21;
    /**
     * 22号
     */
    @ApiModelProperty(value = "22号")
    private BigDecimal d22;
    /**
     * 23号
     */
    @ApiModelProperty(value = "23号")
    private BigDecimal d23;
    /**
     * 24号
     */
    @ApiModelProperty(value = "24号")
    private BigDecimal d24;
    /**
     * 25号
     */
    @ApiModelProperty(value = "25号")
    private BigDecimal d25;
    /**
     * 26号
     */
    @ApiModelProperty(value = "26号")
    private BigDecimal d26;
    /**
     * 27号
     */
    @ApiModelProperty(value = "27号")
    private BigDecimal d27;
    /**
     * 28号
     */
    @ApiModelProperty(value = "28号")
    private BigDecimal d28;
    /**
     * 29号
     */
    @ApiModelProperty(value = "29号")
    private BigDecimal d29;
    /**
     * 30号
     */
    @ApiModelProperty(value = "30号")
    private BigDecimal d30;
    /**
     * 31号
     */
    @ApiModelProperty(value = "31号")
    private BigDecimal d31;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 可靠性验证
     */
    @ApiModelProperty(value = "可靠性验证")
    private String reliabilityCheck;
}
