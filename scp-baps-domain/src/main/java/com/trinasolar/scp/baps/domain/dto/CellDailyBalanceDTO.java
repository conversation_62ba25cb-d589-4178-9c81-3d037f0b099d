package com.trinasolar.scp.baps.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.trinasolar.scp.baps.domain.entity.BigDecimalSerializer;
import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 每日结存报表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-12 06:19:24
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "每日结存报表DTO对象", description = "DTO对象")
public class CellDailyBalanceDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内外Id
     */
    @ApiModelProperty(value = "国内外Id")
    private Long isOverseaId;
    /**
     * 国内外
     */
    @ApiModelProperty(value = "国内外")
    private String isOversea;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String project;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 低碳
     */
    @ApiModelProperty(value = "低碳")
    private String dt;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 数据来源版本
     */
    @ApiModelProperty(value = "数据来源版本")
    private String fromVersion;
    /**
     * 1号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "1号")
    private BigDecimal d1;
    /**
     * 2号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "2号")
    private BigDecimal d2;
    /**
     * 3号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "3号")
    private BigDecimal d3;
    /**
     * 4号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "4号")
    private BigDecimal d4;
    /**
     * 5号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "5号")
    private BigDecimal d5;
    /**
     * 6号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "6号")
    private BigDecimal d6;
    /**
     * 7号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "7号")
    private BigDecimal d7;
    /**
     * 8号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "8号")
    private BigDecimal d8;
    /**
     * 9号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "9号")
    private BigDecimal d9;
    /**
     * 10号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "10号")
    private BigDecimal d10;
    /**
     * 11号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "11号")
    private BigDecimal d11;
    /**
     * 12号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "12号")
    private BigDecimal d12;
    /**
     * 13号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "13号")
    private BigDecimal d13;
    /**
     * 14号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "14号")
    private BigDecimal d14;
    /**
     * 15号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "15号")
    private BigDecimal d15;
    /**
     * 16号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "16号")
    private BigDecimal d16;
    /**
     * 17号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "17号")
    private BigDecimal d17;
    /**
     * 18号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "18号")
    private BigDecimal d18;
    /**
     * 19号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "19号")
    private BigDecimal d19;
    /**
     * 20号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "20号")
    private BigDecimal d20;
    /**
     * 21号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "21号")
    private BigDecimal d21;
    /**
     * 22号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "22号")
    private BigDecimal d22;
    /**
     * 23号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "23号")
    private BigDecimal d23;
    /**
     * 24号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "24号")
    private BigDecimal d24;
    /**
     * 25号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "25号")
    private BigDecimal d25;
    /**
     * 26号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "26号")
    private BigDecimal d26;
    /**
     * 27号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "27号")
    private BigDecimal d27;
    /**
     * 28号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "28号")
    private BigDecimal d28;
    /**
     * 29号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "29号")
    private BigDecimal d29;
    /**
     * 30号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "30号")
    private BigDecimal d30;
    /**
     * 31号
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty(value = "31号")
    private BigDecimal d31;
}
