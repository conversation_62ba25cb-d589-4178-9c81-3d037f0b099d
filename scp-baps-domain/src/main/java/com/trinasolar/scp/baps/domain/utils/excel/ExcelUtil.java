package com.trinasolar.scp.baps.domain.utils.excel;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.trinasolar.scp.common.api.util.BeanUtils;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LocalDateConverter;
import com.trinasolar.scp.common.api.util.LocalDateTimeConverter;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 动态表头Excel工具类
 *
 * <AUTHOR>
 * @date 2022年9月21日16:15:31
 */
public class ExcelUtil {
    /**
     * 导出
     *
     * @param response
     * @param excelMain
     * @throws IOException
     */
    public static void export(HttpServletResponse response, ExcelMain excelMain) throws IOException {
        EasyExcel.write(response.getOutputStream())
                .head(ExcelUtil.buildHead(excelMain.getExcelHeads()))
                .sheet("Sheet1")
                .registerWriteHandler(new MergeStrategy(excelMain))//自定义合并单元格
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .doWrite(ExcelUtil.buildData(excelMain));
    }

    /**
     * 导出到指定文件
     *
     * @param file
     * @param excelMain
     * @throws IOException
     */
    public static void export(File file, ExcelMain excelMain) throws IOException {
        EasyExcel.write(file)
                .head(ExcelUtil.buildHead(excelMain.getExcelHeads()))
                .sheet("Sheet1")
                .registerWriteHandler(new MergeStrategy(excelMain))//自定义合并单元格
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .doWrite(ExcelUtil.buildData(excelMain));
    }

    /**
     * 导出
     *
     * @param response
     * @param sheetMap
     * @throws IOException
     */
    public static void export(HttpServletResponse response, Map<String, ExcelMain> sheetMap) throws IOException {
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        sheetMap.forEach((sheetName, excelMain) -> {
            WriteSheet sheet = EasyExcel
                    .writerSheet(sheetName)
                    .head(ExcelUtil.buildHead(excelMain.getExcelHeads()))
                    .registerWriteHandler(new MergeStrategy(excelMain))
                    .registerConverter(new LocalDateConverter())
                    .registerConverter(new LocalDateTimeConverter())
                    .build();
            excelWriter.write(ExcelUtil.buildData(excelMain), sheet);
        });
        excelWriter.finish();
    }




    public static void setExportResponseHeader(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        try {
            fileName = fileName + "_" + com.ibm.dpf.base.core.util.DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            String fileNameCode = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileNameCode + ".xlsx");
            response.setHeader("Access-Control-Allow-Origin", "Content-Disposition");
            response.setHeader("Access-Control-Allow-Origin", "Content-disposition");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        } catch (Throwable var3) {
            throw var3;
        }
    }

    public static void verifyFile(MultipartFile multipartFile) {
        if (multipartFile == null || multipartFile.isEmpty()) {
            throw new BizException("导入文件不能为空！");
        }
        //校验文件格式，必须为excel文件
        String fileName = multipartFile.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!"xlsx".equals(suffix)) {
            throw new BizException("导入文件只支持xlsx类型后缀！");
        }
    }

    /**
     * 构建导出表头
     */
    public static List<List<String>> buildHead(List<ExcelHead> heads) {
        boolean needMerge = false;
        for (ExcelHead headVO : heads) {
            if (CollectionUtils.isNotEmpty(headVO.getChildren())) {
                needMerge = true;
            }
        }
        List<List<String>> headList = new ArrayList<>();
        for (ExcelHead excelHead : heads) {
            if (CollectionUtils.isNotEmpty(excelHead.getChildren())) {
                excelHead.getChildren().forEach(child -> {
                    List<String> head = new ArrayList<>();
                    head.add(excelHead.getLabel());
                    head.add(child.getLabel());
                    headList.add(head);
                });
            } else {
                List<String> head = new ArrayList<>();
                head.add(excelHead.getLabel());
                if (needMerge) {
                    head.add(excelHead.getLabel());
                }
                headList.add(head);
            }
        }
        return headList;
    }

    /**
     * 构建导出数据
     */
    public static List<List<Object>> buildData(ExcelMain excelMain) {
        int maxExcel = 32767;
        List<List<Object>> list = new ArrayList<>();
        if (Objects.isNull(excelMain) || CollectionUtils.isEmpty(excelMain.getData())) {
            return list;
        }
        List<ExcelHead> heads = excelMain.getExcelHeads();
        for (Map<String, Object> map : excelMain.getData()) {
            List<Object> data = new ArrayList<>();
            for (ExcelHead head : heads) {
                if (CollectionUtils.isNotEmpty(head.getChildren())) {
                    head.getChildren().forEach(child -> {
                        Object value = map.get(child.getProp());
                        if ((value + "").length() >= maxExcel) {
                            data.add((value + "").substring(0, 255) + "字符过长");
                        } else {
                            data.add(value);
                        }
                    });
                } else {
                    Object value = map.get(head.getProp());
                    if ((value + "").length() >= maxExcel) {
                        data.add((value + "").substring(0, 255) + "字符过长");
                    } else {
                        data.add(value);
                    }
                }
            }
            list.add(data);
        }
        return list;
    }

    /**
     * 导入读取单元格内容
     * 返回如果需要转换类型，请判断是否为空，或者空字符串
     *
     * @param file      文件
     * @param clz       接收类型
     * @param cellIndex 动态列开始索引
     * @param consumer  动态列回调操作
     * @param <T>
     * @return
     */
    @SneakyThrows
    public static <T> List<T> readExcel(MultipartFile file, Class<T> clz, int cellIndex, Consumer<OperateRowData> consumer) {
        return readExcel(file, clz, false, cellIndex, consumer);
    }

    /**
     * 导入读取单元格内容
     * 返回如果需要转换类型，请判断是否为空，或者空字符串
     *
     * @param file      文件
     * @param clz       接收类型
     * @param reserve   如果动态列部分为空时，是否保留固定栏位
     * @param cellIndex 动态列开始索引
     * @param consumer  动态列回调操作
     * @param <T>
     * @return
     */
    @SneakyThrows
    public static <T> List<T> readExcel(MultipartFile file, Class<T> clz, boolean reserve, int cellIndex, Consumer<OperateRowData> consumer) {
        //获取excel
        Workbook workbook = verifyExcel(file.getOriginalFilename(), file.getInputStream());
        //返回结果
        List<T> resultList = new ArrayList<>();
        // 读取第一个工作簿
        Sheet sheet = workbook.getSheetAt(0);
        Row titleRow = sheet.getRow(0);
        //总行数
        int rowCount = sheet.getPhysicalNumberOfRows();
        //总列数
        int cellCount = titleRow.getLastCellNum();
        //获取映射字段
        Map<Integer, Field> annotationFields = getAnnotationFields(clz);
        //循环行
        for (int i = 1; i < rowCount; i++) {
            Row row = sheet.getRow(i);
            if (Objects.nonNull(row)) {
                T rowData = clz.getDeclaredConstructor().newInstance();
                for (int j = 0; j < cellCount; j++) {
                    Cell cell = row.getCell(j);
                    //存在映射索引
                    if (annotationFields.containsKey(j)) {
                        Field field = annotationFields.get(j);
                        Object cellValue = getCellValue(sheet, cell);
                        ExcelHeader annotation = field.getAnnotation(ExcelHeader.class);
                        if (annotation.isPercent()) {
                            cellValue = OperateRowData.parsingDataToBigDecimal(cellValue);
                        }
                        ReflectUtil.setFieldValue(rowData, field, cellValue);
                    }
                    //从第cellIndex列开始都是动态列，需要读取表头的信息
                    if (j >= cellIndex) {
                        //每一个动态列都需要转换为一行数据
                        T t = clz.getDeclaredConstructor().newInstance();
                        BeanUtils.copyProperties(rowData, t);
                        Object ratio = getCellValue(sheet, cell);
                        if (Objects.nonNull(ratio) && StringUtils.isNotBlank(ratio.toString())) {
                            //取第一行 j列数值
                            Cell efficiencyCell = sheet.getRow(0).getCell(j);
                            Object efficiency = getCellValue(sheet, efficiencyCell);
                            //回调进行参数处理，使方法通用
                            OperateRowData operateRowData = new OperateRowData(t, efficiency, ratio);
                            consumer.accept(operateRowData);
                            t = (T) operateRowData.getRowData();
                            resultList.add(t);
                        } else {
                            if (reserve) {
                                resultList.add(t);
                            }
                        }
                    }
                }
            }
        }
        return resultList.stream().collect(Collectors.toList());
    }

    static Workbook verifyExcel(String fileName, InputStream is) {
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
            throw new BizException("上传文件格式不正确");
        }
        Workbook workbook = null;
        try {
            if (fileName.endsWith("xlsx")) {
                workbook = new XSSFWorkbook(is);
            }
            if (fileName.endsWith("xls")) {
                workbook = new HSSFWorkbook(is);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("导入失败！");
        }
        return workbook;
    }

    /**
     * 获取注解字段
     *
     * @param clz
     * @return
     */
    private static Map<Integer, Field> getAnnotationFields(Class clz) {
        // 反射获取字段
        List<Field> fields = Lists.newArrayList(clz.getDeclaredFields());
        return fields.stream().filter(field -> {
            // 设置属性可访问
            field.setAccessible(true);
            return field.isAnnotationPresent(ExcelHeader.class);
        }).collect(Collectors.toMap(field -> {
            ExcelHeader annotation = field.getAnnotation(ExcelHeader.class);
            return annotation.index();
        }, Function.identity()));
    }


    /**
     * 获取单元格数据
     *
     * @param sheet
     * @param cell
     * @return
     */
    public static Object getCellValue(Sheet sheet, Cell cell) {
        if (Objects.isNull(cell)) {
            return null;
        }
        Object cellValue = "";
        // 以下是判断数据的类型
        switch (cell.getCellType()) {
            case NUMERIC: // 数字
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    cellValue = sdf.format(org.apache.poi.ss.usermodel.DateUtil.getJavaDate(cell.getNumericCellValue()));
                } else {
                    //解决当读取的单元格的内容，被自动加上".0"后缀
                    //若读取的单元格的值没有".0",则可吧把cell类型转为String，则会去掉".0"
                    cell.setCellType(CellType.STRING);
                    cellValue = cell.getStringCellValue();
                    //若读取的单元格本来就存在了小数点位，则保留两位小数
                    if (cellValue.toString().indexOf(".") > -1) {
                        cellValue = Double.valueOf(cellValue.toString());
                        NumberFormat nf = NumberFormat.getNumberInstance();
                        nf.setMaximumFractionDigits(2);
                        /*
                         * setMinimumFractionDigits设置成2
                         * 如果不这么做，那么当value的值是100.00的时候返回100
                         * 而不是100.00
                         */
                        nf.setMinimumFractionDigits(6);
                        nf.setRoundingMode(RoundingMode.HALF_UP);
                        //如果想输出的格式用逗号隔开，可以设置成true
                        nf.setGroupingUsed(false);
                        cellValue = nf.format(cellValue);
                    }
                }
                break;
            case STRING:
                cellValue = cell.getStringCellValue();
                if (Objects.nonNull(cellValue)) {
                    cellValue = StringUtils.trim((String) cellValue);
                }
                break;
            case BOOLEAN:
                cellValue = cell.getBooleanCellValue();
                break;
            case FORMULA:
                cellValue = getExcelForFormulaEva(sheet, cell);
                break;
            case ERROR:
                cellValue = "非法字符";
                break;
            default:
                break;
        }
        return cellValue;
    }


    /**
     * 若单元格是公式，则计算结果返回
     *
     * @param sheet
     * @param cell
     * @return
     */
    public static Object getExcelForFormulaEva(Sheet sheet, Cell cell) {
        FormulaEvaluator evaluator = sheet.getWorkbook().getCreationHelper().createFormulaEvaluator();
        CellValue evaluate = evaluator.evaluate(cell);
        return evaluate.formatAsString();
    }
}
