package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 入库计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellPlanLineExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 订单表
     */
    @ExcelProperty(value = "订单表")
    private String orderCode;
    /**
     * 来源类型
     */
    @ExcelProperty(value = "来源类型")
    private String sourceType;
    /**
     * 需求版本号
     */
    @ExcelProperty(value = "需求版本号")
    private String demandVersion;
    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元Id
     */
    @ExcelProperty(value = "生产单元Id")
    private Long workunitId;
    /**
     * 生产线体
     */
    @ExcelProperty(value = "生产线体")
    private String lineName;
    /**
     * 产线数量
     */
    @ExcelProperty(value = "产线数量")
    private BigDecimal numberLine;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;

    /**
     * 供应方式
     */
    @ExcelProperty(value = "供应方式")
    private String supplyMethod;

    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 产品等级
     */
    @ExcelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 小区域国家
     */
    @ExcelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 5A料号
     */
    @ExcelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 需求地
     */
    @ExcelProperty(value = "需求地")
    private String demandBasePlace;
    /**
     * 是否电池特殊要求
     */
    @ExcelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 低阻
     */
    @ExcelProperty(value = "低阻")
    private String lowResistance;
    /**
     * 电池厂家
     */
    @ExcelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * 银浆厂家
     */
    @ExcelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 需求数量
     */
    @ExcelProperty(value = "需求数量")
    private BigDecimal demandQty;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * MV
     */
    @ExcelProperty(value = "MV")
    private BigDecimal cellMv;
    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String version;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 数量（片）
     */
    @ExcelProperty(value = "数量（片）")
    private BigDecimal qtyPc;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private Long regionalCountryId;
    /**
     * 是否H兼容
     */
    @ApiModelProperty(value = "是否H兼容")
    private String hChangeFlag;

    /**
     * 计划员
     */
    @ExcelProperty(value = "计划员")
    private String planner;

    /**
     * APS导出备注
     */
    @ExcelProperty(value = "APS导出备注")
    private String exportComments;
}
