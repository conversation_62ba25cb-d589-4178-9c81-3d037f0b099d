package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * IE产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Data
@ApiModel(value = "CellBaseCapacity查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellBaseCapacityQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate beginDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endDate;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型id")
    private Long cellsTypeId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型id")
    private List<Long> cellsTypeIdList;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外id")
    private Long isOverseaId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间id")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元id")
    private Long workunitid;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    private BigDecimal numberLine;
    /**
     * 可用线体数量
     */
    @ApiModelProperty(value = "可用线体数量")
    private BigDecimal usageLine;
    /**
     * 产能（单线）
     */
    @ApiModelProperty(value = "产能（单线）")
    private BigDecimal singleCapacity;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 能否生产单玻
     */
    @ApiModelProperty(value = "能否生产单玻")
    private String isSingleGlass;
    /**
     * 能否生产低碳
     */
    @ApiModelProperty(value = "能否生产低碳")
    private String isDt;
    /**
     * 能否生产小区域国家
     */
    @ApiModelProperty(value = "能否生产小区域国家")
    private String isRegionalCountry;
    /**
     * 能否生产H兼容
     */
    @ApiModelProperty(value = "能否生产H兼容")
    private String isHChangeFlag;
    /**
     * 能否生产H追溯
     */
    @ApiModelProperty(value = "能否生产H追溯")
    private String isHTrace;
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
    //给dm用
    private List<String> monthList = new ArrayList<>();
}
