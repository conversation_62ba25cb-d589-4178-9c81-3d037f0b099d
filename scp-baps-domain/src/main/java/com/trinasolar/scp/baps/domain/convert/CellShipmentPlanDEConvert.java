package com.trinasolar.scp.baps.domain.convert;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanDTO;
import com.trinasolar.scp.baps.domain.query.CellShipmentPlanQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellShipmentPlan;
import com.trinasolar.scp.baps.domain.dto.CellShipmentPlanDTO;
import com.trinasolar.scp.baps.domain.excel.CellShipmentPlanExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellShipmentPlanSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 可发货计划表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-28 11:52:39
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellShipmentPlanDEConvert extends BaseDEConvert<CellShipmentPlanDTO, CellShipmentPlan> {

    CellShipmentPlanDEConvert INSTANCE = Mappers.getMapper(CellShipmentPlanDEConvert.class);

    List<CellShipmentPlanExcelDTO> toExcelDTO(List<CellShipmentPlanDTO> dtos);

    CellShipmentPlanExcelDTO toExcelDTO(CellShipmentPlanDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellShipmentPlan saveDTOtoEntity(CellShipmentPlanSaveDTO saveDTO, @MappingTarget CellShipmentPlan entity);

    @Mappings({
            @Mapping(target = "certCode"),
            @Mapping(target = "ratioCode")
    })
    List<CellShipmentPlanDTO> toShipmentPlanDTO(List<CellInstockPlanDTO> dtos);

    @Mappings({
            @Mapping(target = "certCode"),
            @Mapping(target = "ratioCode")
    })
    List<CellShipmentPlanSaveDTO> toShipmentPlanSaveDTO(List<CellShipmentPlanDTO> dtos);

    List<CellShipmentPlan> toCellShipmentPlan(List<CellShipmentPlanDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,query.getCellsType(),language))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,query.getIsOversea(),language))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,query.getBasePlace(),language))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,query.getWorkshop(),language))"),
                    @Mapping(target = "workunit" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_UNIT,query.getWorkunit(),language))")
            }
    )
    CellShipmentPlanQuery toCellShipmentPlanQueryCNByName(CellShipmentPlanQuery query, String language);
    @Mappings({
            @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,entity.getCellsType()))"),
            @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,entity.getIsOversea()))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getBasePlace()))"),
            @Mapping(target = "workshop"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,entity.getWorkshop()))"),
            @Mapping(target = "workunit"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_UNIT,entity.getWorkunit()))"),
            @Mapping(target = "HTrace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,entity.getHTrace()))"),
            @Mapping(target = "aesthetics"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,entity.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlass"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,entity.getTransparentDoubleGlass()))"),
            @Mapping(target = "cellSource"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_SOURCE,entity.getCellSource()))"),
            @Mapping(target = "regionalCountry"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.REGIONAL_COUNTRY,entity.getRegionalCountry()))"),
            @Mapping(target = "productionGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,entity.getProductionGrade()))"),
            @Mapping(target = "sourceType"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BDM_DEMAND_SOURCE,entity.getSourceType()))"),
            @Mapping(target = "demandBasePlace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getDemandBasePlace()))"),
            @Mapping(target = "cellMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_MFRS,entity.getCellMfrs()))"),
            @Mapping(target = "silverPulpMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SILVER_PULP_MFRS, entity.getSilverPulpMfrs()))"),
            @Mapping(target = "siMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SI_MFRS, entity.getSiMfrs()))"),
            @Mapping(target = "waferGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WAFER_GRADE,entity.getWaferGrade()))"),
            @Mapping(target = "processCategory"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,entity.getProcessCategory()))")
    })
    @Named("toCellShipmentPlanDTONameByCnName")
    CellShipmentPlanDTO toCellShipmentPlanDTONameByCnName(CellShipmentPlanDTO entity);
    @IterableMapping(qualifiedByName = "toCellShipmentPlanDTONameByCnName")
    List<CellShipmentPlanDTO> toCellShipmentPlanDTONameByCnName(List<CellShipmentPlanDTO> dtos);

    @Override
    @Mappings(
            {
                    @Mapping(target = "certCode" ),
                    @Mapping(target = "ratioCode" )
            }
    )
    List<CellShipmentPlanDTO> toDto(List<CellShipmentPlan> dtos);
    default  void test(){
        // MapStrutUtil.getCNNameByNameLang()
    }
}
