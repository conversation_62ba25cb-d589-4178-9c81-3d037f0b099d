package com.trinasolar.scp.baps.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 投产计划 拆分 确认
 *
 * <AUTHOR>
 * @email
 * @date 2025-03-27
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "投产计划表拆分确认DTO对象", description = "DTO对象")
@ToString
public class ProcessCategorySplitSubmitDTO {

    @ApiModelProperty(value = "入库计划-拆分-查询条件")
    private ProcessCategorySplitDto processCategorySplitQuery;

    @ApiModelProperty(value = "入库计划-拆分-确认")
    private List<CellPlanLineSplitDTO> cellPlanLineSplitDTOS;
}
