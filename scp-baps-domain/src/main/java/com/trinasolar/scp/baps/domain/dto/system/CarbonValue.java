package com.trinasolar.scp.baps.domain.dto.system;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "法碳产地表", description = "DTO对象")
public class CarbonValue extends BasePO implements Serializable, Cloneable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 证书头ID
     */
    @ApiModelProperty(value = "证书头ID", notes = "证书头ID")
    private Long certHeaderId;

    /**
     * 功率
     */
    @ApiModelProperty(value = "功率", notes = "功率")
    private String power;

    /**
     * 碳值
     */
    @ApiModelProperty(value = "碳值")
    private String carbonValue;

    /**
     * 碳值归类
     */
    @ApiModelProperty(value = "碳值归类")
    private String carbonFootprint;
}