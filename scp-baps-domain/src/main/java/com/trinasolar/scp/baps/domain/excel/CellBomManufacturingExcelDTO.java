package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 制造BOM表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellBomManufacturingExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 产线总数
     */
    @ExcelProperty(value = "产线总数")
    private BigDecimal totalLine;
    /**
     * 可用产线数
     */
    @ExcelProperty(value = "可用产线数")
    private BigDecimal usageLine;
    /**
     * 产能（单元）
     */
    @ExcelProperty(value = "产能（单元）")
    private BigDecimal capacityQuantity;
    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String unit;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 工序代码
     */
    @ExcelProperty(value = "工序代码")
    private String processCode;
    /**
     * 工序编号
     */
    @ExcelProperty(value = "工序编号")
    private Integer processId;
    /**
     * 使用类型
     */
    @ExcelProperty(value = "使用类型")
    private String instructionType;
    /**
     * 使用代码
     */
    @ExcelProperty(value = "使用代码")
    private String instructionCode;
    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String version;
    /**
     * 国内国外
     */
    @ExcelProperty(value = "国内国外")
    private String isOversea;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workUnit;
    /**
     * 生产线体
     */
    @ExcelProperty(value = "生产线体")
    private String lineName;
    /**
     * 制造
     */
    @ExcelProperty(value = "制造")
    private String manufacturing;
    /**
     * 优先级
     */
    @ExcelProperty(value = "优先级")
    private Integer rate;
    /**
     * 开始日期
     */
    @ExcelProperty(value = "开始日期")
    private LocalDateTime startDate;
    /**
     * 结束日期
     */
    @ExcelProperty(value = "结束日期")
    private LocalDateTime endDate;
    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 标识
     */
    @ExcelProperty(value = "标识")
    private Integer flag;
    /**
     * 符合率
     */
    @ExcelProperty(value = "符合率")
    private BigDecimal finePercent;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String gradeWorkDate;
    /**
     * 可靠性验证
     */
    @ExcelProperty(value = "可靠性验证")
    private String reliabilityCheck;
}
