package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;


/**
 * 投产计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "投产计划版本管理表DTO对象", description = "DTO对象")
public class CellPlanLineVersionDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    private String month;
    /**
     * 最終确认发布的版本
     */
    @ApiModelProperty(value = "最終确认发布的版本")
    private String finalVersion;
    /**
     * 排产版本
     */
    @ApiModelProperty(value = "排产版本")
    private String version;
    /**
     * 是否进行了投产提前期转化过
     */
    @ApiModelProperty(value = "是否进行了投产提前期转化过")
    private Integer isTransform;
    /**
     * 是否进行了硅料厂家拆分
     */
    @ApiModelProperty(value = "是否进行了硅料厂家拆分")
    private Integer isSiMfrs;
    @ApiModelProperty(value = "是否进行了硅料厂家拆分")
    public String getIsSiMfrsName(){
        if (Objects.equals(isSiMfrs,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否已经进行硅片等级拆分
     */
    @ApiModelProperty(value = "是否已经进行硅片等级拆分")
    private Integer isWaferGrade;
    @ApiModelProperty(value = "是否已经进行硅片等级拆分")
    public String getIsWaferGradeName(){
        if (Objects.equals(isWaferGrade,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否已经进行了加工类型拆分
     */
    @ApiModelProperty(value = "是否已经进行了加工类型拆分")
    private Integer isProcessCategory;
    @ApiModelProperty(value = "是否已经进行了加工类型拆分")
    public String getIsProcessCategoryName(){
        if (Objects.equals(isProcessCategory,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否进行了计划确认
     */
    @ApiModelProperty(value = "是否进行了计划确认")
    private Integer isConfirmPlan;
    @ApiModelProperty(value = "是否进行了计划确认")
    public String getIsConfirmPlanName(){
        if (Objects.equals(isConfirmPlan,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否创建了入库计划
     */
    @ApiModelProperty(value = "是否创建了入库计划")
    private Integer isCreateInstockPlan;
    @ApiModelProperty(value = "是否创建了入库计划")
    public String getIsCreateInstockPlanName(){
        if (Objects.equals(isCreateInstockPlan,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否发送了邮件
     */
    @ApiModelProperty(value = "是否发送了邮件")
    private Integer isSendEmail;
    @ApiModelProperty(value = "是否发送了邮件")
    public String getIsSendEmailName(){
        if (Objects.equals(isSendEmail,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 哪个月排的
     */
    @ApiModelProperty(value = "哪个月排的")
    private String scheduleMonth;
}
