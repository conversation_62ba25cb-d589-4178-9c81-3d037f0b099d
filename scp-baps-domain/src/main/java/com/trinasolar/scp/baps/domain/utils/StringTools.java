package com.trinasolar.scp.baps.domain.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

public class StringTools {
    public static String joinWith(String separator,String ... values){

        if (values==null || values.length==0){
            return "";
        }
        StringBuilder sb=new StringBuilder();
        Arrays.stream(values).forEach(value->{
            value= Optional.ofNullable(value).orElse("");
            sb.append(separator);
            sb.append(value);
        });
        return  sb.substring(1);
    }


}
