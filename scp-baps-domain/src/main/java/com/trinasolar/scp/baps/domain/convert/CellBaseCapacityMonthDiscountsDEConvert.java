package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDiscountsDTO;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityDiscountsQuery;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityMonthDiscountsQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacityDiscountsSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellBaseCapacityMonthDiscounts;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityMonthDiscountsDTO;
import com.trinasolar.scp.baps.domain.excel.CellBaseCapacityMonthDiscountsExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacityMonthDiscountsSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Optional;

/**
 * IE产能打折月度（人力）表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-28 03:34:54
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovUtils.class, LovHeaderCodeConstant.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellBaseCapacityMonthDiscountsDEConvert extends BaseDEConvert<CellBaseCapacityMonthDiscountsDTO, CellBaseCapacityMonthDiscounts> {

    CellBaseCapacityMonthDiscountsDEConvert INSTANCE = Mappers.getMapper(CellBaseCapacityMonthDiscountsDEConvert.class);

    List<CellBaseCapacityMonthDiscountsExcelDTO> toExcelDTO(List<CellBaseCapacityMonthDiscountsDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkshopId()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkunitId()))"),
                    @Mapping(target = "d1" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD1(),2))"),
                    @Mapping(target = "d2" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD2(),2))"),
                    @Mapping(target = "d3" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD3(),2))"),
                    @Mapping(target = "d4" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD4(),2))"),
                    @Mapping(target = "d5" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD5(),2))"),
                    @Mapping(target = "d6" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD6(),2))"),
                    @Mapping(target = "d7" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD7(),2))"),
                    @Mapping(target = "d8" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD8(),2))"),
                    @Mapping(target = "d9" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD9(),2))"),
                    @Mapping(target = "d10" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD10(),2))"),
                    @Mapping(target = "d11" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD11(),2))"),
                    @Mapping(target = "d12" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD12(),2))"),
                    @Mapping(target = "d13" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD13(),2))"),
                    @Mapping(target = "d14" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD14(),2))"),
                    @Mapping(target = "d15" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD15(),2))"),
                    @Mapping(target = "d16" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD16(),2))"),
                    @Mapping(target = "d17" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD17(),2))"),
                    @Mapping(target = "d18" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD18(),2))"),
                    @Mapping(target = "d19" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD19(),2))"),
                    @Mapping(target = "d20" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD20(),2))"),
                    @Mapping(target = "d21" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD21(),2))"),
                    @Mapping(target = "d22" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD22(),2))"),
                    @Mapping(target = "d23" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD23(),2))"),
                    @Mapping(target = "d24" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD24(),2))"),
                    @Mapping(target = "d25" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD25(),2))"),
                    @Mapping(target = "d26" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD26(),2))"),
                    @Mapping(target = "d27" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD27(),2))"),
                    @Mapping(target = "d28" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD28(),2))"),
                    @Mapping(target = "d29" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD29(),2))"),
                    @Mapping(target = "d30" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD30(),2))"),
                    @Mapping(target = "d31" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.addPercentage(dto.getD31(),2))")
            }
    )
    CellBaseCapacityMonthDiscountsExcelDTO toExcelDTO(CellBaseCapacityMonthDiscountsDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    default void test( CellBaseCapacityMonthDiscountsExcelDTO dto){
        MapStrutUtil.getIdByName(LovHeaderCodeConstant.WORK_UNIT,dto.getWorkunit());
    }
    CellBaseCapacityMonthDiscounts saveDTOtoEntity(CellBaseCapacityMonthDiscountsSaveDTO saveDTO, @MappingTarget CellBaseCapacityMonthDiscounts entity);
    @Mappings({
    @Mapping(target = "isOverseaId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()).getLovLineId())"),
    @Mapping(target = "basePlaceId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlace()).getLovLineId())"),
    @Mapping(target = "workshopId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop()).getLovLineId())"),
            @Mapping(target = "workunitId" ,expression = "java(MapStrutUtil.getIdByName(LovHeaderCodeConstant.WORK_UNIT,dto.getWorkunit()))"),
    @Mapping(target = "d1" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD1(),4))"),
    @Mapping(target = "d2" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD2(),4))"),
    @Mapping(target = "d3" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD3(),4))"),
    @Mapping(target = "d4" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD4(),4))"),
    @Mapping(target = "d5" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD5(),4))"),
    @Mapping(target = "d6" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD6(),4))"),
    @Mapping(target = "d7" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD7(),4))"),
    @Mapping(target = "d8" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD8(),4))"),
    @Mapping(target = "d9" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD9(),4))"),
    @Mapping(target = "d10" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD10(),4))"),
    @Mapping(target = "d11" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD11(),4))"),
    @Mapping(target = "d12" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD12(),4))"),
    @Mapping(target = "d13" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD13(),4))"),
    @Mapping(target = "d14" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD14(),4))"),
    @Mapping(target = "d15" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD15(),4))"),
    @Mapping(target = "d16" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD16(),4))"),
    @Mapping(target = "d17" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD17(),4))"),
    @Mapping(target = "d18" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD18(),4))"),
    @Mapping(target = "d19" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD19(),4))"),
    @Mapping(target = "d20" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD20(),4))"),
    @Mapping(target = "d21" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD21(),4))"),
    @Mapping(target = "d22" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD22(),4))"),
    @Mapping(target = "d23" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD23(),4))"),
    @Mapping(target = "d24" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD24(),4))"),
    @Mapping(target = "d25" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD25(),4))"),
    @Mapping(target = "d26" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD26(),4))"),
    @Mapping(target = "d27" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD27(),4))"),
    @Mapping(target = "d28" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD28(),4))"),
    @Mapping(target = "d29" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD29(),4))"),
    @Mapping(target = "d30" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD30(),4))"),
    @Mapping(target = "d31" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.removePercentage(dto.getD31(),4))")
    })
    CellBaseCapacityMonthDiscountsSaveDTO excelDtoToSaveDto( CellBaseCapacityMonthDiscountsExcelDTO dto);

    List<CellBaseCapacityMonthDiscountsSaveDTO> excelDtoToSaveDto(List<CellBaseCapacityMonthDiscountsExcelDTO> excelDtos);
    @Mappings(
            {
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,query.getWorkshop(),language))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,query.getIsOversea(),language))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BASE_PLACE,query.getBasePlace(),language))"),
                    @Mapping(target = "workunit" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_UNIT,query.getWorkunit(),language))"),
            }
    )
    CellBaseCapacityMonthDiscountsQuery toCellBaseCapacityMonthDiscountsQueryCNNameByName(CellBaseCapacityMonthDiscountsQuery query, String language);
    @Mappings(
            {
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getBasePlaceId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkshopId()))"),
                    @Mapping(target = "workunit" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getWorkunitId()))")
            }
    )
    @Named("toCellBaseCapacityMonthDiscountsDTOSNameById")
    CellBaseCapacityMonthDiscountsDTO toCellBaseCapacityMonthDiscountsDTOSNameById(CellBaseCapacityMonthDiscountsDTO dto);
    @IterableMapping(qualifiedByName = "toCellBaseCapacityMonthDiscountsDTOSNameById")
    List<CellBaseCapacityMonthDiscountsDTO> toCellBaseCapacityMonthDiscountsDTOSNameById(List<CellBaseCapacityMonthDiscountsDTO> dtos);
    @Mappings(
            {
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshopId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOverseaId()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlaceId()))"),
                    @Mapping(target = "workunit" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_UNIT,dto.getWorkunitId()))")
            }
    )
    @Named("toCellBaseCapacityMonthDiscountsSaveDTOCNNameById")
    CellBaseCapacityMonthDiscountsSaveDTO toCellBaseCapacityMonthDiscountsSaveDTOCNNameById(CellBaseCapacityMonthDiscountsSaveDTO dto);
    @IterableMapping(qualifiedByName = "toCellBaseCapacityMonthDiscountsSaveDTOCNNameById")
    List<CellBaseCapacityMonthDiscountsSaveDTO> toCellBaseCapacityMonthDiscountsSaveDTOCNNameById(List<CellBaseCapacityMonthDiscountsSaveDTO> saveDTOS);
}
