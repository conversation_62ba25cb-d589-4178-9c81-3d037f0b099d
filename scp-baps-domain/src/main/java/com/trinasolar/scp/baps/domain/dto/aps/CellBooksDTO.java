package com.trinasolar.scp.baps.domain.dto.aps;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 帐套信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "帐套信息DTO对象", description = "DTO对象")
public class CellBooksDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshopName;
    /**
     * 帐套类型:电池/组件/组件&电池
     */
    @ApiModelProperty(value = "帐套类型:电池/组件/组件&电池")
    private String booksType;
    /**
     * 帐套类型:电池/组件/组件&电池
     */
    @ApiModelProperty(value = "帐套类型:电池/组件/组件&电池")
    private String booksTypeName;
    /**
     * 账套
     */
    @ApiModelProperty(value = "账套")
    private String books;
    /**
     * 账套
     */
    @ApiModelProperty(value = "账套")
    private String booksName;
    /**
     * SO
     */
    @ApiModelProperty(value = "SO")
    private String so;
    /**
     * ERP代码
     */
    @ApiModelProperty(value = "ERP代码")
    private String erpCode;
    /**
     * 组件货位
     */
    @ApiModelProperty(value = "组件货位")
    private String moduleSlotting;
    /**
     * 组件子货位
     */
    @ApiModelProperty(value = "组件子货位")
    private String subModuleSlotting;
    /**
     * 电池货位
     */
    @ApiModelProperty(value = "电池货位")
    private String cellSlotting;
    /**
     * 电池子货位
     */
    @ApiModelProperty(value = "电池子货位")
    private String subCellSlotting;
    /**
     * 厂区
     */
    @ApiModelProperty(value = "厂区")
    private String factory;
}
