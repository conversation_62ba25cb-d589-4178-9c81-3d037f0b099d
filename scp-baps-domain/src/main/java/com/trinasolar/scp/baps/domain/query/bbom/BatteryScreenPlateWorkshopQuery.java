package com.trinasolar.scp.baps.domain.query.bbom;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 车间级网版切换维护
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
@ApiModel(value = "BatteryScreenPlateWorkshop查询条件", description = "查询条件")
@Accessors(chain = true)
public class BatteryScreenPlateWorkshopQuery extends PageDTO implements Serializable {

    @ApiModelProperty(value = "ID主键")
    private Long id;

    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;

    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;

    @ApiModelProperty(value = "单玻")
    private String singleGlassFlag;

    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    @ApiModelProperty(value = "生产车间")
    private String workshop;

    @ApiModelProperty(value = "正电极网版细栅")
    private String positiveElectrodeScreenFineGrid;

    @ApiModelProperty(value = "背电极网版细栅")
    private String negativeElectrodeScreenFineGrid;

    @ApiModelProperty(value = "有效期起")
    private LocalDate effectiveStartDate;

    @ApiModelProperty(value = "有效期止")
    private LocalDate effectiveEndDate;

    @ApiModelProperty(value = "备注")
    private String remark;
}
