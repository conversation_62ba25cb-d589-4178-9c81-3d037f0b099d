package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 工单生成预览
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_wip")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_wip SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_wip SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellWip extends BasePO implements Serializable{
 private static final long serialVersionUID=1L;

 /**
  * ID主键
  */
 @Id
 @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
         strategy = GenerationType.SEQUENCE)
 @GenericGenerator(
         name = "SnowflakeIdGeneratorConfig",
         strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
 @ApiModelProperty(value = "ID主键")
 @Column(name = "id")
 private Long id;

 /**
  * 工单号
  */
 @ApiModelProperty(value = "工单号")
 @Column(name = "wip_code")
 private String wipCode;

 /**
  * 分类
  */
 @ApiModelProperty(value = "分类")
 @Column(name = "category")
 private String category;

 /**
  * 装配件
  */
 @ApiModelProperty(value = "装配件")
 @Column(name = "item_code")
 private String itemCode;

 /**
  * 状态
  */
 @ApiModelProperty(value = "状态")
 @Column(name = "state")
 private String state;

 /**
  * 车间
  */
 @ApiModelProperty(value = "车间")
 @Column(name = "workshop")
 private String workshop;

 /**
  * 起始时间
  */
 @ApiModelProperty(value = "起始时间")
 @Column(name = "start_time")
 private LocalDateTime startTime;

 /**
  * 结束时间
  */
 @ApiModelProperty(value = "结束时间")
 @Column(name = "end_time")
 private LocalDateTime endTime;

 /**
  * 项目编码
  */
 @ApiModelProperty(value = "项目编码")
 @Column(name = "project_code")
 private String projectCode;

 /**
  * 工艺路线替代项
  */
 @ApiModelProperty(value = "工艺路线替代项")
 @Column(name = "alternate_routing_designator")
 private String alternateRoutingDesignator;

 /**
  * bom替代项
  */
 @ApiModelProperty(value = "bom替代项")
 @Column(name = "alternate_bom_designator")
 private String alternateBomDesignator;

 /**
  * 组织id
  */
 @ApiModelProperty(value = "组织id")
 @Column(name = "organization_id")
 private String organizationId;

 /**
  * 是否哈密瓜
  */
 @ApiModelProperty(value = "是否哈密瓜")
 @Column(name = "is_h")
 private String isH;

 /**
  * 数量
  */
 @ApiModelProperty(value = "数量")
 @Column(name = "qty")
 private BigDecimal qty;

 /**
  * 保税形态
  */
 @ApiModelProperty(value = "保税形态")
 @Column(name = "bonded_form")
 private String bondedForm;

 /**
  * 外协厂家
  */
 @ApiModelProperty(value = "外协厂家")
 @Column(name = "outsourcing_factory")
 private String outsourcingFactory;

 /**
  * 是否提前投产
  */
 @ApiModelProperty(value = "是否提前投产")
 @Column(name = "is_advance_production")
 private String isAdvanceProduction;

 /**
  * 是否外售
  */
 @ApiModelProperty(value = "是否外售")
 @Column(name = "is_sell")
 private String isSell;

 /**
  * 可靠性标识
  */
 @ApiModelProperty(value = "可靠性标识")
 @Column(name = "reliability")
 private String reliability;

 /**
  * 验证日期
  */
 @ApiModelProperty(value = "验证日期")
 @Column(name = "verification_date")
 private LocalDateTime verificationDate;

 /**
  * 月份
  */
 @ApiModelProperty(value = "月份")
 @Column(name = "month")
 private String month;
 /**
  * 最終邮件确认发布的版本
  */
 @ApiModelProperty(value = "最終邮件确认发布的版本")
 @Column(name = "final_version")
 private String finalVersion;
 /**
  * 国内海外Id
  */
 @ApiModelProperty(value = "国内海外Id")
 @Column(name = "is_oversea_id")
 private Long isOverseaId;

 /**
  * 国内海外
  */
 @ApiModelProperty(value = "国内海外")
 @Column(name = "is_oversea")
 private String isOversea;
 /**
  * 返工标识
  */
 @ApiModelProperty(value = "返工标识")
 @Column(name = "rework_flag")
 private String reworkFlag;
}
