package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;


/**
 * 产能并行损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellParallelLossExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 数据类型
     */
    @ExcelProperty(value = "数据类型")
    private String parallelType;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型1")
    private String cellsType;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型2")
    private String cellsTypeTwo;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 线体数量
     */
    @ExcelProperty(value = "线体数量")
    private  BigDecimal lineNumber;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 损失产能
     */
    @ExcelProperty(value = "损失产能（万片）")
    private BigDecimal loss;
    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    @ExcelIgnore
    private String unit;
}
