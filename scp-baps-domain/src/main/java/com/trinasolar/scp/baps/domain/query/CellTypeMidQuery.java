package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 电池类型转换表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@ApiModel(value = "CellTypeMid查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellTypeMidQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 电池类型
         */
        @ApiModelProperty(value = "电池类型")
    private String cellsType;
        /**
         * 电池类型左
         */
        @ApiModelProperty(value = "电池类型左")
    private String cellsTypeLeft;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
