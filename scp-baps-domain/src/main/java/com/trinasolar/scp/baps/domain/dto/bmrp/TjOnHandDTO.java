package com.trinasolar.scp.baps.domain.dto.bmrp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TjOnHandDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long tjOnHandLineId;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private String organizationCode;

    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    private String subInventoryCode;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Long itemId;

    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemDesc;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String uom;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 库存日期
     */
    @ApiModelProperty(value = "库存日期")
    private LocalDateTime inventoryDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * *种类
     */
    @ApiModelProperty(value = "电池种类(浆料)")
    private String subCategory;
    /**
     * *正背面
     */
    @ApiModelProperty(value = "正背面")
    private String frontBack;
    /**
     * *工位
     */
    @ApiModelProperty(value = "工位")
    private String position;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private List<String> workshopList;
}
