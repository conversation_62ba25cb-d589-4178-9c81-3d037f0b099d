package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 手动设置硅片等级DTO
 */
@ApiModel(value = "手动设置硅片等级DTO", description = "DTO对象")
@Data
public class HandSetWaferGradeDto {
    /**
     * 入库计划Id
     */
    @ApiModelProperty(value = "入库计划Id")
    private  Long id;
    /**
     * 良率
     */
    @ApiModelProperty(value = "良率")
    private BigDecimal cellFine;
    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String waferGrade;

}
