package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 电池资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-08 02:52:37
 */
@Data
@ApiModel(value = "CellResource查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellResourceQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 国内海外id
         */
        @ApiModelProperty(value = "国内海外id")
    private Long isOverseaId;
        /**
         * 国内海外
         */
        @ApiModelProperty(value = "国内海外")
    private String isOversea;
        /**
         * 生产基地id
         */
        @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
        /**
         * 生产基地
         */
        @ApiModelProperty(value = "生产基地")
    private String basePlace;
        /**
         * 生产车间id
         */
        @ApiModelProperty(value = "生产车间id")
    private Long workshopId;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshop;
        /**
         * 生产单元id
         */
        @ApiModelProperty(value = "生产单元id")
    private Long workunitId;
        /**
         * 生产单元
         */
        @ApiModelProperty(value = "生产单元")
    private String workunit;
        /**
         * 拆分标识
         */
        @ApiModelProperty(value = "拆分标识")
    private Integer isSplited;
        /**
         * 生产线体
         */
        @ApiModelProperty(value = "生产线体")
    private String lineName;
        /**
         * 资源组
         */
        @ApiModelProperty(value = "资源组")
    private String resourceGroup;
        /**
         * 线体数量
         */
        @ApiModelProperty(value = "线体数量")
        private BigDecimal numberLine;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源Id")
    private Long fromId;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private Integer ieorgrade;
}
