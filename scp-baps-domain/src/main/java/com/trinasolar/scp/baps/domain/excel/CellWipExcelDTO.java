package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import java.io.Serializable;


/**
 * 工单生成预览
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellWipExcelDTO {

 /**
  * ID主键
  */
 @ExcelProperty(value = "ID主键")
 private Long id;
 /**
  * 工单号
  */
 @ExcelProperty(value = "工单号")
 private String wipCode;
 /**
  * 分类
  */
 @ExcelProperty(value = "分类")
 private String category;
 /**
  * 装配件
  */
 @ExcelProperty(value = "装配件")
 private String itemCode;
 /**
  * 状态
  */
 @ExcelProperty(value = "状态")
 private String state;
 /**
  * 车间
  */
 @ExcelProperty(value = "车间")
 private String workshop;
 /**
  * 起始时间
  */
 @ExcelProperty(value = "起始时间")
 private LocalDateTime startTime;
 /**
  * 结束时间
  */
 @ExcelProperty(value = "结束时间")
 private LocalDateTime endTime;
 /**
  * 项目编码
  */
 @ExcelProperty(value = "项目编码")
 private String projectCode;
 /**
  * 工艺路线替代项
  */
 @ExcelProperty(value = "工艺路线替代项")
 private String alternateRoutingDesignator;
 /**
  * bom替代项
  */
 @ExcelProperty(value = "bom替代项")
 private String alternateBomDesignator;
 /**
  * 组织id
  */
 @ExcelProperty(value = "组织id")
 private String organizationId;
 /**
  * 是否哈密瓜
  */
 @ExcelProperty(value = "是否哈密瓜")
 private String isH;
 /**
  * 数量
  */
 @ExcelProperty(value = "数量")
 private BigDecimal qty;
 /**
  * 保税形态
  */
 @ExcelProperty(value = "保税形态")
 private String bondedForm;
 /**
  * 外协厂家
  */
 @ExcelProperty(value = "外协厂家")
 private String outsourcingFactory;
 /**
  * 是否提前投产
  */
 @ExcelProperty(value = "是否提前投产")
 private String isAdvanceProduction;
 /**
  * 是否外售
  */
 @ExcelProperty(value = "是否外售")
 private String isSell;
 /**
  * 可靠性标识
  */
 @ExcelProperty(value = "可靠性标识")
 private String reliability;
 /**
  * 验证日期
  */
 @ExcelProperty(value = "验证日期")
 private LocalDateTime verificationDate;
}
