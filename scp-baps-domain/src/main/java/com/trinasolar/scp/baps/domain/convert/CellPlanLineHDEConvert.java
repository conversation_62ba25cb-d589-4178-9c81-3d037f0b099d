package com.trinasolar.scp.baps.domain.convert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.entity.CellPlanLine;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineH;
import com.trinasolar.scp.baps.domain.entity.CellProductionPlan;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineSiliconTotalQuery;
import com.trinasolar.scp.baps.domain.query.SiliconSplitQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划H表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class , LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanLineHDEConvert extends BaseDEConvert<CellPlanLineHDTO, CellPlanLineH> {

    CellPlanLineHDEConvert INSTANCE = Mappers.getMapper(CellPlanLineHDEConvert.class);

    @Override
    CellPlanLineH toEntity(CellPlanLineHDTO cellPlanLineHDTO);

    @Override
    List<CellPlanLineH> toEntity(List<CellPlanLineHDTO> list);
    @IterableMapping(qualifiedByName = "toEntityFromCellPlanLineDto")
    List<CellPlanLineH> toEntityFromCellPlanLineDto(List<CellPlanLineDTO> list);
    @Named("toEntityFromCellPlanLineDto")
    CellPlanLineH toEntityFromCellPlanLineDto(CellPlanLineDTO dto);
}
