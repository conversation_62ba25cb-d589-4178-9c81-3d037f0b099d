package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.baps.domain.utils.CustomDateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * IE产能打折（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellBaseCapacityDiscountsExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间",converter = CustomDateConverter.class)
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间",converter = CustomDateConverter.class)
    private LocalDate endTime;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    @ExcelIgnore
    private String workunit;
    /**
     * 比例
     */
    @ExcelProperty(value = "比例")
    private String ratio;
    /**
     * IE确认
     */
    @ExcelProperty(value = "IE确认")
    @ExcelIgnore
    private String ieConfirm;
    /**
     * 计划确认
     */
    @ExcelProperty(value = "计划确认")
    @ExcelIgnore
    private String planConfirm;
    /**
     * 导入人
     */
    @ExcelProperty(value = "导入人")
    @ExcelIgnore
    private String importer;
    /**
     * 版本号
     */
    @ExcelProperty(value = "版本号")
    @ExcelIgnore
    private String discountVersion;
}
