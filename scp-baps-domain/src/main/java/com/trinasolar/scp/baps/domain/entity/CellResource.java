package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 电池资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-08 02:52:37
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_resource")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_resource SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_resource SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CellResource extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内海外id
     */
    @ApiModelProperty(value = "国内海外id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 生产基地id
     */
    @ApiModelProperty(value = "生产基地id")
    @Column(name = "base_place_id")
    private Long basePlaceId;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 生产车间id
     */
    @ApiModelProperty(value = "生产车间id")
    @Column(name = "workshop_id")
    private Long workshopId;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 生产单元id
     */
    @ApiModelProperty(value = "生产单元id")
    @Column(name = "workunit_id")
    private Long workunitId;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;

    /**
     * 拆分标识
     */
    @ApiModelProperty(value = "拆分标识")
    @Column(name = "is_splited")
    private Integer isSplited;

    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    @Column(name = "line_name")
    private String lineName;

    /**
     * 资源组
     */
    @ApiModelProperty(value = "资源组")
    @Column(name = "resource_group")
    private String resourceGroup;

    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    @Column(name = "number_line")
    private BigDecimal numberLine;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源Id")
    @Column(name = "from_id")
    private Long fromId;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    @Column(name = "ieorgrade")
    private Integer ieorgrade;

    //忽略startTime属性和数据库对应
    @Transient
    private LocalDate startTime;
    @Transient
    private LocalDate endTime;
}
