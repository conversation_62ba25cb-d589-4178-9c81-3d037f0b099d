package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 电池类型转化规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-11 01:42:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellTypeRuleExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 解析顺序
     */
    @ExcelProperty(value = "解析顺序")
    private String sortNo;
    /**
     * 模块
     */
    @ExcelProperty(value = "模块")
    private String module;
    /**
     * 模块
     */
    @ExcelProperty(value = "模块名称")
    private String moduleName;
    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    private String illustrate;
    /**
     * 解析字段
     */
    @ExcelProperty(value = "解析字段")
    private String field;
    /**
     * 解析字段名称
     */
    @ExcelProperty(value = "解析字段名称")
    private String fieldName;
    /**
     * 规则
     */
    @ExcelProperty(value = "规则")
    private String rule;
    /**
     * 结果
     */
    @ExcelProperty(value = "结果")
    private String result;
}
