package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 硅片拆分规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 07:44:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SiliconSplitRule保存参数", description = "保存参数")
public class SiliconSplitRuleSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String waferGrade;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 电池良率
     */
    @ApiModelProperty(value = "电池良率")
    private BigDecimal cellFine;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endDate;
    /**
     * 分片规则类型
     */
    @ApiModelProperty(value = "分片规则类型")
    private String ruleType;
    /**
     * 硅料厂家Id
     */
    @ApiModelProperty(value = "硅料厂家Id")
    private String siMfrsId;
    /**
     * 硅片等级Id
     */
    @ApiModelProperty(value = "硅片等级Id")
    private String waferGradeId;
}
