package com.trinasolar.scp.baps.domain.factory;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.trinasolar.scp.baps.domain.HTraceWarnType;
import com.trinasolar.scp.baps.domain.constant.CommonConstant;
import com.trinasolar.scp.baps.domain.dto.HTraceDayResult;
import com.trinasolar.scp.baps.domain.dto.HTracePlan;
import com.trinasolar.scp.baps.domain.dto.HTraceSupply;
import com.trinasolar.scp.baps.domain.utils.MathUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: HTraceFactory
 * @date 2024/6/12 09:59
 */
@Data
public class HTraceFactory {

    private HTraceFactory() {
    }

    private static class HTraceFactoryInstance {
        private static final HTraceFactory FACTORY = new HTraceFactory();
    }

    private List<HTraceDayResult> resultList = Lists.newArrayList();

    /**
     * H库存结余
     */
    private Map<String, BigDecimal> hSurplusMap = Maps.newConcurrentMap();

    /**
     * 常规库存结余
     */
    private Map<String, BigDecimal> nonSurplusMap = Maps.newConcurrentMap();

    /**
     * 初始化
     */
    public static void init() {
        HTraceFactory factory = HTraceFactoryInstance.FACTORY;
        factory.resultList.clear();
        factory.hSurplusMap.clear();
        factory.nonSurplusMap.clear();
    }

    /**
     * 按key获取数据或生成新对象
     *
     * @param hTrace
     * @param dayResult
     * @param supplyMap
     * @return
     */
    static HTraceSupply getOrBuild(String hTrace, HTraceDayResult dayResult, Map<String, HTraceSupply> supplyMap) {
        if (supplyMap.containsKey(hTrace)) {
            return supplyMap.get(hTrace);
        }
        if (Objects.nonNull(dayResult)) {
            HTraceSupply supply = HTraceSupply.build(dayResult, hTrace);
            supplyMap.put(hTrace, supply);
            return supply;
        } else {
            return new HTraceSupply();
        }
    }

    /**
     * 计算
     *
     * @param supplyMap
     * @param planMap
     */
    public static void compute(Map<String, HTraceSupply> supplyMap, Map<String, List<HTracePlan>> planMap) {
        HTraceFactory factory = HTraceFactoryInstance.FACTORY;
        //结果
        List<HTraceDayResult> results = Lists.newArrayList();
        //供应、电池排产、安全库存数据生成
        results.addAll(HTraceDayResult.build(planMap));
        HTraceDayResult dayResult = results.stream().findAny().orElse(null);
        /*************************************** H对象属性 ***************************************/
        //H的被兼容掉数量
        BigDecimal hCompatibleQuantity = BigDecimal.ZERO;
        //H的供应
        HTraceSupply hTraceSupply = getOrBuild(CommonConstant.H, dayResult, supplyMap);
        //H的供应数量
        BigDecimal hSupplyQuantity = Optional.ofNullable(hTraceSupply).map(HTraceSupply::getQuantity).orElse(BigDecimal.ZERO);
        //H的结余数量
        BigDecimal hBalance = factory.hSurplusMap.getOrDefault(hTraceSupply.hSurplusSign(), BigDecimal.ZERO);
        //计入结余数量
        hTraceSupply.setSurplus(hBalance);
        //H的排产
        List<HTracePlan> hTracePlanList = planMap.getOrDefault(CommonConstant.H, Lists.newArrayList());
        //H的排产总数
        BigDecimal hPlanQuantity = hTracePlanList.stream().map(HTracePlan::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //H的安全库存总数
        BigDecimal hSafetyStockQuantity = hTracePlanList.stream().map(HTracePlan::getSafetyStockQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //H的剩余 = 当天剩余（供应-安全库存-排产） + 历史结余
        BigDecimal hSurplus = hSupplyQuantity.subtract(hSafetyStockQuantity).subtract(hPlanQuantity).add(hBalance);
        /*************************************** 常规对象属性 ***************************************/
        //常规的供应
        HTraceSupply traceSupply = getOrBuild(CommonConstant.NON, dayResult, supplyMap);
        //常规的供应数量
        BigDecimal supplyQuantity = Optional.ofNullable(traceSupply).map(HTraceSupply::getQuantity).orElse(BigDecimal.ZERO);
        //常规的结余数量
        BigDecimal balance = factory.nonSurplusMap.getOrDefault(traceSupply.hSurplusSign(), BigDecimal.ZERO);
        //计入结余数量
        traceSupply.setSurplus(balance);
        //常规的排产
        List<HTracePlan> tracePlanList = planMap.getOrDefault(CommonConstant.NON, Lists.newArrayList());
        //常规的排产总数
        BigDecimal planQuantity = tracePlanList.stream().map(HTracePlan::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //常规的安全库存总数
        BigDecimal safetyStockQuantity = tracePlanList.stream().map(HTracePlan::getSafetyStockQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //常规的剩余 = 当天剩余（供应-安全库存-排产） + 历史结余
        BigDecimal surplus = supplyQuantity.subtract(safetyStockQuantity).subtract(planQuantity).add(balance);
        //供应数据产生
        results.addAll(HTraceDayResult.build(HTraceWarnType.SUPPLY, supplyMap));

        /*************************************** 计算 ***************************************/
        //若 常规剩余 < 0 且 H剩余 > 0，可以用H的可用库存，兼容剩余的常规的电池排产，直至兼容完，若兼容不完，则报缺。基于完成兼容的，修改电池排产的H排产量；计算中需要剔除安全库存的数量
        if (hSurplus.compareTo(BigDecimal.ZERO) > 0 && surplus.compareTo(BigDecimal.ZERO) < 0) {
            //兼容数量
            BigDecimal compatibleQuantity = supplyQuantity.add(balance).subtract(safetyStockQuantity);
            //自身剩余的量，不需要兼容的数量
            BigDecimal selfSurlyQuantity = BigDecimal.ZERO.compareTo(compatibleQuantity) > 0 ? BigDecimal.ZERO : compatibleQuantity;
            compatibleQuantity = BigDecimal.ZERO.compareTo(compatibleQuantity) > 0 ? hSurplus : hSurplus.add(compatibleQuantity);

            //计算出H剩余被消耗的量
            hCompatibleQuantity = compatibleQuantity.compareTo(surplus.abs()) >= 0 ? surplus.abs() : hSurplus;
            //按排产数量排序，优先满足数量小的
            tracePlanList.sort(Comparator.comparing(HTracePlan::getQuantity));
            for (HTracePlan hTracePlan : tracePlanList) {
                BigDecimal quantity = hTracePlan.getQuantity();
                if (compatibleQuantity.compareTo(BigDecimal.ZERO) >= 0 && selfSurlyQuantity.compareTo(quantity) >= 0) {
                    //不需要兼容，自身的量就够满足
                    selfSurlyQuantity = selfSurlyQuantity.subtract(quantity);
                    compatibleQuantity = compatibleQuantity.subtract(quantity);
                } else {
                    if (compatibleQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        if (compatibleQuantity.compareTo(quantity) >= 0) {
                            //可以满足当前排产数据
                            compatibleQuantity = compatibleQuantity.subtract(quantity);
                        } else {
                            hTracePlan.setQuantity(quantity.subtract(compatibleQuantity));
                            quantity = compatibleQuantity;
                            compatibleQuantity = BigDecimal.ZERO;
                            //产生缺口
                            HTraceDayResult shortage = HTraceDayResult.build(HTraceWarnType.SHORTAGE, hTracePlan);
                            results.add(shortage);
                        }
                        //产生兼容数据
                        HTraceDayResult compatible = HTraceDayResult.build(HTraceWarnType.COMPATIBLE, hTracePlan, quantity.subtract(selfSurlyQuantity));
                        results.add(compatible);
                        selfSurlyQuantity = BigDecimal.ZERO;
                    } else {
                        //产生缺口
                        HTraceDayResult shortage = HTraceDayResult.build(HTraceWarnType.SHORTAGE, hTracePlan);
                        results.add(shortage);
                    }
                }
            }
        } else if (surplus.compareTo(BigDecimal.ZERO) < 0 || hSurplus.compareTo(BigDecimal.ZERO) < 0) {
            //常规缺口
            results.addAll(computeShortage(supplyQuantity, balance, safetyStockQuantity, tracePlanList));
            //H缺口
            results.addAll(computeShortage(hSupplyQuantity, hBalance, hSafetyStockQuantity, hTracePlanList));
        }
        /*************************************** 结余计算 ***************************************/
        //常规可用剩余
        balance = computeSurplus(balance, supplyQuantity, planQuantity, safetyStockQuantity, BigDecimal.ZERO);
        //H可用剩余
        hBalance = computeSurplus(hBalance, hSupplyQuantity, hPlanQuantity, hSafetyStockQuantity, hCompatibleQuantity);
        //结余数据
        results.addAll(HTraceDayResult.build(HTraceWarnType.BALANCE, supplyMap, balance, hBalance));
        //将H结余存入
        factory.hSurplusMap.put(hTraceSupply.hSurplusSign(), hBalance);
        factory.nonSurplusMap.put(hTraceSupply.hSurplusSign(), balance);
        //产生结果存入
        factory.resultList.addAll(results);
    }

    /**
     * 缺口计算
     *
     * @param planMap
     */
    public static void shortage(Map<String, List<HTracePlan>> planMap) {
        HTraceFactory factory = HTraceFactoryInstance.FACTORY;
        //结果
        List<HTraceDayResult> results = Lists.newArrayList();
        //供应、电池排产、安全库存数据生成
        results.addAll(HTraceDayResult.build(planMap));
        HTraceDayResult dayResult = results.stream().findAny().get();
        /*************************************** H对象属性 ***************************************/
        //H的被兼容掉数量
        BigDecimal hCompatibleQuantity = BigDecimal.ZERO;
        //H的供应数量
        BigDecimal hSupplyQuantity = BigDecimal.ZERO;
        //H的结余数量
        BigDecimal hBalance = factory.hSurplusMap.getOrDefault(dayResult.hSurplusSign(), BigDecimal.ZERO);
        //H的排产
        List<HTracePlan> hTracePlanList = planMap.getOrDefault(CommonConstant.H, Lists.newArrayList());
        //H的排产总数
        BigDecimal hPlanQuantity = hTracePlanList.stream().map(HTracePlan::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //H的安全库存总数
        BigDecimal hSafetyStockQuantity = hTracePlanList.stream().map(HTracePlan::getSafetyStockQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //H的剩余 = 当天剩余（供应-安全库存-排产） + 历史结余
        BigDecimal hSurplus = hSupplyQuantity.subtract(hSafetyStockQuantity).subtract(hPlanQuantity).add(hBalance);
        /*************************************** 常规对象属性 ***************************************/
        //常规的供应数量
        BigDecimal supplyQuantity = BigDecimal.ZERO;
        //常规的结余数量
        BigDecimal balance = factory.hSurplusMap.getOrDefault(dayResult.hSurplusSign(), BigDecimal.ZERO);
        //常规的排产
        List<HTracePlan> tracePlanList = planMap.getOrDefault(CommonConstant.NON, Lists.newArrayList());
        //常规的排产总数
        BigDecimal planQuantity = tracePlanList.stream().map(HTracePlan::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //常规的安全库存总数
        BigDecimal safetyStockQuantity = tracePlanList.stream().map(HTracePlan::getSafetyStockQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //常规的剩余 = 当天剩余（供应-安全库存-排产） + 历史结余
        BigDecimal surplus = supplyQuantity.subtract(safetyStockQuantity).subtract(planQuantity).add(balance);
        //供应数据
        results.addAll(HTraceDayResult.build(HTraceWarnType.SUPPLY, hBalance, balance, dayResult));

        //若 常规剩余 < 0 且 H剩余 > 0，可以用H的可用库存，兼容剩余的常规的电池排产，直至兼容完，若兼容不完，则报缺。基于完成兼容的，修改电池排产的H排产量
        if (hSurplus.compareTo(BigDecimal.ZERO) > 0 && surplus.compareTo(BigDecimal.ZERO) < 0) {
            //兼容数量
            BigDecimal compatibleQuantity = supplyQuantity.add(balance).subtract(safetyStockQuantity);
            //自身剩余的量，不需要兼容的数量
            BigDecimal selfSurlyQuantity = BigDecimal.ZERO.compareTo(compatibleQuantity) > 0 ? BigDecimal.ZERO : compatibleQuantity;
            compatibleQuantity = BigDecimal.ZERO.compareTo(compatibleQuantity) > 0 ? hSurplus : hSurplus.add(compatibleQuantity);
            //计算出H剩余被消耗的量
            hCompatibleQuantity = compatibleQuantity.compareTo(surplus.abs()) >= 0 ? surplus.abs() : hSurplus;

            //按排产数量排序，优先满足数量小的
            tracePlanList.sort(Comparator.comparing(HTracePlan::getQuantity));
            for (HTracePlan hTracePlan : tracePlanList) {
                BigDecimal quantity = hTracePlan.getQuantity();
                if (compatibleQuantity.compareTo(BigDecimal.ZERO) >= 0 && selfSurlyQuantity.compareTo(quantity) >= 0) {
                    //不需要兼容，自身的量就够满足
                    selfSurlyQuantity = selfSurlyQuantity.subtract(quantity);
                    compatibleQuantity = compatibleQuantity.subtract(quantity);
                } else {
                    if (compatibleQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        if (compatibleQuantity.compareTo(quantity) >= 0) {
                            //可以满足当前排产数据
                            compatibleQuantity = compatibleQuantity.subtract(quantity);
                        } else {
                            hTracePlan.setQuantity(quantity.subtract(compatibleQuantity));
                            quantity = compatibleQuantity;
                            compatibleQuantity = BigDecimal.ZERO;
                            //产生缺口
                            HTraceDayResult shortage = HTraceDayResult.build(HTraceWarnType.SHORTAGE, hTracePlan);
                            results.add(shortage);
                        }
                        //产生兼容数据
                        HTraceDayResult compatible = HTraceDayResult.build(HTraceWarnType.COMPATIBLE, hTracePlan, quantity.subtract(selfSurlyQuantity));
                        results.add(compatible);
                        selfSurlyQuantity = BigDecimal.ZERO;
                    } else {
                        //产生缺口
                        HTraceDayResult shortage = HTraceDayResult.build(HTraceWarnType.SHORTAGE, hTracePlan);
                        results.add(shortage);
                    }
                }
            }
        } else if (surplus.compareTo(BigDecimal.ZERO) < 0 || hSurplus.compareTo(BigDecimal.ZERO) < 0) {
            //常规缺口
            results.addAll(computeShortage(supplyQuantity, balance, safetyStockQuantity, tracePlanList));
            //H缺口
            results.addAll(computeShortage(hSupplyQuantity, hBalance, hSafetyStockQuantity, hTracePlanList));
        }
        /*************************************** 结余计算 ***************************************/
        //常规可用剩余
        balance = computeSurplus(balance, supplyQuantity, planQuantity, safetyStockQuantity, BigDecimal.ZERO);
        //H可用剩余
        hBalance = computeSurplus(hBalance, hSupplyQuantity, hPlanQuantity, hSafetyStockQuantity, hCompatibleQuantity);
        //结余数据
        results.addAll(HTraceDayResult.build(HTraceWarnType.BALANCE, balance, hBalance, dayResult));
        //将H结余存入
        factory.hSurplusMap.put(dayResult.hSurplusSign(), hBalance);
        factory.nonSurplusMap.put(dayResult.hSurplusSign(), balance);
        //产生结果存入
        factory.resultList.addAll(results);
    }

    /**
     * 计算缺口数据
     *
     * @param supplyQuantity
     * @param balance
     * @param safetyStockQuantity
     * @param tracePlanList
     * @return
     */
    private static List<HTraceDayResult> computeShortage(BigDecimal supplyQuantity, BigDecimal balance, BigDecimal safetyStockQuantity, List<HTracePlan> tracePlanList) {
        List<HTraceDayResult> results = Lists.newArrayList();
        //兼容数量
        BigDecimal compatibleQuantity = supplyQuantity.add(balance).subtract(safetyStockQuantity);
        compatibleQuantity = BigDecimal.ZERO.compareTo(compatibleQuantity) > 0 ? BigDecimal.ZERO : compatibleQuantity;
        //按排产数量排序，优先满足数量小的
        tracePlanList.sort(Comparator.comparing(HTracePlan::getQuantity));
        for (HTracePlan hTracePlan : tracePlanList) {
            BigDecimal quantity = hTracePlan.getQuantity();
            if (!MathUtils.checkIsZero(compatibleQuantity) && compatibleQuantity.compareTo(quantity) >= 0) {
                //可以满足当前排产数据
                compatibleQuantity = compatibleQuantity.subtract(quantity);
            } else {
                hTracePlan.setQuantity(quantity.subtract(compatibleQuantity));
                compatibleQuantity = BigDecimal.ZERO;
                //产生缺口
                HTraceDayResult shortage = HTraceDayResult.build(HTraceWarnType.SHORTAGE, hTracePlan);
                results.add(shortage);
            }
        }
        return results;
    }

    /**
     * 计算剩余
     *
     * @param balance
     * @param supplyQuantity
     * @param demandQuantity
     * @param safetyStockQuantity
     * @param hCompatibleQuantity
     * @return
     */
    private static BigDecimal computeSurplus(BigDecimal balance, BigDecimal supplyQuantity, BigDecimal demandQuantity, BigDecimal safetyStockQuantity, BigDecimal hCompatibleQuantity) {
        //剩余数量 = 需求 + 结余 - 安全库存 - 被兼容数量（如果是H）
        BigDecimal surplus = supplyQuantity.add(balance).subtract(safetyStockQuantity).subtract(hCompatibleQuantity);
        if (MathUtils.checkPositiveNumber(surplus)) {
            //剩余量 - 当天需求 >= 0，结余 = 安全库存 + 剩余数量- 需求量；剩余量 - 当天需求 < 0 结余 = 当天安全库存
            if (surplus.subtract(demandQuantity).compareTo(BigDecimal.ZERO) >= 0) {
                return safetyStockQuantity.add(surplus.subtract(demandQuantity));
            } else {
                return safetyStockQuantity;
            }
        } else {
            return supplyQuantity.add(balance);
        }
    }

    /**
     * 获取结果
     *
     * @return
     */
    public static List<HTraceDayResult> result() {
        return new ArrayList<>(HTraceFactoryInstance.FACTORY.resultList);
    }

}
