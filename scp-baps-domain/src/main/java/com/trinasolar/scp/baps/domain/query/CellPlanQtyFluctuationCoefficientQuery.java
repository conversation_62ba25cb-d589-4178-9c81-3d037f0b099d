package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 投产计划浮动系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-30 01:49:53
 */
@Data
@ApiModel(value = "CellPlanQtyFluctuationCoefficient查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellPlanQtyFluctuationCoefficientQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 开始时间
         */
        @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
        /**
         * 结束时间
         */
        @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshop;
        /**
         * 计划数量浮动系数
         */
        @ApiModelProperty(value = "计划数量浮动系数")
    private BigDecimal fluctuationCoefficient;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
