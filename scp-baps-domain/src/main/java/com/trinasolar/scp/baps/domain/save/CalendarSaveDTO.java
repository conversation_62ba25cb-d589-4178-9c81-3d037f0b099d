package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;

import javax.persistence.Column;


/**
 * 生产日历
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "Calendar保存参数", description = "保存参数")
public class CalendarSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    @ApiModelProperty(value = "ie爬坡产能标识0代表ie，1代表爬坡")
    private Integer ieorgrade;
    @ApiModelProperty(value = "数据来源id(IE或爬坡id)")
    private Long fromid;
    /**
     * 日历类型
     */
    @ApiModelProperty(value = "日历类型")
    private String type;
    /**
     * 资源单元Id
     */
    @ApiModelProperty(value = "资源单元Id")
    private Long workunitId;
    /**
     * 资源单元
     */
    @ApiModelProperty(value = "资源单元")
    private String workunit;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDate date;
    /**
     * 出勤代码
     */
    @ApiModelProperty(value = "出勤代码")
    private String shiftcode;
    /**
     * 资源量
     */
    @ApiModelProperty(value = "资源量")
    private BigDecimal defaultqty;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer sortorder;
    /**
     * 标准产线数
     */
    @ApiModelProperty(value = "标准产线数")
    private BigDecimal numLines;
}
