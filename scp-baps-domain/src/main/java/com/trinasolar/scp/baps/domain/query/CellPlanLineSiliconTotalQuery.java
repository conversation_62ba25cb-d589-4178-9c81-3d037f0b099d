package com.trinasolar.scp.baps.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
@Data
@ApiModel(value = "手动设置硅片拆分Query条件", description = "手动设置硅片拆分Query条件")
public class CellPlanLineSiliconTotalQuery implements Serializable {

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    private String processCategory;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private  String siMfrs;
    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String waferGrade;
    /**
     * 要修改哪几天
     */
    @ApiModelProperty(value = "要修改哪几天")
   List<Integer> days=new ArrayList<>();
}
