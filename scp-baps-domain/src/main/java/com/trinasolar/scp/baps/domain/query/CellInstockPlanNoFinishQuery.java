package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 实际入库表(待转化)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
@Data
@ApiModel(value = "CellInstockPlanNoFinish查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellInstockPlanNoFinishQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    private String locatorCode;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 产品等级Id
     */
    @ApiModelProperty(value = "产品等级Id")
    private Long productionGradeId;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    private Long cellSourceId;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 透明双玻Id
     */
    @ApiModelProperty(value = "透明双玻Id")
    private Long transparentDoubleGlassId;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private Long hTraceId;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 是否小区域国家
     */
    @ApiModelProperty(value = "是否小区域国家")
    private String isRegionalCountry;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private Long regionalCountryId;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 实际入库时间
     */
    @ApiModelProperty(value = "实际入库时间")
    private LocalDateTime finishedDate;
    /**
     * 实际入库数量（片）
     */
    @ApiModelProperty(value = "实际入库数量（片）")
    private BigDecimal finishedQty;
    /**
     * erp原始创建时间
     */
    @ApiModelProperty(value = "erp原始创建时间")
    private LocalDateTime creationDate;
    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学Id")
    private Long aestheticsId;
}
