package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;


/**
 * 电池良率月拆分表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-05 05:51:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellFineMonthExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellType;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 年份
     */
    @ExcelProperty(value = "年份")
    private Integer year;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private Integer month;
    /**
     * 良率
     */
    @ExcelProperty(value = "良率")
    private BigDecimal yield;
}
