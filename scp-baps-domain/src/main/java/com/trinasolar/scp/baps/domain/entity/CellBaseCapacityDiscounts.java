package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * IE产能打折（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_base_capacity_discounts")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_base_capacity_discounts SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_base_capacity_discounts SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellBaseCapacityDiscounts extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @Column(name = "start_time")
    private LocalDate startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_time")
    private LocalDate endTime;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间id")
    @Column(name = "workshopid")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元id")
    @Column(name = "workunitid")
    private Long workunitid;
    /**
     * 比例
     */
    @ApiModelProperty(value = "比例")
    @Column(name = "ratio")
    private BigDecimal ratio;

    /**
     * IE确认
     */
    @ApiModelProperty(value = "IE确认")
    @Column(name = "ie_confirm")
    private Integer ieConfirm;

    /**
     * 计划确认
     */
    @ApiModelProperty(value = "计划确认")
    @Column(name = "plan_confirm")
    private Integer planConfirm;

    /**
     * 导入人
     */
    @ApiModelProperty(value = "导入人")
    @Column(name = "importer")
    private String importer;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @Column(name = "discount_version")
    private String discountVersion;
    @ApiModelProperty(value = "来源id")
    @Column(name = "from_id")
    private Long fromId;


}
