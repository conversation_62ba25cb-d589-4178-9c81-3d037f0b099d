package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;


/**
 * 计划与qop
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellVersionPlanQopExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 国内/海外Id
     */
    @ExcelProperty(value = "国内/海外Id")
    @ExcelIgnore
    private Long isOverseaId;
    /**
     * 国内/海外
     */
    @ExcelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 电池类型编号
     */
    @ExcelProperty(value = "电池类型编号")
    @ExcelIgnore
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    @ExcelIgnore
    private Long workshopId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 产能利用率
     */
    @ExcelProperty(value = "产能利用率")
    @ExcelIgnore
    private BigDecimal rate;
    @ExcelProperty(value = "产能利用率")
    private String ratePercent;
    public String getRatePercent(){
        return MapStrutUtil.addPercentage(rate,2);
    }
    /**
     * qop目标
     */
    @ExcelProperty(value = "qop目标")
    private BigDecimal target;
    /**
     * 计划产能
     */
    @ExcelProperty(value = "计划产能")
    private BigDecimal planCapacity;
    /**
     * 差异
     */
    @ExcelProperty(value = "差异")
    private BigDecimal gap;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
