package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * ERP工单分类返回对象
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022年9月10日09:30:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ClassCodeDTO对象", description = "DTO对象")
public class ClassCodeDTO {
 /**
  * organizationId
  */
 @ApiModelProperty(value = "organizationId")
 private Long organizationId;
 /**
  * 组织名称
  */
 @ApiModelProperty(value = "组织名称")
 private String organizationName;
 /**
  * 组织编码
  */
 @ApiModelProperty(value = "组织编码")
 private String organizationCode;
 /**
  * 描述
  */
 @ApiModelProperty(value = "描述")
 private String description;
 /**
  * 分类
  */
 @ApiModelProperty(value = "分类")
 private String classType;
 /**
  * 分类描述
  */
 @ApiModelProperty(value = "分类描述")
 private String classTypeDesc;
 /**
  * 分类
  */
 @ApiModelProperty(value = "分类")
 private String classCode;
 /**
  * 车间
  */
 @ApiModelProperty(value = "车间")
 private String attribute6;

 /**
  * attribute2
  */
 @ApiModelProperty(value = "attribute2")
 private String attribute2;
 /**
  * 失效日期
  */
 @ApiModelProperty(value = "失效日期")
 private LocalDateTime disableDate;
}