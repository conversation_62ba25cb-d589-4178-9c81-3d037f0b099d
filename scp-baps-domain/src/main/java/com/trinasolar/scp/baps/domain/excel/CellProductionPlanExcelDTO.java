package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;

import java.io.Serializable;


/**
 * 投产计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellProductionPlanExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ExcelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ExcelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元Id
     */
    @ExcelProperty(value = "生产单元Id")
    private Long workunitId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * 生产线体
     */
    @ExcelProperty(value = "生产线体")
    private String lineName;
    /**
     * 线体数量
     */
    @ExcelProperty(value = "线体数量")
    private BigDecimal numberLine;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * MV
     */
    @ExcelProperty(value = "MV")
    private BigDecimal cellMv;
    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String version;
    /**
     * 投产计划汇总版本号
     */
    @ExcelProperty(value = "投产计划汇总版本号")
    private String finalVersion;
    /**
     * 5A料号
     */
    @ExcelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 数量（片）
     */
    @ExcelProperty(value = "数量（片）")
    private BigDecimal qtyPc;
}
