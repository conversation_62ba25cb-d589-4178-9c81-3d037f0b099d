package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 物流线路优先级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:51:00
 */
@Data
@ApiModel(value = "PriorityLogisticsLine查询条件", description = "查询条件")
@Accessors(chain = true)
public class PriorityLogisticsLineQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 生产基地Id
         */
        @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
        /**
         * 生产基地
         */
        @ApiModelProperty(value = "生产基地")
    private String basePlace;
        /**
         * 需求基地Id
         */
        @ApiModelProperty(value = "需求基地Id")
    private Long demandBasePlaceId;
        /**
         * 需求基地
         */
        @ApiModelProperty(value = "需求基地")
    private String demandBasePlace;
        /**
         * 系列
         */
        @ApiModelProperty(value = "系列")
    private String series;
        /**
         * 优先级
         */
        @ApiModelProperty(value = "优先级")
    private Integer priority;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
