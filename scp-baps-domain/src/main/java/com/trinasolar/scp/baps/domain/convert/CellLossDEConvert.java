package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.query.CellLossQuery;
import com.trinasolar.scp.baps.domain.save.CellLossSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellLoss;
import com.trinasolar.scp.baps.domain.dto.CellLossDTO;
import com.trinasolar.scp.baps.domain.excel.CellLossExcelDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 产能切换损失表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Mapper(componentModel = "spring",
        imports = {MapStrutUtil.class, LovUtils.class, LovHeaderCodeConstant.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellLossDEConvert extends BaseDEConvert<CellLossDTO, CellLoss> {

    CellLossDEConvert INSTANCE = Mappers.getMapper(CellLossDEConvert.class);

    List<CellLossExcelDTO> toExcelDTO(List<CellLossDTO> dtos);

    CellLossExcelDTO toExcelDTO(CellLossDTO dto);
    @Override
    @Mappings(
            {
                    @Mapping(target = "oldProduct" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellLoss.getOldProductId()))"),
                    @Mapping(target = "newProduct" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellLoss.getNewProductId()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(cellLoss.getWorkshopid()))")

            }
    )
    CellLossDTO toDto(CellLoss cellLoss);

    @Mappings(
            {
                    @Mapping(target = "oldProductId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getOldProduct()).getLovLineId())"),
                    @Mapping(target = "newProductId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getNewProduct()).getLovLineId())"),
                    @Mapping(target = "workshopid" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop()).getLovLineId())")
            }
    )
    CellLossSaveDTO excelDtoToSaveDto(CellLossExcelDTO excelDTO);

    List<CellLossSaveDTO> excelDtoToSaveDto(List<CellLossExcelDTO> dto);
    @Mappings(
            {
                    @Mapping(target = "oldProduct" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,query.getOldProduct(),language))"),
                    @Mapping(target = "newProduct" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,query.getNewProduct(),language))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.WORK_SHOP,query.getWorkshop(),language))")
            }
    )
    CellLossQuery toCellLossCNNameQuery(CellLossQuery query, String language);
    @Mappings(
            {
                    @Mapping(target = "oldProduct" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BATTERY_TYPE,dto.getOldProductId()))"),
                    @Mapping(target = "newProduct" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.BATTERY_TYPE,dto.getNewProductId()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameById(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshopid()))")
            }
    )
    @Named("toCellLossCNNameSaveDTOById")
    CellLossSaveDTO toCellLossCNNameSaveDTOById(CellLossSaveDTO dto);
    @IterableMapping(qualifiedByName = "toCellLossCNNameSaveDTOById")
    List<CellLossSaveDTO> toCellLossCNNameSaveDTOById(List<CellLossSaveDTO> saveDTOS);
}
