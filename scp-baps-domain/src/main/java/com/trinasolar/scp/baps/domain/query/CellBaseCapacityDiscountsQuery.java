package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * IE产能打折（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Data
@ApiModel(value = "CellBaseCapacityDiscounts查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellBaseCapacityDiscountsQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 开始时间
         */
        @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
        /**
         * 结束时间
         */
        @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外id")
    private Long isOverseaId;
        /**
         * 生产基地
         */
        @ApiModelProperty(value = "生产基地")
    private String basePlace;
        /**
         * 生产车间
         */
        @ApiModelProperty(value = "生产车间")
    private String workshop;
        /**
         * 生产单元
         */
        @ApiModelProperty(value = "生产单元")
    private String workunit;
        /**
         * 比例
         */
        @ApiModelProperty(value = "比例")
    private BigDecimal ratio;
        /**
         * IE确认
         */
        @ApiModelProperty(value = "IE确认")
    private Integer ieConfirm;
        /**
         * 计划确认
         */
        @ApiModelProperty(value = "计划确认")
    private Integer planConfirm;
        /**
         * 导入人
         */
        @ApiModelProperty(value = "导入人")
    private String importer;
        /**
         * 版本号
         */
        @ApiModelProperty(value = "版本号")
    private String discountVersion;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
    @ApiModelProperty(value = "来源id")
    private Long fromId;
}
