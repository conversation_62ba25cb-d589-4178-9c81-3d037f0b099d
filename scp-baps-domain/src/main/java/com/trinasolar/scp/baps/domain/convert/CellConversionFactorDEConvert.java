package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.MwCoefficientDTO;
import com.trinasolar.scp.baps.domain.query.CellConversionFactorQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellConversionFactor;
import com.trinasolar.scp.baps.domain.dto.CellConversionFactorDTO;
import com.trinasolar.scp.baps.domain.excel.CellConversionFactorExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellConversionFactorSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 万片与兆瓦折算系数 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-31 10:33:29
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellConversionFactorDEConvert extends BaseDEConvert<CellConversionFactorDTO, CellConversionFactor> {

    CellConversionFactorDEConvert INSTANCE = Mappers.getMapper(CellConversionFactorDEConvert.class);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getName(dto.getCellsTypeId()))"),
            }
    )
    CellConversionFactorExcelDTO toExcelDTO(CellConversionFactorDTO dto);
    List<CellConversionFactorExcelDTO> toExcelDTO(List<CellConversionFactorDTO> dtos);



    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true),
            @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsType()).getLovLineId())")
    })
    CellConversionFactor saveDTOtoEntity(CellConversionFactorSaveDTO dto, @MappingTarget CellConversionFactor entity);
    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "cellsTypeId" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsType()).getLovLineId())")
    })
    CellConversionFactorSaveDTO  excelDtoToSaveDto(CellConversionFactorExcelDTO dto);
    List<CellConversionFactorSaveDTO>  excelDtoToSaveDto(List<CellConversionFactorExcelDTO> excelDTO);

    List<CellConversionFactorSaveDTO>convertMwCoeffcient(List<MwCoefficientDTO>coefficientDTOList);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByNameLang( LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellsType(),lang))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByNameLang( LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea(),lang))"),
            }
    )
    CellConversionFactorQuery toCellConversionFactorQueryCNName(CellConversionFactorQuery dto, String lang);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(LovUtils.getName(dto.getCellsTypeId()))"),
                    @Mapping(target = "isOversea" ,expression = "java(LovUtils.getName(dto.getIsOverseaId()))")
            }
    )
    @Override
    CellConversionFactorDTO toDto(CellConversionFactor dto);
}
