package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 投产计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Data
@ApiModel(value = "CellPlanLineVersionQueryDto查询投产计划执行状态条件", description = "查询投产计划执行状态条件")
@Accessors(chain = true)
public class CellPlanLineVersionQueryDto   implements Serializable {


        /**
         * 国内海外Id
         */
        @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
        /**
         * 国内海外
         */
        @ApiModelProperty(value = "国内海外")
    private String isOversea;
        /**
         * 排产月份
         */
        @ApiModelProperty(value = "排产月份")
    private String month;

}
