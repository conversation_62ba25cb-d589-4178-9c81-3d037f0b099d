package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

import org.hibernate.annotations.GenericGenerator;

/**
 * 入库计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_instock_plan_version")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_instock_plan_version SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_instock_plan_version SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellInstockPlanVersion extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    @Column(name = "month")
    private String month;

    /**
     * 最終确认发布的版本
     */
    @ApiModelProperty(value = "最終确认发布的版本")
    @Column(name = "version")
    private String version;

    /**
     * 来自哪个投产版本
     */
    @ApiModelProperty(value = "来自哪个投产版本")
    @Column(name = "from_version")
    private String fromVersion;

    /**
     * 是否进行了A-拆分
     */
    @ApiModelProperty(value = "是否进行了A-拆分")
    @Column(name = "is_a_split")
    private Integer isASplit;

    /**
     * 是否进行了低效拆分
     */
    @ApiModelProperty(value = "是否进行了低效拆分")
    @Column(name = "is_le_split")
    private Integer isLESplit;

    @ApiModelProperty(value = "是否进行了A-拆分")
    public String getIsASplitName(){
        if (Objects.equals(isASplit,1)){
            return "已执行";
        }else{
            return "";
        }
    }

    /**
     * 是否已经进行了透明双玻拆分
     */
    @ApiModelProperty(value = "是否已经进行了透明双玻拆分")
    @Column(name = "is_transparent_double_glass")
    private Integer isTransparentDoubleGlass;
    @ApiModelProperty(value = "是否已经进行了透明双玻拆分")
    public String getIsTransparentDoubleGlassName(){
        if (Objects.equals(isTransparentDoubleGlass,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否已经进行了分档规则拆分
     */
    @ApiModelProperty(value = "是否已经进行了分档规则拆分")
    @Column(name = "is_grade_rule")
    private Integer isGradeRule;
    @ApiModelProperty(value = "是否已经进行了分档规则拆分")
    public String getIsGradeRuleName(){
        if (Objects.equals(isGradeRule,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否进行了计划确认
     */
    @ApiModelProperty(value = "是否进行了计划确认")
    @Column(name = "is_confirm_plan")
    private Integer isConfirmPlan;
    @ApiModelProperty(value = "是否进行了计划确认")
    public String getIsConfirmPlanName(){
        if (Objects.equals(isConfirmPlan,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 是否进行了邮件发送
     */
    @ApiModelProperty(value = "是否进行了邮件发送")
    @Column(name = "is_send_email")
    private Integer isSendEmail;
    @ApiModelProperty(value = "是否进行了邮件发送")
    public String getIsSendEmailName(){
        if (Objects.equals(isSendEmail,1)){
            return "已执行";
        }else{
            return "";
        }
    }
    /**
     * 哪个月排的
     */
    @ApiModelProperty(value = "哪个月排的")
    @Column(name = "schedule_month")
    private String scheduleMonth;

    /**
     * 是否已经进行了美学拆分
     */
    @ApiModelProperty(value = "是否已经进行了美学拆分")
    @Column(name = "is_aesthetics")
    private Integer isAesthetics;
}
