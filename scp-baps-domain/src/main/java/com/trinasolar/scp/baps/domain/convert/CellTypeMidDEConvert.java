package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellTypeMid;
import com.trinasolar.scp.baps.domain.dto.CellTypeMidDTO;
import com.trinasolar.scp.baps.domain.excel.CellTypeMidExcelDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池类型转换表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellTypeMidDEConvert extends BaseDEConvert<CellTypeMidDTO, CellTypeMid> {

    CellTypeMidDEConvert INSTANCE = Mappers.getMapper(CellTypeMidDEConvert.class);

    List<CellTypeMidExcelDTO> toExcelDTO(List<CellTypeMidDTO> dtos);

    CellTypeMidExcelDTO toExcelDTO(CellTypeMidDTO dto);
    @Mappings({
            @Mapping(target = "id",source = "id",ignore = true),
            @Mapping(target = "cellsType",source = "batteryName"),
            @Mapping(target = "cellsTypeLeft",expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCellsTypeLeft(dto.getBatteryName()))")
    })
    CellTypeMid toCellTypeMid(BatteryTypeMainDTO dto);
    List<CellTypeMid> toCellTypeMid(List<BatteryTypeMainDTO> dtos);
}
