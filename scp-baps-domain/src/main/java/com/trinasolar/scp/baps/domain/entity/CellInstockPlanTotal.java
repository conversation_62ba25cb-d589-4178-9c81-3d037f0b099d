package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 入库计划汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-26 11:20:51
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_instock_plan_total")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_instock_plan_total SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_instock_plan_total SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellInstockPlanTotal extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshop_id")
    private Long workshopId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cells_type_id")
    private Long cellsTypeId;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    @Column(name = "h_trace")
    private String hTrace;
    /**
     * 是否H兼容
     */
    @ApiModelProperty(value = "是否H兼容")
    @Column(name = "h_change_flag")
    private String hChangeFlag;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    @Column(name = "aesthetics")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    @Column(name = "transparent_double_glass")
    private String transparentDoubleGlass;

    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    @Column(name = "production_grade")
    private String productionGrade;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    @Column(name = "regional_country")
    private String regionalCountry;
    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    @Column(name = "item_code")
    private String itemCode;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    @Column(name = "cell_source")
    private String cellSource;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 万片
     */
    @ApiModelProperty(value = "万片")
    @Column(name = "qty_thousand_pc")
    private BigDecimal qtyThousandPc;

    /**
     * MV
     */
    @ApiModelProperty(value = "MV")
    @Column(name = "cell_mv")
    private BigDecimal cellMv;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "version")
    private String version;

    /**
     * 投产表数据版本
     */
    @ApiModelProperty(value = "投产表数据版本")
    @Column(name = "from_version")
    private String fromVersion;



    /**
     * d1
     */
    @ApiModelProperty(value = "d1")
    @Column(name = "d1")
    private BigDecimal d1;

    /**
     * d2
     */
    @ApiModelProperty(value = "d2")
    @Column(name = "d2")
    private BigDecimal d2;

    /**
     * d3
     */
    @ApiModelProperty(value = "d3")
    @Column(name = "d3")
    private BigDecimal d3;

    /**
     * d4
     */
    @ApiModelProperty(value = "d4")
    @Column(name = "d4")
    private BigDecimal d4;

    /**
     * d5
     */
    @ApiModelProperty(value = "d5")
    @Column(name = "d5")
    private BigDecimal d5;

    /**
     * d6
     */
    @ApiModelProperty(value = "d6")
    @Column(name = "d6")
    private BigDecimal d6;

    /**
     * d7
     */
    @ApiModelProperty(value = "d7")
    @Column(name = "d7")
    private BigDecimal d7;

    /**
     * d8
     */
    @ApiModelProperty(value = "d8")
    @Column(name = "d8")
    private BigDecimal d8;

    /**
     * d9
     */
    @ApiModelProperty(value = "d9")
    @Column(name = "d9")
    private BigDecimal d9;

    /**
     * d10
     */
    @ApiModelProperty(value = "d10")
    @Column(name = "d10")
    private BigDecimal d10;

    /**
     * d11
     */
    @ApiModelProperty(value = "d11")
    @Column(name = "d11")
    private BigDecimal d11;

    /**
     * d12
     */
    @ApiModelProperty(value = "d12")
    @Column(name = "d12")
    private BigDecimal d12;

    /**
     * d13
     */
    @ApiModelProperty(value = "d13")
    @Column(name = "d13")
    private BigDecimal d13;

    /**
     * d14
     */
    @ApiModelProperty(value = "d14")
    @Column(name = "d14")
    private BigDecimal d14;

    /**
     * d15
     */
    @ApiModelProperty(value = "d15")
    @Column(name = "d15")
    private BigDecimal d15;

    /**
     * d16
     */
    @ApiModelProperty(value = "d16")
    @Column(name = "d16")
    private BigDecimal d16;

    /**
     * d17
     */
    @ApiModelProperty(value = "d17")
    @Column(name = "d17")
    private BigDecimal d17;

    /**
     * d18
     */
    @ApiModelProperty(value = "d18")
    @Column(name = "d18")
    private BigDecimal d18;

    /**
     * d19
     */
    @ApiModelProperty(value = "d19")
    @Column(name = "d19")
    private BigDecimal d19;

    /**
     * d20
     */
    @ApiModelProperty(value = "d20")
    @Column(name = "d20")
    private BigDecimal d20;

    /**
     * d21
     */
    @ApiModelProperty(value = "d21")
    @Column(name = "d21")
    private BigDecimal d21;

    /**
     * d22
     */
    @ApiModelProperty(value = "d22")
    @Column(name = "d22")
    private BigDecimal d22;

    /**
     * d23
     */
    @ApiModelProperty(value = "d23")
    @Column(name = "d23")
    private BigDecimal d23;

    /**
     * d24
     */
    @ApiModelProperty(value = "d24")
    @Column(name = "d24")
    private BigDecimal d24;

    /**
     * d25
     */
    @ApiModelProperty(value = "d25")
    @Column(name = "d25")
    private BigDecimal d25;

    /**
     * d26
     */
    @ApiModelProperty(value = "d26")
    @Column(name = "d26")
    private BigDecimal d26;

    /**
     * d27
     */
    @ApiModelProperty(value = "d27")
    @Column(name = "d27")
    private BigDecimal d27;

    /**
     * d28
     */
    @ApiModelProperty(value = "d28")
    @Column(name = "d28")
    private BigDecimal d28;

    /**
     * d29
     */
    @ApiModelProperty(value = "d29")
    @Column(name = "d29")
    private BigDecimal d29;

    /**
     * d30
     */
    @ApiModelProperty(value = "d30")
    @Column(name = "d30")
    private BigDecimal d30;

    /**
     * d31
     */
    @ApiModelProperty(value = "d31")
    @Column(name = "d31")
    private BigDecimal d31;

    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_space")
    private String mainGridSpace;

    @ApiModelProperty(value = "需求来源")
    @Column(name = "source_type")
    private String sourceType;

    @ApiModelProperty(value = "来源类型Id")
    @Column(name = "source_type_id")
    private Long sourceTypeId;

    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    @Column(name = "supply_method")
    private String supplyMethod;

    /**
     * 供应方式Id
     */
    @ApiModelProperty(value = "供应方式Id")
    @Column(name = "supply_method_id")
    private Long supplyMethodId;

    /**
     * 背面细栅
     */
    @ApiModelProperty(value = "背面细栅")
    @Column(name = "back_fine_grid")
    private String backFineGrid;
    /**
     * 正面细栅
     */
    @ApiModelProperty(value = "正面细栅")
    @Column(name = "front_fine_grid")
    private String frontFineGrid;
    /**
     * 硅片厚度
     */
    @ApiModelProperty(value = "硅片厚度")
    @Column(name = "silicon_wafer_thickness")
    private String siliconWaferThickness;
    /**
     * 硅片尺寸
     */
    @ApiModelProperty(value = "硅片尺寸")
    @Column(name = "silicon_wafer_size")
    private String siliconWaferSize;

    @ApiModelProperty(value = "证书编号")
    @Column(name = "cert_code")
    private String certCode;

    @ApiModelProperty(value = "配比")
    @Column(name = "ratio_code")
    private String ratioCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    @Column(name = "item_desc")
    private String itemDesc;

    /**
     * 生命周期状态
     */
    @ApiModelProperty(value = "生命周期状态")
    @Column(name = "lifecycle_state")
    private String lifecycleState;

    @ApiModelProperty(value = "临时量产标识")
    @Column(name = "is_temporary_output")
    private String isTemporaryOutput;
}
