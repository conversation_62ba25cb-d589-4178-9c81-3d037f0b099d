package com.trinasolar.scp.baps.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * 投产计划H表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "入库计划H表DTO对象", description = "DTO对象")
@ToString
public class CellPlanLineHDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 订单表
     */
    @ApiModelProperty(value = "订单表")
    private String orderCode;
    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceType;

    /**
     * 需求版本号
     */
    @ApiModelProperty(value = "需求版本号")
    private String demandVersion;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workunitId;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 产线数量
     */
    @ApiModelProperty(value = "产线数量")
    private BigDecimal numberLine;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    private String demandBasePlace;
    /**
     * 是否电池特殊要求
     */
    @ApiModelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    private String lowResistance;
    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQty;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "计算开始时间")
    private LocalDate culStartDate;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * MV
     */
    @ApiModelProperty(value = "MV")
    private BigDecimal cellMv;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;
    /**
     * 最終邮件确认发布的版本
     *
     */
    @ApiModelProperty(value = "最終邮件确认发布的版本")
    private String finalVersion;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 数量（片）
     */
    @ApiModelProperty(value = "数量（片）")
    private BigDecimal qtyPc;
    /**
     * 数量（片）拆分前的值
     */
    @ApiModelProperty(value = "数量（片）拆分前的值")
    private BigDecimal oldQtyPc;
    /**
     * 汇总明细行id
     */
    @ApiModelProperty(value = "汇总明细行id")
    private Long demandSummarylinesId;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;

    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;
    /**
     * 起始效率
     */
    @ApiModelProperty(value = "起始效率")
    private String startEfficiency;
    /**
     * 最大分布效率
     */
    @ApiModelProperty(value = "最大分布效率")
    private String maxEfficiency;
    /**
     * 电池特殊单号
     */
    @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;
    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    private LocalDate demandDate;
    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String waferGrade;
    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    private String processCategory;
    /**
     * 需求说明
     */
    @ApiModelProperty(value = "需求说明")
    private String demandRemark;
    /**
     * 分档规则
     */
    @ApiModelProperty(value = "分档规则")
    private String gradeRule;
    /**
     * 是否进行了硅料厂家拆分
     */
    @ApiModelProperty(value = "是否进行了硅料厂家拆分")
    private  Integer isSiMfrs;
    /**
     * 是否进行了硅片等级拆分
     */
    @ApiModelProperty(value = "是否进行了硅片等级拆分")
    private  Integer isWaferGrade;
    /**
     * 是否进行了A-拆分
     */
    @ApiModelProperty(value = "是否进行了A-拆分")
    private Integer isASplit;
    /**
     * 是否进行了透明双玻拆分
     */
    @ApiModelProperty(value = "是否进行了透明双玻拆分")
    private  Integer  isTransparentDoubleGlass;
    /**
     * 验证标识
     */
    @ApiModelProperty(value = "验证标识")
    private  String verificationMark;

    /**
     * 出库日期
     */
    @ApiModelProperty(value = "出库日期")
    private String outboundDate;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String leadType;
    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    private  String demandSource;
    /**
     * 是否进行了加工类型拆分
     */
    @ApiModelProperty(value = "是否进行了加工类型拆分")
    private Integer isProcessCategory;
    /**
     * 是否进行手动加工类型指定
     */
    @ApiModelProperty(value = "是否进行手动加工类型指定")
    private Integer isHandProcessCategory;
    /**
     * 加工类型处理优先级
     */
    @ApiModelProperty(value = "加工类型处理优先级")
    private Integer processCategoryPriority;
    /**
     * gap差额
     */
    @ApiModelProperty(value = "gap差额")
    private BigDecimal gap;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * bbom中对应的Id
     */
    @ApiModelProperty(value = "bbom中对应的Id")
    private Long bbomId;
    /**
     * 对应排产计划中的Id
     */
    @ApiModelProperty(value = "对应排产计划中的Id")
    private Long fromId;
    /**
     * 拆分前的Id
     */
    @ApiModelProperty(value = "拆分前的Id")
    private Long parentId;
    /**
     * 原入库开始时间
     */
    @ApiModelProperty(value = "原入库开始时间")
    private LocalDateTime oldStartTime;
    /**
     * 原入库结束时间
     */
    @ApiModelProperty(value = "原入库结束时间")
    private LocalDateTime oldEndTime;
    /**
     * 投产计划确认
     */
    @ApiModelProperty(value = "投产计划确认")
    private Integer confirmPlan;
    /**
     * 原排产月份
     */
    @ApiModelProperty(value = "原入库月份")
    private String oldMonth;
    /**
     * 哪个月份排的
     */
    @ApiModelProperty(value = "哪个月份排的")
    private String scheduleMonth;
    /**
     * 多个时间拼接
     */
    @ApiModelProperty(value = "多个时间拼接")
    private String strTime;

    @JsonIgnore
    private BigDecimal curQty;

    @JsonIgnore
    private List<CellPlanSplitDTO> cellPlanSplitDTOList;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    public String getStartTimeStr(){
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return fmt.format(this.getStartTime());
    }
    //料号匹配分组维度 生成电池料号匹配头表数据
    public String filedGroup(){
        return StringTools.joinWith("/",this.basePlace,this.workshop,this.workunit,this.cellsType,
                this.hTrace,this.aesthetics,this.transparentDoubleGlass,this.regionalCountry,
                this.cellSource,this.waferGrade,this.cellMfrs,this.isSpecialRequirement,this.siMfrs
                ,this.screenPlateMfrs,this.silverPulpMfrs,this.lowResistance,this.demandBasePlace,this.processCategory,this.month,this.productionGrade,String.valueOf(this.numberLine)
        );
    }
    /**
     * 小区域国家Id
     */
    @ApiModelProperty(value = "小区域国家Id")
    private Long regionalCountryId;
    /**
     * 来源类型Id
     */
    @ApiModelProperty(value = "来源类型Id")
    private Long sourceTypeId;
    /**
     * H追溯Id
     */
    @ApiModelProperty(value = "H追溯Id")
    private Long hTraceId;

    /**
     * 美学Id
     */
    @ApiModelProperty(value = "美学Id")
    private Long aestheticsId;

    /**
     * 透明双玻Id
     */
    @ApiModelProperty(value = "透明双玻Id")
    private Long transparentDoubleGlassId;

    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    private Long cellSourceId;

    /**
     * 需求地Id
     */
    @ApiModelProperty(value = "需求地Id")
    private Long demandBasePlaceId;


    /**
     * 电池厂家Id
     */
    @ApiModelProperty(value = "电池厂家Id")
    private Long cellMfrsId;

    /**
     * 银浆厂家Id
     */
    @ApiModelProperty(value = "银浆厂家Id")
    private Long silverPulpMfrsId;
    /**
     * 硅料厂家Id
     */
    @ApiModelProperty(value = "硅料厂家Id")
    private Long siMfrsId;
    /**
     * 硅片等级Id
     */
    @ApiModelProperty(value = "硅片等级Id")
    private Long waferGradeId;
    /**
     * 加工类型Id
     */
    @ApiModelProperty(value = "加工类型Id")
    private Long processCategoryId;
    /**
     * 产品等级Id
     */
    @ApiModelProperty(value = "产品等级Id")
    private Long productionGradeId;
    /**
     * 片源级投产良率
     */
    @ApiModelProperty(value = "片源级投产良率")
    private BigDecimal waferYieldRatio;
    /**
     * 片源级A-比例
     */
    @ApiModelProperty(value = "片源级A-比例")
    private BigDecimal waferGradeRatio;
    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;
}
