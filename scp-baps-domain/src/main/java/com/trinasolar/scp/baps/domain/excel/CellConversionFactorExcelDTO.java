package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serializable;


/**
 * 万片与兆瓦折算系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-31 10:33:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellConversionFactorExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 国内海外Id
     */
    @ExcelProperty(value = "国内海外Id")
    @ExcelIgnore
    private Long isOverseaId;

    /**
     * 国内海外名称
     */
    @ExcelProperty(value = "国内海外名称")
    private String isOversea;
    /**
     * 电池类型Id
     */
    @ExcelProperty(value = "电池类型Id")
    @ExcelIgnore
    private Long cellsTypeId;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String cellsType;

    /**
     * 折算系数
     */
    @ExcelProperty(value = "折算系数")
    private BigDecimal conversionFactor;
}
