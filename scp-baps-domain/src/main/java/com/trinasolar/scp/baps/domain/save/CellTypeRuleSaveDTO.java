package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 电池类型转化规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-11 01:42:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellTypeRule保存参数", description = "保存参数")
public class CellTypeRuleSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 解析顺序
     */
    @ApiModelProperty(value = "解析顺序")
    private Integer sortNo;
    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private String module;
    /**
     * 说明
     */
    @ApiModelProperty(value = "说明")
    private String illustrate;
    /**
     * 解析字段
     */
    @ApiModelProperty(value = "解析字段")
    private String field;
    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String rule;
    /**
     * 结果
     */
    @ApiModelProperty(value = "结果")
    private String result;
}
