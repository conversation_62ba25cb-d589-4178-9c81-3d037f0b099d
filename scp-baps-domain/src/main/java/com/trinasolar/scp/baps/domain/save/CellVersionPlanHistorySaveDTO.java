package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 计划与上一版本计划对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellVersionPlanHistory保存参数", description = "保存参数")
public class CellVersionPlanHistorySaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内/海外Id
     */
    @ApiModelProperty(value = "国内/海外Id")
    private Long isOverseaId;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 电池类型编号
     */
    @ApiModelProperty(value = "电池类型编号")
    private Long cellTypeId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshopName;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    private String itemCode;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 月份计划（上上版）
     */
    @ApiModelProperty(value = "月份计划（上上版）")
    private BigDecimal monthV0;
    /**
     * 月份计划（上版）
     */
    @ApiModelProperty(value = "月份计划（上版）")
    private BigDecimal monthV1;
    /**
     * 月份计划新版
     */
    @ApiModelProperty(value = "月份计划新版")
    private BigDecimal monthV2;
    /**
     * 差异
     */
    @ApiModelProperty(value = "差异")
    private BigDecimal gap;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
