package com.trinasolar.scp.baps.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * IE产能打折（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "IE产能打折（人力）表DTO对象", description = "DTO对象")
public class CellBaseCapacityDiscountsDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外id")
    private Long isOverseaId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopid;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workunitid;
    /**
     * 比例
     */
    @ApiModelProperty(value = "比例")
    private String ratio;
    /**
     * IE确认
     */
    @ApiModelProperty(value = "IE确认")
    private String ieConfirm;
    /**
     * 计划确认
     */
    @ApiModelProperty(value = "计划确认")
    private String planConfirm;
    /**
     * 导入人
     */
    @ApiModelProperty(value = "导入人")
    private String importer;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String discountVersion;
    @ApiModelProperty(value = "来源id")
    private Long fromId;
}
