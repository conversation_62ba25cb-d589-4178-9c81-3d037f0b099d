package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellLocatorWorkshopRelation;
import com.trinasolar.scp.baps.domain.dto.CellLocatorWorkshopRelationDTO;
import com.trinasolar.scp.baps.domain.excel.CellLocatorWorkshopRelationExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellLocatorWorkshopRelationSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 货位对应车间关系表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 02:57:01
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
imports = {LovUtils.class, MapStrutUtil.class, LovHeaderCodeConstant.class})
public interface CellLocatorWorkshopRelationDEConvert extends BaseDEConvert<CellLocatorWorkshopRelationDTO, CellLocatorWorkshopRelation> {

    CellLocatorWorkshopRelationDEConvert INSTANCE = Mappers.getMapper(CellLocatorWorkshopRelationDEConvert.class);

    List<CellLocatorWorkshopRelationExcelDTO> toExcelDTO(List<CellLocatorWorkshopRelationDTO> dtos);

    CellLocatorWorkshopRelationExcelDTO toExcelDTO(CellLocatorWorkshopRelationDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellLocatorWorkshopRelation saveDTOtoEntity(CellLocatorWorkshopRelationSaveDTO saveDTO, @MappingTarget CellLocatorWorkshopRelation entity);

    @IterableMapping(qualifiedByName = "excelDtoToSaveDto")
    List<CellLocatorWorkshopRelationSaveDTO> excelDtoToSaveDto(List<CellLocatorWorkshopRelationExcelDTO> excelDtos);
    @Mappings({
            @Mapping(target = "workshop",expression = "java(MapStrutUtil.getValueByName(LovHeaderCodeConstant.WORK_SHOP,excelDto.getWorkshopName()))")
    })
    @Named("excelDtoToSaveDto")
     CellLocatorWorkshopRelationSaveDTO excelDtoToSaveDto( CellLocatorWorkshopRelationExcelDTO excelDto);
    default  void test( CellLocatorWorkshopRelationExcelDTO excelDto){

    }
}
