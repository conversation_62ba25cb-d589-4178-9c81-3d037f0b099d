package com.trinasolar.scp.baps.domain.save;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.trinasolar.scp.common.api.base.TokenDTO;


/**
 * 入库计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CellInstockPlanVersion保存参数", description = "保存参数")
public class CellInstockPlanVersionSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    private String month;
    /**
     * 最終确认发布的版本
     */
    @ApiModelProperty(value = "最終确认发布的版本")
    private String version;
    /**
     * 来自哪个投产版本
     */
    @ApiModelProperty(value = "来自哪个投产版本")
    private String fromVersion;
    /**
     * 是否进行了A-拆分
     */
    @ApiModelProperty(value = "是否进行了A-拆分")
    private Integer isASplit;
    /**
     * 是否已经进行了透明双玻拆分
     */
    @ApiModelProperty(value = "是否已经进行了透明双玻拆分")
    private Integer isTransparentDoubleGlass;
    /**
     * 是否已经进行了分档规则拆分
     */
    @ApiModelProperty(value = "是否已经进行了分档规则拆分")
    private Integer isGradeRule;
    /**
     * 是否进行了计划确认
     */
    @ApiModelProperty(value = "是否进行了计划确认")
    private Integer isConfirmPlan;
    /**
     * 是否进行了邮件发送
     */
    @ApiModelProperty(value = "是否进行了邮件发送")
    private Integer isSendEmail;
    /**
     * 哪个月排的
     */
    @ApiModelProperty(value = "哪个月排的")
    private String scheduleMonth;
    /**
     * 是否已经进行了美学拆分
     */
    @ApiModelProperty(value = "是否已经进行了美学拆分")
    private Integer isAesthetics;
}
