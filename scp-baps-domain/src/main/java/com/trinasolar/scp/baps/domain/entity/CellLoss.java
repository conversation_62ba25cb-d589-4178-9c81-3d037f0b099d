package com.trinasolar.scp.baps.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 产能切换损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_loss")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_loss SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_loss SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellLoss extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间id")
    @Column(name = "workshopid")
    private Long workshopid;
    /**
     * 起始月份
     */
    @ApiModelProperty(value = "起始月份")
    @Column(name = "start_month")
    private String startMonth;

    /**
     * 结束月份
     */
    @ApiModelProperty(value = "结束月份")
    @Column(name = "end_month")
    private String endMonth;

    /**
     * 电池类型1
     */
    @ApiModelProperty(value = "电池类型1")
    @Column(name = "old_product")
    private String oldProduct;
    /**
     * 电池类型1
     */
    @ApiModelProperty(value = "电池类型1")
    @Column(name = "old_product_id")
    private Long oldProductId;
    /**
     * 电池类型2
     */
    @ApiModelProperty(value = "电池类型2")
    @Column(name = "new_product")
    private String newProduct;
    /**
     * 电池类型2
     */
    @ApiModelProperty(value = "电池类型2")
    @Column(name = "new_product_id")
    private Long newProductId;
    /**
     * 损失时间(小时)
     */
    @ApiModelProperty(value = "损失时间(小时)")
    @Column(name = "loss_time")
    private BigDecimal lossTime;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;

    /**
     * 是否单玻1
     */
    @ApiModelProperty(value = "是否单玻1")
    @Column(name = "old_is_single_glass")
    private String oldIsSingleGlass;
    /**
     * 是否单玻2
     */
    @ApiModelProperty(value = "是否单玻2")
    @Column(name = "new_is_single_glass")
    private String newIsSingleGlass;
    /**
     * 是否小区域国家1
     */
    @ApiModelProperty(value = "是否小区域国家1")
    @Column(name = "old_is_regional_country")
    private String oldIsRegionalCountry;
    /**
     * 是否小区域国家2
     */
    @ApiModelProperty(value = "是否小区域国家2")
    @Column(name = "new_is_regional_country")
    private String newIsRegionalCountry;
    /**
     * 规格1
     */
    @ApiModelProperty(value = "规格1")
    @Column(name = "spec_one")
    private String specOne;
    /**
     * 规格2
     */
    @ApiModelProperty(value = "规格2")
    @Column(name = "spec_two")
    private String specTwo;


    @ApiModelProperty(value = "是否10.8_1")
    @Column(name = "is_1081")
    private String is1081;

    @ApiModelProperty(value = "是否10.8_2")
    @Column(name = "is_1082")
    private String is1082;
}
