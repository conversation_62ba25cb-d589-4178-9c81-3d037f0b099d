package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.CellGradeRuleMateDto;
import com.trinasolar.scp.baps.domain.dto.CellGradeRuleMateTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanSiliconTotalDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.SiliconSplitQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlan;
import com.trinasolar.scp.baps.domain.excel.CellInstockPlanExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanSaveDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:13:25
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellInstockPlanDEConvert extends BaseDEConvert<CellInstockPlanDTO, CellInstockPlan> {

    CellInstockPlanDEConvert INSTANCE = Mappers.getMapper(CellInstockPlanDEConvert.class);

    List<CellInstockPlanExcelDTO> toExcelDTO(List<CellInstockPlanDTO> dtos);

    CellInstockPlanExcelDTO toExcelDTO(CellInstockPlanDTO dto);

    @Override
    @Mappings({
            @Mapping(target ="certCode"),
            @Mapping(target ="ratioCode"),
            @Mapping(target ="ecsCode"),
    })
    List<CellInstockPlan> toEntity(List<CellInstockPlanDTO> entities);

    @Override
    @Mappings({
            @Mapping(target ="certCode"),
            @Mapping(target ="ratioCode")
    })
    List<CellInstockPlanDTO> toDto(List<CellInstockPlan> entities);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellInstockPlan saveDTOtoEntity(CellInstockPlanSaveDTO saveDTO, @MappingTarget CellInstockPlan entity);

    CellInstockPlanQuery toCellInstockPlanFromSiliconSplitQuery(SiliconSplitQuery siliconSplitQuery);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop()))")
            }
    )
    CellInstockPlanQuery toCellInstockPlanByName(CellInstockPlanQuery dto);

    CellInstockPlanQuery toCellInstockPlanQueryFromCellGradeRuleMateDto(CellGradeRuleMateDto dto);

    CellGradeRuleMateTotalDTO toCellGradeRuleMateTotalDTO(CellInstockPlanDTO dto);

    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.trinasolar.scp.baps.domain.utils.MapStrutUtil.getCNNameByValue(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    CellInstockPlanQuery toCellInstockPlanQueryByName(CellInstockPlanQuery query);
    @Mappings({
            @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,entity.getCellsType()))"),
            @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,entity.getIsOversea()))"),
            @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getBasePlace()))"),
            @Mapping(target = "workshop"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,entity.getWorkshop()))"),
            @Mapping(target = "workunit"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_UNIT,entity.getWorkunit()))"),
            @Mapping(target = "HTrace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,entity.getHTrace()))"),
            @Mapping(target = "aesthetics"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,entity.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlass"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,entity.getTransparentDoubleGlass()))"),
            @Mapping(target = "cellSource"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_SOURCE,entity.getCellSource()))"),
            @Mapping(target = "regionalCountry"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.REGIONAL_COUNTRY,entity.getRegionalCountry()))"),
            @Mapping(target = "productionGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,entity.getProductionGrade()))"),
            @Mapping(target = "sourceType"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BDM_DEMAND_SOURCE,entity.getSourceType()))"),
            @Mapping(target = "demandBasePlace"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getDemandBasePlace()))"),
            @Mapping(target = "cellMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_MFRS,entity.getCellMfrs()))"),
            @Mapping(target = "silverPulpMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SILVER_PULP_MFRS, entity.getSilverPulpMfrs()))"),
            @Mapping(target = "siMfrs"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SI_MFRS, entity.getSiMfrs()))"),
            @Mapping(target = "waferGrade"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WAFER_GRADE,entity.getWaferGrade()))"),
            @Mapping(target = "processCategory"  ,expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,entity.getProcessCategory()))")
    })
    @Named("toCellInstockPlanDTONameByCnName")
    CellInstockPlanDTO toCellInstockPlanDTONameByCnName(CellInstockPlanDTO entity);
    @IterableMapping(qualifiedByName = "toCellInstockPlanDTONameByCnName")
    List<CellInstockPlanDTO> toCellInstockPlanDTONameByCnName(List<CellInstockPlanDTO> noFindRuleCellPlanLines);

    CellInstockPlanSiliconTotalDTO toCellInstockPlanSiliconTotalDTO(CellInstockPlanDTO cellInstockPlanDTO);
}
