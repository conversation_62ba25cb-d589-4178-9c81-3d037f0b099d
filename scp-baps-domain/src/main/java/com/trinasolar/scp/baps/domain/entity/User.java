package com.trinasolar.scp.baps.domain.entity;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2022/4/22
 */
@Entity
@Table(name = "user", schema = "scp_system", catalog = "")
@ToString
public class User {
    private Long id;
    private String name;
    private Integer age;
    private String email;

    @Id
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @Basic
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Basic
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

}
