package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

import org.hibernate.annotations.GenericGenerator;

/**
 * 计划与qop
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_version_plan_qop")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_version_plan_qop SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_version_plan_qop SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellVersionPlanQop extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外Id
     */
    @ApiModelProperty(value = "国内/海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea_name")
    private String isOverseaName;

    /**
     * 电池类型编号
     */
    @ApiModelProperty(value = "电池类型编号")
    @Column(name = "cell_type_id")
    private Long cellTypeId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cell_type_name")
    private String cellTypeName;

    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshop_id")
    private Long workshopId;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop_name")
    private String workshopName;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 产能利用率
     */
    @ApiModelProperty(value = "产能利用率")
    @Column(name = "rate")
    private BigDecimal rate;

    /**
     * qop目标
     */
    @ApiModelProperty(value = "qop目标")
    @Column(name = "target")
    private BigDecimal target;

    /**
     * 计划产能
     */
    @ApiModelProperty(value = "计划产能")
    @Column(name = "plan_capacity")
    private BigDecimal planCapacity;

    /**
     * 差异
     */
    @ApiModelProperty(value = "差异")
    @Column(name = "gap")
    private BigDecimal gap;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
     //   if (!super.equals(o)) return false;
        CellVersionPlanQop that = (CellVersionPlanQop) o;
        return Objects.equals(cellTypeName, that.cellTypeName) && Objects.equals(workshopName, that.workshopName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), cellTypeName, workshopName);
    }
}
