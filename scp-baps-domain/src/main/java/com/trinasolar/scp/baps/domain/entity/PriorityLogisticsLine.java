package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 物流线路优先级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:51:00
 */
@Entity
@ToString
@Data
@Table(name = "baps_priority_logistics_line")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_priority_logistics_line SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_priority_logistics_line SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class PriorityLogisticsLine extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 需求基地Id
     */
    @ApiModelProperty(value = "需求基地Id")
    @Column(name = "demand_base_place_id")
    private Long demandBasePlaceId;

    /**
     * 需求基地
     */
    @ApiModelProperty(value = "需求基地")
    @Column(name = "demand_base_place")
    private String demandBasePlace;

    /**
     * 系列
     */
    @ApiModelProperty(value = "系列")
    @Column(name = "series")
    private String series;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @Column(name = "priority")
    private Integer priority;

    /**
     * 天数
     */
    @ApiModelProperty(value = "天数")
    @Column(name = "days")
    private Integer days;


}
