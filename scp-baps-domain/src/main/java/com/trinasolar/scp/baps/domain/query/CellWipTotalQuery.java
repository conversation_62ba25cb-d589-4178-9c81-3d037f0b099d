package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.time.LocalDate;

import com.trinasolar.scp.common.api.base.PageDTO;

/**
 * 开立工单明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Data
@ApiModel(value = "CellWipTotal查询条件", description = "查询条件")
@Accessors(chain = true)
public class CellWipTotalQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderCode;
    /**
     * 开单账号
     */
    @ApiModelProperty(value = "开单账号")
    private String billingAccount;
    /**
     * 开单日期
     */
    @ApiModelProperty(value = "开单日期")
    private LocalDateTime billingDate;
    /**
     * erp工单号
     */
    @ApiModelProperty(value = "erp工单号")
    private String erpOrderCode;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    private BigDecimal qty;
    /**
     * 已开单数量
     */
    @ApiModelProperty(value = "已开单数量")
    private BigDecimal qtyBilling;
    /**
     * 累计已开单数量
     */
    @ApiModelProperty(value = "累计已开单数量")
    private BigDecimal qtyBillingTotal;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 5a料号
     */
    @ApiModelProperty(value = "5a料号")
    private String itemFivea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 最終邮件确认发布的版本
     */
    @ApiModelProperty(value = "最終邮件确认发布的版本")
    private String finalVersion;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
