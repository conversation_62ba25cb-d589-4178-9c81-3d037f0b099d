package com.trinasolar.scp.baps.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2024/8/20
 */
@Entity
@ToString
@Data
@Table(name = "baps_manufacturing_switch_plan")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_manufacturing_switch_plan SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_manufacturing_switch_plan SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class ManufacturingSwitchPlan extends BasePO implements Serializable {
    private static final long serialVersionUID=1L;

    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    @ApiModelProperty(value = "制造工艺")
    @Column(name = "manufacturing")
    private String manufacturing;

    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    @ApiModelProperty(value = "生产基地Id")
    @Column(name = "base_place_id")
    private Long basePlaceId;

    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    @ApiModelProperty(value = "生产车间Id")
    @Column(name = "workshop_id")
    private Long workshopid;

    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;

    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit_id")
    private Long workunitid;

    @ApiModelProperty(value = "生产产线")
    @Column(name = "production_line")
    private String productionLine;

    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;

    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "cells_type_id")
    private Long cellsTypeId;

    @ApiModelProperty(value = "产能（单元）")
    @Column(name = "capacity_quantity")
    private BigDecimal capacityQuantity;

    @ApiModelProperty(value = "开始时间")
    @Column(name = "start_time")
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_time")
    private LocalDate endTime;
}
