package com.trinasolar.scp.baps.domain.query;
import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
@Data
@ApiModel(value = "手动设置硅片拆分集合Query条件", description = "手动设置硅片拆分集合Query条件")
public class CellPlanLineSiliconTotalListQuery extends PageDTO implements Serializable {
    @ApiModelProperty(value = "月份",notes = "月份")
    private  String month;
    @ApiModelProperty(value = "国内海外",notes = "国内海外")
    private String isOversea;

    private List<CellPlanLineSiliconTotalQuery> queryItems=new ArrayList<>();
}
