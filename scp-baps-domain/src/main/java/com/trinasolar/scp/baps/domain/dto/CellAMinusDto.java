package com.trinasolar.scp.baps.domain.dto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
@ApiModel(value = "A-拆分比例规则DTO（用于前端传递参数）",description = "A-拆分比例规则DTO（用于前端传递参数）")
public class CellAMinusDto {
    @ApiModelProperty(value = "月份")
    private String month;
    @ApiModelProperty(value = "车间")
    private String workshop;
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
}
