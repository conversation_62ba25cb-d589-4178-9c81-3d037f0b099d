package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@ApiModel(value = "电池A-占比查询条件", description = "查询条件")
@Accessors(chain = true)
public class ConfigCellAMinusQuery extends PageDTO implements Serializable {

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "车间")
    private String workshop;

    @ApiModelProperty(value = "电池型号")
    private String cellModel;

    @ApiModelProperty(value = "分片方式")
    private String fragmentType;

    @ApiModelProperty(value = "电池类型")
    private String cellType;

    @ApiModelProperty(value = "供应方")
    private String supplier;

    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    @ApiModelProperty(value = "产品来源：自产/外购")
    private String productFrom;

    @ApiModelProperty(value = "占比类型：SUPPLY/PRODUCE")
    private String cellPercentType;
}
