package com.trinasolar.scp.baps.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: CellInstockPlanSummaryDTO
 * @date 2024/6/13 11:09
 */
@Data
public class CellInstockPlanSummaryDTO {

    private Long isOverseaId;
    private String isOversea;
    private Long cellTypeId;
    private String cellType;
    private String productionGrade;
    private Long transparentDoubleGlassId;
    private String transparentDoubleGlass;
    private Long regionalCountryId;
    private String regionalCountry;
    private Long aestheticsId;
    private String aesthetics;
    private Long cellSourceId;
    private String cellSource;
    private Long hTraceId;
    private String hTrace;
    private String month;
    private BigDecimal quantity;
}
