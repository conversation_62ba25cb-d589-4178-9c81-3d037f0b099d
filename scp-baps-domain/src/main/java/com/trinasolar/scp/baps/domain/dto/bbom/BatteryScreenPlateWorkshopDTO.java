package com.trinasolar.scp.baps.domain.dto.bbom;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;


/**
 * 车间级网版切换维护
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "车间级网版切换维护DTO对象", description = "DTO对象")
public class BatteryScreenPlateWorkshopDTO extends BaseDTO {

    @ApiModelProperty(value = "ID主键")
    private Long id;

    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;

    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;

    @ApiModelProperty(value = "单玻")
    private String singleGlassFlag;

    @ApiModelProperty(value = "单玻名称")
    private String singleGlassFlagName;

    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    @ApiModelProperty(value = "生产基地名称")
    private String basePlaceName;

    @ApiModelProperty(value = "生产车间")
    private String workshop;

    @ApiModelProperty(value = "生产车间名称")
    private String workshopName;

    @ApiModelProperty(value = "正电极网版细栅")
    private String positiveElectrodeScreenFineGrid;

    @ApiModelProperty(value = "正电极网版细栅名称")
    private String positiveElectrodeScreenFineGridName;

    @ApiModelProperty(value = "背电极网版细栅")
    private String negativeElectrodeScreenFineGrid;

    @ApiModelProperty(value = "背电极网版细栅名称")
    private String negativeElectrodeScreenFineGridName;

    @ApiModelProperty(value = "有效期起")
    private LocalDate effectiveStartDate;

    @ApiModelProperty(value = "有效期止")
    private LocalDate effectiveEndDate;

    @ApiModelProperty(value = "有效期起名称")
    private String effectiveStartDateName;

    @ApiModelProperty(value = "有效期止名称")
    private String effectiveEndDateName;

    @ApiModelProperty(value = "备注")
    private String remark;
}
