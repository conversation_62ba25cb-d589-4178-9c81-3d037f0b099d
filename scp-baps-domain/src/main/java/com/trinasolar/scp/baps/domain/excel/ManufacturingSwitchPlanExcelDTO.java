package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 制造工艺切换计划维护 导出DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ManufacturingSwitchPlanExcelDTO {
    /**
     * 解析顺序
     */
    @ExcelProperty(value = "解析顺序")
    private String sortNo;

    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "国内/海外")
    private String isOversea;

    @ExcelProperty(value = "国内/海外Id")
    @ExcelIgnore
    private Long isOverseaId;

    @ExcelProperty(value = "制造工艺")
    private String manufacturing;

    @ExcelProperty(value = "生产基地")
    private String basePlace;

    @ExcelProperty(value = "生产基地Id")
    @ExcelIgnore
    private Long basePlaceId;

    @ExcelProperty(value = "生产车间")
    private String workshop;

    @ExcelProperty(value = "生产车间Id")
    @ExcelIgnore
    private Long workshopid;

    @ExcelProperty(value = "生产单元")
    private String workunit;

    @ExcelProperty(value = "生产单元Id")
    @ExcelIgnore
    private Long workunitid;

    @ExcelProperty(value = "生产产线")
    private String productionLine;

    @ExcelProperty(value = "电池类型")
    private String cellsType;

    @ExcelProperty(value = "电池类型Id")
    @ExcelIgnore
    private Long cellsTypeId;

    @ExcelProperty(value = "产能")
    private BigDecimal capacityQuantity;

    @ExcelProperty(value = "开始时间")
    private LocalDate startTime;

    @ExcelProperty(value = "结束时间")
    private LocalDate endTime;
}
