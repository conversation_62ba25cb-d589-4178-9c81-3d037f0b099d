package com.trinasolar.scp.baps.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: HTraceWarnType
 * @date 2024/6/11 17:43
 */
@Getter
@AllArgsConstructor
public enum HTraceWarnType {

    SUPPLY("supply", "供应", 1),
    CELL_SCHEDULE("cell_schedule", "电池排产", 2),
    BALANCE("balance", "结存", 3),
    SAFETY_STOCK("safety_stock", "安全库存", 4),
    SHORTAGE("shortage", "供应短缺", 5),
    COMPATIBLE("compatible", "兼容", 6);

    String code;
    String remark;
    Integer sort;

    public static Integer sort(String type) {
        for (HTraceWarnType value : HTraceWarnType.values()) {
            if (value.getCode().equals(type)) {
                return value.sort;
            }
        }
        return Integer.MAX_VALUE;
    }
}
