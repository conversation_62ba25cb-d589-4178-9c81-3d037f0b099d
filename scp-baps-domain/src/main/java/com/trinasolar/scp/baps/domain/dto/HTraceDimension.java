package com.trinasolar.scp.baps.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: HTraceDimension
 * @date 2024/6/14 16:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HTraceDimension {
    private Long isOversea;

    private String month;

    private String version;

    public static HTraceDimension build(CellPlanLineDTO cellPlanLine) {
        return new HTraceDimension(cellPlanLine.getIsOverseaId(), cellPlanLine.getOldMonth(), cellPlanLine.getVersion());
    }
}
