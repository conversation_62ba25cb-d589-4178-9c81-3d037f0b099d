package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 车间优先度效率目标值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Entity
@ToString
@Data
@Table(name = "baps_cell_workshop_priority_target")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_cell_workshop_priority_target SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_cell_workshop_priority_target SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class CellWorkshopPriorityTarget extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 车间Id
     */
    @ApiModelProperty(value = "车间Id")
    @Column(name = "workshop_id")
    private Long workshopId;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 效率分布
     */
    @ApiModelProperty(value = "效率分布")
    @Column(name = "efficiency")
    private String efficiency;
    /**
     * 目标效率
     */
    @ApiModelProperty(value = "目标效率")
    @Column(name = "target_efficiency")
    private String targetEfficiency;

    /**
     * 实际效率
     */
    @ApiModelProperty(value = "实际效率")
    @Column(name = "actual_efficiency")
    private String actualEfficiency;
    /**
     * 效率达成率
     */
    @ApiModelProperty(value = "效率达成率")
    @Column(name = "rate_efficiency")
    private String rateEfficiency;


}
