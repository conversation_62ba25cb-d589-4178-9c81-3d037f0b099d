package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.baps.domain.entity.DeliveryHoliday;
import com.trinasolar.scp.baps.domain.dto.DeliveryHolidayDTO;
import com.trinasolar.scp.baps.domain.excel.DeliveryHolidayExcelDTO;
import com.trinasolar.scp.baps.domain.save.DeliveryHolidaySaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 物流节假日表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DeliveryHolidayDEConvert extends BaseDEConvert<DeliveryHolidayDTO, DeliveryHoliday> {

    DeliveryHolidayDEConvert INSTANCE = Mappers.getMapper(DeliveryHolidayDEConvert.class);

    List<DeliveryHolidayExcelDTO> toExcelDTO(List<DeliveryHolidayDTO> dtos);

    DeliveryHolidayExcelDTO toExcelDTO(DeliveryHolidayDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    DeliveryHoliday saveDTOtoEntity(DeliveryHolidaySaveDTO saveDTO, @MappingTarget DeliveryHoliday entity);
}
