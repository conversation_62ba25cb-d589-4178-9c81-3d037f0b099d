package com.trinasolar.scp.baps.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ScheduledTaskLineExcelExportDTO {

    @ExcelIgnore
    private Long taskLineId;

    @ExcelProperty("任务编号")
    private String taskNumber;

    @ExcelProperty("任务名称")
    private String taskName;

    @ExcelProperty("版本号")
    @ExcelIgnore
    private String versions;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("提交时间")
    private LocalDateTime requestDate;

    @ExcelProperty("完成时间")
    private LocalDateTime completeDate;

    @ExcelProperty("提交人")
    private String requestBy;

    @ExcelProperty("运行日志")
    private String log;

    @ExcelIgnore
    private String remark;

}
