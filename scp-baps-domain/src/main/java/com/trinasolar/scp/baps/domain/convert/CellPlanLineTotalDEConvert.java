package com.trinasolar.scp.baps.domain.convert;

import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineTotal;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineTotalExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineTotalHExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineTotalSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划汇总表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring", imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanLineTotalDEConvert extends BaseDEConvert<CellPlanLineTotalDTO, CellPlanLineTotal> {

    CellPlanLineTotalDEConvert INSTANCE = Mappers.getMapper(CellPlanLineTotalDEConvert.class);

    @Mappings({
            @Mapping(target = "certCode"),
            @Mapping(target = "ratioCode"),
            @Mapping(target ="ecsCode"),
    }
    )
    List<CellPlanLineTotalExcelDTO> toExcelDTO(List<CellPlanLineTotalDTO> dtos);

    List<CellPlanLineTotalHExcelDTO> toHExcelDTO(List<CellPlanLineTotalHDTO> dtos);

    CellPlanLineTotalExcelDTO toExcelDTO(CellPlanLineTotalDTO dto);

    @Mappings(
            {
                    @Mapping(target = "cellsType", expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea", expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace", expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop", expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))"),
                    @Mapping(target = "certCode"),
                    @Mapping(target = "ratioCode"),
                    @Mapping(target ="ecsCode"),
            }
    )
    CellPlanLineTotalQuery toCellPlanLineTotalQuery(CellPlanLineTotalQuery query);

    default void test(CellPlanLineTotalQuery query) {
        MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType());
        MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea());
        MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace());
        MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop());


    }

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    CellPlanLineTotal saveDTOtoEntity(CellPlanLineTotalSaveDTO saveDTO, @MappingTarget CellPlanLineTotal entity);

    @Mappings({
            @Mapping(target = "certCode"),
            @Mapping(target = "ratioCode"),
            @Mapping(target ="ecsCode"),
    })
    CellPlanLineQuery toCellPlanLineQuery(CellPlanLineTotalQuery query);

    @Mappings({
            @Mapping(target = "cellsTypeId", expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, entity.getCellsType()).getLovLineId())"),
            @Mapping(target = "isOverseaId", expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, entity.getIsOversea()).getLovLineId())"),
            @Mapping(target = "basePlaceId", expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, entity.getBasePlace()).getLovLineId())"),
            @Mapping(target = "workshopId", expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, entity.getWorkshop()).getLovLineId())"),
            @Mapping(target = "certCode"),
            @Mapping(target = "ratioCode"),
            @Mapping(target ="ecsCode"),
    })
    CellPlanLineTotalSaveDTO toSaveDtoFromEntity(CellPlanLineTotal entity);

    List<CellPlanLineTotalSaveDTO> toSaveDtoFromEntity(List<CellPlanLineTotal> entity);

    CellPlanLineTotal toEntityFromSaveDto(CellPlanLineTotalSaveDTO dto);

    List<CellPlanLineTotal> toEntityFromSaveDto(List<CellPlanLineTotalSaveDTO> dto);

    @Mappings({
            @Mapping(target = "version", source = "finalVersion"),
            @Mapping(target = "certCode"),
            @Mapping(target = "ratioCode"),
            @Mapping(target ="ecsCode"),
    })
    CellPlanLineTotalDTO toCellPlanLineTotalDTO(CellPlanLineDTO dto);

    CellPlanLineTotalHDTO toCellPlanLineTotaHlDTO(CellPlanLineDTO dto);

    CellPlanLineTotalHDTO toCellPlanLineTotaHlDTO(CellPlanLineHDTO dto);

    @Mappings({
            @Mapping(target = "cellsType", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellsType()))"),
            @Mapping(target = "isOversea", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOversea()))"),
            @Mapping(target = "basePlace", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlace()))"),
            @Mapping(target = "workshop", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshop()))"),
            @Mapping(target = "HTrace", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,dto.getHTrace()))"),
            @Mapping(target = "cellSource", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_SOURCE,dto.getCellSource()))"),
            @Mapping(target = "regionalCountry", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.REGIONAL_COUNTRY,dto.getRegionalCountry()))"),
            @Mapping(target = "aesthetics", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,dto.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlass", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,dto.getTransparentDoubleGlass()))"),
            @Mapping(target = "certCode"),
            @Mapping(target = "ratioCode"),
            @Mapping(target ="ecsCode"),
    })
    @Named("toCellPlanLineTotalNameByCnNameDTO")
    CellPlanLineTotalDTO toCellPlanLineTotalNameByCnNameDTO(CellPlanLineTotalDTO dto);

    @IterableMapping(qualifiedByName = "toCellPlanLineTotalNameByCnNameDTO")
    List<CellPlanLineTotalDTO> toCellPlanLineTotalNameByCnNameDTO(List<CellPlanLineTotalDTO> dtos);

    @Mappings({
            @Mapping(target = "cellType", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellType()))"),
            @Mapping(target = "isOversea", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOversea()))"),
            @Mapping(target = "basePlace", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlace()))"),
            @Mapping(target = "workshop", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshop()))"),
            @Mapping(target = "HTrace", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,dto.getHTrace()))"),
            @Mapping(target = "aesthetics", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,dto.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlass", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,dto.getTransparentDoubleGlass()))"),
            @Mapping(target = "productionGrade", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,dto.getProductionGrade()))"),
            @Mapping(target = "processCategory", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,dto.getProcessCategory()))"),
            @Mapping(target = "siMfrs", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SI_MFRS,dto.getSiMfrs()))"),
            @Mapping(target = "waferGrade", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WAFER_GRADE,dto.getWaferGrade()))")
    })
    @Named("changeSiliconProcessCategoryTotalDTONameByCnName")
    CellPlanLineProcessCategoryTotalDTO changeSiliconProcessCategoryTotalDTONameByCnName(CellPlanLineProcessCategoryTotalDTO dto);

    @IterableMapping(qualifiedByName = "changeSiliconProcessCategoryTotalDTONameByCnName")
    List<CellPlanLineProcessCategoryTotalDTO> changeSiliconProcessCategoryTotalDTONameByCnName(List<CellPlanLineProcessCategoryTotalDTO> sortList);

    @Mappings({
            @Mapping(target = "cellType", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,dto.getCellType()))"),
            @Mapping(target = "isOversea", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,dto.getIsOversea()))"),
            @Mapping(target = "basePlace", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlace()))"),
            @Mapping(target = "workshop", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,dto.getWorkshop()))"),
            @Mapping(target = "HTrace", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,dto.getHTrace()))"),
            @Mapping(target = "aesthetics", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,dto.getAesthetics()))"),
            @Mapping(target = "transparentDoubleGlass", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,dto.getTransparentDoubleGlass()))"),
            @Mapping(target = "productionGrade", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.PRODUCTION_GRADE,dto.getProductionGrade()))"),
            @Mapping(target = "processCategory", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY,dto.getProcessCategory()))"),
            @Mapping(target = "siMfrs", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SI_MFRS,dto.getSiMfrs()))"),
            @Mapping(target = "waferGrade", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WAFER_GRADE,dto.getWaferGrade()))")
    })
    @Named("toCellPlanLineSiliconTotalDTONameByCnName")
    CellPlanLineSiliconTotalDTO toCellPlanLineSiliconTotalDTONameByCnName(CellPlanLineSiliconTotalDTO dto);

    @IterableMapping(qualifiedByName = "toCellPlanLineSiliconTotalDTONameByCnName")
    List<CellPlanLineSiliconTotalDTO> toCellPlanLineSiliconTotalDTONameByCnName(List<CellPlanLineSiliconTotalDTO> sortList);

    @Mappings({
            @Mapping(target = "cellsType", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,entity.getCellsType()))"),
            @Mapping(target = "isOversea", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,entity.getIsOversea()))"),
            @Mapping(target = "basePlace", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BASE_PLACE,entity.getBasePlace()))"),
            @Mapping(target = "workshop", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,entity.getWorkshop()))"),
            @Mapping(target = "workunit", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_UNIT,entity.getWorkunit()))"),
            @Mapping(target = "HTrace", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,entity.getHTrace()))"),
            @Mapping(target = "cellMfrs", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_MFRS,entity.getCellMfrs()))"),
            @Mapping(target = "silverPulpMfrs", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SILVER_PULP_MFRS, entity.getSilverPulpMfrs()))"),
            @Mapping(target = "siMfrs", expression = "java(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.SI_MFRS, entity.getSiMfrs()))")
    })
    @Named("toCellPlanLineTotalHDTONameFromCNName")
    CellPlanLineTotalHDTO toCellPlanLineTotalHDTONameFromCNName(CellPlanLineTotalHDTO entity);

    @IterableMapping(qualifiedByName = "toCellPlanLineTotalHDTONameFromCNName")
    List<CellPlanLineTotalHDTO> toCellPlanLineTotalHDTONameFromCNName(List<CellPlanLineTotalHDTO> dtos);

    CellPlanLineSiliconTotalDTO cellPlanLineDTOToCellPlanLineSiliconTotalDTO(CellPlanLineDTO cellPlanLineDTO);

    CellInstockPlanSplitDTO toSplitDto(CellInstockPlanSiliconTotalDTO cellInstockPlanSiliconTotalDTO);
}
