package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 电池需求计划明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-21 08:58:37
 */
@Data
@ApiModel(value = "DemandPlanLines查询条件", description = "查询条件")
@Accessors(chain = true)
public class DemandPlanLinesQuery extends PageDTO implements Serializable {

        /**
         * ID主键
         */
        @ApiModelProperty(value = "ID主键")
    private Long id;
        /**
         * 需求计划头表
         */
        @ApiModelProperty(value = "需求计划头表")
    private Long demandPlanHeaderId;
        /**
         * 电池需求计划来源行表ID
         */
        @ApiModelProperty(value = "电池需求计划来源行表ID")
    private Long demandPlanSourceId;
        /**
         * 汇总明细行id
         */
        @ApiModelProperty(value = "汇总明细行id")
    private Long demandSummaryLinesId;
        /**
         * 电池需求计划号
         */
        @ApiModelProperty(value = "电池需求计划号")
    private String demandPlanCode;
        /**
         * 来源类型
         */
        @ApiModelProperty(value = "来源类型")
    private String sourceType;
        /**
         * 需求来源
         */
        @ApiModelProperty(value = "需求来源")
    private String demandSource;
        /**
         * 需求版本号
         */
        @ApiModelProperty(value = "需求版本号")
    private String demandVersion;
        /**
         * 国内/海外
         */
        @ApiModelProperty(value = "国内/海外")
    private String domesticOversea;
        /**
         * 电池类型编码
         */
        @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
        /**
         * 电池类型名称
         */
        @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
        /**
         * H追溯
         */
        @ApiModelProperty(value = "H追溯")
    private String hTrace;
        /**
         * 片源种类
         */
        @ApiModelProperty(value = "片源种类")
    private String pcsSourceType;
        /**
         * 透明双玻
         */
        @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
        /**
         * 美学
         */
        @ApiModelProperty(value = "美学")
    private String aesthetics;
        /**
         * 小区域国家
         */
        @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
        /**
         * 需求地
         */
        @ApiModelProperty(value = "需求地")
    private String basePlace;
        /**
         * 是否电池特殊要求
         */
        @ApiModelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
        /**
         * 低阻
         */
        @ApiModelProperty(value = "低阻")
    private String lowResistance;
        /**
         * 电池厂家
         */
        @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
        /**
         * 银浆厂家
         */
        @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
        /**
         * 需求数量
         */
        @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQty;
        /**
         * 需求日期
         */
        @ApiModelProperty(value = "需求日期")
    private Date demandDate;
        /**
         * 排产数量
         */
        @ApiModelProperty(value = "排产数量")
    private BigDecimal scheduleQty;
        /**
         * 是否sop
         */
        @ApiModelProperty(value = "是否sop")
    private String isSop;
        /**
         * 说明
         */
        @ApiModelProperty(value = "说明")
    private String remark;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
    /**
     * '硅料厂家'
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;

    /**
     * '硅料厂家'
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;

    /**
     * 起始效率
     */
    @ApiModelProperty(value = "起始效率")
    private String startEfficiency;

    /**
     * 最大分布效率
     */
    @ApiModelProperty(value = "最大分布效率")
    private String maxEfficiency;

    /**
     * 最大分布效率
     */
    @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;

    /**
     * 指定车间
     */
    @ApiModelProperty(value = "指定车间")
    private String scheduleWorkshop;

    /**
     * 电池物料编码
     */
    @ApiModelProperty(value = "电池物料编码")
    private String batteryMaterialCode;

    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    private Long supplyMode;
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private LocalDate demandStartDate;
    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private LocalDate demandEndDate;
}
