package com.trinasolar.scp.baps.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * MW折算系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Data
@ApiModel(value = "MwCoefficient查询条件", description = "查询条件")
@Accessors(chain = true)
public class MwCoefficientQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private List<String> cellTypeList;
    /**
     * 系数
     */
    @ApiModelProperty(value = "系数")
    private BigDecimal coefficient;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
