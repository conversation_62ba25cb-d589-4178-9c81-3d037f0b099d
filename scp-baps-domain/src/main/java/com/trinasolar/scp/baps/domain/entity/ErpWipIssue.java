package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * Erp实际入库来源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:23:58
 */
@Entity
@ToString
@Data
@Table(name = "baps_erp_wip_issue")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_erp_wip_issue SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_erp_wip_issue SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class ErpWipIssue extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 事务数量
     */
    @ApiModelProperty(value = "事务数量")
    @Column(name = "transaction_quantity")
    private BigDecimal transactionQuantity;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    @Column(name = "item_number")
    private String itemNumber;

    /**
     * 交易库存组织Id
     */
    @ApiModelProperty(value = "交易库存组织Id")
    @Column(name = "transfer_organization_id")
    private Long transferOrganizationId;

    /**
     * 事务单位编码
     */
    @ApiModelProperty(value = "事务单位编码")
    @Column(name = "transaction_uom_code")
    private String transactionUomCode;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    @Column(name = "item_description")
    private String itemDescription;

    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @Column(name = "locator_code")
    private String locatorCode;

    /**
     * 事务来源类型名
     */
    @ApiModelProperty(value = "事务来源类型名")
    @Column(name = "transaction_source_type_name")
    private String transactionSourceTypeName;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @Column(name = "work_order")
    private String workOrder;

    /**
     * 库存组织名
     */
    @ApiModelProperty(value = "库存组织名")
    @Column(name = "organization_name")
    private String organizationName;

    /**
     * 事务类型Id
     */
    @ApiModelProperty(value = "事务类型Id")
    @Column(name = "transaction_type_id")
    private Long transactionTypeId;

    /**
     * lot主要数据量
     */
    @ApiModelProperty(value = "lot主要数据量")
    @Column(name = "lot_primary_quantity")
    private BigDecimal lotPrimaryQuantity;

    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    @Column(name = "subinventory_code")
    private String subinventoryCode;

    /**
     * lot编码
     */
    @ApiModelProperty(value = "lot编码")
    @Column(name = "lot_number")
    private String lotNumber;

    /**
     * 事务处理日期
     */
    @ApiModelProperty(value = "事务处理日期")
    @Column(name = "transaction_date")
    private LocalDateTime transactionDate;

    /**
     * 事务来源Id
     */
    @ApiModelProperty(value = "事务来源Id")
    @Column(name = "transaction_source_id")
    private Long transactionSourceId;

    /**
     * 事务类型名
     */
    @ApiModelProperty(value = "事务类型名")
    @Column(name = "transaction_type_name")
    private String transactionTypeName;

    /**
     * 事务Id
     */
    @ApiModelProperty(value = "事务Id")
    @Column(name = "transaction_id")
    private Long transactionId;

    /**
     * 事务来源类型Id
     */
    @ApiModelProperty(value = "事务来源类型Id")
    @Column(name = "transaction_source_type_id")
    private Long transactionSourceTypeId;

    /**
     * 物料Id
     */
    @ApiModelProperty(value = "物料Id")
    @Column(name = "inventory_item_id")
    private Long inventoryItemId;

    /**
     * 库存组织编号
     */
    @ApiModelProperty(value = "库存组织编号")
    @Column(name = "organization_code")
    private String organizationCode;

    /**
     * 货位Id
     */
    @ApiModelProperty(value = "货位Id")
    @Column(name = "locator_id")
    private Long locatorId;

    /**
     * 主要数量
     */
    @ApiModelProperty(value = "主要数量")
    @Column(name = "primary_quantity")
    private BigDecimal primaryQuantity;

    /**
     * lot事务处理数量
     */
    @ApiModelProperty(value = "lot事务处理数量")
    @Column(name = "lot_transaction_quantity")
    private BigDecimal lotTransactionQuantity;

    /**
     * erp创建时间
     */
    @ApiModelProperty(value = "erp创建时间")
    @Column(name = "creation_date")
    private LocalDateTime creationDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;


}
