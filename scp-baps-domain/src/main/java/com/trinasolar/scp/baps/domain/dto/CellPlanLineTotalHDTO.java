package com.trinasolar.scp.baps.domain.dto;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;


/**
 * 入库计划汇总表(H兼容拆分)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CellPlanLineTotalHDTO {

    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")

    private Long isOverseaId;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")

    private String basePlace;
    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")

    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")

    private Long workshopId;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;

    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元Id")

    private Long workunitId;
    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")

    private Long cellsTypeId;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;
    /**
     * 片源种类Id
     */
    @ApiModelProperty(value = "片源种类Id")
    private Long cellSourceId;
    /**
     * 是否电池特殊要求
     */
    @ApiModelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    private String lowResistance;

    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * 电池厂家Id
     */
    @ApiModelProperty(value = "电池厂家Id")
    private Long cellMfrsId;
    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 银浆厂家Id
     */
    @ApiModelProperty(value = "银浆厂家Id")
    private Long silverPulpMfrsId;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 硅料厂家Id
     */
    @ApiModelProperty(value = "硅料厂家Id")
    private Long siMfrsId;
    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;
    /**
     * 网版厂家Id
     */
    @ApiModelProperty(value = "网版厂家Id")
    private Long screenPlateMfrsId;
    /**
     * 电池特殊单号
     */
    @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;
    /**
     * 验证标识
     */
    @ApiModelProperty(value = "验证标识")
    private  String verificationMark;
    /**
     * 片源级投产良率
     */
    @ApiModelProperty(value = "片源级投产良率")
    private BigDecimal  waferYieldRatio;
    /**
     * 片源级A-比例
     */
    @ApiModelProperty(value = "片源级A-比例")
    private BigDecimal waferGradeRatio;
    /**
     * 计划版本
     */
    @ApiModelProperty(value = "计划版本")
    private String version;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 月份
     */
    @ApiModelProperty(value = "入库月份")
    private String oldMonth;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;
    /**
     * H追溯Id
     */
    @ApiModelProperty(value = "H追溯Id")
    private Long hTraceId;
    /**
     * d1
     */
    @ApiModelProperty(value = "1")
    private BigDecimal d1;
    /**
     * d2
     */
    @ApiModelProperty(value = "2")
    private BigDecimal d2;
    /**
     * d3
     */
    @ApiModelProperty(value = "3")
    private BigDecimal d3;
    /**
     * d4
     */
    @ApiModelProperty(value = "4")
    private BigDecimal d4;
    /**
     * d5
     */
    @ApiModelProperty(value = "5")
    private BigDecimal d5;
    /**
     * d6
     */
    @ApiModelProperty(value = "6")
    private BigDecimal d6;
    /**
     * d7
     */
    @ApiModelProperty(value = "7")
    private BigDecimal d7;
    /**
     * d8
     */
    @ApiModelProperty(value = "8")
    private BigDecimal d8;
    /**
     * d9
     */
    @ApiModelProperty(value = "9")
    private BigDecimal d9;
    /**
     * d10
     */
    @ApiModelProperty(value = "10")
    private BigDecimal d10;
    /**
     * d11
     */
    @ApiModelProperty(value = "11")
    private BigDecimal d11;
    /**
     * d12
     */
    @ApiModelProperty(value = "12")
    private BigDecimal d12;
    /**
     * d13
     */
    @ApiModelProperty(value = "13")
    private BigDecimal d13;
    /**
     * d14
     */
    @ApiModelProperty(value = "14")
    private BigDecimal d14;
    /**
     * d15
     */
    @ApiModelProperty(value = "15")
    private BigDecimal d15;
    /**
     * d16
     */
    @ApiModelProperty(value = "16")
    private BigDecimal d16;
    /**
     * d17
     */
    @ApiModelProperty(value = "17")
    private BigDecimal d17;
    /**
     * d18
     */
    @ApiModelProperty(value = "18")
    private BigDecimal d18;
    /**
     * d19
     */
    @ApiModelProperty(value = "19")
    private BigDecimal d19;
    /**
     * d20
     */
    @ApiModelProperty(value = "20")
    private BigDecimal d20;
    /**
     * d21
     */
    @ApiModelProperty(value = "21")
    private BigDecimal d21;
    /**
     * d22
     */
    @ApiModelProperty(value = "22")
    private BigDecimal d22;
    /**
     * d23
     */
    @ApiModelProperty(value = "23")
    private BigDecimal d23;
    /**
     * d24
     */
    @ApiModelProperty(value = "24")
    private BigDecimal d24;
    /**
     * d25
     */
    @ApiModelProperty(value = "25")
    private BigDecimal d25;
    /**
     * d26
     */
    @ApiModelProperty(value = "26")
    private BigDecimal d26;
    /**
     * d27
     */
    @ApiModelProperty(value = "27")
    private BigDecimal d27;
    /**
     * d28
     */
    @ApiModelProperty(value = "28")
    private BigDecimal d28;
    /**
     * d29
     */
    @ApiModelProperty(value = "29")
    private BigDecimal d29;
    /**
     * d30
     */
    @ApiModelProperty(value = "30")
    private BigDecimal d30;
    /**
     * d31
     */
    @ApiModelProperty(value = "31")
    private BigDecimal d31;
    private List<CellPlanLineDTO> details= Lists.newArrayList();
}
