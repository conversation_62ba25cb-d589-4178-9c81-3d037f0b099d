package com.trinasolar.scp.baps.domain.dto.bmrp;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 硅片外购计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 03:17:19
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "硅片外购计划DTO对象", description = "DTO对象")
public class SiliconSlicePurchasePlanDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String materialNo;
    /**
     * 料号属性
     */
    @ApiModelProperty(value = "料号属性")
    private String materialAttribute;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 1号
     */
    @ApiModelProperty(value = "1号")
    private BigDecimal d1Quantity;
    /**
     * 2号
     */
    @ApiModelProperty(value = "2号")
    private BigDecimal d2Quantity;
    /**
     * 3号
     */
    @ApiModelProperty(value = "3号")
    private BigDecimal d3Quantity;
    /**
     * 4号
     */
    @ApiModelProperty(value = "4号")
    private BigDecimal d4Quantity;
    /**
     * 5号
     */
    @ApiModelProperty(value = "5号")
    private BigDecimal d5Quantity;
    /**
     * 6号
     */
    @ApiModelProperty(value = "6号")
    private BigDecimal d6Quantity;
    /**
     * 7号
     */
    @ApiModelProperty(value = "7号")
    private BigDecimal d7Quantity;
    /**
     * 8号
     */
    @ApiModelProperty(value = "8号")
    private BigDecimal d8Quantity;
    /**
     * 9号
     */
    @ApiModelProperty(value = "9号")
    private BigDecimal d9Quantity;
    /**
     * 10号
     */
    @ApiModelProperty(value = "10号")
    private BigDecimal d10Quantity;
    /**
     * 11号
     */
    @ApiModelProperty(value = "11号")
    private BigDecimal d11Quantity;
    /**
     * 12号
     */
    @ApiModelProperty(value = "12号")
    private BigDecimal d12Quantity;
    /**
     * 13号
     */
    @ApiModelProperty(value = "13号")
    private BigDecimal d13Quantity;
    /**
     * 14号
     */
    @ApiModelProperty(value = "14号")
    private BigDecimal d14Quantity;
    /**
     * 15号
     */
    @ApiModelProperty(value = "15号")
    private BigDecimal d15Quantity;
    /**
     * 16号
     */
    @ApiModelProperty(value = "16号")
    private BigDecimal d16Quantity;
    /**
     * 17号
     */
    @ApiModelProperty(value = "17号")
    private BigDecimal d17Quantity;
    /**
     * 18号
     */
    @ApiModelProperty(value = "18号")
    private BigDecimal d18Quantity;
    /**
     * 19号
     */
    @ApiModelProperty(value = "19号")
    private BigDecimal d19Quantity;
    /**
     * 20号
     */
    @ApiModelProperty(value = "20号")
    private BigDecimal d20Quantity;
    /**
     * 21号
     */
    @ApiModelProperty(value = "21号")
    private BigDecimal d21Quantity;
    /**
     * 22号
     */
    @ApiModelProperty(value = "22号")
    private BigDecimal d22Quantity;
    /**
     * 23号
     */
    @ApiModelProperty(value = "23号")
    private BigDecimal d23Quantity;
    /**
     * 24号
     */
    @ApiModelProperty(value = "24号")
    private BigDecimal d24Quantity;
    /**
     * 25号
     */
    @ApiModelProperty(value = "25号")
    private BigDecimal d25Quantity;
    /**
     * 26号
     */
    @ApiModelProperty(value = "26号")
    private BigDecimal d26Quantity;
    /**
     * 27号
     */
    @ApiModelProperty(value = "27号")
    private BigDecimal d27Quantity;
    /**
     * 28号
     */
    @ApiModelProperty(value = "28号")
    private BigDecimal d28Quantity;
    /**
     * 29号
     */
    @ApiModelProperty(value = "29号")
    private BigDecimal d29Quantity;
    /**
     * 30号
     */
    @ApiModelProperty(value = "30号")
    private BigDecimal d30Quantity;
    /**
     * 31号
     */
    @ApiModelProperty(value = "31号")
    private BigDecimal d31Quantity;
}
