package com.trinasolar.scp.baps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@ApiModel(value = "ConfigCellAMinusPercentDTO对象", description = "DTO对象")
public class ConfigCellAMinusPercentDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String countryFlag;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;

    /**
     * 产品来源：IE/PURCHASE
     */
    @ApiModelProperty(value = "产品来源：IE/PURCHASE")
    private String productFrom;

    /**
     * 占比类型：SUPPLY/PRODUCE
     */
    @ApiModelProperty(value = "占比类型：SUPPLY/PRODUCE")
    private String cellPercentType;

    /**
     * 主栅数
     */
    @ApiModelProperty(value = "主栅数")
    private String mainGrid;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @NotBlank(groups = IEGroup.class, message = "车间列未填写")
    private String workshop;
    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    @NotBlank(groups = PurchaseGroup.class, message = "供应方列未填写")
    private String supplier;

    /**
     * 供应方名称
     */
    @ApiModelProperty(value = "供应方名称")
    private String supplierName;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @NotBlank(groups = IEGroup.class, message = "生产基地列未填写")
    private String basePlace;

    /**
     * 电池型号
     */
    @ApiModelProperty(value = "电池型号")
    @NotBlank(groups = PurchaseGroup.class, message = "电池型号列未填写")
    private String cellModel;
    /**
     * 分片方式
     */
    @ApiModelProperty(value = "分片方式")
    private String fragmentType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;

    /**
     * 1月占比
     */
    @ApiModelProperty(value = "1月占比")
    private String m1Percent;

    /**
     * 2月占比
     */
    @ApiModelProperty(value = "2月占比")
    private String m2Percent;

    /**
     * 3月占比
     */
    @ApiModelProperty(value = "3月占比")
    private String m3Percent;

    /**
     * 4月占比
     */
    @ApiModelProperty(value = "4月占比")
    private String m4Percent;

    /**
     * 5月占比
     */
    @ApiModelProperty(value = "5月占比")
    private String m5Percent;

    /**
     * 6月占比
     */
    @ApiModelProperty(value = "6月占比")
    private String m6Percent;

    /**
     * 7月占比
     */
    @ApiModelProperty(value = "7月占比")
    private String m7Percent;

    /**
     * 8月占比
     */
    @ApiModelProperty(value = "8月占比")
    private String m8Percent;

    /**
     * 9月占比
     */
    @ApiModelProperty(value = "9月占比")
    private String m9Percent;

    /**
     * 10月占比
     */
    @ApiModelProperty(value = "10月占比")
    private String m10Percent;

    /**
     * 11月占比
     */
    @ApiModelProperty(value = "11月占比")
    private String m11Percent;

    /**
     * 12月占比
     */
    @ApiModelProperty(value = "12月占比")
    private String m12Percent;

    public interface IEGroup {

    }

    public interface PurchaseGroup {

    }

    public interface FragmentTypeBlankGroup {

    }

    public interface MainGridBlankGroup {

    }

}
