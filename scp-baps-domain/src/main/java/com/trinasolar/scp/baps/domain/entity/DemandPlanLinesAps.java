package com.trinasolar.scp.baps.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import com.trinasolar.scp.common.api.base.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import java.math.BigDecimal;
import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.GenericGenerator;

/**
 * 需求计划明细（APS）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 08:30:30
 */
@Entity
@ToString
@Data
@Table(name = "baps_demand_plan_lines_aps")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE baps_demand_plan_lines_aps SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE baps_demand_plan_lines_aps SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class DemandPlanLinesAps extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
        strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
        name = "SnowflakeIdGeneratorConfig",
        strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @Column(name = "batch_no")
    private String batchNo;

    /**
     * 实际覆盖日期
     */
    @ApiModelProperty(value = "实际覆盖日期")
    @Column(name = "actual_coverage_date")
    private LocalDate actualCoverageDate;

    /**
     * 计划锁定日期
     */
    @ApiModelProperty(value = "计划锁定日期")
    @Column(name = "plan_lock_date")
    private LocalDate planLockDate;

    /**
     * 后端提供给APS的唯一ID
     */
    @ApiModelProperty(value = "后端提供给APS的唯一ID")
    @Column(name = "dp_id")
    private String dpId;

    /**
     * 电池需求计划号
     */
    @ApiModelProperty(value = "电池需求计划号")
    @Column(name = "demand_plan_code")
    private String demandPlanCode;

    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    @Column(name = "source_type")
    private String sourceType;

    /**
     * 国内/海外名称
     */
    @ApiModelProperty(value = "国内/海外名称")
    @Column(name = "domestic_oversea_name")
    private String domesticOverseaName;

    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    @Column(name = "battery_name")
    private String batteryName;

    /**
     * 电池物料编码
     */
    @ApiModelProperty(value = "电池物料编码")
    @Column(name = "battery_material_code")
    private String batteryMaterialCode;

    /**
     * H追溯名称
     */
    @ApiModelProperty(value = "H追溯名称")
    @Column(name = "h_trace_name")
    private String hTraceName;

    /**
     * 片源种类名称
     */
    @ApiModelProperty(value = "片源种类名称")
    @Column(name = "pcs_source_type_name")
    private String pcsSourceTypeName;

    /**
     * 透明双波名称
     */
    @ApiModelProperty(value = "透明双波名称")
    @Column(name = "transparent_double_glass_name")
    private String transparentDoubleGlassName;

    /**
     * 美学名称
     */
    @ApiModelProperty(value = "美学名称")
    @Column(name = "aesthetics_name")
    private String aestheticsName;

    /**
     * 小区域国家名称
     */
    @ApiModelProperty(value = "小区域国家名称")
    @Column(name = "regional_country_name")
    private String regionalCountryName;

    /**
     * 需求地名称
     */
    @ApiModelProperty(value = "需求地名称")
    @Column(name = "base_place_name")
    private String basePlaceName;

    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    @Column(name = "cell_mfrs")
    private String cellMfrs;

    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    @Column(name = "silver_pulp_mfrs")
    private String silverPulpMfrs;

    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    @Column(name = "si_mfrs")
    private String siMfrs;

    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    @Column(name = "screen_plate_mfrs")
    private String screenPlateMfrs;

    /**
     * 起始效率
     */
    @ApiModelProperty(value = "起始效率")
    @Column(name = "start_efficiency")
    private BigDecimal startEfficiency;

    /**
     * 电池特殊单号
     */
    @ApiModelProperty(value = "电池特殊单号")
    @Column(name = "special_order_no")
    private String specialOrderNo;

    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    @Column(name = "demand_date")
    private LocalDate demandDate;

    /**
     * 符合率
     */
    @ApiModelProperty(value = "符合率")
    @Column(name = "pass_percent")
    private BigDecimal passPercent;

    /**
     * 电池生产车间
     */
    @ApiModelProperty(value = "电池生产车间")
    @Column(name = "schedule_workshop")
    private String scheduleWorkshop;

    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    @Column(name = "process_category_name")
    private String processCategoryName;

    /**
     * 需求数量(汇总)
     */
    @ApiModelProperty(value = "需求数量(汇总)")
    @Column(name = "demand_qty")
    private BigDecimal demandQty;

    /**
     * 外部需求ID
     */
    @ApiModelProperty(value = "外部需求ID")
    @Column(name = "exterior_demand_id")
    private String exteriorDemandId;

    /**
     * H转换标记
     */
    @ApiModelProperty(value = "H转换标记")
    @Column(name = "h_change_flag")
    private String hChangeFlag;

    /**
     * DT转换标记
     */
    @ApiModelProperty(value = "DT转换标记")
    @Column(name = "pcs_source_dt_change_flag")
    private String pcsSourceDtChangeFlag;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_space")
    private String mainGridSpace;


    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    @Column(name = "supply_method")
    private String supplyMethod;

    /**
     * 配比
     */
    @ApiModelProperty(value = "配比")
    @Column(name = "ratio_code")
    private String ratioCode;


}
