package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.dto.Cell5AItemCodeListDto;
import com.trinasolar.scp.baps.domain.dto.CellProductionPlanDTO;
import com.trinasolar.scp.baps.domain.save.CellProductionPlanSaveDTO;
import com.trinasolar.scp.baps.service.service.CellProductionPlanService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellProductionPlanSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellProductionPlanDTO;
import com.trinasolar.scp.baps.service.service.CellProductionPlanService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 投产计划 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@RestController
@RequestMapping("/cell-production-plan")
@RequiredArgsConstructor
@Api(value = "cell-production-plan", tags = "投产计划操作")
public class CellProductionPlanController {
    private final CellProductionPlanService cellProductionPlanService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "投产计划分页列表", notes = "获得投产计划分页列表")
    public ResponseEntity<Results<Page<CellProductionPlanDTO>>> queryByPage(@RequestBody CellProductionPlanQuery query) {
        return Results.createSuccessRes(cellProductionPlanService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellProductionPlanDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellProductionPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellProductionPlanDTO>> save(@Valid @RequestBody CellProductionPlanSaveDTO saveDTO) {
        return Results.createSuccessRes(cellProductionPlanService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellProductionPlanService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellProductionPlanService.export(query, response);
    }

    @PostMapping("/listForMatchItem")
    @ApiOperation(value = "提供给料号匹配,查询投产计划", notes = "获得投产计划列表")
    public ResponseEntity<Results<List<CellProductionPlanDTO>>> listForMatchItem(@RequestBody CellProductionPlanQuery query) {
        return Results.createSuccessRes(cellProductionPlanService.listForMatchItem(query));
    }
    /**
     * 工单开立
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/")
    @ApiOperation(value = "工单开立分页列表", notes = "工单开立分页列表")
    public ResponseEntity<Results<Page<CellProductionPlanDTO>>> queryTicketOpening(@RequestBody CellProductionPlanQuery query) {
        return Results.createSuccessRes(cellProductionPlanService.queryTicketOpening(query));
    }
    /**
     * bom进行5A料号匹配后进行回改投产计划数据
     */
    @PostMapping("/changeDataBy5AMatch")
    @ApiOperation(value = "bom进行5A料号匹配后进行回改投产计划数据")

    public ResponseEntity<Results<Object>> changeDataBy5AMatch(@RequestBody Cell5AItemCodeListDto dto) throws InterruptedException {
        cellProductionPlanService.changeDataBy5AMatch(dto);
        return Results.createSuccessRes();
    }

    @PostMapping("/addMatch")
    @ApiOperation(value = "测试手工执行数据", notes = "测试手工执行数据")
    public void addMatch(@RequestBody CellProductionPlanQuery query) {
        cellProductionPlanService.summaryGroupByCellProductionPlan(query);
    }
}
