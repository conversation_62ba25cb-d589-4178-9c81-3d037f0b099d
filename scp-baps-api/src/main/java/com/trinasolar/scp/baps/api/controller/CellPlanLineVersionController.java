package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellPlanLineVersionDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionQueryDto;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellPlanLineVersionQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellPlanLineVersionSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionDTO;
import com.trinasolar.scp.baps.service.service.CellPlanLineVersionService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 投产计划版本管理表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@RestController
@RequestMapping("/cell-plan-line-version")
@RequiredArgsConstructor
@Api(value = "cell-plan-line-version", tags = "投产计划版本管理表操作")
public class CellPlanLineVersionController {
    private final CellPlanLineVersionService cellPlanLineVersionService;
    private final CellPlanLineVersionDEConvert convert;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    //@PostMapping("/page")
    @ApiOperation(value = "投产计划版本管理表分页列表", notes = "获得投产计划版本管理表分页列表")
    public ResponseEntity<Results<Page<CellPlanLineVersionDTO>>> queryByPage(@RequestBody CellPlanLineVersionQuery query) {
        return Results.createSuccessRes(cellPlanLineVersionService.queryByPage(query));
    }
    /**
     * 查看投产执行状态
     *
     * @param query 筛选条件
     * @return 查看投产执行状态
     */
    @PostMapping("/query")
    @ApiOperation(value = "查看投产执行状态", notes = "查看投产执行状态")
    public ResponseEntity<Results<CellPlanLineVersionDTO>> query(@RequestBody CellPlanLineVersionQueryDto query) {
        query=  convert.toCellPlanLineVersionQueryDtoByName(query); //lov-value->name
        CellPlanLineVersionDTO dto=   cellPlanLineVersionService.query(query);
        return Results.createSuccessRes(dto);
    }
    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
  //  @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellPlanLineVersionDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellPlanLineVersionService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
 //   @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellPlanLineVersionDTO>> save(@Valid @RequestBody CellPlanLineVersionSaveDTO saveDTO) {
        return Results.createSuccessRes(cellPlanLineVersionService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
  //  @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellPlanLineVersionService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

   // @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellPlanLineVersionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellPlanLineVersionService.export(query, response);
    }
}
