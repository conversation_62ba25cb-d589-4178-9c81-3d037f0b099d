package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellPlanLineDEConvert;
import com.trinasolar.scp.baps.domain.dto.Cell5AItemCodeListDto;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanQuery;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellPlanLineSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineDTO;
import com.trinasolar.scp.baps.service.service.CellPlanLineService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 投产计划表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@RestController
@RequestMapping("/cell-plan-line")
@RequiredArgsConstructor
@Api(value = "cell-plan-line", tags = "投产计划表操作")
public class CellPlanLineController {
    private final CellPlanLineService cellPlanLineService;
    private final CellPlanLineDEConvert convert;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "投产计划表分页列表", notes = "获得投产计划表分页列表")
    public ResponseEntity<Results<Page<CellPlanLineDTO>>> queryByPage(@RequestBody CellPlanLineQuery query) {
        return Results.createSuccessRes(cellPlanLineService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellPlanLineDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellPlanLineService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellPlanLineDTO>> save(@Valid @RequestBody CellPlanLineSaveDTO saveDTO) {
        return Results.createSuccessRes(cellPlanLineService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellPlanLineService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellPlanLineQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellPlanLineService.export(query, response);
    }

    /**
     * bom进行5A料号匹配后进行回改投产计划数据
     */
    @PostMapping("/changeDataBy5AMatch")
    @ApiOperation(value = "bom进行5A料号匹配后进行回改投产计划数据")

    public ResponseEntity<Results<Object>> changeDataBy5AMatch(@RequestBody Cell5AItemCodeListDto dto) throws InterruptedException {
        cellPlanLineService.changeDataBy5AMatch(dto);
        return Results.createSuccessRes();
    }

    /**
     * 工单开立
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryTicketOpening")
    @ApiOperation(value = "工单开立分页列表", notes = "工单开立分页列表")
    public ResponseEntity<Results<Page<CellPlanLineDTO>>> queryTicketOpening(@RequestBody CellPlanLineQuery query) {
        query = convert.toCellPlanLineQueryCNameByName(query, MyThreadLocal.get().getLang());
        return Results.createSuccessRes(cellPlanLineService.queryTicketOpening(query));
    }

    @PostMapping("/match7AInfo")
    @ApiOperation(value = "测试手工执行数据", notes = "测试手工执行数据")
    public  ResponseEntity<Results<Object>> match7AInfo(@RequestBody CellPlanLineQuery query) {
        cellPlanLineService.match7AInfo(query);
        return Results.createSuccessRes();
    }
}
