package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.entity.User;
import com.trinasolar.scp.baps.service.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/21
 */
@Controller
public class DemoController {
    @Autowired
    UserService userService;

    @ResponseBody
    @GetMapping("/hello")
    public String hello(){
        return "Hello World";
    }

    @ResponseBody
    @GetMapping("/users")
    public List<User> getUsers(){
        List<User> users = userService.list();
        return users;
    }
}
