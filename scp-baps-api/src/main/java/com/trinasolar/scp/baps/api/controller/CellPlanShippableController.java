package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.dto.aps.CellPlanShippableSaveListDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellPlanShippableQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellPlanShippableSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanShippableDTO;
import com.trinasolar.scp.baps.service.service.CellPlanShippableService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 同步投产数据接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cell-plan-shippable")
@RequiredArgsConstructor
@Api(value = "cell-plan-shippable", tags = "可发货计划表操作")
public class CellPlanShippableController {
    private final CellPlanShippableService cellPlanShippableService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "可发货计划表分页列表", notes = "获得可发货计划表分页列表")
    public ResponseEntity<Results<Page<CellPlanShippableDTO>>> queryByPage(@RequestBody CellPlanShippableQuery query) {
        return Results.createSuccessRes(cellPlanShippableService.queryByPage(query));
    }
    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellPlanShippableDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellPlanShippableService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellPlanShippableDTO>> save(@Valid @RequestBody CellPlanShippableSaveDTO saveDTO) {
        return Results.createSuccessRes(cellPlanShippableService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellPlanShippableService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellPlanShippableQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellPlanShippableService.export(query, response);
    }
    /**
     * 暂时同步当前月的数据
     * 同步入库数据
     * @return 新增或更新数据结果
     */
    @PostMapping("/syncDataInstockPlan")
    @ApiOperation(value = "同步入库数据")
    public ResponseEntity<Results<CellPlanShippableDTO>> syncDataInstockPlan(@RequestBody CellPlanShippableQuery query) {
        cellPlanShippableService.syncDataInstockPlan(query);
        return Results.createSuccessRes();
    }
}
