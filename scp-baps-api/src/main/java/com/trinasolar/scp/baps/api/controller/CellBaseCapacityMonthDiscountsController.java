package com.trinasolar.scp.baps.api.controller;

import cn.hutool.core.map.MapUtil;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDiscountsDTO;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityDiscountsQuery;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.EmailConstant;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityMonthDiscountsQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacityMonthDiscountsSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellBaseCapacityMonthDiscounts;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityMonthDiscountsDTO;
import com.trinasolar.scp.baps.service.service.CellBaseCapacityMonthDiscountsService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * IE产能打折月度（人力）表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-28 03:34:54
 */
@RestController
@RequestMapping("/cell-base-capacity-month-discounts")
@RequiredArgsConstructor
@Api(value = "cell-base-capacity-month-discounts", tags = "IE产能打折月度（人力）表操作")
public class CellBaseCapacityMonthDiscountsController {
    private final CellBaseCapacityMonthDiscountsService cellBaseCapacityMonthDiscountsService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "IE产能打折月度（人力）表分页列表", notes = "获得IE产能打折月度（人力）表分页列表")
    public ResponseEntity<Results<Page<CellBaseCapacityMonthDiscountsDTO>>> queryByPage(@RequestBody CellBaseCapacityMonthDiscountsQuery query) {
        return Results.createSuccessRes(cellBaseCapacityMonthDiscountsService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellBaseCapacityMonthDiscountsDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellBaseCapacityMonthDiscountsService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellBaseCapacityMonthDiscountsDTO>> save(@Valid @RequestBody CellBaseCapacityMonthDiscountsSaveDTO saveDTO) {
        return Results.createSuccessRes(cellBaseCapacityMonthDiscountsService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellBaseCapacityMonthDiscountsService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));

        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellBaseCapacityMonthDiscountsQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellBaseCapacityMonthDiscountsService.export(query, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile) {
        cellBaseCapacityMonthDiscountsService.importData(multipartFile);
        return Results.createSuccessRes();
    }
}
