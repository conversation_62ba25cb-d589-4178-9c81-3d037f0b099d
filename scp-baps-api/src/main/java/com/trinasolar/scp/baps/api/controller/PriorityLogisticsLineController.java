package com.trinasolar.scp.baps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.PriorityLogisticsLineQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.PriorityLogisticsLineSaveDTO;
import com.trinasolar.scp.baps.domain.entity.PriorityLogisticsLine;
import com.trinasolar.scp.baps.domain.dto.PriorityLogisticsLineDTO;
import com.trinasolar.scp.baps.service.service.PriorityLogisticsLineService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 物流线路优先级 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:51:00
 */
@RestController
@RequestMapping("/priority-logistics-line")
@RequiredArgsConstructor
@Api(value = "priority-logistics-line", tags = "物流线路优先级操作")
public class PriorityLogisticsLineController {
    private final PriorityLogisticsLineService priorityLogisticsLineService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "物流线路优先级分页列表", notes = "获得物流线路优先级分页列表")
    public ResponseEntity<Results<Page<PriorityLogisticsLineDTO>>> queryByPage(@RequestBody PriorityLogisticsLineQuery query) {
        return Results.createSuccessRes(priorityLogisticsLineService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    // @PostMapping("/detail")
    //  @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PriorityLogisticsLineDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(priorityLogisticsLineService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    //  @PostMapping("/save")
    // @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PriorityLogisticsLineDTO>> save(@Valid @RequestBody PriorityLogisticsLineSaveDTO saveDTO) {
        return Results.createSuccessRes(priorityLogisticsLineService.save(saveDTO));
    }

    /**
     * 刷新数据
     *
     * @return
     */
    @GetMapping("/refresh-data")
    @ApiOperation(value = "更新数据")
    public ResponseEntity<Results<Object>> refreshData() {
        priorityLogisticsLineService.refreshData();
        return Results.createSuccessRes();
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        priorityLogisticsLineService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PriorityLogisticsLineQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        priorityLogisticsLineService.export(query, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile) {
        priorityLogisticsLineService.importData(multipartFile);
        return Results.createSuccessRes();
    }
}
