package com.trinasolar.scp.baps.api.controller;
import com.trinasolar.scp.baps.domain.dto.ScheduledTaskLinesDTO;
import com.trinasolar.scp.baps.domain.query.ScheduledTaskLinesQuery;
import com.trinasolar.scp.baps.service.service.ScheduledTaskLinesService;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/scheduled-task-lines")
@Api(value = "scheduled-task-lines", tags = "计划任务日志controller")
public class ScheduledTaskLinesController {

    @Resource
    private ScheduledTaskLinesService scheduledTaskLinesService;

    @ApiOperation(value = "分页列表", notes = "分页列表")
    @PostMapping("/page")
    public ResponseEntity<Results<Page<ScheduledTaskLinesDTO>>> queryByPage(@RequestBody ScheduledTaskLinesQuery query) {
        return Results.createSuccessRes(scheduledTaskLinesService.queryByPage(query));
    }


    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping("/export")
    public void export(@RequestBody ScheduledTaskLinesQuery query, HttpServletResponse response) {
        scheduledTaskLinesService.exportToExcel(query, response);
    }


}
