package com.trinasolar.scp.baps.api.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2022/10/29
 */
@Configuration
public class ExecutorConfig {
    @Bean("defaultExecutor")
    public Executor defaultExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors()+1);
        // 最大线程
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors()+1);
        // 队列容量
        executor.setQueueCapacity(10000);
        // 保持时间
        executor.setKeepAliveSeconds(60);
        // 名称前缀
        executor.setThreadNamePrefix("Async-");
        executor.setRejectedExecutionHandler( new ThreadPoolExecutor.AbortPolicy());
        executor.initialize();
        return TtlExecutors.getTtlExecutorService(executor.getThreadPoolExecutor());
    }
}
