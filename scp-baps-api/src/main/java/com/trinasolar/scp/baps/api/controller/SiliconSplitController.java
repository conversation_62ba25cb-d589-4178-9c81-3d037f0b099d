package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellInstockPlanDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellPlanLineDEConvert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.baps.service.service.CellPlanLineService;
import com.trinasolar.scp.baps.service.service.CellPlanLineVersionService;
import com.trinasolar.scp.baps.service.service.SiliconSplitService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/silicon-split")
@RequiredArgsConstructor
@Api(value = "silicon-split", tags = "硅片拆分操作")
public class SiliconSplitController {

    private final SiliconSplitService siliconSplitService;

    private final CellPlanLineDEConvert cellPlanLineDEConvert;

    private final CellPlanLineService cellPlanLineService;

    private final CellInstockPlanDEConvert cellInstockPlanDEConvert;

    private final CellPlanLineVersionService cellPlanLineVersionService;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询
     */
    @PostMapping("/page")
    @ApiOperation(value = "硅片拆分列表-针对投产", notes = "硅片拆分列表-针对投产")
    public ResponseEntity<Results<Page<CellPlanLineSiliconTotalDTO>>> queryByPage(@RequestBody CellPlanLineQuery query) {
        query = cellPlanLineDEConvert.toCellPlanLineQueryByName(query);
        return Results.createSuccessRes(siliconSplitService.query(query));
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询
     */
    @PostMapping("/instock-page")
    @ApiOperation(value = "硅片拆分列表-针对入库", notes = "硅片拆分列表-针对入库")
    public ResponseEntity<Results<Page<CellInstockPlanSiliconTotalDTO>>> queryByPage(@RequestBody CellInstockPlanQuery query) {
        query = cellInstockPlanDEConvert.toCellInstockPlanQueryByName(query);
        return Results.createSuccessRes(siliconSplitService.query(query));
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询
     */
    @PostMapping("/process-category/page")
    @ApiOperation(value = "加工类型拆分统计列表-针对投产", notes = "加工类型拆分统计列表-针对投产")
    public ResponseEntity<Results<Page<CellPlanLineProcessCategoryTotalDTO>>> queryProcessCategoryByPage(@RequestBody CellPlanLineQuery query) {
        query = cellPlanLineDEConvert.toCellPlanLineQueryByName(query);
        Page<CellPlanLineProcessCategoryTotalDTO> datas = siliconSplitService.processCategoryQuery(query);
        return Results.createSuccessRes(datas);
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询
     */
    @PostMapping("/grade-rule-mate/page")
    @ApiOperation(value = "匹配分档规则统计列表-针对入库计划", notes = "匹配分档规则统计列表-针对入库计划")
    public ResponseEntity<Results<Page<CellGradeRuleMateTotalDTO>>> queryGradeRuleTotalByPage(@RequestBody CellInstockPlanQuery query) {
        query = cellInstockPlanDEConvert.toCellInstockPlanQueryByName(query);
        Page<CellGradeRuleMateTotalDTO> datas = siliconSplitService.gradeRuleTotalQuery(query);
        return Results.createSuccessRes(datas);
    }

    /**
     * 拆分
     *
     * @param siliconSplitQuery 筛选条件
     * @return 查询
     */
    @PostMapping("/split")
    @ApiOperation(value = "硅片拆分执行(硅片等级、硅料厂商两种处理)-针对投产", notes = "硅片拆分执行（硅片等级、硅料厂商两种处理）-针对投产")
    public ResponseEntity<Results<Object>> siliconSplit(@RequestBody SiliconSplitQuery siliconSplitQuery) {
        CellPlanLineQuery cellPlanLineQuery = cellPlanLineDEConvert.toCellPlanLineQueryFromSiliconSplitQuery(siliconSplitQuery);
        CellPlanLineQuery query = cellPlanLineDEConvert.toCellPlanLineQueryByName(cellPlanLineQuery);
        setCellPlanLineQueryNull(query);
        siliconSplitService.siliconSplit(query, siliconSplitQuery.getType(), siliconSplitQuery.isCover());
        return Results.createSuccessRes();
    }

    private void setCellPlanLineQueryNull(CellPlanLineQuery query) {
        if (query != null) {
            query.setCellsType(null);
            query.setBasePlace(null);
            query.setWorkshop(null);
            query.setCellsTypeId(null);
            query.setBasePlaceId(null);
            query.setWorkshopId(null);
        }

    }

    private void setCellInstockPlanQueryNull(CellInstockPlanQuery query) {
        if (query != null) {
            query.setCellsType(null);
            query.setBasePlace(null);
            query.setWorkshop(null);
            query.setCellsTypeId(null);
            query.setBasePlaceId(null);
            query.setWorkshopId(null);
        }

    }

    /**
     * 拆分
     *
     * @param siliconSplitQuery 筛选条件
     * @return 查询
     */
    @PostMapping("/a-split")
    @ApiOperation(value = "硅片拆分执行(A-拆分处理)-针对入库计划", notes = "硅片拆分执行（A-拆分处理）-针对入库计划")
    public ResponseEntity<Results<Object>> siliconASplit(@RequestBody SiliconSplitQuery siliconSplitQuery) {
        CellInstockPlanQuery cellInstockPlanQuery = cellInstockPlanDEConvert.toCellInstockPlanFromSiliconSplitQuery(siliconSplitQuery);
        CellInstockPlanQuery query = cellInstockPlanDEConvert.toCellInstockPlanByName(cellInstockPlanQuery);
        setCellInstockPlanQueryNull(query);
        siliconSplitService.siliconASplit(query);
        return Results.createSuccessRes();
    }

    /**
     * 硅片拆分执行(低效) 执行
     *
     * @param siliconSplitQuery
     * @return
     */
    @PostMapping("/low-efficiency-split")
    @ApiOperation(value = "硅片拆分执行(低效)-针对入库", notes = "硅片拆分执行（低效）-针对入库")
    public ResponseEntity<Results<Object>> lowEfficiencySplit(@RequestBody SiliconSplitQuery siliconSplitQuery) {
        CellInstockPlanQuery cellInstockPlanQuery = cellInstockPlanDEConvert.toCellInstockPlanFromSiliconSplitQuery(siliconSplitQuery);
        CellInstockPlanQuery query = cellInstockPlanDEConvert.toCellInstockPlanByName(cellInstockPlanQuery);
        siliconSplitService.siliconLowEfficiencySplit(query);
        return Results.createSuccessRes();
    }

    /**
     * 获得A-拆分规则列表 （前端直接调用一期的接口https://scpapidev.trinasolar.com/scp-aop-api/config-cell-a-minus-percent/ie/page）
     *
     * @param dto
     * @return
     */
    // @PostMapping("/a-split-rule")
    //@ApiOperation(value = "获得A-拆分规则列表", notes = "获得A-拆分规则列表")
    public ResponseEntity<Results<List<ConfigCellAMinusPercentDTO>>> siliconASplitRuleList(@RequestBody CellAMinusDto dto) {
        //  dto=cellPlanLineDEConvert.toCellAMinusDtoByName(dto);
        return Results.createSuccessRes(siliconSplitService.siliconASplitRules(dto));
    }

    /**
     * 硅片拆分执行(透明双玻处理) 执行
     *
     * @param dto
     * @return
     */
    @PostMapping("/transparent-double-glass-split")
    @ApiOperation(value = "硅片拆分执行(透明双玻处理)-针对入库", notes = "硅片拆分执行（透明双玻处理）-针对入库")
    public ResponseEntity<Results<Object>> siliconTransparentDoubleGlassSplit(@RequestBody TransparentDoubleGlassSplitDto dto) {
        dto.setBasePlace(null);
        dto = cellPlanLineDEConvert.toTransparentDoubleGlassSplitDtoByName(dto);
        siliconSplitService.siliconTransparentDoubleGlassSplit(dto);
        return Results.createSuccessRes();
    }
    /**
     * 美学拆分执行(美学处理) 执行
     *
     * @param dto
     * @return
     */
    @PostMapping("/aesthetics-split")
    @ApiOperation(value = "美学拆分执行(美学处理)-针对入库", notes = "美学拆分执行(美学处理)-针对入库")
    public ResponseEntity<Results<Object>> siliconAestheticsSplit(@RequestBody AestheticsSplitDto dto) {
        dto = cellPlanLineDEConvert.toAestheticsSplitDtoByName(dto);
        siliconSplitService.siliconAestheticsSplit(dto);
        return Results.createSuccessRes();
    }
    /**
     * 获取需要硅片拆分手动要指定的投产计划数据
     *
     * @param listQuery
     * @return
     */
    @PostMapping("/hand-split-data")
    @ApiOperation(value = "获取需要手动要指定的入库投产数据列表", notes = "获取需要手动要指定的投产计划数据列表")
    public ResponseEntity<Results<Page<CellPlanLineDTO>>> getSelfCellPlanLines(@RequestBody CellPlanLineSiliconTotalListQuery listQuery) {
        listQuery.setPageNumber(1);
        listQuery.setPageSize(GlobalConstant.max_page_size);
        return Results.createSuccessRes(cellPlanLineService.getHandCellPlanLines(listQuery));
    }

    /**
     * 手动硅片等级指定
     *
     * @param listDto
     * @return
     */
    @PostMapping("/hand-set-wafer-grade")
    @ApiOperation(value = "手动硅片等级指定-针对投产", notes = "手动硅片等级指定-针对投产")
    public ResponseEntity<Results<Object>> handSetWaferGrade(@RequestBody HandSetWaferGradeListDto listDto) {
        siliconSplitService.handSetWaferGrade(listDto);
        return Results.createSuccessRes();
    }

    /**
     * 手动硅料厂家指定
     *
     * @param listDto
     * @return
     */
    @PostMapping("/hand-set-si-mfrs")
    @ApiOperation(value = "手动硅料厂家指定-针对投产", notes = "手动硅料厂家指定-针对投产")
    public ResponseEntity<Results<Object>> handSetSiMfrs(@RequestBody HandSetSiMfrsListDto listDto) {
        siliconSplitService.handSetSiMfrs(listDto);
        return Results.createSuccessRes();
    }

    /**
     * 手动设置加工类型
     *
     * @param listDto
     * @return
     */
    @PostMapping("/hand-set-process-category")
    @ApiOperation(value = "手动设置加工类型-针对投产", notes = "手动设置加工类型-针对投产")
    public ResponseEntity<Results<List<CellPlanLineDTO>>> handSetProcessCategory(@RequestBody HandSetProcessCategoryListDto listDto) {

        ProcessCategorySplitDto dto = cellPlanLineDEConvert.toProcessCategorySplitDtoByName(listDto.getProcessCategorySplitDto());
        listDto.setProcessCategorySplitDto(dto);
        List<CellPlanLineDTO> cellPlanLines = siliconSplitService.handSetProcessCategory(listDto);
        return Results.createSuccessRes(cellPlanLines);
    }

    /**
     * 供应能力列表
     *
     * @param query
     * @return
     */
    @PostMapping("/supply/list")
    @ApiOperation(value = "供应能力列表", notes = "供应能力列表")
    public ResponseEntity<Results<List<SiliconSliceSupplyLinesDTO>>> supplyLinesList(@RequestBody SiliconSliceSupplyLinesQuery query) {
        List<SiliconSliceSupplyLinesDTO> list = siliconSplitService.supplyLinesList(query);
        return Results.createSuccessRes(list);
    }

    @PostMapping("/cellPlanLineSplit/list")
    @ApiOperation(value = "硅片供应能力拆分列表", notes = "硅片供应能力拆分列表")
    public ResponseEntity<Results<List<CellPlanLineSplitDTO>>> findCellPlanLineSplit(@RequestBody CellPlanLineQuery query) {
        query = cellPlanLineDEConvert.toCellPlanLineQueryByName(query);
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        return Results.createSuccessRes(cellPlanLineService.querySplitList(query));
    }

    /**
     * 加工类型拆分
     *
     * @param dto
     * @return
     */
    @PostMapping("/process-category-split")
    @ApiOperation(value = "加工类型拆分执行-针对投产", notes = "加工类型拆分执行-针对投产")
    public ResponseEntity<Results<List<CellPlanLineDTO>>> processCategorySplit(@RequestBody ProcessCategorySplitDto dto) {
        dto = cellPlanLineDEConvert.toProcessCategorySplitDtoByName(dto);

        // 校验是否已经执行过
        CellPlanLineVersionQueryDto cellPlanLineVersionQueryDto = new CellPlanLineVersionQueryDto();
        cellPlanLineVersionQueryDto.setIsOversea(dto.getIsOversea());
        cellPlanLineVersionQueryDto.setMonth(dto.getMonth());
        // 1. 去掉只能加工类型只能拆一次的限制
//        CellPlanLineVersionDTO cellPlanLineVersionDTO = cellPlanLineVersionService.query(cellPlanLineVersionQueryDto);
//        if (cellPlanLineVersionDTO != null) {
//            if (Objects.equals(cellPlanLineVersionDTO.getIsProcessCategory(), 1)) {
//                throw new BizException("此操作不允许重复执行，如需重新执行请重新创建排产计划！");
//            }
//        }
        return Results.createSuccessRes(siliconSplitService.processCategorySplit(dto));
    }

    @PostMapping("/process-category-split/submit")
    @ApiOperation(value = "加工类型拆分执行-针对投产", notes = "加工类型拆分执行-针对投产")
    public ResponseEntity<Results<List<CellPlanLineDTO>>> processCategorySplitSubmit(@RequestBody ProcessCategorySplitSubmitDTO query) {
        return Results.createSuccessRes(siliconSplitService.processCategorySplitByCellPlanLine(query));
    }
    /**
     * 分档规则匹配
     *
     * @param dto
     * @return
     */
    @PostMapping("/cell-grade-rule-mate")
    @ApiOperation(value = "分档规则匹配执行-针对入库计划", notes = "分档规则匹配执行-针对入库计划")
    public ResponseEntity<Results<List<CellInstockPlanDTO>>> cellGradeRuleMate(@RequestBody CellGradeRuleMateDto dto) {
        dto.setWorkshop(null);
        dto.setBasePlace(null);
        dto.setCellsType(null);
        String cellsType = dto.getCellsType();
        dto = cellPlanLineDEConvert.toCellGradeRuleMateDtoByName(dto);
        return Results.createSuccessRes(siliconSplitService.cellGradeRuleMate(dto, cellsType));
    }

    /**
     * 分档规则匹配汇总列表
     *
     * @param query 筛选条件
     * @return 查询
     */
    @PostMapping("/grade-rule-page")
    @ApiOperation(value = "分档规则匹配汇总列表-针对入库", notes = "分档规则匹配汇总列表-针对入库")
    public ResponseEntity<Results<Page<CellGradeRuleMateTotalDTO>>> queryGradeRuleByPage(@RequestBody CellInstockPlanQuery query) {
        query = cellInstockPlanDEConvert.toCellInstockPlanQueryByName(query);
        return Results.createSuccessRes(siliconSplitService.gradeRuleTotalQuery(query));
    }

    /**
     * 入库料号匹配
     *
     * @param query
     * @return
     */
    @PostMapping("/itemCodeMatching")
    @ApiOperation(value = "入库料号匹配", notes = "入库料号匹配")
    public ResponseEntity<Results<Object>> itemCodeMatching(@RequestBody CellInstockPlanQuery query) {
        siliconSplitService.itemCodeMatching(query);
        siliconSplitService.matchItemCallBack(query);
        return Results.createSuccessRes();
    }

    @PostMapping("/cell-instock-plan-split")
    @ApiOperation(value = "入库计划拆分列表（A-拆分，低效电池拆分，透明双玻拆分，美学拆分）", notes = "入库计划拆分列表（A-拆分，低效电池拆分，透明双玻拆分，美学拆分）")
    public ResponseEntity<Results<List<CellInstockPlanSplitDTO>>> cellInstockPlanSplit(@RequestBody CellInstockPlanQuery query) {
        return Results.createSuccessRes(siliconSplitService.cellInstockPlanSplit(query));
    }

    @PostMapping("/a-split-submit")
    @ApiOperation(value = "入库计划拆分列表(A-拆分处理)", notes = "入库计划拆分列表(A-拆分处理)")
    public ResponseEntity<Results<Object>> aSplitSubmit(@RequestBody CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        siliconSplitService.aSplitSubmit(splitSubmitDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/low-efficiency-split-submit")
    @ApiOperation(value = "入库计划拆分列表(低效电池拆分处理)", notes = "入库计划拆分列表(低效电池拆分处理)")
    public ResponseEntity<Results<Object>> lowEfficiencySplitSubmit(@RequestBody CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        siliconSplitService.lowEfficiencySplitSubmit(splitSubmitDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/transparent-double-glass-split-submit")
    @ApiOperation(value = "入库计划拆分列表(透明双玻拆分处理)", notes = "入库计划拆分列表(透明双玻拆分处理)")
    public ResponseEntity<Results<Object>> transparentDoubleGlassSplitSubmit(@RequestBody CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        siliconSplitService.transparentDoubleGlassSplitSubmit(splitSubmitDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/aesthetics-split-submit")
    @ApiOperation(value = "入库计划拆分列表(美学拆分处理)", notes = "入库计划拆分列表(美学拆分处理)")
    public ResponseEntity<Results<Object>> aestheticsSplitSubmit(@RequestBody CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        siliconSplitService.aestheticsSplitSubmit(splitSubmitDTO);
        return Results.createSuccessRes();
    }
}
