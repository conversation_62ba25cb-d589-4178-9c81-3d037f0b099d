package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.dto.CalculateDemandParams;
import com.trinasolar.scp.baps.service.service.CalculateDemandService;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: CalculateDemandController
 * @date 2024/6/15 09:40
 */
@RestController
@RequestMapping("/calculate-demand")
@RequiredArgsConstructor
@Api(value = "calculate-demand", tags = "计算需求剩余")
public class CalculateDemandController {

    private final CalculateDemandService calculateDemandService;

    @PostMapping("/calculateDemand")
    @ApiOperation(value = "实际入库覆盖，计算剩余需求")
    public ResponseEntity<Results<Object>> calculateDemand(@RequestBody @Valid CalculateDemandParams params) {
        calculateDemandService.calculateDemand(params);
        return Results.createSuccessRes();
    }

}
