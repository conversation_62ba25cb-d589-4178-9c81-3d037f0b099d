package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellPlanLineALowDEConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellPlanLineALowQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellPlanLineALowSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineALowDTO;
import com.trinasolar.scp.baps.service.service.CellPlanLineALowService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 入库计划表A- 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@RestController
@RequestMapping("/cell-plan-line-a-low")
@RequiredArgsConstructor
@Api(value = "cell-plan-line-a-low", tags = "入库计划表A-操作")
public class CellPlanLineALowController {
    private final CellPlanLineALowService cellPlanLineALowService;
    private final CellPlanLineALowDEConvert convert;
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "入库计划表查询", notes = "获得入库计划表A查询")
    public ResponseEntity<Results<Page<CellPlanLineALowDTO>>> queryByPage(@RequestBody CellPlanLineALowQuery query) {
        query=convert.toCellPlanLineALowQueryByName(query);
        return Results.createSuccessRes(cellPlanLineALowService.queryByPage(query));
    }
   // @PostMapping("/calc")
  //  @ApiOperation(value = "计算", notes = "计算")
    public  ResponseEntity<Results<Object>> calc(@RequestBody CellPlanLineALowQuery query) {
        cellPlanLineALowService.calc(query);
        return Results.createSuccessRes();
    }
    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
   // @PostMapping("/detail")
   // @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellPlanLineALowDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellPlanLineALowService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
  //  @PostMapping("/save")
  //  @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellPlanLineALowDTO>> save(@Valid @RequestBody CellPlanLineALowSaveDTO saveDTO) {
        return Results.createSuccessRes(cellPlanLineALowService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
   // @PostMapping("/delete")
  //  @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellPlanLineALowService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellPlanLineALowQuery query, HttpServletResponse response) {
        query=convert.toCellPlanLineALowQueryByName(query);
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellPlanLineALowService.export(query, response);
    }
}
