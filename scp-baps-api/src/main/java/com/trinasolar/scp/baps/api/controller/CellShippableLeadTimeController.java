package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellShippableLeadTimeQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellShippableLeadTimeSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellShippableLeadTimeDTO;
import com.trinasolar.scp.baps.service.service.CellShippableLeadTimeService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 可发货计划提前期 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-08 06:14:56
 */
@RestController
@RequestMapping("/cell-shippable-lead-time")
@RequiredArgsConstructor
@Api(value = "cell-shippable-lead-time", tags = "可发货计划提前期操作")
public class CellShippableLeadTimeController {
 private final CellShippableLeadTimeService cellShippableLeadTimeService;


 /**
  * 页列表
  *
  * @param query 筛选条件
  * @return 查询结果
  */
 @PostMapping("/page")
 @ApiOperation(value = "可发货计划提前期分页列表", notes = "获得可发货计划提前期分页列表")
 public ResponseEntity<Results<Page<CellShippableLeadTimeDTO>>> queryByPage(@RequestBody CellShippableLeadTimeQuery query) {
  return Results.createSuccessRes(cellShippableLeadTimeService.queryByPage(query));
 }

 /**
  * 通过主键查询单条数据
  *
  * @param idDTO 主键
  * @return 单条数据
  */
 @PostMapping("/detail")
 @ApiOperation(value = "通过主键查询单条数据")
 public ResponseEntity<Results<CellShippableLeadTimeDTO>> queryById(@RequestBody IdDTO idDTO) {
  return Results.createSuccessRes(cellShippableLeadTimeService.queryById(Long.parseLong(idDTO.getId())));
 }

 /**
  * 新增或更新数据
  *
  * @param saveDTO save实体
  * @return 新增或更新数据结果
  */
 @PostMapping("/save")
 @ApiOperation(value = "新增或更新数据")
 public ResponseEntity<Results<CellShippableLeadTimeDTO>> save(@Valid @RequestBody CellShippableLeadTimeSaveDTO saveDTO) {
  return Results.createSuccessRes(cellShippableLeadTimeService.save(saveDTO));
 }

 /**
  * 批量逻辑删除数据
  *
  * @param idsDTO 主键
  * @return 删除是否成功
  */
 @PostMapping("/delete")
 @ApiOperation(value = "批量逻辑删除数据")
 public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
  cellShippableLeadTimeService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
  return Results.createSuccessRes();
 }

 @PostMapping("/export")
 @ApiOperation(value = "导出")
 public void export(@RequestBody CellShippableLeadTimeQuery query, HttpServletResponse response) {
  query.setPageNumber(1);
  query.setPageSize(GlobalConstant.max_page_size);
  cellShippableLeadTimeService.export(query, response);
 }

 @PostMapping(value = "/import")
 @ApiOperation(value = "导入")
 public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile ,@RequestPart(value = "excelPara") ExcelPara excelPara) {
  cellShippableLeadTimeService.importData(multipartFile,excelPara);
  return Results.createSuccessRes();
 }
}
