package com.trinasolar.scp.baps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.DeliveryHolidayQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.DeliveryHolidaySaveDTO;
import com.trinasolar.scp.baps.domain.entity.DeliveryHoliday;
import com.trinasolar.scp.baps.domain.dto.DeliveryHolidayDTO;
import com.trinasolar.scp.baps.service.service.DeliveryHolidayService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 物流节假日表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
@RestController
@RequestMapping("/delivery-holiday")
@RequiredArgsConstructor
@Api(value = "delivery-holiday", tags = "物流节假日表操作")
public class DeliveryHolidayController {
    private final DeliveryHolidayService deliveryHolidayService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
   // @PostMapping("/page")
    @ApiOperation(value = "物流节假日表分页列表", notes = "获得物流节假日表分页列表")
    public ResponseEntity<Results<Page<DeliveryHolidayDTO>>> queryByPage(@RequestBody DeliveryHolidayQuery query) {
        return Results.createSuccessRes(deliveryHolidayService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
   // @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<DeliveryHolidayDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(deliveryHolidayService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
  //  @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<DeliveryHolidayDTO>> save(@Valid @RequestBody DeliveryHolidaySaveDTO saveDTO) {
        return Results.createSuccessRes(deliveryHolidayService.save(saveDTO));
    }
    /**
     * 新增或更新数据
     *
     * @param saveDTOS save实体集合
     * @return 新增或更新数据结果
     */
    @PostMapping("/save-all")
    @ApiOperation(value = "批量增加")
    public ResponseEntity<Results<DeliveryHolidayDTO>> saveAll(@Valid @RequestBody List<DeliveryHolidaySaveDTO> saveDTOS) {
        deliveryHolidayService.saveAll(saveDTOS);
        return Results.createSuccessRes();
    }
    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
  //  @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        deliveryHolidayService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

  //  @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody DeliveryHolidayQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            deliveryHolidayService.export(query, response);
    }
}
