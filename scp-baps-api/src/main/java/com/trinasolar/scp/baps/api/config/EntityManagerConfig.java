package com.trinasolar.scp.baps.api.config;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.web.client.RestTemplate;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

/**
 * <AUTHOR>
 * @date 2022/6/7
 */
@Configuration
public class EntityManagerConfig {
    @PersistenceContext
    EntityManager entityManager;

    @Primary
    @Bean
    @Scope("prototype")
    public JPAQueryFactory jpaQueryFactory() {
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        return jpaQueryFactory;
    }

    @Bean
//    @LoadBalanced
    public RestTemplate getRestTemplate() {
        return new RestTemplate();
    }
}
