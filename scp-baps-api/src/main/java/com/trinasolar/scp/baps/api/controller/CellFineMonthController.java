package com.trinasolar.scp.baps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellFineMonthQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellFineMonthSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellFineMonth;
import com.trinasolar.scp.baps.domain.dto.CellFineMonthDTO;
import com.trinasolar.scp.baps.service.service.CellFineMonthService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 电池良率月拆分表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-05 05:51:04
 */
@RestController
@RequestMapping("/cell-fine-month")
@RequiredArgsConstructor
@Api(value = "cell-fine-month", tags = "电池良率月拆分表操作")
public class CellFineMonthController {
    private final CellFineMonthService cellFineMonthService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池良率月拆分表分页列表", notes = "获得电池良率月拆分表分页列表")
    public ResponseEntity<Results<Page<CellFineMonthDTO>>> queryByPage(@RequestBody CellFineMonthQuery query) {
        return Results.createSuccessRes(cellFineMonthService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellFineMonthDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellFineMonthService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellFineMonthDTO>> save(@Valid @RequestBody CellFineMonthSaveDTO saveDTO) {
        return Results.createSuccessRes(cellFineMonthService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellFineMonthService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellFineMonthQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellFineMonthService.export(query, response);
    }
}
