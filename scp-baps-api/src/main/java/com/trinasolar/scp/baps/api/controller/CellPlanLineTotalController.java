package com.trinasolar.scp.baps.api.controller;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import com.trinasolar.scp.baps.domain.convert.CellPlanLineTotalDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalHDTO;
import com.trinasolar.scp.baps.domain.enums.PlanChangeStatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalQuery;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalDTO;
import com.trinasolar.scp.baps.service.service.CellPlanLineTotalService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 投产计划汇总表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@RestController
@RequestMapping("/cell-plan-line-total")
@RequiredArgsConstructor
@Api(value = "cell-plan-line-total", tags = "投产计划汇总表操作")
public class CellPlanLineTotalController {
    private final CellPlanLineTotalService cellPlanLineTotalService;
    private final CellPlanLineTotalDEConvert convert;

    /**
     * 查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */

    @PostMapping("/page")
    @ApiOperation(value = "投产计划查询", notes = "查询")
    public ResponseEntity<Results<Page<CellPlanLineTotalDTO>>> queryByPage(@RequestBody CellPlanLineTotalQuery query) {
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转cnname
        Pair<String, String> versions = cellPlanLineTotalService.getSendedEmailOrLastVersion(query, false);
        Page<CellPlanLineTotalDTO> cellPlanLineTotalDTOS = cellPlanLineTotalService.queryByPage(query, versions);
        //判断变更状态以及是否修改
        cellPlanLineTotalService.checkContrast(query,versions,cellPlanLineTotalDTOS);

        cellPlanLineTotalDTOS.forEach(item->{
            if (CollectionUtils.isEmpty(item.getChangeContrastList())) {
                item.setChangeContrastList(new ArrayList<>());
            }
        });
        return Results.createSuccessRes(cellPlanLineTotalDTOS);
    }

    /**
     * 查询（预留接口，暂不需要）
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/twomonth/page")
    @ApiOperation(value = "投产计划查询(两个月行转列)", notes = "投产计划查询(两个月行转列)")
    public ResponseEntity<Results<Page<CellPlanLineTotalDTO>>> queryTwoMonthByPage(@RequestBody CellPlanLineTotalQuery query) {
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转cnname
        return Results.createSuccessRes(cellPlanLineTotalService.queryTwnMonthByPage(query));
    }

    /**
     * 查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/formrp/page")
    @ApiOperation(value = "投产计划查询-给MRP物料使用", notes = "查询")
    public ResponseEntity<Results<Page<CellPlanLineTotalDTO>>> queryByPageForMrp(@RequestBody CellPlanLineTotalQuery query) {
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转name
        return Results.createSuccessRes(cellPlanLineTotalService.queryByPageForMrp(query));
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出", notes = "导出")
    public void export(@RequestBody CellPlanLineTotalQuery query, HttpServletResponse response) {
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转name
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellPlanLineTotalService.export(query, response);
    }

    @PostMapping("/page_h")
    @ApiOperation(value = "投产计划H兼容查询", notes = "投产计划H兼容查询")
    public ResponseEntity<Results<Page<CellPlanLineTotalHDTO>>> queryHByPage(@RequestBody CellPlanLineTotalQuery query) {
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转cnname
        return Results.createSuccessRes(cellPlanLineTotalService.queryHByPage(query));
    }
    @PostMapping("/export_h")
    @ApiOperation(value = "导出H兼容", notes = "导出H兼容")
    public void exportH(@RequestBody CellPlanLineTotalQuery query, HttpServletResponse response) {
        setCellPlanLineTotalQueryNull(query);
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转name
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellPlanLineTotalService.exportH(query, response);
    }
    /**
     * 导出（列转行两个月）（预留接口，暂不需要）
     *
     * @param query
     * @param response
     */
    @PostMapping("/twomonth/export")
    @ApiOperation(value = "导出(列转行两个月)", notes = "列转行两个月")
    public void twoMonthExport(@RequestBody CellPlanLineTotalQuery query, HttpServletResponse response) {
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转name
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellPlanLineTotalService.twoMonthExport(query, response);
    }

    @PostMapping("/formrp/export")
    @ApiOperation(value = "导出", notes = "导出")
    public void exportForMrp(@RequestBody CellPlanLineTotalQuery query, HttpServletResponse response) {
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转name
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellPlanLineTotalService.exportForMrp(query, response);
    }

    /**
     * 邮件发送
     *
     * @param query
     * @return
     */
    @PostMapping("/email")
    @ApiOperation(value = "邮件发送", notes = "发送邮件")
    public ResponseEntity<Results<Object>> email(@RequestBody CellPlanLineTotalQuery query) {
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转name
        setCellPlanLineTotalQueryNull(query);
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellPlanLineTotalService.email(query);
        return Results.createSuccessRes();
    }

    /**
     * 投产确认
     *
     * @param query
     * @return
     */
    @PostMapping("/confirm")
    @ApiOperation(value = "投产确认", notes = "投产确认")
    public ResponseEntity<Results<Object>> confirm(@RequestBody CellPlanLineTotalQuery query) {
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转name
        setCellPlanLineTotalQueryNull(query);
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellPlanLineTotalService.confirm(query);
        //计划确认后触发成批匹配料号
        cellPlanLineTotalService.matchItemCallBack(query);
        return Results.createSuccessRes();
    }

    private void setCellPlanLineTotalQueryNull(CellPlanLineTotalQuery query) {
        if (query != null) {
            query.setCellsType(null);
            query.setBasePlace(null);
            query.setWorkshop(null);
            query.setCellsTypeId(null);
            query.setBasePlaceId(null);
            query.setWorkshopId(null);
        }

    }

    /**
     * 生成入库计划
     *
     * @param query
     * @return
     */
    @PostMapping("/make")
    @ApiOperation(value = "生成入库计划", notes = "生成入库计划")
    public ResponseEntity<Results<Object>> make(@RequestBody CellPlanLineTotalQuery query) {
        setCellPlanLineTotalQueryNull(query);
        query = convert.toCellPlanLineTotalQuery(query);// lov->value转name
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellPlanLineTotalService.makeCellInstockPlan(query);
        return Results.createSuccessRes();
    }
    @PostMapping(value = "/import_h")
    @ApiOperation(value = "导入H兼容")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile) {
        cellPlanLineTotalService.importData(multipartFile);
        return Results.createSuccessRes();
    }

    @PostMapping(value = "/getMaxVersion")
    @ApiOperation(value = "获取最大版本号")
    public ResponseEntity<Results<List<CellPlanLineTotalDTO>>> getMaxVersion() {
        return Results.createSuccessRes(cellPlanLineTotalService.getMaxVersion());
    }
}
