package com.trinasolar.scp.baps.api;

import java.util.*;
import java.util.stream.*;
import static java.util.stream.Collectors.*;

public class GroupAndSortExample {

    public static void main(String[] args) {

        List<Item> items = Arrays.asList(
                new Item("E", 10),
                new Item("D", 10),
                new Item("A", 10),
                new Item("C", 5),
                new Item("B", 7),
                new Item("A", 20),
                new Item("B", 3)
        );

        // 使用TreeMap来按键排序分组结果
        Map<String, List<Item>> grouped = items.stream()
                .collect(groupingBy(Item::getName, TreeMap::new, toList()));

        // 输出结果
        grouped.forEach((k, v) -> System.out.println(k + ": " + v));
    }

    static class Item {
        String name;
        int value;

        Item(String name, int value) {
            this.name = name;
            this.value = value;
        }

        String getName() {
            return name;
        }

        int getValue() {
            return value;
        }

        @Override
        public String toString() {
            return "Item{" + "name='" + name + '\'' + ", value=" + value + '}';
        }
    }
}