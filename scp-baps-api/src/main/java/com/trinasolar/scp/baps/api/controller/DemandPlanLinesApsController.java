package com.trinasolar.scp.baps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.DemandPlanLinesApsQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.DemandPlanLinesApsSaveDTO;
import com.trinasolar.scp.baps.domain.entity.DemandPlanLinesAps;
import com.trinasolar.scp.baps.domain.dto.DemandPlanLinesApsDTO;
import com.trinasolar.scp.baps.service.service.DemandPlanLinesApsService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 需求计划明细（APS） 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 08:30:30
 */
@RestController
@RequestMapping("/demand-plan-lines-aps")
@RequiredArgsConstructor
@Api(value = "demand-plan-lines-aps", tags = "需求计划明细（APS）操作")
public class DemandPlanLinesApsController {
    private final DemandPlanLinesApsService demandPlanLinesApsService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "需求计划明细（APS）分页列表", notes = "获得需求计划明细（APS）分页列表")
    public ResponseEntity<Results<Page<DemandPlanLinesApsDTO>>> queryByPage(@RequestBody DemandPlanLinesApsQuery query) {
        return Results.createSuccessRes(demandPlanLinesApsService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<DemandPlanLinesApsDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(demandPlanLinesApsService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<DemandPlanLinesApsDTO>> save(@Valid @RequestBody DemandPlanLinesApsSaveDTO saveDTO) {
        return Results.createSuccessRes(demandPlanLinesApsService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        demandPlanLinesApsService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody DemandPlanLinesApsQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        demandPlanLinesApsService.export(query, response);
    }
}
