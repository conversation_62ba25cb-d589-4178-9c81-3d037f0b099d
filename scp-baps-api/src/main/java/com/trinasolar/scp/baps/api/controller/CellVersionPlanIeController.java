package com.trinasolar.scp.baps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellVersionPlanIeQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellVersionPlanIeSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanIe;
import com.trinasolar.scp.baps.domain.dto.CellVersionPlanIeDTO;
import com.trinasolar.scp.baps.service.service.CellVersionPlanIeService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 计划与ie产能对比 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@RestController
@RequestMapping("/cell-version-plan-ie")
@RequiredArgsConstructor
@Api(value = "cell-version-plan-ie", tags = "计划与ie产能对比操作")
public class CellVersionPlanIeController {
    private final CellVersionPlanIeService cellVersionPlanIeService;


    /**
     * 查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/query")
    @ApiOperation(value = "计划与ie产能对比分页列表", notes = "获得计划与ie产能对比分页列表")
    public ResponseEntity<Results<Map<String, Object>>> queryByPage(@RequestBody CellVersionPlanIeQuery query) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        return Results.createSuccessRes(cellVersionPlanIeService.makeReport(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellVersionPlanIeDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellVersionPlanIeService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellVersionPlanIeDTO>> save(@Valid @RequestBody CellVersionPlanIeSaveDTO saveDTO) {
        return Results.createSuccessRes(cellVersionPlanIeService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellVersionPlanIeService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellVersionPlanIeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellVersionPlanIeService.export(query, response);
    }
}
