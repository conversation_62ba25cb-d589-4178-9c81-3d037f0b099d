package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellConversionFactorDEConvert;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellConversionFactorQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellConversionFactorSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellConversionFactorDTO;
import com.trinasolar.scp.baps.service.service.CellConversionFactorService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 万片与兆瓦折算系数 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-31 10:33:29
 */
@RestController
@RequestMapping("/cell-conversion-factor")
@RequiredArgsConstructor
@Api(value = "cell-conversion-factor", tags = "万片与兆瓦折算系数操作")
public class CellConversionFactorController {
    private final CellConversionFactorService cellConversionFactorService;
    private final CellConversionFactorDEConvert convert;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "万片与兆瓦折算系数查询", notes = "获得万片与兆瓦折算系数查询")
    public ResponseEntity<Results<Page<CellConversionFactorDTO>>> queryByPage(@RequestBody CellConversionFactorQuery query) {
        query=convert.toCellConversionFactorQueryCNName(query, MyThreadLocal.get().getLang());
        return Results.createSuccessRes(cellConversionFactorService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
  //  @PostMapping("/detail")
   // @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellConversionFactorDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellConversionFactorService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
  //  @PostMapping("/save")
    //@ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellConversionFactorDTO>> save(@Valid @RequestBody CellConversionFactorSaveDTO saveDTO) {
        return Results.createSuccessRes(cellConversionFactorService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
   @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellConversionFactorService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellConversionFactorQuery query, HttpServletResponse response) {
        query=convert.toCellConversionFactorQueryCNName(query, MyThreadLocal.get().getLang());
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellConversionFactorService.export(query, response);
    }
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile) {
        cellConversionFactorService.importData(multipartFile);
        return Results.createSuccessRes();
    }
    /**
     * 获取所有电池类型lov数据更新
     *
     * @return 查询结果
     */
    @PostMapping("/updateConversionFactor")
    @ApiOperation(value = "更新万片与兆瓦折算系数", notes = "更新万片与兆瓦折算系数")
    public ResponseEntity<Results<Object>> updateConversionFactor() {
        cellConversionFactorService.updateConversionFactor();
        return Results.createSuccessRes();
    }
}
