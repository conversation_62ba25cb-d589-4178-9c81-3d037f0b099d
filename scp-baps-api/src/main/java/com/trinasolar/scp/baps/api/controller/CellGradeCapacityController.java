package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellGradeCapacityDEConvert;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellGradeCapacityQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellGradeCapacitySaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellGradeCapacityDTO;
import com.trinasolar.scp.baps.service.service.CellGradeCapacityService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 爬坡产能表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@RestController
@RequestMapping("/cell-grade-capacity")
@RequiredArgsConstructor
@Api(value = "cell-grade-capacity", tags = "爬坡产能表操作")
public class CellGradeCapacityController {
    private final CellGradeCapacityService cellGradeCapacityService;
    private final CellGradeCapacityDEConvert convert;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "爬坡产能表分页列表", notes = "获得爬坡产能表分页列表")
    public ResponseEntity<Results<Page<CellGradeCapacityDTO>>> queryByPage(@RequestBody CellGradeCapacityQuery query) {
        query=  convert.toCellGradeCapacityQueryCNNameByName(query, MyThreadLocal.get().getLang());
        return Results.createSuccessRes(cellGradeCapacityService.queryByPage(query));
    }
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/pagefordm")
    @ApiOperation(value = "爬坡产能表分页列表", notes = "获得爬坡产能表分页列表")
    public ResponseEntity<Results<List<CellGradeCapacityDTO>>> queryForDm(@RequestBody CellGradeCapacityQuery query) {
        query=  convert.toCellGradeCapacityQueryCNNameByName(query, MyThreadLocal.get().getLang());
        return Results.createSuccessRes(cellGradeCapacityService.queryForDm(query));
    }
    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellGradeCapacityDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellGradeCapacityService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellGradeCapacityDTO>> save(@Valid @RequestBody CellGradeCapacitySaveDTO saveDTO) {
        return Results.createSuccessRes(cellGradeCapacityService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellGradeCapacityService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellGradeCapacityQuery query, HttpServletResponse response) {
        query=  convert.toCellGradeCapacityQueryCNNameByName(query, MyThreadLocal.get().getLang());
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellGradeCapacityService.export(query, response);
    }
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile,@RequestPart(value = "excelPara") ExcelPara excelPara) {
        cellGradeCapacityService.importData(multipartFile,excelPara);
        return Results.createSuccessRes();
    }
}
