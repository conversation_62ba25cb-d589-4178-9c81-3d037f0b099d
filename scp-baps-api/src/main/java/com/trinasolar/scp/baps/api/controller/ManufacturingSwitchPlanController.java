package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.ManufacturingSwitchPlanDEConvert;
import com.trinasolar.scp.baps.domain.dto.ManufacturingSwitchPlanDTO;
import com.trinasolar.scp.baps.domain.query.ManufacturingSwitchPlanQuery;
import com.trinasolar.scp.baps.service.service.ManufacturingSwitchPlanService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2024/8/20
 */
@RestController
@RequestMapping("/manufacturing-switch-plan")
@RequiredArgsConstructor
@Api(value = "manufacturing-switch-plan", tags = "制造工艺切换计划维护操作")
public class ManufacturingSwitchPlanController {
    private final ManufacturingSwitchPlanService manufacturingSwitchPlanService;
    private final ManufacturingSwitchPlanDEConvert convert;

    @PostMapping("/page")
    @ApiOperation(value = "制造工艺切换计划维护列表", notes = "获制造工艺切换计划维护列表")
    public ResponseEntity<Results<Page<ManufacturingSwitchPlanDTO>>> queryByPage(@RequestBody ManufacturingSwitchPlanQuery query) {
        query=  convert.toQueryCNNameByName(query, MyThreadLocal.get().getLang());
        return Results.createSuccessRes(manufacturingSwitchPlanService.queryByPage(query));
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ManufacturingSwitchPlanQuery query, HttpServletResponse response) {
        query=  convert.toQueryCNNameByName(query, MyThreadLocal.get().getLang());
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        manufacturingSwitchPlanService.export(query, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile, @RequestPart(value = "excelPara") ExcelPara excelPara) {
        manufacturingSwitchPlanService.importData(multipartFile,excelPara);
        return Results.createSuccessRes();
    }
}
