package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellVersionQopIeQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellVersionQopIeSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellVersionQopIeDTO;
import com.trinasolar.scp.baps.service.service.CellVersionQopIeService;
import com.trinasolar.scp.common.api.util.Results;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * qop与ie对比 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:08
 */
@RestController
@RequestMapping("/cell-version-qop-ie")
@RequiredArgsConstructor
@Api(value = "cell-version-qop-ie", tags = "qop与ie对比操作")
public class CellVersionQopIeController {
    private final CellVersionQopIeService cellVersionQopIeService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/query")
    @ApiOperation(value = "qop与ie对比分页列表", notes = "获得qop与ie对比分页列表")
    public ResponseEntity<Results<Map<String, Object>>> queryByPage(@RequestBody CellVersionQopIeQuery query) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);

        return Results.createSuccessRes(cellVersionQopIeService.makeReport(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellVersionQopIeDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellVersionQopIeService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellVersionQopIeDTO>> save(@Valid @RequestBody CellVersionQopIeSaveDTO saveDTO) {
        return Results.createSuccessRes(cellVersionQopIeService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellVersionQopIeService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellVersionQopIeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellVersionQopIeService.export(query, response);
    }
}
