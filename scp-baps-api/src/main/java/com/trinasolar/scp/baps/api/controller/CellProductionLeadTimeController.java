package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellProductionLeadTimeQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellProductionLeadTimeSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellProductionLeadTimeDTO;
import com.trinasolar.scp.baps.service.service.CellProductionLeadTimeService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 投产提前期 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@RestController
@RequestMapping("/cell-production-lead-time")
@RequiredArgsConstructor
@Api(value = "cell-production-lead-time", tags = "投产提前期操作")
public class CellProductionLeadTimeController {
    private final CellProductionLeadTimeService cellProductionLeadTimeService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "投产提前期分页列表", notes = "获得投产提前期分页列表")
    public ResponseEntity<Results<Page<CellProductionLeadTimeDTO>>> queryByPage(@RequestBody CellProductionLeadTimeQuery query) {
        return Results.createSuccessRes(cellProductionLeadTimeService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
   // @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellProductionLeadTimeDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellProductionLeadTimeService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
  //  @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellProductionLeadTimeDTO>> save(@Valid @RequestBody CellProductionLeadTimeSaveDTO saveDTO) {
        return Results.createSuccessRes(cellProductionLeadTimeService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellProductionLeadTimeService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellProductionLeadTimeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellProductionLeadTimeService.export(query, response);
    }
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile, @RequestPart(value = "excelPara") ExcelPara excelPara) {
        cellProductionLeadTimeService.importData(multipartFile,excelPara);
        return Results.createSuccessRes();
    }
}
