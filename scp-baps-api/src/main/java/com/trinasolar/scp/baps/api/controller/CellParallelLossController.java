package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellParallelLossDEConvert;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellParallelLossQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellParallelLossSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellParallelLossDTO;
import com.trinasolar.scp.baps.service.service.CellParallelLossService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 产能并行损失表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@RestController
@RequestMapping("/cell-parallel-loss")
@RequiredArgsConstructor
@Api(value = "cell-parallel-loss", tags = "产能并行损失表操作")
public class CellParallelLossController {
    private final CellParallelLossService cellParallelLossService;
    private final CellParallelLossDEConvert convert;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "产能并行损失表分页列表", notes = "获得产能并行损失表分页列表")
    public ResponseEntity<Results<Page<CellParallelLossDTO>>> queryByPage(@RequestBody CellParallelLossQuery query) {

        return Results.createSuccessRes(cellParallelLossService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellParallelLossDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellParallelLossService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellParallelLossDTO>> save(@Valid @RequestBody CellParallelLossSaveDTO saveDTO) {
        return Results.createSuccessRes(cellParallelLossService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellParallelLossService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellParallelLossQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellParallelLossService.export(query, response);
    }
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile, @RequestPart(value = "excelPara") ExcelPara excelPara) {
        cellParallelLossService.importData(multipartFile,excelPara);
        return Results.createSuccessRes();
    }
}
