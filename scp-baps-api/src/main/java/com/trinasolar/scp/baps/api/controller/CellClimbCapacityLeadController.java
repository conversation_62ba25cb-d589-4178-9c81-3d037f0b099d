package com.trinasolar.scp.baps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellClimbCapacityLeadQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellClimbCapacityLeadSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellClimbCapacityLead;
import com.trinasolar.scp.baps.domain.dto.CellClimbCapacityLeadDTO;
import com.trinasolar.scp.baps.service.service.CellClimbCapacityLeadService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 爬坡产能可靠性验证表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-05 08:10:43
 */
@RestController
@RequestMapping("/cell-climb-capacity-lead")
@RequiredArgsConstructor
@Api(value = "cell-climb-capacity-lead", tags = "爬坡产能可靠性验证表操作")
public class CellClimbCapacityLeadController {
    private final CellClimbCapacityLeadService cellClimbCapacityLeadService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "爬坡产能可靠性验证表分页列表", notes = "获得爬坡产能可靠性验证表分页列表")
    public ResponseEntity<Results<Page<CellClimbCapacityLeadDTO>>> queryByPage(@RequestBody CellClimbCapacityLeadQuery query) {
        return Results.createSuccessRes(cellClimbCapacityLeadService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellClimbCapacityLeadDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellClimbCapacityLeadService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellClimbCapacityLeadDTO>> save(@Valid @RequestBody CellClimbCapacityLeadSaveDTO saveDTO) {
        return Results.createSuccessRes(cellClimbCapacityLeadService.save(saveDTO));
    }
    /**
     * 批量更新数据
     * @param saveDTOList save实体
     */
    @PostMapping("/batchUpdate")
    @ApiOperation(value = "批量更新数据")
    public ResponseEntity<Results<List<CellClimbCapacityLeadDTO>>> batchUpdate(@Valid @RequestBody List<CellClimbCapacityLeadSaveDTO> saveDTOList) {
        return Results.createSuccessRes(cellClimbCapacityLeadService.batchUpdate(saveDTOList));
    }
    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellClimbCapacityLeadService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellClimbCapacityLeadQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellClimbCapacityLeadService.export(query, response);
    }
    /**
     * 同步爬坡产能数据
     */
    @PostMapping("/syncClimbCapacity")
    @ApiOperation(value = "同步爬坡产能数据")
    public ResponseEntity<Results<Object>> syncClimbCapacity() {
        cellClimbCapacityLeadService.syncClimbCapacity();
        return Results.createSuccessRes();
    }
}
