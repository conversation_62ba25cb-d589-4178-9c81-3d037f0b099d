package com.trinasolar.scp.baps.api.controller;

import cn.hutool.log.Log;
import com.trinasolar.scp.baps.domain.dto.CellWipDTO;
import com.trinasolar.scp.baps.service.service.erp.DataSyncService;
import com.trinasolar.scp.common.api.util.Results;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

@RestController
@RequiredArgsConstructor
@Slf4j
public class ErpDataController {
    private final DataSyncService dataSyncService;
    @Resource(name = "dataSyncThreadPool")
    private ExecutorService executorService;

    @PostMapping(path = "/syncWipIssue")
    public ResponseEntity<Results<Object>> syncWipIssue() {
        dataSyncService.syncWipIssue(null, null);
//        CompletableFuture.runAsync(() -> {
//            dataSyncService.syncWipIssue(null, null);
//        }, executorService);
//        log.info("异步获取数据");
        return Results.createSuccessRes();
    }
}
