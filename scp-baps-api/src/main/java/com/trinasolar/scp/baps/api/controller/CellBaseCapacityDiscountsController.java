package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellBaseCapacityDiscountsDEConvert;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityDiscountsQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacityDiscountsSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDiscountsDTO;
import com.trinasolar.scp.baps.service.service.CellBaseCapacityDiscountsService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * IE产能打折（人力）表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@RestController
@RequestMapping("/cell-base-capacity-discounts")
@RequiredArgsConstructor
@Api(value = "cell-base-capacity-discounts", tags = "IE产能打折（人力）表操作")
public class CellBaseCapacityDiscountsController {
    private final CellBaseCapacityDiscountsService cellBaseCapacityDiscountsService;
    private final CellBaseCapacityDiscountsDEConvert convert;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "IE产能打折（人力）表分页列表", notes = "获得IE产能打折（人力）表分页列表")
    public ResponseEntity<Results<Page<CellBaseCapacityDiscountsDTO>>> queryByPage(@RequestBody CellBaseCapacityDiscountsQuery query) {
        query=  convert.toCellBaseCapacityDiscountsQueryCNNameByName(query, MyThreadLocal.get().getLang());
        return Results.createSuccessRes(cellBaseCapacityDiscountsService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellBaseCapacityDiscountsDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellBaseCapacityDiscountsService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellBaseCapacityDiscountsDTO>> save(@Valid @RequestBody CellBaseCapacityDiscountsSaveDTO saveDTO) {
        return Results.createSuccessRes(cellBaseCapacityDiscountsService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellBaseCapacityDiscountsService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }
    /**
     * 批量批量ie确认
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/ieconfirm/yes")
    @ApiOperation(value = "批量ie确认")
    public ResponseEntity<Results<Object>> batchIeConfirm(@RequestBody IdsDTO idsDTO) {
        cellBaseCapacityDiscountsService.ieConfirm(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()),1);
        return Results.createSuccessRes();
    }
    /**
     * 批量批量ie确认取消
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/ieconfirm/no")
    @ApiOperation(value = "批量ie确认取消")
    public ResponseEntity<Results<Object>> batchIeCancel(@RequestBody IdsDTO idsDTO) {
        cellBaseCapacityDiscountsService.ieConfirm(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()),0);
        return Results.createSuccessRes();
    }

    /**
     * 批量批量计划确认
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/planconfirm/yes")
    @ApiOperation(value = "批量计划确认")
    public ResponseEntity<Results<Object>> batchPlanConfirm(@RequestBody IdsDTO idsDTO) {
        cellBaseCapacityDiscountsService.planConfirm(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()),1);
        return Results.createSuccessRes();
    }
    /**
     * 批量批量计划确认取消
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/planconfirm/no")
    @ApiOperation(value = "批量计划确认取消")
    public ResponseEntity<Results<Object>> batchPlanCancel(@RequestBody IdsDTO idsDTO) {
        cellBaseCapacityDiscountsService.planConfirm(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()),0);
        return Results.createSuccessRes();
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile, @RequestPart(value = "excelPara") ExcelPara excelPara) {
        cellBaseCapacityDiscountsService.importData(multipartFile,excelPara);
        return Results.createSuccessRes();
    }
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellBaseCapacityDiscountsQuery query, HttpServletResponse response) {
        query=  convert.toCellBaseCapacityDiscountsQueryCNNameByName(query, MyThreadLocal.get().getLang());
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellBaseCapacityDiscountsService.export(query, response);
    }
}
