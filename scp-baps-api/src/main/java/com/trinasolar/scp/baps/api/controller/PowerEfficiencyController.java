package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.dto.PowerEfficiencyDTO;
import com.trinasolar.scp.baps.domain.query.PowerEfficiencyQuery;
import com.trinasolar.scp.baps.service.service.PowerEfficiencyService;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 全年效率值 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:13
 */
@RestController
@RequestMapping("/power-efficiency")
@Api(value = "power-efficiency", tags = "全年效率值操作")
public class PowerEfficiencyController {
    @Autowired
    PowerEfficiencyService powerEfficiencyService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "全年效率值分页列表", notes = "获得全年效率值分页列表")
    public ResponseEntity<Results<List<PowerEfficiencyDTO>>> queryByPage(@RequestBody PowerEfficiencyQuery query) {
        return Results.createSuccessRes(powerEfficiencyService.queryWithRelations(query));
    }

    /**
     * 读取第第三方接口scp-aps的效率分布接口后进行数据整合
     * @return
     */
    @GetMapping(value = "/refresh-data")
    @ApiOperation(value = "更新")
    public ResponseEntity<Results<Object>> refreshData() {
        powerEfficiencyService.refreshData();
        return Results.createSuccessRes();
    }
}
