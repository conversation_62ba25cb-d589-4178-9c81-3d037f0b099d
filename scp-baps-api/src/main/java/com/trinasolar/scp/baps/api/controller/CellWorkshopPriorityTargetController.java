package com.trinasolar.scp.baps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellWorkshopPriorityTargetQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellWorkshopPriorityTargetSaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellWorkshopPriorityTargetDTO;
import com.trinasolar.scp.baps.service.service.CellWorkshopPriorityTargetService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 车间优先度效率目标值 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@RestController
@RequestMapping("/cell-workshop-priority-target")
@RequiredArgsConstructor
@Api(value = "cell-workshop-priority-target", tags = "车间优先度效率目标值操作")
public class CellWorkshopPriorityTargetController {
    private final CellWorkshopPriorityTargetService cellWorkshopPriorityTargetService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "车间优先度效率目标值分页列表", notes = "获得车间优先度效率目标值分页列表")
    public ResponseEntity<Results<Page<CellWorkshopPriorityTargetDTO>>> queryByPage(@RequestBody CellWorkshopPriorityTargetQuery query) {
        return Results.createSuccessRes(cellWorkshopPriorityTargetService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
   // @PostMapping("/detail")
  //  @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellWorkshopPriorityTargetDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellWorkshopPriorityTargetService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
  //  @PostMapping("/save")
  //  @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellWorkshopPriorityTargetDTO>> save(@Valid @RequestBody CellWorkshopPriorityTargetSaveDTO saveDTO) {
        return Results.createSuccessRes(cellWorkshopPriorityTargetService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
  //  @PostMapping("/delete")
  //  @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellWorkshopPriorityTargetService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 读取第第三方接口scp-aps的效率分布接口后进行数据整合
     * @return
     */
    @GetMapping(value = "/refresh-data")
    @ApiOperation(value = "更新")
    public ResponseEntity<Results<Object>> refreshData() {
        cellWorkshopPriorityTargetService.refreshData();
        return Results.createSuccessRes();
    }
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellWorkshopPriorityTargetQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellWorkshopPriorityTargetService.export(query, response);
    }
}
