package com.trinasolar.scp.baps.api.controller;
import com.trinasolar.scp.baps.domain.dto.MainGridSpacingRuleDTO;
import com.trinasolar.scp.baps.domain.query.MainGridSpacingRuleQuery;
import com.trinasolar.scp.baps.domain.save.MainGridSpacingRuleSaveDTO;
import com.trinasolar.scp.baps.service.service.MainGridSpacingRuleService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池主栅间距规则 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-17 08:55:14
 */
@RestController
@RequestMapping("/main-grid-spacing-rule")
@RequiredArgsConstructor
@Api(value = "main-grid-spacing-rule", tags = "电池主栅间距规则操作")
public class MainGridSpacingRuleController {
    private final MainGridSpacingRuleService mainGridSpacingRuleService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池主栅间距规则分页列表", notes = "获得电池主栅间距规则分页列表")
    public ResponseEntity<Results<Page<MainGridSpacingRuleDTO>>> queryByPage(@RequestBody MainGridSpacingRuleQuery query) {
        return Results.createSuccessRes(mainGridSpacingRuleService.queryByPage(query));
    }

    @PostMapping("/applyRule")
    @ApiOperation(value = "电池主栅间距规则分页列表", notes = "获得电池主栅间距规则分页列表")
    public ResponseEntity<Results<MainGridSpacingRuleDTO>> applyRule(@RequestBody MainGridSpacingRuleQuery query) {
        return Results.createSuccessRes(mainGridSpacingRuleService.applyRule(query));
    }

    @PostMapping("/getAllRules")
    @ApiOperation(value = "获取所有的规则", notes = "获取所有的规则")
    public ResponseEntity<Results<List<MainGridSpacingRuleDTO>>> getAllRules(@RequestBody MainGridSpacingRuleQuery query) {
        return Results.createSuccessRes(mainGridSpacingRuleService.getAllRules(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<MainGridSpacingRuleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(mainGridSpacingRuleService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<MainGridSpacingRuleDTO>> save(@Valid @RequestBody MainGridSpacingRuleSaveDTO saveDTO) {
        return Results.createSuccessRes(mainGridSpacingRuleService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        mainGridSpacingRuleService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody MainGridSpacingRuleQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        mainGridSpacingRuleService.export(query, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile) {
        mainGridSpacingRuleService.importData(multipartFile);
        return Results.createSuccessRes();
    }

    @PostMapping("/getMainGridSpaceList")
    @ApiOperation(value = "获取主栅间距列表", notes = "获取主栅间距列表")
    public ResponseEntity<Results<List<String>>> getMainGridSpaceList() {
        List<MainGridSpacingRuleDTO> allRules = mainGridSpacingRuleService.getAllRules(new MainGridSpacingRuleQuery());
        List<String> mainGridSpaceList = allRules.stream().map(MainGridSpacingRuleDTO::getMainGridSpacing).distinct().collect(Collectors.toList());
        return Results.createSuccessRes(mainGridSpaceList);
    }
}