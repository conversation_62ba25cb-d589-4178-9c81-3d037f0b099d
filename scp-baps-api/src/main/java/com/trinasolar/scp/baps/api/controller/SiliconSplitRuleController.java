package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.SiliconSplitRuleQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.SiliconSplitRuleSaveDTO;
import com.trinasolar.scp.baps.domain.entity.SiliconSplitRule;
import com.trinasolar.scp.baps.domain.dto.SiliconSplitRuleDTO;
import com.trinasolar.scp.baps.service.service.SiliconSplitRuleService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 硅片拆分规则 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 07:44:54
 */
@RestController
@RequestMapping("/silicon-split-rule")
@RequiredArgsConstructor
@Api(value = "silicon-split-rule", tags = "硅片拆分规则操作")
public class SiliconSplitRuleController {
    private final SiliconSplitRuleService siliconSplitRuleService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "硅片拆分规则分页列表", notes = "获得硅片拆分规则分页列表")
    public ResponseEntity<Results<Page<SiliconSplitRuleDTO>>> queryByPage(@RequestBody SiliconSplitRuleQuery query) {
        return Results.createSuccessRes(siliconSplitRuleService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
   // @PostMapping("/detail")
   // @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<SiliconSplitRuleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(siliconSplitRuleService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
   // @PostMapping("/save")
   // @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<SiliconSplitRuleDTO>> save(@Valid @RequestBody SiliconSplitRuleSaveDTO saveDTO) {
        return Results.createSuccessRes(siliconSplitRuleService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
   // @PostMapping("/delete")
   // @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        siliconSplitRuleService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody SiliconSplitRuleQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            siliconSplitRuleService.export(query, response);
    }
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile, @RequestPart(value = "excelPara") ExcelPara excelPara) {
        siliconSplitRuleService.importData(multipartFile, excelPara);
        return Results.createSuccessRes();
    }
}
