package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellBaseCapacityDEConvert;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacitySaveDTO;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDTO;
import com.trinasolar.scp.baps.service.service.CellBaseCapacityService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * IE产能表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@RestController
@RequestMapping("/cell-base-capacity")
@RequiredArgsConstructor
@Api(value = "cell-base-capacity", tags = "IE产能表操作")
public class CellBaseCapacityController {
    private final CellBaseCapacityService cellBaseCapacityService;
    private final CellBaseCapacityDEConvert cellBaseCapacityDEConvert;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "IE产能表分页列表", notes = "获得IE产能表分页列表")
    public ResponseEntity<Results<Page<CellBaseCapacityDTO>>> queryByPage(@RequestBody CellBaseCapacityQuery query) {

       query=  cellBaseCapacityDEConvert.toCNNameByName(query, MyThreadLocal.get().getLang());

       return Results.createSuccessRes(cellBaseCapacityService.queryByPage(query));
    }
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/pagefordm")
    @ApiOperation(value = "IE产能表数据获取", notes = "IE产能表数据获取")
    public ResponseEntity<Results<Map<String,List<CellBaseCapacityDTO>>>> queryForDm(@RequestBody CellBaseCapacityQuery query) {
        query=  cellBaseCapacityDEConvert.toCNNameByName(query, MyThreadLocal.get().getLang());
        return Results.createSuccessRes(cellBaseCapacityService.queryForDm(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellBaseCapacityDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellBaseCapacityService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellBaseCapacityDTO>> save(@Valid @RequestBody CellBaseCapacitySaveDTO saveDTO) {
        return Results.createSuccessRes(cellBaseCapacityService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellBaseCapacityService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellBaseCapacityQuery query, HttpServletResponse response) {
        query=  cellBaseCapacityDEConvert.toCNNameByName(query, MyThreadLocal.get().getLang());
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellBaseCapacityService.export(query, response);
    }
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile, @RequestPart(value = "excelPara") ExcelPara excelPara) {
        cellBaseCapacityService.importData(multipartFile, excelPara);
        return Results.createSuccessRes();
    }
}
