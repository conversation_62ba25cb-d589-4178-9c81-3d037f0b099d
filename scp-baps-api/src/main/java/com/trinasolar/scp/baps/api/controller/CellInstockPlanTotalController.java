package com.trinasolar.scp.baps.api.controller;

import com.google.common.collect.Maps;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanTotalDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellInStockPlanRemarkGroupDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanTotalDTO;
import com.trinasolar.scp.baps.domain.enums.PlanChangeStatusEnum;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanTotalQuery;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanTotalSaveDTO;
import com.trinasolar.scp.baps.service.service.CellInStockPlanRemarkService;
import com.trinasolar.scp.baps.service.service.CellInstockPlanTotalService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 入库计划汇总表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-26 11:20:51
 */
@RestController
@RequestMapping("/cell-instock-plan-total")
@RequiredArgsConstructor
@Api(value = "入库计划汇总表操作", tags = "入库计划汇总表操作")
public class CellInstockPlanTotalController {
    private final CellInstockPlanTotalService cellInstockPlanTotalService;
    private final CellInstockPlanTotalDEConvert convert;
    private final CellInStockPlanRemarkService cellInStockPlanRemarkService;
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "入库计划汇总表分页列表", notes = "获得入库计划汇总表分页列表")
    public ResponseEntity<Results<Page<CellInstockPlanTotalDTO>>> queryByPage(@RequestBody CellInstockPlanTotalQuery query) {
        Pair<String, String> versions = cellInstockPlanTotalService.getSendedEmailOrLastVersion(query, false);
        Pair<String, String> sentEmailVersions = cellInstockPlanTotalService.getSendedEmailOrLastVersion(query, true);
        query=   convert.toCellInstockPlanTotalQuery(query);// lov->value转name
        Page<CellInstockPlanTotalDTO> cellLaughingstockTotalDOS = cellInstockPlanTotalService.queryByPage(query, versions);
        Boolean sameVersionFlag = (Objects.isNull(sentEmailVersions.getLeft()) && Objects.isNull(sentEmailVersions.getRight()))
                || ((Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getLeft()) && Objects.equals(versions.getLeft(), sentEmailVersions.getLeft()))
                && (Objects.nonNull(versions.getRight()) && Objects.nonNull(versions.getRight()) && Objects.equals(versions.getRight(), sentEmailVersions.getRight())));
        Page<CellInstockPlanTotalDTO> sendedEmailDTOS = new PageImpl(Collections.emptyList());
        Map<String, CellInstockPlanTotalDTO> sendedEmailDTOMap;
        if (!sameVersionFlag) {
            query.setPageNumber(1);
            query.setPageSize(Integer.MAX_VALUE);
            query.setBusinessType("1");
            sendedEmailDTOS = cellInstockPlanTotalService.queryByPage(query, sentEmailVersions);
            sendedEmailDTOMap = sendedEmailDTOS.getContent().stream().collect(Collectors.toMap(CellInstockPlanTotalDTO::group, Function.identity(), (v1, v2) -> v1));
        } else {
            sendedEmailDTOMap = Maps.newHashMap();
        }
        cellLaughingstockTotalDOS.getContent().forEach(ele -> {
            if (MapUtils.isNotEmpty(ele.getDataStructures())) {
                TreeMap<String, String> dataStructures = ele.getDataStructures();
                dataStructures.forEach((k, v) -> {
                    dataStructures.put(k, !StringUtils.isEmpty(v) ? String.valueOf(new BigDecimal(v).setScale(2, RoundingMode.HALF_UP)) : "");
                });
                ele.setDataStructures(dataStructures);

                TreeMap<String, Pair<String, Boolean>> dataStructurePairMap = Maps.newTreeMap();
                dataStructures.forEach((k, v) ->{
                    dataStructurePairMap.put(k, Pair.of(v, false));
                });
                ele.setDataStructurePairs(dataStructurePairMap);
            }

            if (sameVersionFlag) {
                ele.setChangeStatusDesc(PlanChangeStatusEnum.UNCHANGE.getDesc());
            } else {
                CellInstockPlanTotalDTO sendedEmailDTO = sendedEmailDTOMap.get(ele.group());
                if (Objects.isNull(sendedEmailDTO)) {
                    ele.setChangeStatusDesc(PlanChangeStatusEnum.ADD.getDesc());
                } else {
                    AtomicReference<Boolean> changeFlag = new AtomicReference<>(false);
                    ele.getDataStructurePairs().forEach((k, v) -> {
                        String sendedEmailValue = sendedEmailDTO.getDataStructures().get(k);
                        if (!Objects.equals(v.getLeft(), sendedEmailValue)) {
                            v = Pair.of(v.getLeft(), true);
                            changeFlag.set(true);
                        }
                    });
                    ele.setChangeStatusDesc(changeFlag.get() ? PlanChangeStatusEnum.CHANGEED.getDesc() : PlanChangeStatusEnum.UNCHANGE.getDesc());
                }
            }
            //每天需要对比
            try{
                cellInstockPlanTotalService.changeContrast(sendedEmailDTOMap.get(ele.group()),ele);
            }catch (Exception e){
                throw new BizException("数据比对失败，请联系运维人员处理");
            }

//            if (sameVersionFlag) {
//                ele.setChangeStatusDesc(PlanChangeStatusEnum.UNCHANGE.getDesc());
//            } else {
//                ele.setChangeStatusDesc(PlanChangeStatusEnum.getDescForCompare(ele, sendedEmailDTOMap.get(ele.group())));
//            }
        });
        cellLaughingstockTotalDOS.forEach(item->{
            if(null==item.getChangeContrastList()){
                item.setChangeContrastList(new ArrayList<>());
            }
        });
        return Results.createSuccessRes(cellLaughingstockTotalDOS);
    }

    @PostMapping("/remark/list")
    @ApiOperation(value = "入库计划备注列表", notes = "入库计划备注列表")
    public ResponseEntity<Results<List<CellInStockPlanRemarkGroupDTO>>> queryRemarkList(@RequestBody CellInstockPlanTotalQuery query) {
        query = convert.toCellInstockPlanTotalQuery(query);
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        List<CellInStockPlanRemarkGroupDTO> cellInStockPlanAllRemarkDTOList = cellInstockPlanTotalService.queryRemarkList(query);
        return Results.createSuccessRes(cellInStockPlanAllRemarkDTOList);
    }

    @PostMapping("/remark/save")
    @ApiOperation(value = "入库计划备注新增/更新", notes = "入库计划备注新增/更新")
    public ResponseEntity<Results<Object>> saveOrUpdateRemark(@RequestBody List<CellInStockPlanRemarkGroupDTO> dtoList) {
        cellInstockPlanTotalService.saveOrUpdateRemark(dtoList);
        return Results.createSuccessRes();
    }



    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellInstockPlanTotalDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellInstockPlanTotalService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellInstockPlanTotalDTO>> save(@Valid @RequestBody CellInstockPlanTotalSaveDTO saveDTO) {
        return Results.createSuccessRes(cellInstockPlanTotalService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellInstockPlanTotalService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellInstockPlanTotalQuery query, HttpServletResponse response) {
        query=   convert.toCellInstockPlanTotalQuery(query);// lov->value转name
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellInstockPlanTotalService.export(query, response);
    }
    /**
     * 入库确认
     * @param query
     * @return
     */
    @PostMapping("/confirm")
    @ApiOperation(value = "入库确认",notes = "入库确认")
    public  ResponseEntity<Results<Object>>  confirm(@RequestBody CellInstockPlanTotalQuery query) {
        setCellInstockPlanTotalQueryNull(query);
        query=  convert.toCellInstockPlanTotalQuery(query);// lov->value转name
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellInstockPlanTotalService.confirm(query);
        return Results.createSuccessRes();
    }
    /**
     * 邮件发送
     * @param query
     * @return
     */
    @PostMapping("/email")
    @ApiOperation(value = "邮件发送",notes = "发送邮件")
    public  ResponseEntity<Results<Object>>  email(@RequestBody CellInstockPlanTotalQuery query) {
        setCellInstockPlanTotalQueryNull(query);
        query=   convert.toCellInstockPlanTotalQuery(query);// lov->value转name
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellInstockPlanTotalService.email(query);
        return Results.createSuccessRes();
    }
    private void setCellInstockPlanTotalQueryNull(CellInstockPlanTotalQuery query){
        if (query!=null){
            query.setCellsType(null);
            query.setBasePlace(null);
            query.setWorkshop(null);
            query.setCellsTypeId(null);
            query.setBasePlaceId(null);
            query.setWorkshopId(null);
        }

    }

    @PostMapping("/queryTwoMonthAllItemCodes")
    @ApiOperation(value = "获取当前月及次月已排产的电池计划中涉及的电池料号")
    public ResponseEntity<Results<List<String>>> queryTwoMonthAllItemCodes() {
        return Results.createSuccessRes(cellInstockPlanTotalService.queryTwoMonthAllItemCodes());
    }
}
