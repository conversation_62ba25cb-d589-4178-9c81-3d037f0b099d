package com.trinasolar.scp.baps.api.controller;
import com.trinasolar.scp.baps.domain.dto.CalcDemandDto;
import com.trinasolar.scp.baps.domain.dto.CalculateDemandParams;
import com.trinasolar.scp.baps.domain.dto.aps.CellBooksDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.service.CellInstockPlanNoFinishService;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 实际入库表(待转化) 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
@RestController
@RequestMapping("/cell-instock-plan-no-finish")
@RequiredArgsConstructor
@Api(value = "cell-instock-plan-no-finish", tags = "实际入库表操作(待转化)")
public class CellInstockPlanNoFinishController {
    private final CellInstockPlanNoFinishService cellInstockPlanNoFinishService;
    @PostMapping("/make-instock-plan-finish")
    @ApiOperation(value = "把待转化的实际入库数据-》生成实际入库数据")
    public ResponseEntity<Results<Object>> makeInstockPlanFinish ( ) {
        cellInstockPlanNoFinishService.makeInstockPlanFinish();
        return Results.createSuccessRes();
    }
    @PostMapping("/make-instock-plan-finish-from-cellbooks")
    @ApiOperation(value = "把待转化的实际入库数据-》生成实际入库数据")
    public ResponseEntity<Results<Object>> makeInstockPlanFinishByCellBooks (@RequestBody List<CellBooksDTO> cellBooksDTOS) {
        cellInstockPlanNoFinishService.makeInstockPlanFinish(cellBooksDTOS);
        return Results.createSuccessRes();
    }
}
