package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.dto.CellPlanHTraceDTO;
import com.trinasolar.scp.baps.domain.dto.HTraceLineDTO;
import com.trinasolar.scp.baps.domain.dto.HTraceResponse;
import com.trinasolar.scp.baps.domain.dto.HTraceResult;
import com.trinasolar.scp.baps.domain.query.CellPlanHTraceQuery;
import com.trinasolar.scp.baps.service.service.CellPlanHTraceService;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池投产计划H兼容 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-05 09:59:28
 */
@RestController
@RequestMapping("/cell-plan-h-trace")
@RequiredArgsConstructor
@Api(value = "cell-plan-h-trace", tags = "电池投产计划H兼容操作")
public class CellPlanHTraceController {
    private final CellPlanHTraceService cellPlanHTraceService;

    /**
     * 查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "投产计划查询", notes = "查询")
    public ResponseEntity<Results<List<CellPlanHTraceDTO>>> list(@RequestBody CellPlanHTraceQuery query) {
        return Results.createSuccessRes(cellPlanHTraceService.list(query));
    }

    /**
     * 计算
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/compute")
    @ApiOperation(value = "计算", notes = "计算")
    public ResponseEntity<Results<HTraceResponse>> compute(@RequestBody CellPlanHTraceQuery query) {
        return Results.createSuccessRes(cellPlanHTraceService.compute(query));
    }


    @PostMapping(value = "/confirmData")
    @ApiOperation(value = "确认数据", notes = "确认数据")
    public ResponseEntity<Results<Object>> confirmData(@RequestBody List<HTraceLineDTO> hTraceLineList) {
        cellPlanHTraceService.confirmData(hTraceLineList);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出", notes = "导出")
    public void export(@RequestBody CellPlanHTraceQuery query, HttpServletResponse response) {
        cellPlanHTraceService.export(query, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入H兼容")
    public ResponseEntity<Results<List<HTraceLineDTO>>> importData(@RequestPart("file") MultipartFile multipartFile) {
        return Results.createSuccessRes(cellPlanHTraceService.importData(multipartFile));
    }
}
