package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellShipmentPlanDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellPlanShippableDTO;
import com.trinasolar.scp.baps.domain.dto.aps.CellPlanShippableSaveListDTO;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellShipmentPlanQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellShipmentPlanSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellShipmentPlan;
import com.trinasolar.scp.baps.domain.dto.CellShipmentPlanDTO;
import com.trinasolar.scp.baps.service.service.CellShipmentPlanService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 可发货计划表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-28 11:52:39
 */
@RestController
@RequestMapping("/cell-shipment-plan")
@RequiredArgsConstructor
@Api(value = "cell-shipment-plan", tags = "可发货计划表操作")
public class CellShipmentPlanController {
    private final CellShipmentPlanService cellShipmentPlanService;
    private final CellShipmentPlanDEConvert convert;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "可发货计划表分页列表", notes = "获得可发货计划表分页列表")
    public ResponseEntity<Results<Page<CellShipmentPlanDTO>>> queryByPage(@RequestBody CellShipmentPlanQuery query) {
        query=  convert.toCellShipmentPlanQueryCNByName(query, MyThreadLocal.get().getLang());
        return Results.createSuccessRes(cellShipmentPlanService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellShipmentPlanDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellShipmentPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellShipmentPlanDTO>> save(@Valid @RequestBody CellShipmentPlanSaveDTO saveDTO) {
        return Results.createSuccessRes(cellShipmentPlanService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellShipmentPlanService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellShipmentPlanQuery query, HttpServletResponse response) {
        query=  convert.toCellShipmentPlanQueryCNByName(query, MyThreadLocal.get().getLang());
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellShipmentPlanService.export(query, response);
    }
    /**
     * 查询final_version 最大的数据推送
     *
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "电池产出计划", notes = "电池产出计划")
    public ResponseEntity<Results<Object>> queryList(@RequestBody CellShipmentPlanQuery query) {
        query=  convert.toCellShipmentPlanQueryCNByName(query, MyThreadLocal.get().getLang());
        cellShipmentPlanService.queryList(query);
        return Results.createSuccessRes();
    }
}
