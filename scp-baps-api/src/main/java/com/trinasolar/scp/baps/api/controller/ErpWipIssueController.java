package com.trinasolar.scp.baps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.ErpWipIssueQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.ErpWipIssueSaveDTO;
import com.trinasolar.scp.baps.domain.entity.ErpWipIssue;
import com.trinasolar.scp.baps.domain.dto.ErpWipIssueDTO;
import com.trinasolar.scp.baps.service.service.ErpWipIssueService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * Erp实际入库来源表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:23:58
 */
@RestController
@RequestMapping("/erp-wip-issue")
@RequiredArgsConstructor
@Api(value = "erp-wip-issue", tags = "Erp实际入库来源表操作")
public class ErpWipIssueController {
    private final ErpWipIssueService erpWipIssueService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "Erp实际入库来源表分页列表", notes = "获得Erp实际入库来源表分页列表")
    public ResponseEntity<Results<Page<ErpWipIssueDTO>>> queryByPage(@RequestBody ErpWipIssueQuery query) {
        return Results.createSuccessRes(erpWipIssueService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ErpWipIssueDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpWipIssueService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ErpWipIssueDTO>> save(@Valid @RequestBody ErpWipIssueSaveDTO saveDTO) {
        return Results.createSuccessRes(erpWipIssueService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        erpWipIssueService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ErpWipIssueQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            erpWipIssueService.export(query, response);
    }
}
