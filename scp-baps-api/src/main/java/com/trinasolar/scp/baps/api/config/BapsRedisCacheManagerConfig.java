package com.trinasolar.scp.baps.api.config;

import com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;

import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/1
 */
@Configuration
public class BapsRedisCacheManagerConfig {
    /**
     * 自定义redisTemplate
     */
    @Bean("bapsRedisTemplate")
    @Qualifier
    public RedisTemplate<Object, Object> bapsRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        // 转换 格式
        GenericFastJsonRedisSerializer fastJsonRedisSerializer = new GenericFastJsonRedisSerializer();
        template.setKeySerializer(fastJsonRedisSerializer);
        template.setValueSerializer(fastJsonRedisSerializer);
        template.setHashKeySerializer(fastJsonRedisSerializer);
        template.setHashValueSerializer(fastJsonRedisSerializer);
        return template;
    }

    /**
     * 基于SpringBoot2 对 RedisCacheManager 的自定义配置
     */
    @Bean("bapsRedisCacheManager")
    @Qualifier
    public RedisCacheManager redisCacheManager(@Autowired @Qualifier("bapsRedisTemplate") RedisTemplate<Object, Object> redisTemplate) {
        RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration
                .defaultCacheConfig()
                // 设置key为String
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(redisTemplate.getStringSerializer()))
                // 设置value 为自动转Json的Object
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(redisTemplate.getValueSerializer()))
                // 不缓存null
                .disableCachingNullValues()
                // 缓存数据保存300s
                .entryTtl(Duration.ofSeconds(30));
        // 构造一个redis缓存管理器
        RedisCacheManager redisCacheManager = RedisCacheManager
                .RedisCacheManagerBuilder
                // Redis 连接工厂
                .fromConnectionFactory(Objects.requireNonNull(redisTemplate.getConnectionFactory()))
                // 设置默认缓存配置
                .cacheDefaults(redisCacheConfiguration)
//                // 设置自定义缓存配置，缓存名为cache_user，它的过期时间为60s
//                .withCacheConfiguration("cache_user", redisCacheConfiguration.entryTtl(Duration.ofSeconds(60)))
//                // 上面默然缓存时间是1小时，但是可以根据cacheName来设置缓存时间
//                .withCacheConfiguration("cache_post", redisCacheConfiguration.entryTtl(Duration.ofSeconds(120)))
                // 配置同步修改或删除 put/evict
                .transactionAware()
                .build();
        return redisCacheManager;
    }
}
