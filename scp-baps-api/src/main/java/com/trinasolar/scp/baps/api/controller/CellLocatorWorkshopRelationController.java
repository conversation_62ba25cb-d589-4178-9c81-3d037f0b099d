package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellLocatorWorkshopRelationQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellLocatorWorkshopRelationSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellLocatorWorkshopRelation;
import com.trinasolar.scp.baps.domain.dto.CellLocatorWorkshopRelationDTO;
import com.trinasolar.scp.baps.service.service.CellLocatorWorkshopRelationService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 货位对应车间关系表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 02:57:01
 */
@RestController
@RequestMapping("/cell-locator-workshop-relation")
@RequiredArgsConstructor
@Api(value = "cell-locator-workshop-relation", tags = "货位对应车间关系表操作")
public class CellLocatorWorkshopRelationController {
    private final CellLocatorWorkshopRelationService cellLocatorWorkshopRelationService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "货位对应车间关系表分页列表", notes = "获得货位对应车间关系表分页列表")
    public ResponseEntity<Results<Page<CellLocatorWorkshopRelationDTO>>> queryByPage(@RequestBody CellLocatorWorkshopRelationQuery query) {
        return Results.createSuccessRes(cellLocatorWorkshopRelationService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellLocatorWorkshopRelationDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellLocatorWorkshopRelationService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellLocatorWorkshopRelationDTO>> save(@Valid @RequestBody CellLocatorWorkshopRelationSaveDTO saveDTO) {
        return Results.createSuccessRes(cellLocatorWorkshopRelationService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellLocatorWorkshopRelationService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellLocatorWorkshopRelationQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        query.setLocator(null);
        query.setWorkshop(null);
            cellLocatorWorkshopRelationService.export(query, response);
    }
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile, @RequestPart(value = "excelPara") ExcelPara excelPara) {
        cellLocatorWorkshopRelationService.importData(multipartFile,excelPara);
        return Results.createSuccessRes();
    }
}
