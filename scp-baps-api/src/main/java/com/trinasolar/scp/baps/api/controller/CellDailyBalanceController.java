package com.trinasolar.scp.baps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.baps.domain.query.CellDailyBalanceQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellDailyBalanceSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellDailyBalance;
import com.trinasolar.scp.baps.domain.dto.CellDailyBalanceDTO;
import com.trinasolar.scp.baps.service.service.CellDailyBalanceService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 每日结存报表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-12 06:19:24
 */
@RestController
@RequestMapping("/cell-daily-balance")
@RequiredArgsConstructor
@Api(value = "cell-daily-balance", tags = "每日结存报表操作")
public class CellDailyBalanceController {
    private final CellDailyBalanceService cellDailyBalanceService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "查询", notes = "查询")
    public ResponseEntity<Results<Page<CellDailyBalanceDTO>>> queryByPage(@RequestBody CellDailyBalanceQuery query) {
        return Results.createSuccessRes(cellDailyBalanceService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
   // @PostMapping("/detail")
  //  @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellDailyBalanceDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellDailyBalanceService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
  //  @PostMapping("/save")
   // @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellDailyBalanceDTO>> save(@Valid @RequestBody CellDailyBalanceSaveDTO saveDTO) {
        return Results.createSuccessRes(cellDailyBalanceService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
  //  @PostMapping("/delete")
   // @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellDailyBalanceService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellDailyBalanceQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellDailyBalanceService.export(query, response);
    }
}
