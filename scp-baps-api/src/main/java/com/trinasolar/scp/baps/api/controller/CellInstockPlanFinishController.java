package com.trinasolar.scp.baps.api.controller;

import com.trinasolar.scp.baps.domain.convert.CellInstockPlanTotalDEConvert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlan;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.service.CalculateDemandService;
import com.trinasolar.scp.baps.service.service.CellInstockPlanVersionService;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanFinishSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanFinish;
import com.trinasolar.scp.baps.service.service.CellInstockPlanFinishService;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 实际入库表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
@RestController
@RequestMapping("/cell-instock-plan-finish")
@RequiredArgsConstructor
@Api(value = "cell-instock-plan-finish", tags = "实际入库表操作")
public class CellInstockPlanFinishController {
    private final CellInstockPlanFinishService cellInstockPlanFinishService;
    private final CellInstockPlanVersionService cellInstockPlanVersionService;
    private final CellInstockPlanTotalDEConvert cellInstockPlanTotalDEConvert;
    private final CalculateDemandService calculateDemandService;
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
   // @PostMapping("/page")
    @ApiOperation(value = "实际入库表分页列表", notes = "获得实际入库表分页列表")
    public ResponseEntity<Results<Page<CellInstockPlanFinishDTO>>> queryByPage(@RequestBody CellInstockPlanFinishQuery query) {
        return Results.createSuccessRes(cellInstockPlanFinishService.queryByPage(query));
    }
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/query")
    @ApiOperation(value = "实际入库排产分页列表", notes = "实际入库排产分页列表")
    public ResponseEntity<Results<Page<CellInstockPlanFinishTotalDTO>>> query (@RequestBody CellInstockPlanFinishTotalQuery query) {
        query=   cellInstockPlanTotalDEConvert.toCellInstockPlanFinishTotalQuery(query);
        Page<CellInstockPlanFinishTotalDTO> page =  cellInstockPlanFinishService.query(query);
        return Results.createSuccessRes(page);
    }
    /**
     * 页列表
     *
     * @return 查询结果
     */
    @PostMapping("/no-locator-code")
    @ApiOperation(value = "获取没有维护的货位信息", notes = "获取没有维护的货位信息")
    public ResponseEntity<Results<List<String>>> findLocatorCode() {
        List<String> locatorCodes =  cellInstockPlanFinishService.findLocatorCode();
        return Results.createSuccessRes(locatorCodes);
    }
    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellInstockPlanFinishDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellInstockPlanFinishService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellInstockPlanFinishDTO>> save(@Valid @RequestBody CellInstockPlanFinishSaveDTO saveDTO) {
        return Results.createSuccessRes(cellInstockPlanFinishService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellInstockPlanFinishService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }


    @PostMapping("/synData")
    @ApiOperation(value = "同步数据")
    public ResponseEntity<Results<Object>> synDataFromErpWipIssue () {

        LocalDate startDate = LocalDate.of(2025, 7, 1);
        LocalDate endDate = LocalDate.of(2025, 7, 20);
        cellInstockPlanFinishService.synDataFromErpWipIssue(startDate,endDate);
        return Results.createSuccessRes();
    }
    @PostMapping("/calcDemand")
    @ApiOperation(value = "实际入库覆盖，计算剩余需求")
    public ResponseEntity<Results<Object>> calcDemand (@RequestBody @Valid CalcDemandDto dto) {
        CalculateDemandParams params = new CalculateDemandParams();
        params.setMonth(dto.getMonth());
        params.setActualCoverageDate(dto.getCoverDate());
        params.setPlanLockDate(dto.getLockDate());
        params.setPlanVersion(dto.getVersion());
        params.setIsOverseaId(LovUtils.get(LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()).getLovLineId());
        calculateDemandService.calculateDemand(params);
        return Results.createSuccessRes();
    }

    @PostMapping("/versions")
    @ApiOperation(value = "获取入库计划国内海外指定月版本")
    public ResponseEntity<Results<List<String>>> getPlanVersions (@RequestBody InstockPlanVersionQuery query) {
      List<String> versions =   cellInstockPlanVersionService.getAllVersions(query);
        return Results.createSuccessRes(versions);
    }
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellInstockPlanFinishTotalQuery query, HttpServletResponse response) {
        query=   cellInstockPlanTotalDEConvert.toCellInstockPlanFinishTotalQuery(query);
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellInstockPlanFinishService.export(query, response);
    }
}
