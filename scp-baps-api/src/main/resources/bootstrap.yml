entity:
  jpa:
    package: com.trinasolar.scp.baps
  scan:
    package: com.trinasolar.scp.baps
swagger:
  base-package: com.trinasolar.scp.baps.api.controller
spring:
  cloud:
    inetutils:
      preferred-networks: 10\.50\..+
    nacos:
      username: ${nacos.username}
      password: ${nacos.password}
      config:
        server-addr: ${nacos.server.addr}
        namespace: ${nacos.namespace}
        file-extension: properties
      discovery:
        server-addr: ${nacos.server.addr}
        namespace: ${nacos.namespace}
  main:
    allow-bean-definition-overriding: true
  application:
    name: scp-battery-aps-api
    version: 1.0.0
  aop:
    proxy-target-class: true
  redis:
    key:
      prefix: scp-baps-
  thymeleaf:
    cache: false
  security:
    endpoints: info
    user:
      name: ${USER_NAME:admin}
      password: ${PASSWORD:1Ydz2ABk5GRzhSnn}
  session:
    store-type: redis
  datasource:
    druid:
      enabled: true
      filter:
        wall:
          enabled: true
        stat:
          enabled: true
    driver-class-name: ${DRIVER_CLASS:com.mysql.cj.jdbc.Driver}
    url: ${spring.datasource.druid.url:}
  data:
    redis:
      repositories:
        enabled: true
  jpa:
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: ${NAMING_STRATEGY:org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy}
    properties:
      hibernate.generate_statistics: false
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      javax.persistence.sharedCache.mode: ENABLE_SELECTIVE
      hibernate.physical_naming_strategy: ${spring.jpa.hibernate.naming.physical-strategy}
      hibernate.dialect: ${spring.jpa.database-platform}
      hibernate.event.merge.entity_copy_observer: allow
      hibernate:
        jdbc:
          batch_size: 500
        order_inserts: true
        order_updates: true
    database-platform: ${DATA_DIALECT:org.hibernate.dialect.MySQL5Dialect}
  messages:
    basename: messages,i18n/messages,i18n/validation,i18n/export,i18n/import
apollo:
  bootstrap:
    eagerLoad:
      enabled: false
    enabled: false
    namespaces: application
  meta: ${apollo.metaurl:http://devopsconfigdev.trinasolar.com}
app:
  id: ${spring.application.name}
feign:
  converter:
    strip: false
  client:
    config:
      default:
        ConnectTimeOut: 100000
        ReadTimeOut: 100000
logging:
  level:
    root: INFO
eureka:
  client:
    enabled: false
server:
  port: 8080
  compression:
    enabled: false
  servlet:
    context-path: /${spring.application.name}
  undertow:
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true
trinasolar:
  project:
    name: scp
  team: scp