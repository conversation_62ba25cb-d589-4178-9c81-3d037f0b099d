row=行
repeat=重复
the.row.baseplace.not.null=第{0}行生产基地名称不能是空
the.row.cellstype.not.null=第{0}行电池类型名称不能是空
the.row.isoversea.not.null=第{0}行国内海外名称不能是空
the.row.workshop.not.null=第{0}行生产车间名称不能是空
the.row.workunit.not.null=第{0}行生产单元名称不能是空
the.row.baseplace.not.exists=第{0}行{1}生产基地名称不存在
the.row.cellstype.not.exists=第{0}行{1}电池类型名称不存在
the.row.isoversea.not.exists=第{0}行{1}国内海外名称不存在
the.row.workshop.not.exists=第{0}行{1}生产车间名称不存在
the.row.workunit.not.exists=第{0}行{1}生产单元名称不存在或格式不是xxx_xx-xx
the.row.baseplace.not.in.isoversea=第{0}行{1}生产基地不属于{2}
the.row.workshop.not.in.baseplace=第{0}行{1}生产车间不属于{2}
the.row.workunit.not.in.workshop=第{0}行{1}生产单元不属于{2}
the.row.endtime.must.not.before.starttime=第{0}行结束时间不能在开始时间之前
the.row.starttime.must.not.after.endtime=第{0}行开始时间不能大于结束时间
the.row.starttime.must.not.null=第{0}行开始时间不能是空的
the.row.endtime.must.not.null=第{0}行结束时间不能是空的
the.row.time.repeat=相同的{0}存在时间段重合
ie.import.the.row.is.not.empty=第{0}行各属性(电池类型、能否生产单玻、能否生产低碳、能否生产小区域国家、能否生产H兼容、能否生产H追溯、国内海外、基地、车间、生产单元)不能存在空值
ie.import.the.row.must.y.or.n=第{0}行各属性(能否生产低碳、能否生产小区域国家、能否生产H兼容、能否生产H追溯)值要求是Y或N
ie.import.not.unique=存在重复数据，请调整生效时间后重新导入！
grade.capacity.import.the.row.is.not.empty=第{0}行各属性(国内海外、电池类型、能否生产单玻、能否生产低碳、能否生产小区域国家、能否生产H兼容、能否生产H追溯、基地、车间、生产单元、是否可靠性验证)不能存在空值
grade.capacity.import.the.row.must.y.or.n=第{0}行各属性(能否生产低碳、能否生产小区域国家、能否生产H兼容、能否生产H追溯，是否可靠性验证)值必须是Y或N
capacity.discount.import.no.email=没有配置收件人信息
capacity.discount.import.email.title=IE产能打折（人力）更新提醒
cellloss.import.start.month.must.not.null=第{0}行开始月份不能为空
cellloss.import.end.month.must.not.null=第{0}行结束月份不能为空
cellloss.import.end.month.must.not.before.start.month=第{0}行结束月份不能在开始月份之前
cellloss.import.oldproduct.must.not.null=第{0}行电池类型1不能为空
cellloss.import.oldproduct.must.not.exists=第{0}行{1}电池类型名称不存在
cellloss.import.newproduct.must.not.null=第{0}行电池类型2不能为空
cellloss.import.newproduct.must.not.exists=第{0}行{1}电池类型名称不存在
cellloss.import.row.not.null=第{0}行各属性(生产车间、电池类型1、电池类型2)不能存在空值
cellloss.import.row.must.y.or.n=第{0}行各属性(是否单玻1、是否单玻2、是否小区域国家1、是否小区域国家2、是否10.8_1、是否10.8_2)必须是Y、N或空
parallelLoss.import.parallelyype.not.null=第{0}行数据类型不能是空
parallelLoss.import.parallelyype.not.exists=第{0}行{1}数据类型不存在
parallelLoss.import.cellstype.not.null=第{0}行电池类型1不能是空
parallelLoss.import.cellstype.not.exists=第{0}行{1}电池类型1不存在
parallelLoss.import.cellstypetwo.not.null=第{0}行电池类型2不能是空
parallelLoss.import.cellstypetwo.not.exists=第{0}行{1}电池类型2不存在
siliconsplitrule.import.ruletype.not.exists=第{0}行{1}分片规则类型不存在
siliconsplitrule.import.ruletype.not.null=第{0}行分片规则类型不能是空
siliconsplitrule.import.wafergrade.not.null=第{0}行硅片等级不能是空的
siliconsplitrule.import.wafergrade.not.exists=第{0}行{1}硅片等级名称不存在
siliconsplitrule.import.cellfine.format.error=第{0}行{1}电池良率必须是百分数
siliconsplitrule.import.cellfine.not.null=第{0}行电池良率不能是空
siliconsplitrule.import.simfrs.not.null=第{0}行硅料厂家不能是空的
siliconsplitrule.import.simfrs.not.exists=第{0}行{1}硅料厂家名称不存在
cellshippableleadtime.import.buffdays.not.null=第{0}行默认常规类型预留天数不能为空
cellshippableleadtime.import.ratepercent.format.error=第{0}行可发货浮动系数百分比格式不正确
cellshippableleadtime.import.ratepercent.not.null=第{0}行可发货浮动系数百分比不能为空
celltyperule.import.module.not.exists=第{0}行{1}模块不存在
celltyperule.import.field.not.exists=第{0}行{1}解析字段不存在
celltyperule.import.row.not.null=第{0}行各属性(解析顺序(必须数字)、模块、解析字段、规则、结果)不能存在空值
cellplanqtyfluctuationcoefficient.import.row.not.null=第{0}行开始时间，结束时间、生产车间、浮动系数不能为空
cellplanqtyfluctuationcoefficient.import.coefficientPercent.format.error=第{0}行计划数量浮动系数百分比格式错误
returnorder.import.shipperisoversea.not.null=第{0}行发货方国内海外数据录入不能是空的
returnorder.import.shipperisoversea.not.exists=第{0}行发货方国内海外数据录入不正确
returnorder.import.receiverisoversea.not.null=第{0}行接收方国内海外数据录入不能是空的
returnorder.import.receiverisoversea.not.exists=第{0}行接收方国内海外数据录入不正确
returnorder.import.cellstype.not.null=第{0}行电池类型数据录入不能是空的
returnorder.import.cellstype.not.exists=第{0}行{1}电池类型不存在
returnorder.import.shipperbaseplace.not.null=第{0}行发货方生产基地不能是空的
returnorder.import.receiverbaseplace.not.null=第{0}行接收方生产基地不能是空的
returnorder.import.shipperbaseplace.not.exists=第{0}行{1}发货方生产基地不存在
returnorder.import.receiverbaseplace.not.exists=第{0}行{1}接收方生产基地不存在
returnorder.import.receiveraccount.not.null=第{0}行接收方账套不能是空的
returnorder.import.receiveraccount.not.exists=第{0}行{1}账套名称不存在
returnorder.import.cellqty.not.null=第{0}行数量(单位：万片)不能为空
returnorder.import.cellqty.not.number=第{0}行数量(单位：万片)必须是数字
returnorder.import.itemcode.not.null=第{0}行电池料号不能为空
returnorder.import.itemcode.not.exists=第{0}行{1}电池料号不存在
returnorder.import.workcell.not.null=第{0}行效率值不能为空
returnorder.import.workcell.not.number=第{0}行效率值必须是数字
returnorder.import.note.not.null=第{0}行对应标识不能为空
returnorder.import.month.not.null=第{0}行月份不能为空
returnorder.import.date.note.null=第{0}行日期不能为空
manufacturingswitchplan.import.row.not.null=第{0}行各属性(生产车间、电池类型、产能、开始时间、结束时间)不能存在空值
manufacturingswitchplan.import.groupKey.repeat=第{0}行各属性(生产车间、生产单元、生产产线、电池类型、开始时间、结束时间)重复
manufacturingswitchplan.import.isoversea.not.exists=第{0}行{1}国内海外名称不存在
manufacturingswitchplan.import.baseplace.not.exists=第{0}行{1}生产基地名称不存在
manufacturingswitchplan.import.workshop.not.exists=第{0}行{1}生产车间名称不存在
manufacturingswitchplan.import.workunit.not.exists=第{0}行{1}生产单元名称不存在
manufacturingswitchplan.import.cellstype.not.exists=第{0}行{1}电池类型名称不存在