row=row
repeat=repeat
the.row.baseplace.not.null=the {0} row baseplace not empty
the.row.cellstype.not.null=the {0} row battery type name not empty
the.row.isoversea.not.null=the {0} row domestic oversea name not empty
the.row.workshop.not.null=the {0} row workshop name not empty
the.row.workunit.not.null=the {0} row workunit name not empty
the.row.baseplace.not.exists=the {0} row {1} baseplace name not exists
the.row.cellstype.not.exists=the {0} row {1} battery type name not exists
the.row.isoversea.not.exists=the {0} row {1} domestic oversea name not exists
the.row.workshop.not.exists=the {0} row {1} workshop name not exists
the.row.workunit.not.exists=the {0} row {1} workunit name not exists or the format is not xxx_xx-xx
the.row.baseplace.not.in.isoversea=the {0} row {1} production base does not belong to {2}
the.row.workshop.not.in.baseplace=the {0} row {1} production workshop does not belong to {2}
the.row.workunit.not.in.workshop=the {0} row {1} production unit does not belong to {2}
the.row.endtime.must.not.before.starttime=the {0} row end time cannot be before the start time
the.row.starttime.must.not.after.endtime=the {0} row start time cannot be greater than the end time
the.row.starttime.must.not.null=the {0} row  start time cannot be empty
the.row.endtime.must.not.null=the {0} row end time cannot be empty
the.row.time.repeat=the same {0} has overlapping time periods
ie.import.the.row.is.not.empty=the {0} row each attribute (battery type, whether single glass can be produced, whether low-carbon can be produced, whether small region countries can be produced, whether H compatibility can be produced, whether H traceability can be produced, domestic and overseas, base, workshop, production unit) cannot have empty values
ie.import.the.row.must.y.or.n=the {0} row required values for each attribute (whether it can produce low-carbon, whether it can produce small regional countries, whether it can produce H compatibility, and whether it can produce H traceability) are Y or N
ie.import.not.unique=There is duplicate data, please adjust the effective time and re import!
grade.capacity.import.the.row.is.not.empty=the {0} row each attribute (domestic and overseas, battery type, whether single glass can be produced, whether low-carbon can be produced, whether small region countries can be produced, whether H compatibility can be produced, whether H traceability can be produced, base, workshop, production unit, and reliability verification) must not have empty values
grade.capacity.import.the.row.must.y.or.n=the {0} row each attribute (whether it can produce low-carbon, whether it can produce small regional countries, whether it can produce H compatibility, whether it can produce H traceability, and whether it can verify reliability) must be Y or N
capacity.discount.import.no.email=no recipient information configured
capacity.discount.import.email.title=IE capacity discount  update reminder
cellloss.import.start.month.must.not.null=the {0} row starting month of a row cannot be empty
cellloss.import.end.month.must.not.null=the {0} row end month cannot be empty
cellloss.import.end.month.must.not.before.start.month=the {0} row  end month cannot be before the start month
cellloss.import.oldproduct.must.not.null=the {0} row battery type 1 cannot be empty
cellloss.import.oldproduct.must.not.exists=the {0} row {1}  battery type name does not exist
cellloss.import.newproduct.must.not.null=the {0} row battery type 2 cannot be empty
cellloss.import.newproduct.must.not.exists=the {0} row {1}  battery type name does not exist
cellloss.import.row.not.null=the {0} row each attribute (production workshop, battery type 1, battery type 2) cannot have empty values
cellloss.import.row.must.y.or.n=the {0} row each attribute (single glass 1, single glass 2, small domain country 1, small domain country 2, 10.8_1, 10.8_2) must be Y, N, or empty
parallelLoss.import.parallelyype.not.null=the {0} row data type cannot be empty
parallelLoss.import.parallelyype.not.exists=the {0} row {1} data type does not exist
parallelLoss.import.cellstype.not.null=the {0} row battery type 1 cannot be empty
parallelLoss.import.cellstype.not.exists=the {0} row {1} battery type 1 does not exist
parallelLoss.import.cellstypetwo.not.null=the {0} row battery type 2 cannot be empty
parallelLoss.import.cellstypetwo.not.exists=the {0} row {1} battery type 2 does not exist
siliconsplitrule.import.ruletype.not.exists= the {0} row {1} rule type does not exist
siliconsplitrule.import.ruletype.not.null= the {0} row rule type cannot be empty
siliconsplitrule.import.wafergrade.not.null= the {0} row silicon wafer level cannot be empty
siliconsplitrule.import.wafergrade.not.exists= the {0} row {1} silicon wafer grade name does not exist
siliconsplitrule.import.cellfine.format.error= the {0} row {1} battery yield must be a percentage
siliconsplitrule.import.cellfine.not.null= the {0} row  battery yield cannot be empty
siliconsplitrule.import.simfrs.not.null= the {0} row silicon material manufacturers cannot be empty
siliconsplitrule.import.simfrs.not.exists= the {0} row {1} name of the silicon material manufacturer does not exist
cellshippableleadtime.import.buffdays.not.null=the {0} row default reserved days for regular types in rows cannot be empty
cellshippableleadtime.import.ratepercent.format.error=the {0} row format of the floating coefficient percentage for row shipments is incorrect
cellshippableleadtime.import.ratepercent.not.null=the {0} row  floating coefficient percentage that can be shipped cannot be empty
celltyperule.import.module.not.exists=the {0} row {1} module does not exist
celltyperule.import.field.not.exists=the {0} row {1}  parsing field does not exist
celltyperule.import.row.not.null=the {0} row each attribute (parsing order (must be a number), module, parsing field, rule, result) cannot have empty values
cellplanqtyfluctuationcoefficient.import.row.not.null=the {0} row start time, end time, production workshop, and floating coefficient cannot be empty
cellplanqtyfluctuationcoefficient.import.coefficientPercent.format.error=the {0} row  format error in percentage of planned quantity fluctuation factor
returnorder.import.shipperisoversea.not.null=the {0} row domestic and overseas data input of the shipper cannot be empty
returnorder.import.shipperisoversea.not.exists=the {0} row incorrect entry of domestic and overseas data for the shipper
returnorder.import.receiverisoversea.not.null=the {0} row   recipient's domestic and overseas data entry cannot be empty
returnorder.import.receiverisoversea.not.exists=the {0} row   recipient's domestic and overseas data entry is incorrect
returnorder.import.cellstype.not.null=the {0} row battery type data input cannot be empty
returnorder.import.cellstype.not.exists=the {0} row {1} battery type does not exist
returnorder.import.shipperbaseplace.not.null=the {0} row production base of the shipper cannot be empty
returnorder.import.receiverbaseplace.not.null=the {0} row recipient's production base cannot be empty
returnorder.import.shipperbaseplace.not.exists=the {0} row {1} production base of the shipper does not exist
returnorder.import.receiverbaseplace.not.exists=the {0} row {1} recipient's production base does not exist
returnorder.import.receiveraccount.not.null=the {0} row recipient's account set cannot be empty
returnorder.import.receiveraccount.not.exists=the {0} row {1} account set name does not exist
returnorder.import.cellqty.not.null=the {0} row quantity (unit: 10000 pieces) cannot be empty
returnorder.import.cellqty.not.number=the {0} row quantity (unit: 10000 pieces) must be a number
returnorder.import.itemcode.not.null=the {0} row battery part number cannot be empty
returnorder.import.itemcode.not.exists=the {0} row {1} battery part number does not exist
returnorder.import.workcell.not.null=the {0} row efficiency value cannot be empty
returnorder.import.workcell.not.number=the {0} row efficiency value must be a number
returnorder.import.note.not.null=the {0} row  corresponding identifier cannot be empty
returnorder.import.month.not.null=the {0} row month cannot be empty
returnorder.import.date.note.null=the {0} row date cannot be empty
manufacturingswitchplan.import.row.not.null=the {0} row each attribute (workshop, cellsType, capacityQuantity, startTime, endTime) cannot have empty values
manufacturingswitchplan.import.groupKey.repeat=the {0} row each attribute (workshop, workunit, productionLine, cellsType, startTime, endTime) repeat
manufacturingswitchplan.import.isoversea.not.exists=the {0} row {1} isOversea does not exist
manufacturingswitchplan.import.baseplace.not.exists=the {0} row {1} basePlace does not exist
manufacturingswitchplan.import.workshop.not.exists=the {0} row {1} workshop does not exist
manufacturingswitchplan.import.workunit.not.exists=the {0} row {1} workunit does not exist
manufacturingswitchplan.import.cellstype.not.exists=the {0} row {1} cellsType does not exist