alter table baps_cell_return_order
    add shipper_is_oversea_id bigint null comment '发货方国内海外id' after id;
alter table baps_cell_return_order
    add shipper_is_oversea varchar(50) null comment '发货方国内海外' after shipper_is_oversea_id;
    
alter table baps_cell_return_order
    add shipper_base_place_id bigint null comment '发货方生产基地id' after shipper_is_oversea;
alter table baps_cell_return_order
    add shipper_base_place varchar(50) null comment '发货方生产基地' after shipper_base_place_id;

alter table baps_cell_return_order
    add receiver_is_oversea_id bigint null comment '接收方国内海外id' after shipper_base_place;
alter table baps_cell_return_order
    add receiver_is_oversea varchar(50) null comment '接收方国内海外' after receiver_is_oversea_id;
    
alter table baps_cell_return_order
    add receiver_base_place_id bigint null comment '接收方生产基地id' after receiver_is_oversea;
alter table baps_cell_return_order
    add receiver_base_place varchar(50) null comment '接收方生产基地' after receiver_base_place_id;
alter table baps_cell_return_order
    add receiver_account varchar(50) null comment '接收方账套' after receiver_base_place;

ALTER TABLE baps_cell_return_order DROP COLUMN is_oversea;
ALTER TABLE baps_cell_return_order DROP COLUMN is_oversea_id;
ALTER TABLE baps_cell_return_order DROP COLUMN base_place_id;
ALTER TABLE baps_cell_return_order DROP COLUMN base_place;
ALTER TABLE baps_cell_return_order DROP COLUMN account;



