CREATE TABLE `baps_cell_type_rule` (
                                       `id` bigint NOT NULL COMMENT 'ID主键',
                                       `sort_no` int DEFAULT NULL COMMENT '解析顺序',
                                       `module` varchar(50) DEFAULT NULL COMMENT '模块',
                                       `illustrate` varchar(50) DEFAULT NULL COMMENT '说明',
                                       `field` varchar(50) DEFAULT NULL COMMENT '解析字段',
                                       `rule` varchar(50) DEFAULT NULL COMMENT '规则',
                                       `result` varchar(50) DEFAULT NULL COMMENT '结果',
                                       `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                       `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                       `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                       `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '-1' COMMENT '创建人',
                                       `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '-1' COMMENT '更新人',
                                       `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='电池类型转化规则';