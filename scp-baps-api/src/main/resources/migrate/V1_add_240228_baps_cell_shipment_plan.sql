CREATE TABLE `baps_cell_shipment_plan` (
  `id` bigint NOT NULL COMMENT 'ID主键',
  `instock_plan_id` bigint DEFAULT NULL COMMENT '入库计划id',
  `order_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '订单表',
  `source_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '来源类型',
  `demand_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '需求版本号',
  `is_oversea_id` bigint DEFAULT NULL COMMENT '国内海外Id',
  `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '国内海外',
  `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产基地',
  `base_place_id` bigint DEFAULT NULL COMMENT '生产基地Id',
  `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产车间',
  `workshop_id` bigint DEFAULT NULL COMMENT '生产车间Id',
  `workunit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产单元',
  `workunit_id` bigint DEFAULT NULL COMMENT '生产单元Id',
  `line_name` varchar(255) DEFAULT NULL COMMENT '生产线体',
  `number_line` decimal(24,6) DEFAULT NULL COMMENT '产线数量',
  `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池类型',
  `cells_type_id` bigint DEFAULT NULL COMMENT '电池类型Id',
  `h_trace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'H追溯',
  `aesthetics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '美学',
  `is_transparent_double_glass` bit(1) DEFAULT b'0' COMMENT '是否进行了透明双玻拆分',
  `transparent_double_glass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '透明双玻',
  `cell_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '片源种类',
  `production_grade` varchar(255) DEFAULT NULL COMMENT '产品等级',
  `regional_country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '小区域国家',
  `item_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '5A料号',
  `demand_base_place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '需求地',
  `is_special_requirement` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否电池特殊要求',
  `low_resistance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '低阻',
  `cell_mfrs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池厂家',
  `silver_pulp_mfrs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银浆厂家',
  `demand_qty` decimal(24,6) DEFAULT NULL COMMENT '需求数量',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入库排产月份',
  `cell_mv` decimal(24,6) DEFAULT NULL COMMENT 'MV',
  `final_version` varchar(255) DEFAULT NULL COMMENT '最終邮件确认发布的版本',
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `old_qty_pc` decimal(24,6) DEFAULT NULL COMMENT '数量（片）拆分前的值',
  `qty_pc` decimal(24,6) DEFAULT NULL COMMENT '数量（片）',
  `demand_summary_lines_id` bigint DEFAULT NULL COMMENT '汇总明细行id',
  `si_mfrs` varchar(255) DEFAULT NULL COMMENT '硅料厂家',
  `is_si_mfrs` bit(1) DEFAULT b'0' COMMENT '是否进行了硅料厂家拆分',
  `silicon_material_manufacturer` varchar(255) DEFAULT NULL COMMENT '硅片厂家',
  `screen_plate_mfrs` varchar(255) DEFAULT NULL COMMENT '网版厂家',
  `start_efficiency` decimal(24,6) DEFAULT NULL COMMENT '起始效率',
  `max_efficiency` decimal(24,6) DEFAULT NULL COMMENT '最大分布效率',
  `special_order_no` varchar(255) DEFAULT NULL COMMENT '电池特殊单号',
  `demand_date` date DEFAULT NULL COMMENT '需求日期',
  `is_wafer_grade` bit(1) DEFAULT b'0' COMMENT '是否已经进行硅片等级拆分',
  `wafer_grade` varchar(255) DEFAULT NULL COMMENT '硅片等级',
  `is_a_split` bit(1) DEFAULT b'0' COMMENT '是否进行了A-拆分',
  `process_category_priority` int DEFAULT NULL COMMENT '加工类型优先级',
  `process_category` varchar(255) DEFAULT NULL COMMENT '加工类型',
  `is_process_category` bit(1) DEFAULT b'0' COMMENT '是否已经进行了加工类型拆分',
  `is_hand_process_category` bit(1) DEFAULT b'0' COMMENT '是否进行手动加工类型指定',
  `gap` decimal(24,6) DEFAULT NULL,
  `demand_remark` varchar(255) DEFAULT NULL COMMENT '需求说明',
  `grade_rule` varchar(255) DEFAULT NULL COMMENT '分档规则',
  `verification_mark` varchar(50) DEFAULT NULL COMMENT '验证标识',
  `demand_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '需求来源',
  `battery_material_code` varchar(100) DEFAULT NULL COMMENT '电池物料编码',
  `old_start_time` datetime DEFAULT NULL COMMENT '原来投产开始时间',
  `old_end_time` datetime DEFAULT NULL COMMENT '原来投产结束时间',
  `scheduling_from_id` bigint DEFAULT NULL COMMENT '排产来源Id',
  `plan_line_from_id` bigint DEFAULT NULL COMMENT '投产来源Id',
  `bbom_id` bigint DEFAULT NULL COMMENT 'bbom中的Id',
  `parent_id` bigint DEFAULT NULL COMMENT '拆前父Id',
  `confirm_plan` int DEFAULT '0' COMMENT '计划确认',
  `old_month` varchar(10) DEFAULT NULL COMMENT '投产月份',
  `out_bound_date` datetime DEFAULT NULL COMMENT '发货(出库)日期',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
  `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `baps_cell_plan_line_pk` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入库计划表';