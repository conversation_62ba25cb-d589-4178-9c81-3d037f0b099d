DROP TABLE IF EXISTS baps_cell_wip;
CREATE TABLE baps_cell_wip(
    id BIGINT NOT NULL   COMMENT 'ID主键' ,
    wip_code VARCHAR(150)    COMMENT '工单号' ,
    class VARCHAR(150)    COMMENT '分类' ,
    item_code VARCHAR(150)    COMMENT '装配件' ,
    state VARCHAR(50)    COMMENT '状态' ,
    workshop VARCHAR(150)    COMMENT '车间' ,
    start_time DATETIME    COMMENT '起始时间' ,
    end_time DATETIME    COMMENT '结束时间' ,
    project_code VARCHAR(150)    COMMENT '项目编码' ,
    alternate_routing_designator VARCHAR(150)    COMMENT '工艺路线替代项' ,
    alternate_bom_designator VARCHAR(255)    COMMENT 'bom替代项' ,
    organization_id VARCHAR(255)    COMMENT '组织id' ,
    is_h VARCHAR(255)    COMMENT '是否哈密瓜' ,
    qty DECIMAL(24,6)    COMMENT '数量' ,
    bonded_form VARCHAR(255)    COMMENT '保税形态' ,
    outsourcing_factory VARCHAR(255)    COMMENT '外协厂家' ,
    is_advance_production VARCHAR(255)    COMMENT '是否提前投产' ,
    is_sell VARCHAR(255)    COMMENT '是否外售' ,
    reliability VARCHAR(255)    COMMENT '可靠性标识' ,
    verification_date DATETIME    COMMENT '验证日期' ,
    tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
    opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
    is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
    created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
    created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
    updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '工单生成预览';

