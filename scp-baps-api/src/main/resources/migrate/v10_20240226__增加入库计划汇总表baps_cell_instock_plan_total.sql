CREATE TABLE `baps_cell_instock_plan_total` (
                                                `id` bigint NOT NULL COMMENT 'ID主键',
                                                `is_oversea_id` bigint DEFAULT NULL COMMENT '国内海外Id',
                                                `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '国内海外',
                                                `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产基地',
                                                `base_place_id` bigint DEFAULT NULL COMMENT '生产基地Id',
                                                `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产车间',
                                                `workshop_id` bigint DEFAULT NULL COMMENT '生产车间Id',
                                                `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电池类型',
                                                `cells_type_id` bigint DEFAULT NULL COMMENT '电池类型Id',
                                                `h_trace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'H追溯',
                                                `aesthetics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '美学',
                                                `transparent_double_glass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '透明双玻',
                                                `production_grade` varchar(255) DEFAULT NULL COMMENT '产品等级',
                                                `cell_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '片源种类',
                                                `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '月份',
                                                `qty_thousand_pc` decimal(24,6) DEFAULT NULL COMMENT '万片',
                                                `cell_mv` decimal(24,6) DEFAULT NULL COMMENT 'MV',
                                                `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本',
                                                `from_version` varchar(255) DEFAULT NULL COMMENT '投产表数据版本',
                                                `item_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '5A料号',
                                                `d1` decimal(24,6) DEFAULT NULL,
                                                `d2` decimal(24,6) DEFAULT NULL,
                                                `d3` decimal(24,6) DEFAULT NULL,
                                                `d4` decimal(24,6) DEFAULT NULL,
                                                `d5` decimal(24,6) DEFAULT NULL,
                                                `d6` decimal(24,6) DEFAULT NULL,
                                                `d7` decimal(24,6) DEFAULT NULL,
                                                `d8` decimal(24,6) DEFAULT NULL,
                                                `d9` decimal(24,6) DEFAULT NULL,
                                                `d10` decimal(24,6) DEFAULT NULL,
                                                `d11` decimal(24,6) DEFAULT NULL,
                                                `d12` decimal(24,6) DEFAULT NULL,
                                                `d13` decimal(24,6) DEFAULT NULL,
                                                `d14` decimal(24,6) DEFAULT NULL,
                                                `d15` decimal(24,6) DEFAULT NULL,
                                                `d16` decimal(24,6) DEFAULT NULL,
                                                `d17` decimal(24,6) DEFAULT NULL,
                                                `d18` decimal(24,6) DEFAULT NULL,
                                                `d19` decimal(24,6) DEFAULT NULL,
                                                `d20` decimal(24,6) DEFAULT NULL,
                                                `d21` decimal(24,6) DEFAULT NULL,
                                                `d22` decimal(24,6) DEFAULT NULL,
                                                `d23` decimal(24,6) DEFAULT NULL,
                                                `d24` decimal(24,6) DEFAULT NULL,
                                                `d25` decimal(24,6) DEFAULT NULL,
                                                `d26` decimal(24,6) DEFAULT NULL,
                                                `d27` decimal(24,6) DEFAULT NULL,
                                                `d28` decimal(24,6) DEFAULT NULL,
                                                `d29` decimal(24,6) DEFAULT NULL,
                                                `d30` decimal(24,6) DEFAULT NULL,
                                                `d31` decimal(24,6) DEFAULT NULL,
                                                `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                                `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                                `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入库计划汇总表';