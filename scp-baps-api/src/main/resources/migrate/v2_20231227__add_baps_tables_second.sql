/*
 Navicat MySQL Data Transfer

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 50709
 Source Host           : localhost:3306
 Source Schema         : local_bsap

 Target Server Type    : MySQL
 Target Server Version : 50709
 File Encoding         : 65001

 Date: 28/12/2023 10:39:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for baps_calendar
-- ----------------------------
DROP TABLE IF EXISTS `baps_calendar`;
CREATE TABLE `baps_calendar`  (
                                  `id` bigint(20) NOT NULL COMMENT 'ID主键',
                                  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '标准日历' COMMENT '日历类型',
                                  `workunit_id` bigint(20) NULL DEFAULT NULL COMMENT '资源单元Id',
                                  `workunit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '资源单元',
                                  `date` date NULL DEFAULT NULL COMMENT '日期',
                                  `shiftcode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '全天' COMMENT '出勤代码',
                                  `defaultqty` decimal(24, 6) NULL DEFAULT NULL COMMENT '资源量',
                                  `sortorder` int(11) NULL DEFAULT NULL COMMENT '优先级',
                                  `num_lines` decimal(24, 6) NULL DEFAULT NULL COMMENT '标准产线数',
                                  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                  `opt_counter` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                  `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
                                  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) COMMENT = '生产日历';

-- ----------------------------
-- Table structure for baps_cell_plan_line
-- ----------------------------
DROP TABLE IF EXISTS `baps_cell_plan_line`;
CREATE TABLE `baps_cell_plan_line`  (
                                        `id` bigint(20) NOT NULL COMMENT 'ID主键',
                                        `order_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单表',
                                        `source_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '来源类型',
                                        `demand_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '需求版本号',
                                        `is_oversea_id` bigint(20) NULL DEFAULT NULL COMMENT '国内海外Id',
                                        `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国内海外',
                                        `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产基地',
                                        `base_place_id` bigint(20) NOT NULL COMMENT '生产基地Id',
                                        `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产车间',
                                        `workshop_id` bigint(20) NOT NULL COMMENT '生产车间Id',
                                        `workunit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产单元',
                                        `workunit_id` bigint(20) NOT NULL COMMENT '生产单元Id',
                                        `number_line` decimal(24, 6) NULL DEFAULT NULL COMMENT '产线数量',
                                        `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电池类型',
                                        `cells_type_id` bigint(20) NOT NULL COMMENT '电池类型Id',
                                        `h_trace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'H追溯',
                                        `aesthetics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '美学',
                                        `transparent_double_glass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '透明双玻',
                                        `cell_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '片源种类',
                                        `regional_country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '小区域国家',
                                        `item_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '5A料号',
                                        `demand_base_place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '需求地',
                                        `is_special_requirement` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否电池特殊要求',
                                        `low_resistance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '低阻',
                                        `cell_mfrs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电池厂家',
                                        `silver_pulp_mfrs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银浆厂家',
                                        `demand_qty` decimal(24, 6) NULL DEFAULT NULL COMMENT '需求数量',
                                        `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                        `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                        `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '月份',
                                        `cell_mv` decimal(24, 6) NULL DEFAULT NULL COMMENT 'MV',
                                        `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本',
                                        `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                        `qty_pc` decimal(24, 6) NULL DEFAULT NULL COMMENT '数量（片）',
                                        `d1` decimal(24, 6) NULL DEFAULT NULL,
                                        `d2` decimal(24, 6) NULL DEFAULT NULL,
                                        `d3` decimal(24, 6) NULL DEFAULT NULL,
                                        `d4` decimal(24, 6) NULL DEFAULT NULL,
                                        `d5` decimal(24, 6) NULL DEFAULT NULL,
                                        `d6` decimal(24, 6) NULL DEFAULT NULL,
                                        `d7` decimal(24, 6) NULL DEFAULT NULL,
                                        `d8` decimal(24, 6) NULL DEFAULT NULL,
                                        `d9` decimal(24, 6) NULL DEFAULT NULL,
                                        `d10` decimal(24, 6) NULL DEFAULT NULL,
                                        `d11` decimal(24, 6) NULL DEFAULT NULL,
                                        `d12` decimal(24, 6) NULL DEFAULT NULL,
                                        `d13` decimal(24, 6) NULL DEFAULT NULL,
                                        `d14` decimal(24, 6) NULL DEFAULT NULL,
                                        `d15` decimal(24, 6) NULL DEFAULT NULL,
                                        `d16` decimal(24, 6) NULL DEFAULT NULL,
                                        `d17` decimal(24, 6) NULL DEFAULT NULL,
                                        `d18` decimal(24, 6) NULL DEFAULT NULL,
                                        `d19` decimal(24, 6) NULL DEFAULT NULL,
                                        `d20` decimal(24, 6) NULL DEFAULT NULL,
                                        `d21` decimal(24, 6) NULL DEFAULT NULL,
                                        `d22` decimal(24, 6) NULL DEFAULT NULL,
                                        `d23` decimal(24, 6) NULL DEFAULT NULL,
                                        `d24` decimal(24, 6) NULL DEFAULT NULL,
                                        `d25` decimal(24, 6) NULL DEFAULT NULL,
                                        `d26` decimal(24, 6) NULL DEFAULT NULL,
                                        `d27` decimal(24, 6) NULL DEFAULT NULL,
                                        `d28` decimal(24, 6) NULL DEFAULT NULL,
                                        `d29` decimal(24, 6) NULL DEFAULT NULL,
                                        `d30` decimal(24, 6) NULL DEFAULT NULL,
                                        `d31` decimal(24, 6) NULL DEFAULT NULL,
                                        `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                        `opt_counter` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                        `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
                                        `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                        `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                        `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE
)  COMMENT = '入库计划表' ;

-- ----------------------------
-- Table structure for baps_cell_plan_line_a_low
-- ----------------------------
DROP TABLE IF EXISTS `baps_cell_plan_line_a_low`;
CREATE TABLE `baps_cell_plan_line_a_low`  (
                                              `id` bigint(20) NOT NULL COMMENT 'ID主键',
                                              `is_oversea_id` bigint(20) NULL DEFAULT NULL COMMENT '国内海外Id',
                                              `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国内海外',
                                              `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产基地',
                                              `base_place_id` bigint(20) NOT NULL COMMENT '生产基地Id',
                                              `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产车间',
                                              `workshop_id` bigint(20) NOT NULL COMMENT '生产车间Id',
                                              `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电池类型',
                                              `cells_type_id` bigint(20) NOT NULL COMMENT '电池类型Id',
                                              `h_trace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'H追溯',
                                              `aesthetics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '美学',
                                              `transparent_double_glass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '透明双玻',
                                              `cell_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '片源种类',
                                              `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '月份',
                                              `cell_mv` decimal(24, 6) NULL DEFAULT NULL COMMENT 'MV',
                                              `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本',
                                              `item_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '5A料号',
                                              `d1` decimal(24, 6) NULL DEFAULT NULL,
                                              `d2` decimal(24, 6) NULL DEFAULT NULL,
                                              `d3` decimal(24, 6) NULL DEFAULT NULL,
                                              `d4` decimal(24, 6) NULL DEFAULT NULL,
                                              `d5` decimal(24, 6) NULL DEFAULT NULL,
                                              `d6` decimal(24, 6) NULL DEFAULT NULL,
                                              `d7` decimal(24, 6) NULL DEFAULT NULL,
                                              `d8` decimal(24, 6) NULL DEFAULT NULL,
                                              `d9` decimal(24, 6) NULL DEFAULT NULL,
                                              `d10` decimal(24, 6) NULL DEFAULT NULL,
                                              `d11` decimal(24, 6) NULL DEFAULT NULL,
                                              `d12` decimal(24, 6) NULL DEFAULT NULL,
                                              `d13` decimal(24, 6) NULL DEFAULT NULL,
                                              `d14` decimal(24, 6) NULL DEFAULT NULL,
                                              `d15` decimal(24, 6) NULL DEFAULT NULL,
                                              `d16` decimal(24, 6) NULL DEFAULT NULL,
                                              `d17` decimal(24, 6) NULL DEFAULT NULL,
                                              `d18` decimal(24, 6) NULL DEFAULT NULL,
                                              `d19` decimal(24, 6) NULL DEFAULT NULL,
                                              `d20` decimal(24, 6) NULL DEFAULT NULL,
                                              `d21` decimal(24, 6) NULL DEFAULT NULL,
                                              `d22` decimal(24, 6) NULL DEFAULT NULL,
                                              `d23` decimal(24, 6) NULL DEFAULT NULL,
                                              `d24` decimal(24, 6) NULL DEFAULT NULL,
                                              `d25` decimal(24, 6) NULL DEFAULT NULL,
                                              `d26` decimal(24, 6) NULL DEFAULT NULL,
                                              `d27` decimal(24, 6) NULL DEFAULT NULL,
                                              `d28` decimal(24, 6) NULL DEFAULT NULL,
                                              `d29` decimal(24, 6) NULL DEFAULT NULL,
                                              `d30` decimal(24, 6) NULL DEFAULT NULL,
                                              `d31` decimal(24, 6) NULL DEFAULT NULL,
                                              `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                              `opt_counter` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                              `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
                                              `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                              `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                              `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                              PRIMARY KEY (`id`) USING BTREE
)  COMMENT = '入库计划表A-' ;

-- ----------------------------
-- Table structure for baps_cell_plan_line_total
-- ----------------------------
DROP TABLE IF EXISTS `baps_cell_plan_line_total`;
CREATE TABLE `baps_cell_plan_line_total`  (
                                              `id` bigint(20) NOT NULL COMMENT 'ID主键',
                                              `is_oversea_id` bigint(20) NULL DEFAULT NULL COMMENT '国内海外Id',
                                              `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国内海外',
                                              `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产基地',
                                              `base_place_id` bigint(20) NOT NULL COMMENT '生产基地Id',
                                              `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产车间',
                                              `workshop_id` bigint(20) NOT NULL COMMENT '生产车间Id',
                                              `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电池类型',
                                              `cells_type_id` bigint(20) NOT NULL COMMENT '电池类型Id',
                                              `h_trace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'H追溯',
                                              `aesthetics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '美学',
                                              `transparent_double_glass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '透明双玻',
                                              `cell_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '片源种类',
                                              `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '月份',
                                              `cell_mv` decimal(24, 6) NULL DEFAULT NULL COMMENT 'MV',
                                              `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本',
                                              `item_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '5A料号',
                                              `d1` decimal(24, 6) NULL DEFAULT NULL,
                                              `d2` decimal(24, 6) NULL DEFAULT NULL,
                                              `d3` decimal(24, 6) NULL DEFAULT NULL,
                                              `d4` decimal(24, 6) NULL DEFAULT NULL,
                                              `d5` decimal(24, 6) NULL DEFAULT NULL,
                                              `d6` decimal(24, 6) NULL DEFAULT NULL,
                                              `d7` decimal(24, 6) NULL DEFAULT NULL,
                                              `d8` decimal(24, 6) NULL DEFAULT NULL,
                                              `d9` decimal(24, 6) NULL DEFAULT NULL,
                                              `d10` decimal(24, 6) NULL DEFAULT NULL,
                                              `d11` decimal(24, 6) NULL DEFAULT NULL,
                                              `d12` decimal(24, 6) NULL DEFAULT NULL,
                                              `d13` decimal(24, 6) NULL DEFAULT NULL,
                                              `d14` decimal(24, 6) NULL DEFAULT NULL,
                                              `d15` decimal(24, 6) NULL DEFAULT NULL,
                                              `d16` decimal(24, 6) NULL DEFAULT NULL,
                                              `d17` decimal(24, 6) NULL DEFAULT NULL,
                                              `d18` decimal(24, 6) NULL DEFAULT NULL,
                                              `d19` decimal(24, 6) NULL DEFAULT NULL,
                                              `d20` decimal(24, 6) NULL DEFAULT NULL,
                                              `d21` decimal(24, 6) NULL DEFAULT NULL,
                                              `d22` decimal(24, 6) NULL DEFAULT NULL,
                                              `d23` decimal(24, 6) NULL DEFAULT NULL,
                                              `d24` decimal(24, 6) NULL DEFAULT NULL,
                                              `d25` decimal(24, 6) NULL DEFAULT NULL,
                                              `d26` decimal(24, 6) NULL DEFAULT NULL,
                                              `d27` decimal(24, 6) NULL DEFAULT NULL,
                                              `d28` decimal(24, 6) NULL DEFAULT NULL,
                                              `d29` decimal(24, 6) NULL DEFAULT NULL,
                                              `d30` decimal(24, 6) NULL DEFAULT NULL,
                                              `d31` decimal(24, 6) NULL DEFAULT NULL,
                                              `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                              `opt_counter` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                              `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
                                              `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                              `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                              `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                              PRIMARY KEY (`id`) USING BTREE
)  COMMENT = '入库计划汇总表' ;

-- ----------------------------
-- Table structure for baps_cell_production_lead_time
-- ----------------------------
DROP TABLE IF EXISTS `baps_cell_production_lead_time`;
CREATE TABLE `baps_cell_production_lead_time`  (
                                                   `id` bigint(20) NOT NULL COMMENT 'ID主键',
                                                   `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生产基地',
                                                   `base_place_id` bigint(20) NULL DEFAULT NULL COMMENT '生产基地',
                                                   `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生产车间',
                                                   `workshop_id` bigint(20) NULL DEFAULT NULL COMMENT '生产车间Id',
                                                   `lead_time` decimal(24, 6) NULL DEFAULT NULL COMMENT '提前期',
                                                   `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                   `opt_counter` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                                   `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
                                                   `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                   `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                   PRIMARY KEY (`id`) USING BTREE
)  COMMENT = '投产提前期' ;

-- ----------------------------
-- Table structure for baps_cell_production_plan
-- ----------------------------
DROP TABLE IF EXISTS `baps_cell_production_plan`;
CREATE TABLE `baps_cell_production_plan`  (
                                              `id` bigint(20) NOT NULL COMMENT 'ID主键',
                                              `is_oversea_id` bigint(20) NULL DEFAULT NULL COMMENT '国内海外Id',
                                              `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国内海外',
                                              `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产基地',
                                              `base_place_id` bigint(20) NOT NULL COMMENT '生产基地Id',
                                              `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产车间',
                                              `workshop_id` bigint(20) NOT NULL COMMENT '生产车间Id',
                                              `workunit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产单元',
                                              `workunit_id` bigint(20) NOT NULL COMMENT '生产单元Id',
                                              `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电池类型',
                                              `cells_type_id` bigint(20) NOT NULL COMMENT '电池类型Id',
                                              `number_line` decimal(24, 6) NULL DEFAULT NULL COMMENT '线体数量',
                                              `h_trace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'H追溯',
                                              `aesthetics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '美学',
                                              `transparent_double_glass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '透明双玻',
                                              `cell_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '片源种类',
                                              `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '月份',
                                              `cell_mv` decimal(24, 6) NULL DEFAULT NULL COMMENT 'MV',
                                              `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本',
                                              `item_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '5A料号',
                                              `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
                                              `end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
                                              `qty_pc` decimal(24, 6) NULL DEFAULT NULL COMMENT '数量（片）',
                                              `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                              `opt_counter` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                              `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
                                              `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                              `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                              `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                              PRIMARY KEY (`id`) USING BTREE
)  COMMENT = '投产计划' ;

-- ----------------------------
-- Table structure for baps_cell_production_plan_total
-- ----------------------------
DROP TABLE IF EXISTS `baps_cell_production_plan_total`;
CREATE TABLE `baps_cell_production_plan_total`  (
                                                    `id` bigint(20) NOT NULL COMMENT 'ID主键',
                                                    `is_oversea_id` bigint(20) NULL DEFAULT NULL COMMENT '国内海外Id',
                                                    `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '国内海外',
                                                    `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产基地',
                                                    `base_place_id` bigint(20) NOT NULL COMMENT '生产基地Id',
                                                    `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产车间',
                                                    `workshop_id` bigint(20) NOT NULL COMMENT '生产车间Id',
                                                    `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电池类型',
                                                    `cells_type_id` bigint(20) NOT NULL COMMENT '电池类型Id',
                                                    `h_trace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'H追溯',
                                                    `aesthetics` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '美学',
                                                    `transparent_double_glass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '透明双玻',
                                                    `cell_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '片源种类',
                                                    `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '月份',
                                                    `cell_mv` decimal(24, 6) NULL DEFAULT NULL COMMENT 'MV',
                                                    `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本',
                                                    `item_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '5A料号',
                                                    `d1` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d2` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d3` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d4` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d5` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d6` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d7` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d8` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d9` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d10` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d11` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d12` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d13` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d14` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d15` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d16` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d17` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d18` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d19` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d20` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d21` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d22` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d23` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d24` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d25` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d26` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d27` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d28` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d29` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d30` decimal(24, 6) NULL DEFAULT NULL,
                                                    `d31` decimal(24, 6) NULL DEFAULT NULL,
                                                    `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                    `opt_counter` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                                    `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
                                                    `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                    `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                    `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                    `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                    PRIMARY KEY (`id`) USING BTREE
)  COMMENT = '投产计划汇总表' ;

-- ----------------------------
-- Table structure for baps_cell_workshop_priority_target
-- ----------------------------
DROP TABLE IF EXISTS `baps_cell_workshop_priority_target`;
CREATE TABLE `baps_cell_workshop_priority_target`  (
                                                       `id` bigint(20) NOT NULL COMMENT 'ID主键',
                                                       `workshop_id` bigint(20) NULL DEFAULT NULL COMMENT '车间Id',
                                                       `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '车间',
                                                       `efficiency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '效率分布',
                                                       `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                       `opt_counter` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                                       `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
                                                       `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                       `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                       `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                       `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                       PRIMARY KEY (`id`) USING BTREE
)  COMMENT = '车间优先度效率目标值' ;

-- ----------------------------
-- Table structure for baps_cell_workshop_priority_target_mid
-- ----------------------------
DROP TABLE IF EXISTS `baps_cell_workshop_priority_target_mid`;
CREATE TABLE `baps_cell_workshop_priority_target_mid`  (
                                                           `id` bigint(20) NOT NULL COMMENT 'ID主键',
                                                           `cells_type_id` bigint(20) NULL DEFAULT NULL COMMENT '电池类型Id',
                                                           `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电池类型',
                                                           `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生产车间(供应方)',
                                                           `workshop_id` bigint(20) NULL DEFAULT NULL COMMENT '生产车间Id供应方)',
                                                           `supply_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '供应类型',
                                                           `rule` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '增/减规则',
                                                           `begin_month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '月度起',
                                                           `end_month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '月度止',
                                                           `priority_target` decimal(24, 6) NULL DEFAULT NULL COMMENT '目标效率',
                                                           `priority_reality` decimal(24, 6) NULL DEFAULT NULL COMMENT '实际效率',
                                                           `ratio` decimal(24, 6) NULL DEFAULT NULL COMMENT '实际效率与目标效率比',
                                                           `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                                           `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                           `opt_counter` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁',
                                                           `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
                                                           `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                           `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                           `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                           `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                           PRIMARY KEY (`id`) USING BTREE
)  COMMENT = '车间优先度效率目标值mid' ;

SET FOREIGN_KEY_CHECKS = 1;
