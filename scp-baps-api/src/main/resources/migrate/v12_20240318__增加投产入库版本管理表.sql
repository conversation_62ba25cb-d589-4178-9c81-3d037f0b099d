CREATE TABLE `baps_cell_plan_line_version` (
                                               `id` bigint NOT NULL COMMENT 'ID主键',
                                               `is_oversea_id` bigint DEFAULT NULL COMMENT '国内海外Id',
                                               `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '国内海外',
                                               `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '排产月份',
                                               `final_version` varchar(255) DEFAULT NULL COMMENT '最終确认发布的版本',
                                               `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '排产版本',
                                               `is_si_mfrs` int DEFAULT '0' COMMENT '是否进行了硅料厂家拆分',
                                               `is_wafer_grade` int DEFAULT '0' COMMENT '是否已经进行硅片等级拆分',
                                               `is_process_category` int DEFAULT '0' COMMENT '是否已经进行了加工类型拆分',
                                               `is_confirm_plan` int DEFAULT '0' COMMENT '是否进行了计划确认',
                                               `is_create_instock_plan` int DEFAULT NULL COMMENT '是否创建了入库计划',
                                               `is_send_email` int DEFAULT NULL COMMENT '是否发送了邮件',
                                               `schedule_month` varchar(10) DEFAULT NULL COMMENT '哪个月排的',
                                               `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                               `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                               `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                               `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                               `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                               `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `baps_cell_plan_line_pk` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='投产计划版本管理表';

CREATE TABLE `baps_cell_instock_plan_version` (
                                                  `id` bigint NOT NULL COMMENT 'ID主键',
                                                  `is_oversea_id` bigint DEFAULT NULL COMMENT '国内海外Id',
                                                  `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '国内海外',
                                                  `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '排产月份',
                                                  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '最終确认发布的版本',
                                                  `form_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '来自哪个投产版本',
                                                  `is_a_split` int DEFAULT '0' COMMENT '是否进行了A-拆分',
                                                  `is_transparent_double_glass` int DEFAULT '0' COMMENT '是否已经进行了透明双玻拆分',
                                                  `is_grade_rule` int DEFAULT '0' COMMENT '是否已经进行了分档规则拆分',
                                                  `is_confirm_plan` int DEFAULT '0' COMMENT '是否进行了计划确认',
                                                  `is_send_email` int DEFAULT NULL COMMENT '是否进行了邮件发送',
                                                  `schedule_month` varchar(10) DEFAULT NULL COMMENT '哪个月排的',
                                                  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                  `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                                  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                                  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE KEY `baps_cell_plan_line_pk` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入库计划版本管理表';