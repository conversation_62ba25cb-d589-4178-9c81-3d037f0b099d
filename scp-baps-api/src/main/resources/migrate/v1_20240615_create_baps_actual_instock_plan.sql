-- auto-generated definition
create table baps_actual_instock_plan
(
    id                            bigint auto_increment comment 'ID主键'
        primary key,
    batch_no                      varchar(255) collate utf8mb4_bin                          null comment '批次号',
    data_type                     varchar(50) collate utf8mb4_bin                           null comment '数据类型',
    order_code                    varchar(255) collate utf8mb4_bin                          null comment '订单表',
    source_type                   varchar(255) collate utf8mb4_bin                          null comment '来源类型',
    source_type_id                bigint                                                    null,
    demand_version                varchar(255) collate utf8mb4_bin                          null comment '需求版本号',
    is_oversea_id                 bigint                                                    null comment '国内海外Id',
    is_oversea                    varchar(255) collate utf8mb4_bin                          null comment '国内海外',
    base_place                    varchar(50) collate utf8mb4_bin                           null comment '生产基地',
    base_place_id                 bigint                                                    null comment '生产基地Id',
    workshop                      varchar(50) collate utf8mb4_bin                           null comment '生产车间',
    workshop_id                   bigint                                                    null comment '生产车间Id',
    workunit                      varchar(50) collate utf8mb4_bin                           null comment '生产单元',
    workunit_id                   bigint                                                    null comment '生产单元Id',
    line_name                     varchar(255)                                              null comment '生产线体',
    number_line                   decimal(24, 6)                                            null comment '产线数量',
    cells_type                    varchar(100) collate utf8mb4_bin                          null comment '电池类型',
    cells_type_id                 bigint                                                    null comment '电池类型Id',
    h_trace                       varchar(255) collate utf8mb4_bin                          null comment 'H追溯',
    h_trace_id                    bigint                                                    null,
    aesthetics                    varchar(255) collate utf8mb4_bin                          null comment '美学',
    aesthetics_id                 bigint                                                    null,
    is_transparent_double_glass   bit                             default b'0'              null comment '是否进行了透明双玻拆分',
    transparent_double_glass_id   bigint                                                    null,
    transparent_double_glass      varchar(255)                    default '无'              null comment '透明双玻',
    cell_source                   varchar(255) collate utf8mb4_bin                          null comment '片源种类',
    cell_source_id                bigint                                                    null,
    production_grade              varchar(255)                                              null comment '产品等级',
    production_grade_id           bigint                                                    null,
    regional_country              varchar(255) collate utf8mb4_bin                          null comment '小区域国家',
    regional_country_id           bigint                                                    null,
    item_code                     varchar(255) collate utf8mb4_bin                          null comment '5A料号',
    demand_base_place_id          bigint                                                    null,
    demand_base_place             varchar(255) collate utf8mb4_bin                          null comment '需求地',
    is_special_requirement        varchar(255) collate utf8mb4_bin                          null comment '是否电池特殊要求',
    low_resistance                varchar(255) collate utf8mb4_bin                          null comment '低阻',
    cell_mfrs_id                  bigint                                                    null,
    cell_mfrs                     varchar(255) collate utf8mb4_bin                          null comment '电池厂家',
    silver_pulp_mfrs_id           bigint                                                    null,
    silver_pulp_mfrs              varchar(255) collate utf8mb4_bin                          null comment '银浆厂家',
    demand_qty                    decimal(24, 6)                                            null comment '需求数量',
    end_time                      datetime                                                  null comment '结束时间',
    start_time                    datetime                                                  null comment '开始时间',
    month                         varchar(10) collate utf8mb4_bin                           null comment '投产月份',
    cell_mv                       decimal(24, 6)                                            null comment 'MV',
    final_version                 varchar(255)                                              null comment '最終邮件确认发布的版本',
    version                       varchar(255) collate utf8mb4_bin                          null comment '版本',
    remark                        varchar(255) collate utf8mb4_bin                          null comment '备注',
    old_qty_pc                    decimal(24, 6)                                            null comment '数量（片）拆分前的值',
    qty_pc                        decimal(24, 6)                                            null comment '数量（片）',
    demand_summary_lines_id       bigint                                                    null comment '汇总明细行id',
    si_mfrs_id                    bigint                                                    null,
    si_mfrs                       varchar(255)                                              null comment '硅料厂家',
    is_si_mfrs                    bit                             default b'0'              null comment '是否进行了硅料厂家拆分',
    silicon_material_manufacturer varchar(255)                                              null comment '硅片厂家',
    screen_plate_mfrs             varchar(255)                                              null comment '网版厂家',
    start_efficiency              decimal(24, 6)                                            null comment '起始效率',
    max_efficiency                decimal(24, 6)                                            null comment '最大分布效率',
    special_order_no              varchar(255)                                              null comment '电池特殊单号',
    demand_date                   date                                                      null comment '需求日期',
    is_wafer_grade                bit                             default b'0'              null comment '是否已经进行硅片等级拆分',
    wafer_grade_id                bigint                                                    null,
    wafer_grade                   varchar(255)                                              null comment '硅片等级对应工单开立片源等级',
    is_a_split                    bit                             default b'0'              null comment '是否进行了A-拆分',
    process_category_priority     int                                                       null comment '加工类型优先级',
    process_category_id           bigint                                                    null,
    process_category              varchar(255)                                              null comment '加工类型',
    is_process_category           bit                             default b'0'              null comment '是否已经进行了加工类型拆分',
    is_hand_process_category      bit                             default b'0'              null comment '是否进行手动加工类型指定',
    gap                           decimal(24, 6)                                            null comment 'gap',
    demand_remark                 varchar(255)                                              null comment '需求说明',
    grade_rule                    varchar(255)                                              null comment '分档规则',
    verification_mark             varchar(50)                                               null comment '验证标识',
    demand_source                 varchar(50)                                               null comment '需求来源',
    battery_material_code         varchar(100)                                              null comment '电池物料编码',
    old_start_time                datetime                                                  null comment '原来入库开始时间',
    old_end_time                  datetime                                                  null comment '原来入库结束时间',
    from_id                       bigint                                                    null comment '原排产ID',
    parent_id                     bigint                                                    null comment '拆前父Id',
    bbom_id                       bigint                                                    null comment '对应bbom中的Id',
    confirm_plan                  int                             default 0                 null comment '计划确认',
    old_month                     varchar(10)                                               null comment '原入库月份',
    schedule_month                varchar(10)                                               null comment '哪个月排的',
    wafer_yield_ratio             decimal(24, 6)                                            null comment '片源级投产良率',
    wafer_grade_ratio             decimal(24, 6)                                            null comment '片源级A-比例',
    main_grid_space               varchar(60)                                               null comment '主栅间距',
    h_change_flag                 varchar(255)                                              null comment '是否H兼容',
    cell_fine                     decimal(24, 6)                                            null comment '记录良率',
    tenant_id                     varchar(32) collate utf8mb4_bin default 'TRINA'           not null comment '租户号',
    opt_counter                   int                             default 1                 not null comment '乐观锁',
    is_deleted                    int                             default 0                 not null comment '是否删除,0=正常，1=删除',
    created_by                    varchar(32) collate utf8mb4_bin default '-1'              not null comment '创建人',
    created_time                  datetime                        default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by                    varchar(32) collate utf8mb4_bin default '-1'              not null comment '更新人',
    updated_time                  datetime                        default CURRENT_TIMESTAMP not null comment '更新时间'
)
    comment '入库数据（ERP实际入库、入库计划）';

create index idx_max_version_index
    on baps_actual_instock_plan (old_month, is_oversea, is_deleted);

create index idx_version_index
    on baps_actual_instock_plan (old_month, is_oversea, version);