DROP TABLE IF EXISTS baps_priority_logistics_line;
CREATE TABLE baps_priority_logistics_line
(
    id                   BIGINT      NOT NULL COMMENT 'ID主键',
    base_place_id        BIGINT COMMENT '生产基地Id',
    base_place           VARCHAR(100) COMMENT '生产基地',
    demand_base_place_id BIGINT COMMENT '需求基地Id',
    demand_base_place    VARCHAR(100) COMMENT '需求基地',
    series               VARCHAR(100) COMMENT '系列',
    priority             INT COMMENT '优先级',
    days                 INT COMMENT '天数',
    tenant_id            VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter          INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted           INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '物流线路优先级';
