DROP TABLE IF EXISTS baps_silicon_split_rule;
CREATE TABLE baps_silicon_split_rule(
                                        id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                        cell_type_id BIGINT    COMMENT '电池类型Id' ,
                                        cell_type VARCHAR(50)    COMMENT '电池类型' ,
                                        workshop_id BIGINT    COMMENT '生产车间Id' ,
                                        workshop VARCHAR(50)    COMMENT '生产车间' ,
                                        wafer_grade VARCHAR(50)    COMMENT '硅片等级' ,
                                        silicon_material_manufacturer VARCHAR(50)    COMMENT '硅片厂家' ,
                                        cell_fine DECIMAL(24,6)    COMMENT '电池良率' ,
                                        start_date DATE    COMMENT '开始时间' ,
                                        end_date DATE    COMMENT '结束时间' ,
                                        tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                        opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                        is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                        created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                        created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                        updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                        updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                        PRIMARY KEY (id)
)  COMMENT = '硅片拆分规则';
