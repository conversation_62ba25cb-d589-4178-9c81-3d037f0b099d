CREATE TABLE `baps_cell_resource` (
                                      `id` bigint NOT NULL COMMENT 'ID主键',
                                      `is_oversea_id` bigint DEFAULT NULL COMMENT '国内海外id',
                                      `is_oversea` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '国内海外',
                                      `base_place_id` bigint DEFAULT NULL COMMENT '生产基地id',
                                      `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '生产基地',
                                      `workshop_id` bigint DEFAULT NULL COMMENT '生产车间id',
                                      `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '生产车间',
                                      `workunit_id` bigint DEFAULT NULL COMMENT '生产单元id',
                                      `workunit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '生产单元',
                                      `is_splited` int DEFAULT NULL COMMENT '拆分标识',
                                      `line_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '生产线体',
                                      `resource_group` varchar(255) DEFAULT NULL COMMENT '资源组',
                                      `number_line` int DEFAULT NULL COMMENT '线体数量',
                                      `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                      `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                      `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                      `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '-1' COMMENT '创建人',
                                      `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '-1' COMMENT '更新人',
                                      `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='电池资源表';