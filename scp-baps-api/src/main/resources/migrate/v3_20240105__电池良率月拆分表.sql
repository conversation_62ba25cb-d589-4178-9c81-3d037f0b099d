CREATE TABLE `baps_cell_fine_month` (
                                        `id` bigint NOT NULL COMMENT 'ID主键',
                                        `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '国内海外',
                                        `cell_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '电池类型',
                                        `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '生产基地',
                                        `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '生产车间',
                                        `year` int DEFAULT NULL COMMENT '年份',
                                        `month` int DEFAULT NULL COMMENT '月份',
                                        `yield` decimal(24,6) DEFAULT NULL COMMENT '良率',
                                        `tenant_id` varchar(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                        `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                        `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                        `created_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
                                        `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updated_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
                                        `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电池良率月拆分表';