CREATE TABLE `baps_delivery_holiday` (
                                         `id` bigint NOT NULL COMMENT 'ID主键',
                                         `is_oversea` varchar(32) DEFAULT NULL COMMENT '国内/海外',
                                         `base_place_from` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发货基地',
                                         `base_place_to` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '到货基地',
                                         `month` varchar(32) DEFAULT NULL COMMENT '年月',
                                         `date_between` varchar(100) DEFAULT NULL COMMENT '日期区间',
                                         `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                         `tenant_id` varchar(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                         `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                         `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                         `created_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
                                         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
                                         `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物流节假日表';