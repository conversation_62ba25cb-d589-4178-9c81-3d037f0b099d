-- auto-generated definition
create table baps_demand_plan_lines_aps
(
    id                            bigint                                not null comment 'ID主键'
        primary key,
    batch_no                      varchar(255) null comment '批次号',
    actual_coverage_date          date null comment '实际覆盖日期',
    plan_lock_date                date null comment '计划锁定日期',
    dp_id                         varchar(255) null comment '后端提供给APS的唯一ID',
    demand_plan_code              varchar(50) null comment '电池需求计划号',
    source_type                   varchar(50) null comment '来源类型',
    domestic_oversea_name         varchar(50) null comment '国内/海外名称',
    battery_name                  varchar(100) null comment '电池类型名称',
    battery_material_code         varchar(100) null comment '电池物料编码',
    h_trace_name                  varchar(20) null comment 'H追溯名称',
    pcs_source_type_name          varchar(20) null comment '片源种类名称',
    transparent_double_glass_name varchar(50) null comment '透明双波名称',
    aesthetics_name               varchar(50) null comment '美学名称',
    regional_country_name         varchar(100) null comment '小区域国家名称',
    base_place_name               varchar(100) null comment '需求地名称',
    cell_mfrs                     varchar(100) null comment '电池厂家',
    silver_pulp_mfrs              varchar(100) null comment '银浆厂家',
    si_mfrs                       varchar(100) null comment '硅料厂家',
    screen_plate_mfrs             varchar(100) null comment '网版厂家',
    start_efficiency              decimal(24, 6) null comment '起始效率',
    special_order_no              varchar(255) null comment '电池特殊单号',
    demand_date                   date null comment '需求日期',
    pass_percent                  decimal(24, 6) null comment '符合率',
    schedule_workshop             varchar(100) null comment '电池生产车间',
    process_category_name         varchar(50) null comment '加工类型',
    demand_qty                    decimal(24, 6) null comment '需求数量(汇总)',
    exterior_demand_id            varchar(100) null comment '外部需求ID',
    h_change_flag                 varchar(255) null comment 'H转换标记',
    pcs_source_dt_change_flag     varchar(255) null comment 'DT转换标记',
    main_grid_space               varchar(50) null comment '主栅间距',
    tenant_id                     varchar(32) default 'TRINA'           not null comment '租户号',
    opt_counter                   int         default 1                 not null comment '乐观锁',
    is_deleted                    int         default 0                 not null comment '是否删除,0=正常，1=删除',
    created_by                    varchar(32) default '-1'              not null comment '创建人',
    created_time                  datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by                    varchar(32) default '-1'              not null comment '更新人',
    updated_time                  datetime    default CURRENT_TIMESTAMP not null comment '更新时间'
) comment '需求计划明细（APS）';
