DROP TABLE IF EXISTS baps_cell_version_qop_ie;
CREATE TABLE baps_cell_version_qop_ie(
                                         id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                         is_oversea_id BIGINT    COMMENT '国内/海外Id' ,
                                         is_oversea_name VARCHAR(20)    COMMENT '国内/海外' ,
                                         cell_type_id BIGINT    COMMENT '电池类型编号' ,
                                         cell_type_name VARCHAR(100)    COMMENT '电池类型' ,
                                         workshop_id BIGINT    COMMENT '生产车间Id' ,
                                         workshop_name VARCHAR(50)    COMMENT '生产车间' ,
                                         month VARCHAR(20)    COMMENT '月份' ,
                                         rate DECIMAL(24,6)    COMMENT 'qop利用率' ,
                                         target DECIMAL(24,6)    COMMENT 'qop目标' ,
                                         capacity DECIMAL(24,6)    COMMENT 'IE产能' ,
                                         gap DECIMAL(24,6)    COMMENT '差异' ,
                                         remark VARCHAR(255)    COMMENT '备注' ,
                                         tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                         opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                         is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                         created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                         created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                         updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                         updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                         PRIMARY KEY (id)
)  COMMENT = 'qop与ie对比';
DROP TABLE IF EXISTS baps_cell_version_plan_ie;
CREATE TABLE baps_cell_version_plan_ie(
                                          id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                          is_oversea_id BIGINT    COMMENT '国内/海外Id' ,
                                          is_oversea_name VARCHAR(20)    COMMENT '国内/海外' ,
                                          cell_type_id BIGINT    COMMENT '电池类型编号' ,
                                          cell_type_name VARCHAR(100)    COMMENT '电池类型' ,
                                          workshop_id BIGINT    COMMENT '生产车间Id' ,
                                          workshop_name VARCHAR(50)    COMMENT '生产车间' ,
                                          month VARCHAR(20)    COMMENT '月份' ,
                                          rate DECIMAL(24,6)    COMMENT '产能利用率' ,
                                          plan_capacity DECIMAL(24,6)    COMMENT '计划产能' ,
                                          capacity DECIMAL(24,6)    COMMENT 'IE产能' ,
                                          gap DECIMAL(24,6)    COMMENT '差异' ,
                                          version VARCHAR(50) COMMENT '版本',
                                          remark VARCHAR(255)    COMMENT '备注' ,
                                          tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                          opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                          is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                          created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                          created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                          updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                          updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                          PRIMARY KEY (id)
)  COMMENT = '计划与ie产能对比';
DROP TABLE IF EXISTS baps_cell_version_plan_qop;
CREATE TABLE baps_cell_version_plan_qop(
                                           id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                           is_oversea_id BIGINT    COMMENT '国内/海外Id' ,
                                           is_oversea_name VARCHAR(20)    COMMENT '国内/海外' ,
                                           cell_type_id BIGINT    COMMENT '电池类型编号' ,
                                           cell_type_name VARCHAR(100)    COMMENT '电池类型' ,
                                           workshop_id BIGINT    COMMENT '生产车间Id' ,
                                           workshop_name VARCHAR(50)    COMMENT '生产车间' ,
                                           month VARCHAR(20)    COMMENT '月份' ,
                                           rate DECIMAL(24,6)    COMMENT '产能利用率' ,
                                           target DECIMAL(24,6)    COMMENT 'qop目标' ,
                                           plan_capacity DECIMAL(24,6)    COMMENT '计划产能' ,
                                           gap DECIMAL(24,6)    COMMENT '差异' ,
                                           remark VARCHAR(255)    COMMENT '备注' ,
                                           tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                           opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                           is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                           created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                           created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                           updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                           updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                           PRIMARY KEY (id)
)  COMMENT = '计划与qop';
DROP TABLE IF EXISTS baps_cell_version_plan_history;
CREATE TABLE baps_cell_version_plan_history(
                                               id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                               is_oversea_id BIGINT    COMMENT '国内/海外Id' ,
                                               is_oversea_name VARCHAR(20)    COMMENT '国内/海外' ,
                                               cell_type_id BIGINT    COMMENT '电池类型编号' ,
                                               cell_type_name VARCHAR(100)    COMMENT '电池类型' ,
                                               workshop_id BIGINT    COMMENT '生产车间Id' ,
                                               workshop_name VARCHAR(50)    COMMENT '生产车间' ,
                                               month VARCHAR(20)    COMMENT '月份' ,
                                               month_v0 DECIMAL(24,6)    COMMENT '月份计划（上上版）' ,
                                               month_v1 DECIMAL(24,6)    COMMENT '月份计划（上版）' ,
                                               month_v2 DECIMAL(24,6)    COMMENT '月份计划新版' ,
                                               gap DECIMAL(24,6)    COMMENT '差异' ,
                                               remark VARCHAR(255)    COMMENT '备注' ,
                                               tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                               opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                               is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                               created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                               created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                               updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                               updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                               PRIMARY KEY (id)
)  COMMENT = '计划与上一版本计划对比';
