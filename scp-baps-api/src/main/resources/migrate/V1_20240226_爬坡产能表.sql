

DROP TABLE IF EXISTS baps_cell_grade_capacity;

CREATE TABLE baps_cell_grade_capacity (
  id bigint NOT NULL COMMENT 'ID主键',
  cells_type_id bigint DEFAULT NULL COMMENT '电池类型id',
  cells_type varchar(100) NOT NULL COMMENT '电池类型',
  is_oversea_id bigint DEFAULT NULL COMMENT '国内海外id',
  is_oversea varchar(20) DEFAULT NULL COMMENT '国内海外',
  base_place_id bigint DEFAULT NULL COMMENT '生产基地id',
  base_place varchar(50) NOT NULL COMMENT '生产基地',
  workshopid bigint DEFAULT NULL COMMENT '生产车间id',
  workshop varchar(50) NOT NULL COMMENT '生产车间',
  workunitid bigint DEFAULT NULL COMMENT '生产单元id',
  workunit varchar(50) NOT NULL COMMENT '生产单元',
  line_name varchar(255) DEFAULT NULL COMMENT '生产线体',
  line_number int NOT NULL COMMENT '线体数量',
  month varchar(10) NOT NULL COMMENT '月份',
  capacity_quantity decimal(24,6) DEFAULT NULL COMMENT '产能',
  unit varchar(20) NOT NULL COMMENT '单位',
  d1 decimal(24,6) DEFAULT NULL COMMENT '1号',
  d2 decimal(24,6) DEFAULT NULL COMMENT '2号',
  d3 decimal(24,6) DEFAULT NULL COMMENT '3号',
  d4 decimal(24,6) DEFAULT NULL COMMENT '4号',
  d5 decimal(24,6) DEFAULT NULL COMMENT '5号',
  d6 decimal(24,6) DEFAULT NULL COMMENT '6号',
  d7 decimal(24,6) DEFAULT NULL COMMENT '7号',
  d8 decimal(24,6) DEFAULT NULL COMMENT '8号',
  d9 decimal(24,6) DEFAULT NULL COMMENT '9号',
  d10 decimal(24,6) DEFAULT NULL COMMENT '10号',
  d11 decimal(24,6) DEFAULT NULL COMMENT '11号',
  d12 decimal(24,6) DEFAULT NULL COMMENT '12号',
  d13 decimal(24,6) DEFAULT NULL COMMENT '13号',
  d14 decimal(24,6) DEFAULT NULL COMMENT '14号',
  d15 decimal(24,6) DEFAULT NULL COMMENT '15号',
  d16 decimal(24,6) DEFAULT NULL COMMENT '16号',
  d17 decimal(24,6) DEFAULT NULL COMMENT '17号',
  d18 decimal(24,6) DEFAULT NULL COMMENT '18号',
  d19 decimal(24,6) DEFAULT NULL COMMENT '19号',
  d20 decimal(24,6) DEFAULT NULL COMMENT '20号',
  d21 decimal(24,6) DEFAULT NULL COMMENT '21号',
  d22 decimal(24,6) DEFAULT NULL COMMENT '22号',
  d23 decimal(24,6) DEFAULT NULL COMMENT '23号',
  d24 decimal(24,6) DEFAULT NULL COMMENT '24号',
  d25 decimal(24,6) DEFAULT NULL COMMENT '25号',
  d26 decimal(24,6) DEFAULT NULL COMMENT '26号',
  d27 decimal(24,6) DEFAULT NULL COMMENT '27号',
  d28 decimal(24,6) DEFAULT NULL COMMENT '28号',
  d29 decimal(24,6) DEFAULT NULL COMMENT '29号',
  d30 decimal(24,6) DEFAULT NULL COMMENT '30号',
  d31 decimal(24,6) DEFAULT NULL COMMENT '31号',
  remark varchar(255) DEFAULT NULL COMMENT '备注',
  tenant_id varchar(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
  opt_counter int NOT NULL DEFAULT '1' COMMENT '乐观锁',
  is_deleted int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
  created_by varchar(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
  created_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_by varchar(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
  updated_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='爬坡产能表';
