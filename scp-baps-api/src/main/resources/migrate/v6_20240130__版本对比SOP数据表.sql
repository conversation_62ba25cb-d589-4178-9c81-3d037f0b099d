DROP TABLE IF EXISTS baps_sop_details;
CREATE TABLE baps_sop_details(
                                 id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                 is_oversea_id BIGINT    COMMENT '国内海外Id' ,
                                 is_oversea_name VARCHAR(50)    COMMENT '国内海外名' ,
                                 workshop_id BIGINT    COMMENT '生产基地Id' ,
                                 workshop_name VARCHAR(50)    COMMENT '生产基地' ,
                                 cell_type_id BIGINT    COMMENT '电池类型Id' ,
                                 cell_type_name VARCHAR(50)    COMMENT '电池类型' ,
                                 month VARCHAR(20)    COMMENT '月份' ,
                                 sop_qty DECIMAL(24,6)    COMMENT '数量' ,
                                 version VARCHAR(50)    COMMENT '版本' ,
                                 tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                 opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                 is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                 created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                 created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                 updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                 updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                 PRIMARY KEY (id)
)  COMMENT = 'SOP数据';
