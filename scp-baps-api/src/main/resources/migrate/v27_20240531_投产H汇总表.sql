CREATE TABLE `baps_cell_plan_line_total_h` (
                                               `id` bigint NOT NULL COMMENT 'ID主键',
                                               `is_oversea_id` bigint DEFAULT NULL COMMENT '国内海外Id',
                                               `is_oversea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '国内海外',
                                               `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产基地',
                                               `base_place_id` bigint DEFAULT NULL COMMENT '生产基地Id',
                                               `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产车间',
                                               `workshop_id` bigint DEFAULT NULL COMMENT '生产车间Id',
                                               `workunit` varchar(100) DEFAULT NULL COMMENT '生产单元',
                                               `workunit_id` bigint DEFAULT NULL COMMENT '生产单元Id',
                                               `line_name` varchar(100) DEFAULT NULL COMMENT '生产线体',
                                               `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池类型',
                                               `cells_type_id` bigint DEFAULT NULL COMMENT '电池类型Id',
                                               `cell_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '片源种类',
                                               `cell_source_id` bigint DEFAULT NULL COMMENT '片源种类Id',
                                               `is_special_requirement` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '是否电池特殊要求',
                                               `low_resistance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '低阻',
                                               `cell_mfrs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '电池厂家',
                                               `cell_mfrs_id` bigint DEFAULT NULL COMMENT '电池厂家Id',
                                               `silver_pulp_mfrs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '银浆厂家',
                                               `silver_pulp_mfrs_id` bigint DEFAULT NULL COMMENT '银浆厂家Id',
                                               `si_mfrs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '硅料厂家',
                                               `si_mfrs_id` bigint DEFAULT NULL COMMENT '硅料厂家Id',
                                               `screen_plate_mfrs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '网版厂家',
                                               `screen_plate_mfrs_id` bigint DEFAULT NULL COMMENT '网版厂家Id',
                                               `special_order_no` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '电池特殊单号',
                                               `verification_mark` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '验证标识',
                                               `wafer_yield_ratio` decimal(24,6) DEFAULT NULL COMMENT '片源级投产良率',
                                               `wafer_grade_ratio` decimal(24,6) DEFAULT NULL COMMENT '片源级A-比例',
                                               `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '计划版本',
                                               `month` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '月份',
                                               `h_trace` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'H追溯',
                                               `h_trace_id` bigint DEFAULT NULL COMMENT 'H追溯Id',
                                               `d1` decimal(24,6) DEFAULT NULL,
                                               `d2` decimal(24,6) DEFAULT NULL,
                                               `d3` decimal(24,6) DEFAULT NULL,
                                               `d4` decimal(24,6) DEFAULT NULL,
                                               `d5` decimal(24,6) DEFAULT NULL,
                                               `d6` decimal(24,6) DEFAULT NULL,
                                               `d7` decimal(24,6) DEFAULT NULL,
                                               `d8` decimal(24,6) DEFAULT NULL,
                                               `d9` decimal(24,6) DEFAULT NULL,
                                               `d10` decimal(24,6) DEFAULT NULL,
                                               `d11` decimal(24,6) DEFAULT NULL,
                                               `d12` decimal(24,6) DEFAULT NULL,
                                               `d13` decimal(24,6) DEFAULT NULL,
                                               `d14` decimal(24,6) DEFAULT NULL,
                                               `d15` decimal(24,6) DEFAULT NULL,
                                               `d16` decimal(24,6) DEFAULT NULL,
                                               `d17` decimal(24,6) DEFAULT NULL,
                                               `d18` decimal(24,6) DEFAULT NULL,
                                               `d19` decimal(24,6) DEFAULT NULL,
                                               `d20` decimal(24,6) DEFAULT NULL,
                                               `d21` decimal(24,6) DEFAULT NULL,
                                               `d22` decimal(24,6) DEFAULT NULL,
                                               `d23` decimal(24,6) DEFAULT NULL,
                                               `d24` decimal(24,6) DEFAULT NULL,
                                               `d25` decimal(24,6) DEFAULT NULL,
                                               `d26` decimal(24,6) DEFAULT NULL,
                                               `d27` decimal(24,6) DEFAULT NULL,
                                               `d28` decimal(24,6) DEFAULT NULL,
                                               `d29` decimal(24,6) DEFAULT NULL,
                                               `d30` decimal(24,6) DEFAULT NULL,
                                               `d31` decimal(24,6) DEFAULT NULL,
                                               `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                               `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                               `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                               `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                               `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                               `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='投产计划H汇总表';