DROP TABLE IF EXISTS baps_cell_wip_total;
CREATE TABLE baps_cell_wip_total(
    id BIGINT NOT NULL   COMMENT 'ID主键' ,
    order_code VARCHAR(255)    COMMENT '订单号' ,
    billing_account VARCHAR(255)    COMMENT '开单账号' ,
    billing_date DATETIME    COMMENT '开单日期' ,
    erp_order_code VARCHAR(255)    COMMENT 'erp工单号' ,
    month INT    COMMENT '月份' ,
    base_place VARCHAR(255)    COMMENT '生产基地' ,
    workshop VARCHAR(255)    COMMENT '车间' ,
    qty DECIMAL(24,6)    COMMENT '计划数量' ,
    qty_billing DECIMAL(24,6)    COMMENT '已开单数量' ,
    qty_billing_total DECIMAL(24,6)    COMMENT '累计已开单数量' ,
    cell_type VARCHAR(255)    COMMENT '电池类型' ,
    item_fivea VARCHAR(255)    COMMENT '5a料号' ,
    is_oversea VARCHAR(255)    COMMENT '国内海外' ,
    start_time DATETIME    COMMENT '起始时间' ,
    end_time DATETIME    COMMENT '结束时间' ,
    tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
    opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
    is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
    created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
    created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
    updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '开立工单明细表';




