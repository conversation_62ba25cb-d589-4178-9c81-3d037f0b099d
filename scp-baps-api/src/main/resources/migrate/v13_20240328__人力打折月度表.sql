CREATE TABLE `baps_cell_base_capacity_month_discounts` (
                                                           `id` bigint NOT NULL COMMENT 'ID主键',
                                                           `is_oversea_id` bigint DEFAULT NULL COMMENT '国内海外id',
                                                           `is_oversea` varchar(50) DEFAULT NULL COMMENT '国内海外',
                                                           `base_place_id` bigint DEFAULT NULL COMMENT '生产基地id',
                                                           `base_place` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '生产基地',
                                                           `workshop_id` bigint DEFAULT NULL COMMENT '生产车间id',
                                                           `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '生产车间',
                                                           `month` varchar(20) DEFAULT NULL COMMENT '月份',
                                                           `d1` decimal(24,6) DEFAULT NULL COMMENT 'd1',
                                                           `d2` decimal(24,6) DEFAULT NULL COMMENT 'd2',
                                                           `d3` decimal(24,6) DEFAULT NULL COMMENT 'd3',
                                                           `d4` decimal(24,6) DEFAULT NULL COMMENT 'd4',
                                                           `d5` decimal(24,6) DEFAULT NULL COMMENT 'd5',
                                                           `d6` decimal(24,6) DEFAULT NULL COMMENT 'd6',
                                                           `d7` decimal(24,6) DEFAULT NULL COMMENT 'd7',
                                                           `d8` decimal(24,6) DEFAULT NULL COMMENT 'd8',
                                                           `d9` decimal(24,6) DEFAULT NULL COMMENT 'd9',
                                                           `d10` decimal(24,6) DEFAULT NULL COMMENT 'd10',
                                                           `d11` decimal(24,6) DEFAULT NULL COMMENT 'd11',
                                                           `d12` decimal(24,6) DEFAULT NULL COMMENT 'd12',
                                                           `d13` decimal(24,6) DEFAULT NULL COMMENT 'd13',
                                                           `d14` decimal(24,6) DEFAULT NULL COMMENT 'd14',
                                                           `d15` decimal(24,6) DEFAULT NULL COMMENT 'd15',
                                                           `d16` decimal(24,6) DEFAULT NULL COMMENT 'd16',
                                                           `d17` decimal(24,6) DEFAULT NULL COMMENT 'd17',
                                                           `d18` decimal(24,6) DEFAULT NULL COMMENT 'd18',
                                                           `d19` decimal(24,6) DEFAULT NULL COMMENT 'd19',
                                                           `d20` decimal(24,6) DEFAULT NULL COMMENT 'd20',
                                                           `d21` decimal(24,6) DEFAULT NULL COMMENT 'd21',
                                                           `d22` decimal(24,6) DEFAULT NULL COMMENT 'd22',
                                                           `d23` decimal(24,6) DEFAULT NULL COMMENT 'd23',
                                                           `d24` decimal(24,6) DEFAULT NULL COMMENT 'd24',
                                                           `d25` decimal(24,6) DEFAULT NULL COMMENT 'd25',
                                                           `d26` decimal(24,6) DEFAULT NULL COMMENT 'd26',
                                                           `d27` decimal(24,6) DEFAULT NULL COMMENT 'd27',
                                                           `d28` decimal(24,6) DEFAULT NULL COMMENT 'd28',
                                                           `d29` decimal(24,6) DEFAULT NULL COMMENT 'd29',
                                                           `d30` decimal(24,6) DEFAULT NULL COMMENT 'd30',
                                                           `d31` decimal(24,6) DEFAULT NULL COMMENT 'd31',
                                                           `tenant_id` varchar(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                           `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                                           `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                                           `created_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                           `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                           `updated_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                           `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='IE产能打折月度（人力）表';