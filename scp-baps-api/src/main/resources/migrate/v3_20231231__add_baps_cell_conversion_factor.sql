DROP TABLE IF EXISTS baps_cell_conversion_factor;
CREATE TABLE baps_cell_conversion_factor(
                                            id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                            cells_type_id BIGINT    COMMENT '电池类型Id' ,
                                            cells_type VARCHAR(100)    COMMENT '电池类型' ,
                                            conversion_factor DECIMAL(24,6)    COMMENT '折算系数' ,
                                            tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                            opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                            is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                            created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                            created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                            updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                            updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                            PRIMARY KEY (id)
)  COMMENT = '万片与兆瓦折算系数';
