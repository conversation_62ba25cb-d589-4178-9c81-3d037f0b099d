DROP TABLE IF EXISTS baps_cell_base_capacity;
CREATE TABLE baps_cell_base_capacity(
                                       id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                       start_time DATE NOT NULL   COMMENT '开始时间' ,
                                       end_time DATE NOT NULL   COMMENT '结束时间' ,
                                       cells_type VARCHAR(100) NOT NULL   COMMENT '电池类型' ,
                                       is_oversea VARCHAR(20) NOT NULL   COMMENT '国内海外' ,
                                       base_place VARCHAR(50) NOT NULL   COMMENT '生产基地' ,
                                       workshop VARCHAR(50) NOT NULL   COMMENT '生产车间' ,
                                       workunit VARCHAR(50) NOT NULL   COMMENT '生产单元' ,
                                       number_line INT NOT NULL   COMMENT '线体数量' ,
                                       usage_line INT NOT NULL   COMMENT '可用线体数量' ,
                                       single_capacity DECIMAL(24,6) NOT NULL   COMMENT '产能（单线）' ,
                                       unit VARCHAR(20) NOT NULL   COMMENT '单位' ,
                                       tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                       opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                       is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                       created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                       created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                       updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                       updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                       PRIMARY KEY (id)
)  COMMENT = 'IE产能表';

DROP TABLE IF EXISTS baps_cell_base_capacity_discounts;
CREATE TABLE baps_cell_base_capacity_discounts(
                                                 id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                                 start_time DATE NOT NULL   COMMENT '开始时间' ,
                                                 end_time DATE NOT NULL   COMMENT '结束时间' ,
                                                 base_place VARCHAR(50) NOT NULL   COMMENT '生产基地' ,
                                                 workshop VARCHAR(50) NOT NULL   COMMENT '生产车间' ,
                                                 workunit VARCHAR(50) NOT NULL   COMMENT '生产单元' ,
                                                 ratio DECIMAL(24,6) NOT NULL   COMMENT '比例' ,
                                                 ie_confirm INT NOT NULL   COMMENT 'IE确认' ,
                                                 plan_confirm INT NOT NULL   COMMENT '计划确认' ,
                                                 importer VARCHAR(255) NOT NULL   COMMENT '导入人' ,
                                                 discount_version VARCHAR(255) NOT NULL   COMMENT '版本号' ,
                                                 tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                                 opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                                 is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                                 created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                                 created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                                 updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                                 updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                                 PRIMARY KEY (id)
)  COMMENT = 'IE产能打折（人力）表';

DROP TABLE IF EXISTS baps_cell_grade_capacity;
CREATE TABLE baps_cell_grade_capacity(
                                        id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                        cells_type VARCHAR(100) NOT NULL   COMMENT '电池类型' ,
                                        base_place VARCHAR(50) NOT NULL   COMMENT '生产基地' ,
                                        workshop VARCHAR(50) NOT NULL   COMMENT '生产车间' ,
                                        workunit VARCHAR(50) NOT NULL   COMMENT '生产单元' ,
                                        line_number INT NOT NULL   COMMENT '线体数量' ,
                                        month VARCHAR(10) NOT NULL   COMMENT '月份' ,
                                        capacity_quantity DECIMAL(24,6) NOT NULL   COMMENT '产能' ,
                                        unit VARCHAR(20) NOT NULL   COMMENT '单位' ,
                                        d1 DECIMAL(24,6) NOT NULL   COMMENT '1号' ,
                                        d2 DECIMAL(24,6) NOT NULL   COMMENT '2号' ,
                                        d3 DECIMAL(24,6) NOT NULL   COMMENT '3号' ,
                                        d4 DECIMAL(24,6) NOT NULL   COMMENT '4号' ,
                                        d5 DECIMAL(24,6) NOT NULL   COMMENT '5号' ,
                                        d6 DECIMAL(24,6) NOT NULL   COMMENT '6号' ,
                                        d7 DECIMAL(24,6) NOT NULL   COMMENT '7号' ,
                                        d8 DECIMAL(24,6) NOT NULL   COMMENT '8号' ,
                                        d9 DECIMAL(24,6) NOT NULL   COMMENT '9号' ,
                                        d10 DECIMAL(24,6) NOT NULL   COMMENT '10号' ,
                                        d11 DECIMAL(24,6) NOT NULL   COMMENT '11号' ,
                                        d12 DECIMAL(24,6) NOT NULL   COMMENT '12号' ,
                                        d13 DECIMAL(24,6) NOT NULL   COMMENT '13号' ,
                                        d14 DECIMAL(24,6) NOT NULL   COMMENT '14号' ,
                                        d15 DECIMAL(24,6) NOT NULL   COMMENT '15号' ,
                                        d16 DECIMAL(24,6) NOT NULL   COMMENT '16号' ,
                                        d17 DECIMAL(24,6) NOT NULL   COMMENT '17号' ,
                                        d18 DECIMAL(24,6) NOT NULL   COMMENT '18号' ,
                                        d19 DECIMAL(24,6) NOT NULL   COMMENT '19号' ,
                                        d20 DECIMAL(24,6) NOT NULL   COMMENT '20号' ,
                                        d21 DECIMAL(24,6) NOT NULL   COMMENT '21号' ,
                                        d22 DECIMAL(24,6) NOT NULL   COMMENT '22号' ,
                                        d23 DECIMAL(24,6) NOT NULL   COMMENT '23号' ,
                                        d24 DECIMAL(24,6) NOT NULL   COMMENT '24号' ,
                                        d25 DECIMAL(24,6) NOT NULL   COMMENT '25号' ,
                                        d26 DECIMAL(24,6) NOT NULL   COMMENT '26号' ,
                                        d27 DECIMAL(24,6) NOT NULL   COMMENT '27号' ,
                                        d28 DECIMAL(24,6) NOT NULL   COMMENT '28号' ,
                                        d29 DECIMAL(24,6) NOT NULL   COMMENT '29号' ,
                                        d30 DECIMAL(24,6) NOT NULL   COMMENT '30号' ,
                                        d31 DECIMAL(24,6) NOT NULL   COMMENT '31号' ,
                                        remark VARCHAR(255)    COMMENT '备注' ,
                                        tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                        opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                        is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                        created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                        created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                        updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                        updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                        PRIMARY KEY (id)
)  COMMENT = '爬坡产能表';


DROP TABLE IF EXISTS baps_cell_loss;
CREATE TABLE baps_cell_loss(
                              id BIGINT NOT NULL   COMMENT 'ID主键' ,
                              workshop VARCHAR(50) NOT NULL   COMMENT '生产车间' ,
                              start_month VARCHAR(10) NOT NULL   COMMENT '起始月份' ,
                              end_month VARCHAR(10) NOT NULL   COMMENT '结束月份' ,
                              old_product VARCHAR(100) NOT NULL   COMMENT '电池类型1' ,
                              new_product VARCHAR(100) NOT NULL   COMMENT '电池类型2' ,
                              loss_time INT NOT NULL   COMMENT '损失时间' ,
                              unit VARCHAR(20) NOT NULL   COMMENT '单位' ,
                              tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                              opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                              is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                              created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                              created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                              updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                              updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                              PRIMARY KEY (id)
)  COMMENT = '产能切换损失表';



DROP TABLE IF EXISTS baps_cell_parallel_loss;
CREATE TABLE baps_cell_parallel_loss(
                                       id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                       cells_type VARCHAR(100) NOT NULL   COMMENT '电池类型' ,
                                       base_place VARCHAR(50) NOT NULL   COMMENT '生产基地' ,
                                       workshop VARCHAR(50) NOT NULL   COMMENT '生产车间' ,
                                       workunit VARCHAR(50) NOT NULL   COMMENT '生产单元' ,
                                       line_number INT NOT NULL   COMMENT '线体数量' ,
                                       month VARCHAR(10) NOT NULL   COMMENT '月份' ,
                                       loss DECIMAL(24,6) NOT NULL   COMMENT '损失产能' ,
                                       unit VARCHAR(20) NOT NULL   COMMENT '单位' ,
                                       tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                       opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                       is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                       created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                       created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                       updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                       updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                       PRIMARY KEY (id)
)  COMMENT = '产能并行损失表';



DROP TABLE IF EXISTS baps_cell_fine;
CREATE TABLE baps_cell_fine(
                              id BIGINT NOT NULL   COMMENT 'ID主键' ,
                              is_oversea VARCHAR(255) NOT NULL   COMMENT '国内海外' ,
                              cells_type VARCHAR(100) NOT NULL   COMMENT '电池类型' ,
                              base_place VARCHAR(50) NOT NULL   COMMENT '生产基地' ,
                              workshop VARCHAR(50) NOT NULL   COMMENT '生产车间' ,
                              year INT NOT NULL   COMMENT '年份' ,
                              m1 DECIMAL(24,6) NOT NULL   COMMENT '1月' ,
                              m2 DECIMAL(24,6) NOT NULL   COMMENT '2月' ,
                              m3 DECIMAL(24,6) NOT NULL   COMMENT '3月' ,
                              m4 DECIMAL(24,6) NOT NULL   COMMENT '4月' ,
                              m5 DECIMAL(24,6) NOT NULL   COMMENT '5月' ,
                              m6 DECIMAL(24,6) NOT NULL   COMMENT '6月' ,
                              m7 DECIMAL(24,6) NOT NULL   COMMENT '7月' ,
                              m8 DECIMAL(24,6) NOT NULL   COMMENT '8月' ,
                              m9 DECIMAL(24,6) NOT NULL   COMMENT '9月' ,
                              m10 DECIMAL(24,6) NOT NULL   COMMENT '10月' ,
                              m11 DECIMAL(24,6) NOT NULL   COMMENT '11月' ,
                              m12 DECIMAL(24,6) NOT NULL   COMMENT '12月' ,
                              tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                              opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                              is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                              created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                              created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                              updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                              updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                              PRIMARY KEY (id)
)  COMMENT = '电池良率表';



DROP TABLE IF EXISTS baps_cell_fine_mid;
CREATE TABLE baps_cell_fine_mid(
                                  id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                  is_oversea VARCHAR(20) NOT NULL   COMMENT '国内/海外' ,
                                  year INT NOT NULL   COMMENT '年份' ,
                                  parameter_type VARCHAR(20) NOT NULL   COMMENT '满产/排产' ,
                                  product_type VARCHAR(50) NOT NULL   COMMENT '产品分类' ,
                                  product_category VARCHAR(50) NOT NULL   COMMENT '品类' ,
                                  main_grid VARCHAR(50) NOT NULL   COMMENT '主栅' ,
                                  crystal_type VARCHAR(50) NOT NULL   COMMENT '晶体类型' ,
                                  crystal_spec VARCHAR(50) NOT NULL   COMMENT '单晶/多晶' ,
                                  workshop VARCHAR(50) NOT NULL   COMMENT '车间' ,
                                  m1 DECIMAL(24,6) NOT NULL   COMMENT '1月数' ,
                                  m2 DECIMAL(24,6) NOT NULL   COMMENT '2月数' ,
                                  m3 DECIMAL(24,6) NOT NULL   COMMENT '3月数' ,
                                  m4 DECIMAL(24,6) NOT NULL   COMMENT '4月数' ,
                                  m5 DECIMAL(24,6) NOT NULL   COMMENT '5月数' ,
                                  m6 DECIMAL(24,6) NOT NULL   COMMENT '6月数' ,
                                  m7 DECIMAL(24,6) NOT NULL   COMMENT '7月数' ,
                                  m8 DECIMAL(24,6) NOT NULL   COMMENT '8月数' ,
                                  m9 DECIMAL(24,6) NOT NULL   COMMENT '9月数' ,
                                  m10 DECIMAL(24,6) NOT NULL   COMMENT '10月数' ,
                                  m11 DECIMAL(24,6) NOT NULL   COMMENT '11月数' ,
                                  m12 DECIMAL(24,6) NOT NULL   COMMENT '12月数' ,
                                  tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                  opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                  is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                  created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                  created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                  updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                  updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                  PRIMARY KEY (id)
)  COMMENT = '电池良率中间表';



DROP TABLE IF EXISTS baps_cell_type_mid;
CREATE TABLE baps_cell_type_mid(
                                   id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                   cells_type VARCHAR(100) NOT NULL   COMMENT '电池类型' ,
                                   cells_type_left VARCHAR(100) NOT NULL   COMMENT '电池类型左' ,
                                   tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                   opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                   is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                   created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                   created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                   updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                   updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                   PRIMARY KEY (id)
)  COMMENT = '电池类型转换表';



DROP TABLE IF EXISTS baps_cell_return_order;
CREATE TABLE baps_cell_return_order(
                                      id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                      is_oversea VARCHAR(20) NOT NULL   COMMENT '是否海外' ,
                                      base_place VARCHAR(50) NOT NULL   COMMENT '生产基地' ,
                                      cells_type VARCHAR(100) NOT NULL   COMMENT '电池类型' ,
                                      item_fivea VARCHAR(50) NOT NULL   COMMENT '电池料号' ,
                                      work_cell DECIMAL(24,6) NOT NULL   COMMENT '效率值' ,
                                      note VARCHAR(50) NOT NULL   COMMENT '对应标识' ,
                                      month VARCHAR(20) NOT NULL   COMMENT '月份' ,
                                      date DATE NOT NULL   COMMENT '日期' ,
                                      cell_qty DECIMAL(24,6) NOT NULL   COMMENT '数量' ,
                                      tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                      opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                      is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                      created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                      created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                      updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                      updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                      PRIMARY KEY (id)
)  COMMENT = '返司';



DROP TABLE IF EXISTS baps_cell_bom_manufacturing;
CREATE TABLE baps_cell_bom_manufacturing(
                                           id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                           cells_type VARCHAR(100) NOT NULL   COMMENT '电池类型' ,
                                           base_place VARCHAR(50) NOT NULL   COMMENT '生产基地' ,
                                           month VARCHAR(20) NOT NULL   COMMENT '月份' ,
                                           total_line INT NOT NULL   COMMENT '产线总数' ,
                                           usage_line INT NOT NULL   COMMENT '可用产线数' ,
                                           capacity_quantity DECIMAL(24,6) NOT NULL   COMMENT '产能（单元）' ,
                                           unit VARCHAR(20) NOT NULL   COMMENT '单位' ,
                                           remark VARCHAR(255)    COMMENT '备注' ,
                                           process_code VARCHAR(50) NOT NULL   COMMENT '工序代码' ,
                                           process_id INT NOT NULL   COMMENT '工序编号' ,
                                           instruction_type VARCHAR(50) NOT NULL   COMMENT '使用类型' ,
                                           instruction_code VARCHAR(50) NOT NULL   COMMENT '使用代码' ,
                                           version VARCHAR(100) NOT NULL   COMMENT '版本' ,
                                           is_oversea VARCHAR(20) NOT NULL   COMMENT '国内国外' ,
                                           work_unit VARCHAR(50) NOT NULL   COMMENT '生产单元' ,
                                           manufacturing VARCHAR(50) NOT NULL   COMMENT '制造' ,
                                           rate INT NOT NULL   COMMENT '优先级' ,
                                           start_date DATE NOT NULL   COMMENT '开始日期' ,
                                           end_date DATE NOT NULL   COMMENT '结束日期' ,
                                           quantity DECIMAL(24,6) NOT NULL   COMMENT '数量' ,
                                           flag INT NOT NULL   COMMENT '标识' ,
                                           fine_percent DECIMAL(24,6) NOT NULL   COMMENT '符合率' ,
                                           grade_work_date VARCHAR(10) NOT NULL   COMMENT '月份' ,
                                           tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                           opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                           is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                           created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                           created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                           updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                           updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                           PRIMARY KEY (id)
)  COMMENT = '制造BOM表';

