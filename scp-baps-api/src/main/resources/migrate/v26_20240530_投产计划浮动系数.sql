CREATE TABLE `baps_cell_plan_qty_fluctuation_coefficient` (
                                                              `id` bigint NOT NULL COMMENT 'ID主键',
                                                              `start_time` date DEFAULT NULL COMMENT '开始时间',
                                                              `end_time` date DEFAULT NULL COMMENT '结束时间',
                                                              `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '生产车间',
                                                              `fluctuation_coefficient` decimal(24,6) NOT NULL COMMENT '计划数量浮动系数',
                                                              `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                              `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                                              `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                                              `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                              `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                              `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                              `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='投产计划浮动系数';