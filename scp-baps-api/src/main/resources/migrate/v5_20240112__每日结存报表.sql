DROP TABLE IF EXISTS baps_cell_daily_balance;
CREATE TABLE baps_cell_daily_balance(
                                        id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                        is_oversea_id BIGINT    COMMENT '国内外Id' ,
                                        is_oversea VARCHAR(100)    COMMENT '国内外' ,
                                        cell_type_id BIGINT    COMMENT '电池类型Id' ,
                                        cell_type VARCHAR(100)    COMMENT '电池类型' ,
                                        month VARCHAR(20)    COMMENT '月份' ,
                                        project VARCHAR(100)    COMMENT '类别' ,
                                        h_trace VARCHAR(100)    COMMENT 'H追溯' ,
                                        dt VARCHAR(50)    COMMENT '低碳' ,
                                        aesthetics VARCHAR(50)    COMMENT '美学' ,
                                        transparent_double_glass VARCHAR(50)    COMMENT '透明双玻' ,
                                        d1 DECIMAL(24,6)    COMMENT '1号' ,
                                        d2 DECIMAL(24,6)    COMMENT '2号' ,
                                        d3 DECIMAL(24,6)    COMMENT '3号' ,
                                        d4 DECIMAL(24,6)    COMMENT '4号' ,
                                        d5 DECIMAL(24,6)    COMMENT '5号' ,
                                        d6 DECIMAL(24,6)    COMMENT '6号' ,
                                        d7 DECIMAL(24,6)    COMMENT '7号' ,
                                        d8 DECIMAL(24,6)    COMMENT '8号' ,
                                        d9 DECIMAL(24,6)    COMMENT '9号' ,
                                        d10 DECIMAL(24,6)    COMMENT '10号' ,
                                        d11 DECIMAL(24,6)    COMMENT '11号' ,
                                        d12 DECIMAL(24,6)    COMMENT '12号' ,
                                        d13 DECIMAL(24,6)    COMMENT '13号' ,
                                        d14 DECIMAL(24,6)    COMMENT '14号' ,
                                        d15 DECIMAL(24,6)    COMMENT '15号' ,
                                        d16 DECIMAL(24,6)    COMMENT '16号' ,
                                        d17 DECIMAL(24,6)    COMMENT '17号' ,
                                        d18 DECIMAL(24,6)    COMMENT '18号' ,
                                        d19 DECIMAL(24,6)    COMMENT '19号' ,
                                        d20 DECIMAL(24,6)    COMMENT '20号' ,
                                        d21 DECIMAL(24,6)    COMMENT '21号' ,
                                        d22 DECIMAL(24,6)    COMMENT '22号' ,
                                        d23 DECIMAL(24,6)    COMMENT '23号' ,
                                        d24 DECIMAL(24,6)    COMMENT '24号' ,
                                        d25 DECIMAL(24,6)    COMMENT '25号' ,
                                        d26 DECIMAL(24,6)    COMMENT '26号' ,
                                        d27 DECIMAL(24,6)    COMMENT '27号' ,
                                        d28 DECIMAL(24,6)    COMMENT '28号' ,
                                        d29 DECIMAL(24,6)    COMMENT '29号' ,
                                        d30 DECIMAL(24,6)    COMMENT '30号' ,
                                        d31 DECIMAL(24,6)    COMMENT '31号' ,
                                        tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                        opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                        is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                        created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                        created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                        updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                        updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                        PRIMARY KEY (id)
)  COMMENT = '每日结存报表';
