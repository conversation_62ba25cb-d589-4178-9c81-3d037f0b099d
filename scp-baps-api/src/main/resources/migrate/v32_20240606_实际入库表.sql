CREATE TABLE `baps_cell_instock_plan_finish` (
                                                 `id` bigint NOT NULL COMMENT 'ID主键',
                                                 `workshop` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产车间',
                                                 `workshop_id` bigint DEFAULT NULL COMMENT '生产车间Id',
                                                 `cells_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池类型',
                                                 `cells_type_id` bigint DEFAULT NULL COMMENT '电池类型Id',
                                                 `item_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '5A料号',
                                                 `production_grade_id` bigint DEFAULT NULL COMMENT '产品等级Id',
                                                 `production_grade` varchar(255) DEFAULT NULL COMMENT '产品等级',
                                                 `cell_source_id` bigint DEFAULT NULL COMMENT '片源种类Id',
                                                 `cell_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '片源种类',
                                                 `transparent_double_glass_id` bigint DEFAULT NULL COMMENT '透明双玻Id',
                                                 `transparent_double_glass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '透明双玻',
                                                 `h_trace_id` bigint DEFAULT NULL COMMENT 'H追溯',
                                                 `h_trace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'H追溯',
                                                 `is_regional_country` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否小区域国家',
                                                 `regional_country_id` bigint DEFAULT NULL COMMENT '小区域国家',
                                                 `regional_country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '小区域国家',
                                                 `finished_date` datetime DEFAULT NULL COMMENT '实际入库时间',
                                                 `finished_qty` decimal(24,6) DEFAULT NULL COMMENT '实际入库数量（片）',
                                                 `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
                                                 `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                 `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                                 `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                                 `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实际入库表';