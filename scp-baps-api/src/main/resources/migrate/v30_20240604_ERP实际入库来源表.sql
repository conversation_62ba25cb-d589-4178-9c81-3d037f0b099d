CREATE TABLE `baps_erp_wip_issue` (
                                      `id` bigint NOT NULL COMMENT '主键',
                                      `transaction_quantity` decimal(24,6) DEFAULT NULL COMMENT '事务数量',
                                      `item_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '料号',
                                      `transfer_organization_id` bigint DEFAULT NULL COMMENT '交易库存组织Id',
                                      `transaction_uom_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '事务单位编码',
                                      `organization_id` bigint DEFAULT NULL COMMENT '库存组织ID',
                                      `item_description` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '物料描述',
                                      `locator_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '货位',
                                      `transaction_source_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '事务来源类型名',
                                      `work_order` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '工单号',
                                      `organization_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '库存组织名',
                                      `transaction_type_id` bigint DEFAULT NULL COMMENT '事务类型Id',
                                      `lot_primary_quantity` decimal(24,6) DEFAULT NULL COMMENT 'lot主要数据量',
                                      `subinventory_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '子库存',
                                      `lot_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'lot编码',
                                      `transaction_date` datetime DEFAULT NULL COMMENT '事务处理日期',
                                      `transaction_source_id` bigint DEFAULT NULL COMMENT '事务来源Id',
                                      `transaction_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '事务类型名',
                                      `transaction_id` bigint DEFAULT NULL COMMENT '事务Id',
                                      `transaction_source_type_id` bigint DEFAULT NULL COMMENT '事务来源类型Id',
                                      `inventory_item_id` bigint DEFAULT NULL COMMENT '物料Id',
                                      `organization_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '库存组织编号',
                                      `locator_id` bigint DEFAULT NULL COMMENT '货位Id',
                                      `primary_quantity` decimal(24,6) DEFAULT NULL COMMENT '主要数量',
                                      `lot_transaction_quantity` decimal(24,6) DEFAULT NULL COMMENT 'lot事务处理数量',
                                      `creation_date` datetime DEFAULT NULL COMMENT 'erp创建时间',
                                      `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
                                      `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户号',
                                      `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                      `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                      `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                      `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
                                      `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='Erp实际入库来源表';