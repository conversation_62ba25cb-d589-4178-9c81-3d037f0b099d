ALTER TABLE `scp_baps`.`baps_cell_instock_plan_finish`
    ADD COLUMN `is_oversea_id` bigint NULL COMMENT '国内海外Id' AFTER `id`,
ADD COLUMN `is_oversea` varchar(100) NULL COMMENT '国内海外' AFTER `is_oversea_id`,
ADD COLUMN `base_place_id` bigint NULL COMMENT '生产基地Id' AFTER `is_oversea`,
ADD COLUMN `base_place` varchar(100) NULL COMMENT '生产基地' AFTER `base_place_id`;
ALTER TABLE `scp_baps`.`baps_cell_instock_plan_finish`
    ADD COLUMN `aesthetics_id` bigint NULL COMMENT '美学Id' AFTER `h_trace`,
ADD COLUMN `aesthetics` varchar(100) NULL COMMENT '美学' AFTER `aesthetics_id`;