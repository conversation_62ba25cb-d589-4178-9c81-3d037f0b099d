ALTER TABLE `scp_baps`.`baps_cell_base_capacity`
    ADD COLUMN `is_single_glass` varchar(50) NULL COMMENT '能否生产单玻' AFTER `unit`,
ADD COLUMN `is_dt` varchar(50) NULL COMMENT '能否生产低碳' AFTER `is_single_glass`,
ADD COLUMN `is_regional_country` varchar(50) NULL COMMENT '能否生产小区域国家' AFTER `is_dt`,
ADD COLUMN `is_h_change_flag` varchar(50) NULL COMMENT '能否生产H兼容' AFTER `is_regional_country`,
ADD COLUMN `is_h_trace` varchar(50) NULL COMMENT '能否生产H追溯' AFTER `is_h_change_flag`;
ALTER TABLE `scp_baps`.`baps_cell_bom_manufacturing`
    ADD COLUMN `is_single_glass` varchar(50) NULL COMMENT '能否生产单玻' AFTER `unit`,
ADD COLUMN `is_dt` varchar(50) NULL COMMENT '能否生产低碳' AFTER `is_single_glass`,
ADD COLUMN `is_regional_country` varchar(50) NULL COMMENT '能否生产小区域国家' AFTER `is_dt`,
ADD COLUMN `is_h_change_flag` varchar(50) NULL COMMENT '能否生产H兼容' AFTER `is_regional_country`,
ADD COLUMN `is_h_trace` varchar(50) NULL COMMENT '能否生产H追溯' AFTER `is_h_change_flag`;
ALTER TABLE `scp_baps`.`baps_cell_bom_manufacturing_summary`
    ADD COLUMN `is_single_glass` varchar(50) NULL COMMENT '能否生产单玻' AFTER `unit`,
ADD COLUMN `is_dt` varchar(50) NULL COMMENT '能否生产低碳' AFTER `is_single_glass`,
ADD COLUMN `is_regional_country` varchar(50) NULL COMMENT '能否生产小区域国家' AFTER `is_dt`,
ADD COLUMN `is_h_change_flag` varchar(50) NULL COMMENT '能否生产H兼容' AFTER `is_regional_country`,
ADD COLUMN `is_h_trace` varchar(50) NULL COMMENT '能否生产H追溯' AFTER `is_h_change_flag`;
ALTER TABLE `scp_baps`.`baps_cell_grade_capacity`
    ADD COLUMN `is_single_glass` varchar(50) NULL COMMENT '能否生产单玻' AFTER `unit`,
ADD COLUMN `is_dt` varchar(50) NULL COMMENT '能否生产低碳' AFTER `is_single_glass`,
ADD COLUMN `is_regional_country` varchar(50) NULL COMMENT '能否生产小区域国家' AFTER `is_dt`,
ADD COLUMN `is_h_change_flag` varchar(50) NULL COMMENT '能否生产H兼容' AFTER `is_regional_country`,
ADD COLUMN `is_h_trace` varchar(50) NULL COMMENT '能否生产H追溯' AFTER `is_h_change_flag`;