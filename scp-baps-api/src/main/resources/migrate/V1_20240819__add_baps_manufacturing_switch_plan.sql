-- scp_baps.baps_manufacturing_switch_plan definition

CREATE TABLE `baps_manufacturing_switch_plan` (
                                                  `id` bigint NOT NULL COMMENT 'ID主键',
                                                  `is_oversea` varchar(255) DEFAULT NULL COMMENT '国内海外',
                                                  `is_oversea_id` bigint DEFAULT NULL COMMENT '国内海外Id',
                                                  `manufacturing` varchar(50) DEFAULT NULL COMMENT '制造工艺',
                                                  `base_place` varchar(50) DEFAULT NULL COMMENT '生产基地',
                                                  `base_place_id` bigint DEFAULT NULL COMMENT '生产基地Id',
                                                  `workshop` varchar(50) DEFAULT NULL COMMENT '生产车间',
                                                  `workshop_id` bigint DEFAULT NULL COMMENT '生产车间Id',
                                                  `workunit` varchar(50) DEFAULT NULL COMMENT '生产单元',
                                                  `workunit_id` bigint DEFAULT NULL COMMENT '生产单元Id',
                                                  `production_line` varchar(100) DEFAULT NULL COMMENT '生产产线',
                                                  `cells_type` varchar(100) DEFAULT NULL COMMENT '电池类型',
                                                  `cells_type_id` bigint DEFAULT NULL COMMENT '电池类型Id',
                                                  `capacity_quantity` decimal(24,6) NOT NULL COMMENT '产能',
                                                  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                                  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                                  `tenant_id` varchar(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                  `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                                  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                                  `created_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `updated_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='制造工艺切换计划维护';