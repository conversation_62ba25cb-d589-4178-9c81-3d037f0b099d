CREATE TABLE `baps_main_grid_spacing_rule` (
                                               `id` bigint NOT NULL COMMENT 'ID',
                                               `battery_type` varchar(255) DEFAULT NULL COMMENT '电池类型',
                                               `battery_type_name` varchar(255) DEFAULT NULL COMMENT '电池类型名',
                                               `item_workshop` varchar(255) DEFAULT NULL COMMENT '组件车间',
                                               `item_workshop_name` varchar(255) DEFAULT NULL COMMENT '组件车间名',
                                               `battery_workshop` varchar(1024) DEFAULT NULL COMMENT '电池车间',
                                               `battery_workshop_name` varchar(1024) DEFAULT NULL COMMENT '电池车间名',
                                               `main_grid_spacing` varchar(255) DEFAULT NULL COMMENT '主栅间距',
                                               `main_grid_spacing_name` varchar(255) DEFAULT NULL COMMENT '主栅间距名',
                                               `effective_start_date` date DEFAULT NULL COMMENT '有效日期_起',
                                               `effective_end_date` date DEFAULT NULL COMMENT '有效日期_止',
                                               `tenant_id` varchar(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                               `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                               `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                               `created_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
                                               `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `updated_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
                                               `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电池主栅间距规则';