DROP TABLE IF EXISTS baps_cell_shippable_lead_time;
CREATE TABLE baps_cell_shippable_lead_time(
                                              id BIGINT NOT NULL   COMMENT 'ID主键' ,
                                              lead_type_id BIGINT NOT NULL   COMMENT '类型ID' ,
                                              lead_type VARCHAR(255) NOT NULL   COMMENT '类型' ,
                                              cell_type_id BIGINT NOT NULL   COMMENT '电池类型Id' ,
                                              cell_type VARCHAR(255) NOT NULL   COMMENT '电池类型' ,
                                              base_place_id BIGINT NOT NULL   COMMENT '生产基地Id' ,
                                              base_place VARCHAR(255) NOT NULL   COMMENT '生产基地' ,
                                              work_shop_id BIGINT NOT NULL   COMMENT '生产车间Id' ,
                                              work_shop VARCHAR(255) NOT NULL   COMMENT '生产车间' ,
                                              work_unit_id BIGINT NOT NULL   COMMENT '生产单元Id' ,
                                              work_unit VARCHAR(255) NOT NULL   COMMENT '生产单元' ,
                                              buff_days INT    COMMENT '预留天数' ,
                                              validate_time DATE    COMMENT '验证时间' ,
                                              tenant_id VARCHAR(32) NOT NULL  DEFAULT 'TRINA' COMMENT '租户号' ,
                                              opt_counter INT NOT NULL  DEFAULT 1 COMMENT '乐观锁' ,
                                              is_deleted INT NOT NULL  DEFAULT 0 COMMENT '是否删除,0=正常，1=删除' ,
                                              created_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '创建人' ,
                                              created_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
                                              updated_by VARCHAR(32) NOT NULL  DEFAULT '-1' COMMENT '更新人' ,
                                              updated_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
                                              PRIMARY KEY (id)
)  COMMENT = '可发货计划提前期';