ALTER TABLE `scp_baps`.`baps_cell_plan_line`
    ADD COLUMN `batch_no` varchar(255) NULL COMMENT '批次号' AFTER `id`,
ADD COLUMN `data_type` int NULL DEFAULT 0 COMMENT '0代表排产数据，1覆盖继承过来的' AFTER `batch_no`;
ALTER TABLE `scp_baps`.`baps_cell_instock_plan`
    ADD COLUMN `batch_no` varchar(255) NULL COMMENT '批次号' AFTER `id`,
ADD COLUMN `data_type` int NULL DEFAULT 0 COMMENT '0代表排产数据，1覆盖继承过来的' AFTER `batch_no`;