package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellInstockPlan;
import com.trinasolar.scp.baps.domain.entity.CellPlanLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 入库计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:13:25
 */
@Repository
public interface CellInstockPlanRepository extends JpaRepository<CellInstockPlan, Long>, QuerydslPredicateExecutor<CellInstockPlan> {
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query("delete from CellInstockPlan c where c.isOversea = :isOversea and c.month = :month and c.version = :version")
    void deleteByVersion(@Param("isOversea") String isOversea,@Param("month") String month,@Param("version") String version);


    @Query(value = "from CellInstockPlan c where c.bbomId = :bbomId")
    public CellInstockPlan selectByBbomid(@Param("bbomId") Long bbomId);
    @Modifying
    @Query(value = "update CellInstockPlan c set  c.itemCode = :itemCode where c.bbomId = :bbomId")
    public  void updateItemCode(@Param("bbomId") Long bbomId,@Param("itemCode") String itemCode);

    @Query(value = "SELECT bcip.month as month, bcip.isOversea as isOversea, MAX(bcip.version) as version FROM CellInstockPlan bcip GROUP BY bcip.month, bcip.isOversea")
    List<Map<String,String>> getMaxVersion();
}
