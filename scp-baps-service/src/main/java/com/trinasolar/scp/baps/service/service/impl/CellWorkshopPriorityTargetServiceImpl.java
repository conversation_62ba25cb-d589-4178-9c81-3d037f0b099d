package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.baps.domain.convert.CellWorkshopPriorityTargetDEConvert;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.excel.CellWorkshopPriorityTargetExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellWorkshopPriorityTargetQuery;
import com.trinasolar.scp.baps.domain.save.CellWorkshopPriorityTargetSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellWorkshopPriorityTargetRepository;
import com.trinasolar.scp.baps.service.service.CellFineService;
import com.trinasolar.scp.baps.service.service.CellWorkshopPriorityTargetService;
import com.trinasolar.scp.baps.service.service.PowerEfficiencyService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.baps.domain.dto.CellWorkshopPriorityTargetDTO;
import com.trinasolar.scp.baps.domain.dto.PowerEfficiencyDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * 车间优先度效率目标值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Slf4j
@Service("cellWorkshopPriorityTargetService")
@RequiredArgsConstructor
public class CellWorkshopPriorityTargetServiceImpl implements CellWorkshopPriorityTargetService {
    private static final QCellWorkshopPriorityTarget qCellWorkshopPriorityTarget = QCellWorkshopPriorityTarget.cellWorkshopPriorityTarget;

    private final CellWorkshopPriorityTargetDEConvert convert;
    private final CellWorkshopPriorityTargetRepository repository;
    //scp-aps效率分布service-feign
    private final PowerEfficiencyService powerEfficiencyService;

    @Override
    public Page<CellWorkshopPriorityTargetDTO> queryByPage(CellWorkshopPriorityTargetQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellWorkshopPriorityTarget> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellWorkshopPriorityTargetQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellWorkshopPriorityTarget.id.eq(query.getId()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellWorkshopPriorityTarget.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellWorkshopPriorityTarget.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getEfficiency())) {
            booleanBuilder.and(qCellWorkshopPriorityTarget.efficiency.eq(query.getEfficiency()));
        }
    }

    @Override
    public CellWorkshopPriorityTargetDTO queryById(Long id) {
        CellWorkshopPriorityTarget queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellWorkshopPriorityTargetDTO save(CellWorkshopPriorityTargetSaveDTO saveDTO) {
        CellWorkshopPriorityTarget newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellWorkshopPriorityTarget());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellWorkshopPriorityTargetQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellWorkshopPriorityTargetDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("没有数据");
        }
        // dto数据转为ExcelData数据
        List<CellWorkshopPriorityTargetExcelDTO> datas = convert.toExcelDTO(dtos);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellWorkshopPriorityTargetExcelDTO.class, datas, JSON.toJSONString(query), "车间优先度效率目标值", response);
    }
    /**
     * 读取第第三方接口scp-aps效率分布的接口后进行数据整合
     * @return
     */
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void refreshData() {
        //1、获取scp-aps 效率分布数据（其它微服务）保存到baps_cell_workshop_priority_target_mid中间表
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        List<PowerEfficiencyDTO> powerEfficiencyDTOS = powerEfficiencyService.queryByPageCN();
        if (powerEfficiencyDTOS.size() > 0) {
            powerEfficiencyDTOS.remove(0);//删除第一行列信息
        }

        //1.1筛选出供应类型是自产，电池类型包含H的数据
        powerEfficiencyDTOS = powerEfficiencyDTOS.stream().filter(dto -> StringUtils.equals(dto.getSupplyType(), "自产") && StringUtils.contains(dto.getCellType(), "H")).collect(Collectors.toList());
        //1.2 去除数据中的%
        powerEfficiencyDTOS.stream().forEach(dto -> {
            if (StringUtils.isNotEmpty(dto.getActualEfficiency())) {
                dto.setActualEfficiency(MapStrutUtil.removePercentageIgnore(dto.getActualEfficiency()).toString());

            }
            if (StringUtils.isNotEmpty(dto.getTargetEfficiency())) {
                dto.setTargetEfficiency(MapStrutUtil.removePercentageIgnore(dto.getTargetEfficiency()).toString());
            }
        });
        //1.2 解决实际值可能是空的问题
        List<PowerEfficiencyDTO> powerEfficiencyDTOSCopy = powerEfficiencyDTOS.stream().collect(Collectors.toList());
        powerEfficiencyDTOS.stream().forEach(powerEfficiencyDTO -> {

            if (StringUtils.isEmpty(powerEfficiencyDTO.getActualEfficiency())) {
                //取最近一个月的实际值
                List<PowerEfficiencyDTO> collect = powerEfficiencyDTOSCopy.stream().filter(dto -> {
                    return dto.getSupplier().equals(powerEfficiencyDTO.getSupplier())
                            && dto.getCellType().equals(powerEfficiencyDTO.getCellType())
                            && dto.getActualEfficiency() != null;
                }).sorted(Comparator.comparing(PowerEfficiencyDTO::getBeginMonth).reversed()).limit(1).collect(Collectors.toList());
                if (collect != null && collect.size() > 0) {
                    powerEfficiencyDTO.setActualEfficiency(collect.get(0).getActualEfficiency());
                }

            }
        });
        //1.3一直没有实际值极端情况处理（筛选去除掉）
        powerEfficiencyDTOS = powerEfficiencyDTOS.stream().filter(dto -> {

            return StringUtils.isNotEmpty(dto.getActualEfficiency());
        }).collect(Collectors.toList());
        //1.4 分组统计出每组（供应方、电池类型）对应的最近月数据
        Map<String, Map<String, Optional<PowerEfficiencyDTO>>> collect = powerEfficiencyDTOS.stream().collect(Collectors.groupingBy(PowerEfficiencyDTO::getSupplier,
                Collectors.groupingBy(PowerEfficiencyDTO::getCellType, Collectors.maxBy(Comparator.comparing(PowerEfficiencyDTO::getBeginMonth))))
        );
        //1.5 构建最终效率分布数据
        List<CellWorkshopPriorityTarget> result = new ArrayList<>();
        collect.entrySet().stream().forEach(entry -> {
            String supply = entry.getKey();
            //对供应方下的电池类型统计
            Set<String> celltypes = entry.getValue().keySet();//电池类型名
            celltypes.stream().forEach(celltype -> {
                PowerEfficiencyDTO dto = entry.getValue().get(celltype).get();
                //
               // if (new BigDecimal(dto.getActualEfficiency()).compareTo(new BigDecimal(dto.getTargetEfficiency())) > 0) {
                    CellWorkshopPriorityTarget target = new CellWorkshopPriorityTarget();
                    target.setWorkshop(supply);
                    target.setEfficiency(dto.getCellType());
                    String rateEfficiency=null;
                    if (new BigDecimal(dto.getTargetEfficiency()).compareTo(BigDecimal.ZERO)>0){
                        rateEfficiency =  new BigDecimal(dto.getActualEfficiency()).divide(new BigDecimal(dto.getTargetEfficiency()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString()+"%";
                    }
                    String targetEfficiency = dto.getTargetEfficiency()+"%";
                    String actualEfficiency = dto.getActualEfficiency()+"%";
                    target.setTargetEfficiency(targetEfficiency);
                    target.setActualEfficiency(actualEfficiency);
                    target.setRateEfficiency(rateEfficiency);
                    result.add(target);
               // }
            });
        });
        List<CellWorkshopPriorityTarget> resultTotal = result.stream().filter(target -> {
            return StringUtils.isNotEmpty(target.getEfficiency());
        }).collect(Collectors.toList());
        //1.6原来数据删除
        repository.deleteAll();
        //1.7新增统计后的数据
        resultTotal.stream().forEach(target -> {
            LovLineDTO lov = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, target.getWorkshop());
            if(Objects.nonNull(lov)){
                Long workshopId = lov.getLovLineId();
                target.setWorkshopId(workshopId);
            }
            repository.save(target);
        });
    }


}
