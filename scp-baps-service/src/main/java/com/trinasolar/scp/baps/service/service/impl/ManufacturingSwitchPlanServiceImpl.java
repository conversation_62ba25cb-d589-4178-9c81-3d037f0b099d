package com.trinasolar.scp.baps.service.service.impl;

import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.convert.ManufacturingSwitchPlanDEConvert;
import com.trinasolar.scp.baps.domain.dto.ManufacturingSwitchPlanDTO;
import com.trinasolar.scp.baps.domain.entity.ManufacturingSwitchPlan;
import com.trinasolar.scp.baps.domain.entity.QManufacturingSwitchPlan;
import com.trinasolar.scp.baps.domain.excel.ManufacturingSwitchPlanExcelDTO;
import com.trinasolar.scp.baps.domain.query.ManufacturingSwitchPlanQuery;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.ManufacturingSwitchPlanRepository;
import com.trinasolar.scp.baps.service.service.ManufacturingSwitchPlanService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/20
 */
@Slf4j
@Service("manufacturingSwitchPlanService")
@RequiredArgsConstructor
public class ManufacturingSwitchPlanServiceImpl implements ManufacturingSwitchPlanService {
    private static final QManufacturingSwitchPlan qManufacturingSwitchPlan = QManufacturingSwitchPlan.manufacturingSwitchPlan;

    private final ManufacturingSwitchPlanDEConvert convert;

    private final ManufacturingSwitchPlanRepository repository;

    @Override
    public Page<ManufacturingSwitchPlanDTO> queryByPage(ManufacturingSwitchPlanQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);

        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ManufacturingSwitchPlan> page = repository.findAll(booleanBuilder, pageable);
        //翻译转换
        MyThreadLocal.get().setLang(oldLang);
        List<ManufacturingSwitchPlanDTO> dto = convert.toDto(page.getContent());

        return new PageImpl(dto, page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ManufacturingSwitchPlanQuery query) {
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qManufacturingSwitchPlan.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qManufacturingSwitchPlan.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qManufacturingSwitchPlan.workshop.eq(query.getWorkshop()));
        }
    }

    @Override
    @SneakyThrows
    public void export(ManufacturingSwitchPlanQuery query, HttpServletResponse response) {
        List<ManufacturingSwitchPlanDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);
        String fileName = BapsMessgeHelper.getMessage("export.manufacturingswitchplan.table.name");
        ExcelUtils.exportExWithLocalDate(response,  fileName, fileName, excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<ManufacturingSwitchPlanExcelDTO> excelDtos = ExcelUtils.readExcel(multipartFile.getInputStream(), null, ManufacturingSwitchPlanExcelDTO.class, excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        //验证数据
        checkInput(excelDtos);
        List<ManufacturingSwitchPlan> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        repository.saveAll(saveDTOS);
    }

    private void checkInput(List<ManufacturingSwitchPlanExcelDTO> excelDTOS) {
        final int[] i = {2};
        List<String> errors = new ArrayList<>();
        List<String> groupKeyList = Lists.newLinkedList();
        for (ManufacturingSwitchPlanExcelDTO excelDTO : excelDTOS) {
            checkNullField(excelDTO, i);
            if (StringUtils.isNotEmpty(excelDTO.getIsOversea())) {
                LovLineDTO lovLineDTOIsOversea = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOversea());
                if (lovLineDTOIsOversea == null) {
                    String message = BapsMessgeHelper.getMessage("manufacturingswitchplan.import.isoversea.not.exists",new Object[]{i[0], excelDTO.getIsOversea()});
                    errors.add(message);
                }
            }
            if (StringUtils.isNotEmpty(excelDTO.getBasePlace())) {
                LovLineDTO lovLineDTOBasePlace = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace());
                if (lovLineDTOBasePlace == null) {
                    String message = BapsMessgeHelper.getMessage("manufacturingswitchplan.import.baseplace.not.exists",new Object[]{i[0], excelDTO.getBasePlace()});
                    errors.add(message);
                }
            }
            LovLineDTO lovLineDTOWorkshop = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop());
            if (lovLineDTOWorkshop == null) {
                String message = BapsMessgeHelper.getMessage("manufacturingswitchplan.import.workshop.not.exists",new Object[]{i[0], excelDTO.getWorkshop()});
                errors.add(message);
            }
            if (StringUtils.isNotEmpty(excelDTO.getWorkunit())) {
                LovLineDTO lovLineDTOWorkunit = LovUtils.getByName(LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit());
                if (lovLineDTOWorkunit == null) {
                    String message = BapsMessgeHelper.getMessage("manufacturingswitchplan.import.workunit.not.exists",new Object[]{i[0], excelDTO.getWorkunit()});
                    errors.add(message);
                }
            }
            LovLineDTO lovLineDTOCellsType = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType());
            if (lovLineDTOCellsType == null) {
                String message = BapsMessgeHelper.getMessage("manufacturingswitchplan.import.cellstype.not.exists",new Object[]{i[0], excelDTO.getCellsType()});
                errors.add(message);
            }
            String groupKey = StringUtils.join(excelDTO.getWorkshop(), excelDTO.getWorkunit(), excelDTO.getProductionLine(), excelDTO.getCellsType(), excelDTO.getStartTime(), excelDTO.getEndTime());
            if (groupKeyList.contains(groupKey)) {
                String message = BapsMessgeHelper.getMessage("manufacturingswitchplan.import.groupKey.repeat",new Object[]{i[0]});
                errors.add(message);
            } else {
                groupKeyList.add(groupKey);
            }
            i[0]++;
        }
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }
    }

    private void checkNullField(ManufacturingSwitchPlanExcelDTO excelDTO, int[] i) {
        if (StringUtils.isEmpty(excelDTO.getWorkshop()) || StringUtils.isEmpty(excelDTO.getCellsType())
                || Objects.isNull(excelDTO.getCapacityQuantity()) || Objects.isNull(excelDTO.getStartTime())
                || Objects.isNull(excelDTO.getEndTime())) {
            String message = BapsMessgeHelper.getMessage("manufacturingswitchplan.import.row.not.null",new Object[]{i[0]});
            throw new BizException(message);
        }
    }
}
