package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellPlanLineALowDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineALowQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineALowSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 入库计划表A- 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
public interface CellPlanLineALowService {
    /**
     * 分页获取入库计划表A-
     *
     * @param query 查询对象
     * @return 入库计划表A-分页对象
     */
    Page<CellPlanLineALowDTO> queryByPage(CellPlanLineALowQuery query);

    /**
     * 根据主键获取入库计划表A-详情
     *
     * @param id 主键
     * @return 入库计划表A-详情
     */
        CellPlanLineALowDTO queryById(Long id);

    /**
     * 保存或更新入库计划表A-
     *
     * @param saveDTO 入库计划表A-保存对象
     * @return 入库计划表A-对象
     */
    CellPlanLineALowDTO save(CellPlanLineALowSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除入库计划表A-
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellPlanLineALowQuery query, HttpServletResponse response);

    /**
     * 计算
     * @param query
     * @return
     */
    List<CellPlanLineALowDTO> calc(CellPlanLineALowQuery query);
}

