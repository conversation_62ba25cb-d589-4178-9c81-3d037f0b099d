package com.trinasolar.scp.baps.service.feign.fallback;

import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.baps.domain.dto.bdm.DemandPlanLinesApsQuery;
import com.trinasolar.scp.baps.service.feign.BatteryDemandFeign;
import com.trinasolar.scp.common.api.util.Results;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class BatteryDemandFeignFallbackFactory implements FallbackFactory<BatteryDemandFeign> {

    @Override
    public BatteryDemandFeign create(Throwable cause) {
        return new BatteryDemandFeign() {
            @Override
            public ResponseEntity<Results<List<BatteryDemandPlanLinesDTO>>> list(DemandPlanLinesApsQuery query) {
                log.warn("【BdmFeign-demand-plan-header/listForAps】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<BatteryDemandPlanLinesDTO>>> queryByConditionForBaps(DemandPlanLinesApsQuery query) {
                log.warn("【BdmFeign-demand-plan-lines-aps/list-baps】发生异常：{}", cause);
                return Results.createFailRes();
            }
        };
    }
}
