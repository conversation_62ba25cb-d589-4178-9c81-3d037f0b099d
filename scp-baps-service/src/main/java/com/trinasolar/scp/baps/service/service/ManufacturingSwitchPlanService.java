package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.ManufacturingSwitchPlanDTO;
import com.trinasolar.scp.baps.domain.query.ManufacturingSwitchPlanQuery;
import com.trinasolar.scp.common.api.util.ExcelPara;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2024/8/20
 */
public interface ManufacturingSwitchPlanService {
    Page<ManufacturingSwitchPlanDTO> queryByPage(ManufacturingSwitchPlanQuery query);

    void export(ManufacturingSwitchPlanQuery query, HttpServletResponse response);

    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}
