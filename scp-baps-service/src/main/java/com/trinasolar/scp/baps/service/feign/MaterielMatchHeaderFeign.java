package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.baps.service.feign.fallback.MaterielMatchHeaderFeignFallbackFactory;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(value = "scp-battery-bom-api",fallbackFactory = MaterielMatchHeaderFeignFallbackFactory.class, path = "/scp-battery-bom-api",configuration = LanguageHeaderInterceptor.class)
public interface MaterielMatchHeaderFeign {
    /**
     * 排产信息获取4A信息返回
     * @param headerDTO
     * @return
     */
    @PostMapping("/materiel-match-header/query4AByMatchHeadDto")
    @ApiOperation(value = "排产信息获取4A料号、id")
    public ResponseEntity<Results<Map<Long,List<String>>>> query4AByMatchHeadDto(@RequestBody List<MaterielMatchHeaderDTO> headerDTO) ;
    /**
     * 排产信息获取5A信息返回(支持A-,透明双波)
     * @param headerDTOs
     * @return
     */
    @PostMapping("/materiel-match-header/query5AByMatchHeadDto")
    @ApiOperation(value = "排产信息获取5A料号、id")
    public ResponseEntity<Results<Map<Long,String>>> query5AByMatchHeadDto(@RequestBody List<MaterielMatchHeaderDTO> headerDTOs);
}
