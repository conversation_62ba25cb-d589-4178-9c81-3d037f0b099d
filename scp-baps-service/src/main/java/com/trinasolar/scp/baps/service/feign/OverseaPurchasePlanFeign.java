package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.OverseaPurchasePlanDTO;
import com.trinasolar.scp.baps.domain.query.OverseaPurchasePlanQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 电池外购计划
 */
@FeignClient(value = FeignConstant.SCP_APS_API, path = "/scp-aps-api",configuration = LanguageHeaderInterceptor.class)
public interface OverseaPurchasePlanFeign {

    @PostMapping(path = "/oversea-purchase-plan/list")
    public ResponseEntity<Results<List<OverseaPurchasePlanDTO>>> List(@RequestBody OverseaPurchasePlanQuery query);
}
