package com.trinasolar.scp.baps.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.convert.QopDetailsDEConvert;
import com.trinasolar.scp.baps.domain.dto.QopDetailsDTO;
import com.trinasolar.scp.baps.domain.dto.aps.PowerSupplyAopDTO;
import com.trinasolar.scp.baps.domain.dto.aps.PowerSupplyAopQuery;
import com.trinasolar.scp.baps.domain.entity.QQopDetails;
import com.trinasolar.scp.baps.domain.entity.QopDetails;
import com.trinasolar.scp.baps.domain.query.QopDetailsQuery;
import com.trinasolar.scp.baps.domain.save.QopDetailsSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.repository.QopDetailsRepository;
import com.trinasolar.scp.baps.service.service.QopDetailsService;
import com.trinasolar.scp.baps.service.service.aps.ApsService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SOP数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-30 03:38:41
 */
@Slf4j
@Service("sopDetailsService")
@RequiredArgsConstructor
public class QopDetailsServiceImpl implements QopDetailsService {
    private static final QQopDetails qopDetails = QQopDetails.qopDetails;
    private final QopDetailsDEConvert convert;
    private final QopDetailsRepository repository;
    private final ApsFeign apsFeign;

    private final ApsService apsService;
    private final QopDetailsDEConvert qopDetailsDEConvert;

    @Override
    public List<QopDetailsDTO> query(QopDetailsQuery query) {
        PowerSupplyAopQuery powerSupplyAopQuery = new PowerSupplyAopQuery();
        List<String> monthList = new ArrayList<>();
        monthList.add(query.getMonth());
        powerSupplyAopQuery.setMonthList(monthList);
        powerSupplyAopQuery.setDataVersion("目标版");
        powerSupplyAopQuery.setSupplyType("自产");
        if (StringUtils.isNotEmpty(query.getIsOverseaName())) {
            String isOverseaName = query.getIsOverseaName();
            if (isOverseaName.equals(OverseaConstant.INLAND)) {
                powerSupplyAopQuery.setIsOversea("INLAND");
            } else {
                powerSupplyAopQuery.setIsOversea("OVERSEA");
            }
        }

        ResponseEntity<Results<List<PowerSupplyAopDTO>>> resultsResponseEntity = apsFeign.powerSupplyAopList(powerSupplyAopQuery);
        List<PowerSupplyAopDTO> datas = resultsResponseEntity.getBody().getData();
        Map<String, LovLineDTO> allCellTypes = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BATTERY_TYPE);
        Map<String, LovLineDTO> allWorkshops = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP);
        Map<String, LovLineDTO> allOverseas = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.DOMESTIC_OVERSEA);
        List<PowerSupplyAopDTO> collect = datas.stream().filter(item -> {
            return allCellTypes.get(item.getCellType()) != null && allWorkshops.get(item.getSupplier()) != null;
        }).collect(Collectors.toList());
        collect.stream().forEach(item -> {
            if ("INLAND".equals(item.getIsOversea())) {
                item.setIsOversea(OverseaConstant.INLAND);
            } else {
                item.setIsOversea(OverseaConstant.OVERSEA);
            }

        });
        List<QopDetailsDTO> qopDetailsDTOS = new ArrayList<>();
        for (PowerSupplyAopDTO dto : collect) {
            QopDetailsDTO qopDetailsDTO = new QopDetailsDTO();
            qopDetailsDTO.setSopQty(dto.getSupplyQuantity());
            qopDetailsDTO.setIsOverseaName(dto.getIsOversea());
            qopDetailsDTO.setIsOverseaId(allOverseas.get(dto.getIsOversea()).getLovLineId());
            qopDetailsDTO.setCellTypeName(dto.getCellType());
            qopDetailsDTO.setCellTypeId(allCellTypes.get(dto.getCellType()).getLovLineId());
            qopDetailsDTO.setWorkshopName(dto.getSupplier());
            qopDetailsDTO.setWorkshopId(allWorkshops.get(dto.getSupplier()).getLovLineId());
            qopDetailsDTO.setMonth(dto.getMonth());
            qopDetailsDTOS.add(qopDetailsDTO);
        }
        return qopDetailsDTOS;
    }

    @Override
    public List<QopDetailsDTO> queryByPage(QopDetailsQuery query) {
        Map<String, LovLineDTO> workShopMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP);
        Map<String, Long> workShopMapId = workShopMap.values().stream().collect(Collectors.toMap(LovLineDTO::getLovName, LovLineDTO::getLovLineId, (v1, v2) -> v1));

        List<PowerSupplyAopDTO> datas = apsService.getPowerSupplyAopDTOS(query.getMonth(), query.getIsOverseaName());
        //判断datas为空
        if (CollectionUtils.isEmpty(datas)){
           throw new BizException("QOP暂无数据");
        }

        List<QopDetailsDTO> qopDetailsDTOS = new ArrayList<>();
        Map<String, LovLineDTO> allCellTypes = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BATTERY_TYPE);
        Map<String, LovLineDTO> allWorkshops = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP);
        for (PowerSupplyAopDTO aopDTO : datas) {

            if (allCellTypes.get(aopDTO.getCellTypeName()) == null) {
                log.warn(String.format("Qop中的%s电池类型在Lov中不存在", aopDTO.getCellTypeName()));
                continue;
            }
            if (allWorkshops.get(aopDTO.getSupplierName()) == null) {
                log.warn(String.format("Qop中的%s供应方在Lov中不存在", aopDTO.getSupplierName()));
                continue;
            }
            QopDetailsDTO qopDetailsDTO = qopDetailsDEConvert.toDtoFromPowerSupplyAopDTO(aopDTO);
            qopDetailsDTO.setWorkshopName(aopDTO.getSupplierName());
            qopDetailsDTO.setWorkshopId(workShopMapId.get(aopDTO.getSupplierName()));
            qopDetailsDTO.setSopQty(aopDTO.getSupplyQuantity());
            qopDetailsDTOS.add(qopDetailsDTO);

        }
        if (CollectionUtils.isNotEmpty(qopDetailsDTOS)){
            if (Objects.nonNull(query.getWorkshopId())){
                qopDetailsDTOS = qopDetailsDTOS.stream().filter(item -> Objects.equals(item.getWorkshopId() ,query.getWorkshopId())).collect(Collectors.toList());
            }
        }
        return qopDetailsDTOS;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, QopDetailsQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qopDetails.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qopDetails.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOverseaName())) {
            booleanBuilder.and(qopDetails.isOverseaName.eq(query.getIsOverseaName()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qopDetails.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshopName())) {
            booleanBuilder.and(qopDetails.workshopName.eq(query.getWorkshopName()));
        }
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qopDetails.cellTypeId.eq(query.getCellTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellTypeName())) {
            booleanBuilder.and(qopDetails.cellTypeName.eq(query.getCellTypeName()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qopDetails.month.eq(query.getMonth()));
        }
        if (query.getSopQty() != null) {
            booleanBuilder.and(qopDetails.sopQty.eq(query.getSopQty()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qopDetails.version.eq(query.getVersion()));
        }
    }

    @Override
    public QopDetailsDTO queryById(Long id) {
        QopDetails queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public QopDetailsDTO save(QopDetailsSaveDTO saveDTO) {
        QopDetails newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new QopDetails());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(QopDetailsQuery query, HttpServletResponse response) {
        List<QopDetailsDTO> dtos = queryByPage(query);

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "SOP数据", "SOP数据", excelPara.getSimpleHeader(), excelData);
    }
}
