package com.trinasolar.scp.baps.service.feign;
import com.trinasolar.scp.baps.domain.dto.aps.RecordTransitionDTO;
import com.trinasolar.scp.baps.domain.query.aps.RecordTransitionQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
@FeignClient(value = FeignConstant.SCP_APS_API, path = "/scp-aps-api")
public interface RecordTransitionFeign {

}
