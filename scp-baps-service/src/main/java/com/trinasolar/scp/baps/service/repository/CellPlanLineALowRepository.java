package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellPlanLineALow;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 入库计划表A-
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Repository
public interface CellPlanLineALowRepository extends JpaRepository<CellPlanLineALow, Long>, QuerydslPredicateExecutor<CellPlanLineALow> {

    @Modifying
    @Query("update CellPlanLineALow c set c.isDeleted=1 where c.month = :month and  c.basePlace = :basePlace " +
            "and c.workshop =:workshop  and c.cellsType = :cellsType  and c.hTrace = :hTrace and c.aesthetics = :aesthetics and " +
            "c.transparentDoubleGlass = :transparentDoubleGlass and " +
            "c.cellSource = :cellSource ")
    void deleteByCell(@Param("month") String month,@Param("basePlace") String basePlace,@Param("workshop") String workshop
       ,@Param("cellsType") String cellsType,@Param("hTrace") String hTrace,@Param("aesthetics") String aesthetics,@Param("transparentDoubleGlass") String transparentDoubleGlass
           ,@Param("cellSource") String cellSource);
}
