package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellFineMidDTO;
import com.trinasolar.scp.baps.domain.query.CellFineMidQuery;
import com.trinasolar.scp.baps.domain.save.CellFineMidSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池良率中间表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
public interface CellFineMidService {
    /**
     * 分页获取电池良率中间表
     *
     * @param query 查询对象
     * @return 电池良率中间表分页对象
     */
    Page<CellFineMidDTO> queryByPage(CellFineMidQuery query);

    /**
     * 根据主键获取电池良率中间表详情
     *
     * @param id 主键
     * @return 电池良率中间表详情
     */
        CellFineMidDTO queryById(Long id);

    /**
     * 保存或更新电池良率中间表
     *
     * @param saveDTO 电池良率中间表保存对象
     * @return 电池良率中间表对象
     */
    CellFineMidDTO save(CellFineMidSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池良率中间表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellFineMidQuery query, HttpServletResponse response);
}

