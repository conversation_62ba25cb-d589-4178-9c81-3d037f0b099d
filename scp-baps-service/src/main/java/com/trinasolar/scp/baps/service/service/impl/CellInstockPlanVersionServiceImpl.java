package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanVersionDTO;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanVersionDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanVersionQueryDto;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionDTO;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanVersion;
import com.trinasolar.scp.baps.domain.entity.QCellInstockPlan;
import com.trinasolar.scp.baps.domain.entity.QCellInstockPlanVersion;
import com.trinasolar.scp.baps.domain.entity.QCellPlanLineVersion;
import com.trinasolar.scp.baps.domain.excel.CellInstockPlanVersionExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanVersionQuery;
import com.trinasolar.scp.baps.domain.query.InstockPlanVersionQuery;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanVersionSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanVersionRepository;
import com.trinasolar.scp.baps.service.service.CellInstockPlanVersionService;
import com.trinasolar.scp.common.api.util.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;

/**
 * 入库计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Slf4j
@Service("cellInstockPlanVersionService")
@RequiredArgsConstructor
public class CellInstockPlanVersionServiceImpl implements CellInstockPlanVersionService {
    private static final QCellInstockPlanVersion qCellInstockPlanVersion = QCellInstockPlanVersion.cellInstockPlanVersion;
    private final JPAQueryFactory jpaQueryFactory;
    private final CellInstockPlanVersionDEConvert convert;

    private final CellInstockPlanVersionRepository repository;

    @Override
    public Page<CellInstockPlanVersionDTO> queryByPage(CellInstockPlanVersionQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellInstockPlanVersion> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellInstockPlanVersionQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellInstockPlanVersion.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellInstockPlanVersion.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellInstockPlanVersion.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellInstockPlanVersion.month.eq(query.getMonth()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qCellInstockPlanVersion.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getFromVersion())) {
            booleanBuilder.and(qCellInstockPlanVersion.fromVersion.eq(query.getFromVersion()));
        }
        if (query.getIsASplit() != null) {
            booleanBuilder.and(qCellInstockPlanVersion.isASplit.eq(query.getIsASplit()));
        }
        if (query.getIsAesthetics() != null) {
            booleanBuilder.and(qCellInstockPlanVersion.isAesthetics.eq(query.getIsAesthetics()));
        }
        if (query.getIsTransparentDoubleGlass() != null) {
            booleanBuilder.and(qCellInstockPlanVersion.isTransparentDoubleGlass.eq(query.getIsTransparentDoubleGlass()));
        }
        if (query.getIsGradeRule() != null) {
            booleanBuilder.and(qCellInstockPlanVersion.isGradeRule.eq(query.getIsGradeRule()));
        }
        if (query.getIsConfirmPlan() != null) {
            booleanBuilder.and(qCellInstockPlanVersion.isConfirmPlan.eq(query.getIsConfirmPlan()));
        }
        if (query.getIsSendEmail() != null) {
            booleanBuilder.and(qCellInstockPlanVersion.isSendEmail.eq(query.getIsSendEmail()));
        }
        if (StringUtils.isNotEmpty(query.getScheduleMonth())) {
            booleanBuilder.and(qCellInstockPlanVersion.scheduleMonth.eq(query.getScheduleMonth()));
        }
    }

    @Override
    public CellInstockPlanVersionDTO queryById(Long id) {
        CellInstockPlanVersion queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellInstockPlanVersionDTO save(CellInstockPlanVersionSaveDTO saveDTO) {
        CellInstockPlanVersion newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellInstockPlanVersion());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellInstockPlanVersionQuery query, HttpServletResponse response) {
        List<CellInstockPlanVersionDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "入库计划版本管理表", "入库计划版本管理表", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    public CellInstockPlanVersionDTO query(CellInstockPlanVersionQueryDto query) {
        if (StringUtils.isEmpty(query.getIsOversea())) {
            String inlandVersion = getLastVersion(OverseaConstant.INLAND, query.getMonth());
            String overseaVersion = getLastVersion(OverseaConstant.OVERSEA, query.getMonth());
            CellInstockPlanVersionDTO cellInstockPlanVersionInland = null;
            if (StringUtils.isNotEmpty(inlandVersion)) {
                cellInstockPlanVersionInland = convert.toDto(repository.selectByVersion(OverseaConstant.INLAND, query.getMonth(), inlandVersion));
            }
            CellInstockPlanVersionDTO cellInstockPlanVersionOversea = null;
            if (StringUtils.isNotEmpty(overseaVersion)) {
                cellInstockPlanVersionOversea = convert.toDto(repository.selectByVersion(OverseaConstant.OVERSEA, query.getMonth(), overseaVersion));
            }
            if (cellInstockPlanVersionInland != null && cellInstockPlanVersionOversea != null) {
                cellInstockPlanVersionInland.setVersion(null);
                //合并
                cellInstockPlanVersionInland.setIsOversea(null);
                if (cellInstockPlanVersionInland.getIsASplit() + cellInstockPlanVersionOversea.getIsASplit() != 2) {
                    cellInstockPlanVersionInland.setIsASplit(0);
                }
                if (!Objects.equals(cellInstockPlanVersionInland.getIsTransparentDoubleGlass() + cellInstockPlanVersionOversea.getIsTransparentDoubleGlass(), 2)) {
                    cellInstockPlanVersionInland.setIsTransparentDoubleGlass(0);
                }
                if (!Objects.equals(cellInstockPlanVersionInland.getIsGradeRule() + cellInstockPlanVersionOversea.getIsGradeRule(), 2)) {
                    cellInstockPlanVersionInland.setIsGradeRule(0);
                }
                if (!Objects.equals(cellInstockPlanVersionInland.getIsConfirmPlan() + cellInstockPlanVersionOversea.getIsConfirmPlan(), 2)) {
                    cellInstockPlanVersionInland.setIsConfirmPlan(0);
                }

                if (!Objects.equals(cellInstockPlanVersionInland.getIsSendEmail() + cellInstockPlanVersionOversea.getIsSendEmail(), 2)) {
                    cellInstockPlanVersionInland.setIsSendEmail(0);
                }
                if (!Objects.equals(Optional.ofNullable(cellInstockPlanVersionInland.getIsAesthetics()).orElse(0) +
                        Optional.ofNullable(cellInstockPlanVersionOversea.getIsAesthetics()).orElse(0), 2)) {
                    cellInstockPlanVersionInland.setIsAesthetics(0);
                }
                return cellInstockPlanVersionInland;
            } else {
                //返回某个
                if (cellInstockPlanVersionInland != null) {
                    return cellInstockPlanVersionInland;
                } else if (cellInstockPlanVersionOversea != null) {
                    return cellInstockPlanVersionOversea;
                }
            }
        } else {
            String version = getLastVersion(query.getIsOversea(), query.getMonth());
            if (StringUtils.isNotEmpty(version)) {
                return convert.toDto(repository.selectByVersion(query.getIsOversea(), query.getMonth(), version));
            }
        }
        CellInstockPlanVersionDTO dto = new CellInstockPlanVersionDTO();
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            dto.setIsOversea(query.getIsOversea());
        }
        dto.setMonth(query.getMonth());
        dto.setIsASplit(0);
        dto.setIsTransparentDoubleGlass(0);
        dto.setIsGradeRule(0);
        dto.setIsConfirmPlan(0);
        dto.setIsSendEmail(0);
        dto.setIsAesthetics(0);
        return dto;
    }

    @Override
    public List<String> getAllVersions(InstockPlanVersionQuery query) {
        if (StringUtils.isBlank(query.getIsOversea())) {
            query.setIsOverseaId(OverseaConstant.INLAND_ID);
        } else {
            query.setIsOverseaId(LovUtils.get(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()).getLovLineId());
        }
        if (StringUtils.isBlank(query.getMonth())) {
            query.setMonth(DateUtil.getMonth(LocalDate.now()));
        }
        QCellInstockPlanVersion qCellInstockPlanVersion = QCellInstockPlanVersion.cellInstockPlanVersion;
        List<String> versions = jpaQueryFactory.select(qCellInstockPlanVersion.fromVersion).from(qCellInstockPlanVersion)
                .where(qCellInstockPlanVersion.month.eq(query.getMonth()))
                .where(qCellInstockPlanVersion.isOverseaId.eq(query.getIsOverseaId()))
                .distinct().fetch();
        return Optional.ofNullable(versions).orElse(Lists.newArrayList()).stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
    }


    @Override
    public String findMaxVersion(Long isOverseaId, String month) {
        QCellInstockPlanVersion qCellInstockPlanVersion = QCellInstockPlanVersion.cellInstockPlanVersion;
        String version = jpaQueryFactory.select(qCellInstockPlanVersion.version.max()).from(qCellInstockPlanVersion).where(qCellInstockPlanVersion.month.eq(month)).where(
                qCellInstockPlanVersion.isOverseaId.eq(isOverseaId)
        ).fetchFirst();
        return version;
    }

    @Override
    public boolean checkVersion(Long isOverseaId, String month, String planVersion) {
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qCellInstockPlanVersion.isOverseaId.eq(isOverseaId));
        builder.and(qCellInstockPlanVersion.month.eq(month));
        builder.and(qCellInstockPlanVersion.fromVersion.eq(planVersion));
        return repository.exists(builder);
    }

    private String getLastVersion(String isOversea, String month) {
        QCellInstockPlanVersion qCellInstockPlanVersion = QCellInstockPlanVersion.cellInstockPlanVersion;
        String version = jpaQueryFactory.select(qCellInstockPlanVersion.fromVersion.max()).from(qCellInstockPlanVersion).where(qCellInstockPlanVersion.month.eq(month)).where(
                qCellInstockPlanVersion.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    @Override
    public String findMaxVersionSendedEmail(Long isOverseaId, String month) {
        QCellInstockPlan qCellInstockPlan = QCellInstockPlan.cellInstockPlan;
        String currentVersion = jpaQueryFactory.select(qCellInstockPlan.version.max()).from(qCellInstockPlan).where(
                qCellInstockPlan.month.eq(month)
        ).where(
                qCellInstockPlan.isOverseaId.eq(isOverseaId)
        ).fetchFirst();
        QCellInstockPlanVersion qCellInstockPlanVersion = QCellInstockPlanVersion.cellInstockPlanVersion;
        JPAQuery<String> jpaQuery = jpaQueryFactory.select(qCellInstockPlanVersion.fromVersion.max()).from(qCellInstockPlanVersion).where(qCellInstockPlanVersion.month.eq(month))
                .where(qCellInstockPlanVersion.isOverseaId.eq(isOverseaId)).where(qCellInstockPlanVersion.fromVersion.isNotNull())
                .where(qCellInstockPlanVersion.isSendEmail.eq(1));
        if(StringUtils.isNotBlank(currentVersion)){
            jpaQuery.where(qCellInstockPlanVersion.fromVersion.ne(currentVersion));
        }
        return jpaQuery.fetchFirst();
    }
}
