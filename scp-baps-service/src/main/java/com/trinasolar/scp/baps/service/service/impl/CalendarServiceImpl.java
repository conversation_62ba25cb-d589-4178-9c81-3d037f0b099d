package com.trinasolar.scp.baps.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CalendarDTO;
import com.trinasolar.scp.baps.domain.convert.CalendarDEConvert;
import com.trinasolar.scp.baps.domain.entity.Calendar;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturing;
import com.trinasolar.scp.baps.domain.entity.QCalendar;
import com.trinasolar.scp.baps.domain.query.CalendarQuery;
import com.trinasolar.scp.baps.domain.save.CalendarSaveDTO;
import com.trinasolar.scp.baps.service.repository.CalendarRepository;
import com.trinasolar.scp.baps.service.service.CalendarService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import javax.servlet.http.HttpServletResponse;

/**
 * 生产日历
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Slf4j
@Service("calendarService")
@RequiredArgsConstructor
public class CalendarServiceImpl implements CalendarService {
    private static final QCalendar qCalendar = QCalendar.calendar;

    private final CalendarDEConvert convert;

    private final CalendarRepository repository;

    @Override
    public Page<CalendarDTO> queryByPage(CalendarQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<Calendar> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CalendarQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCalendar.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getType())) {
            booleanBuilder.and(qCalendar.type.eq(query.getType()));
        }
        if (query.getWorkunitId() != null) {
            booleanBuilder.and(qCalendar.workunitId.eq(query.getWorkunitId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qCalendar.workunit.eq(query.getWorkunit()));
        }
        if (query.getDate() != null) {
            booleanBuilder.and(qCalendar.date.eq(query.getDate()));
        }
        if (StringUtils.isNotEmpty(query.getShiftcode())) {
            booleanBuilder.and(qCalendar.shiftcode.eq(query.getShiftcode()));
        }
        if (query.getDefaultqty() != null) {
            booleanBuilder.and(qCalendar.defaultqty.eq(query.getDefaultqty()));
        }
        if (query.getSortorder() != null) {
            booleanBuilder.and(qCalendar.sortorder.eq(query.getSortorder()));
        }
        if (query.getNumLines() != null) {
            booleanBuilder.and(qCalendar.numLines.eq(query.getNumLines()));
        }
        if (query.getLineName() != null) {
            booleanBuilder.and(qCalendar.lineName.eq(query.getLineName()));
        }
    }

    @Override
    public CalendarDTO queryById(Long id) {
        Calendar queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CalendarDTO save(CalendarSaveDTO saveDTO) {
        Calendar newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new Calendar());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CalendarQuery query, HttpServletResponse response) {
        List<CalendarDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "生产日历", "生产日历", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @SneakyThrows
    public List<Calendar> deduplication(CellBomManufacturing bom, List<Calendar> calendars) {
        //bom中对应一个时间段数据的情况（如IE产能的情况）
        LocalDate startDate = bom.getStartDate().toLocalDate();
        LocalDate endDate = bom.getEndDate().toLocalDate();
        //查出重复的数据
        List<Calendar> list = null;
        if (StringUtils.isEmpty(bom.getLineName())) {

            list = repository.selectByWorkunitDate(bom.getWorkunit(), startDate, endDate,bom.getIeorgrade());
        } else {
            list = repository.selectByWorkunitDate(bom.getWorkunit(), bom.getLineName(), startDate, endDate,bom.getIeorgrade());
        }
        for (Calendar c : calendars) {
            CalendarRepository.setDefault(c);//设置默认值
            if (CollectionUtils.isNotEmpty(list)) {
                for (Calendar calendar : list) {
                    //同一生产单元、同产线同一日期有多条重复数据。需在写入表前按生产单元、日期归集数据，归集时标准产线数和资源量取最大值
                    if (c.getDate().equals(calendar.getDate()) ) {
                       if (StringUtils.isEmpty(c.getLineName()) && StringUtils.isEmpty(calendar.getLineName())){
                           if (calendar.getDefaultqty().compareTo(c.getDefaultqty()) > 0) {
                               c.setDefaultqty(calendar.getDefaultqty());
                           }
                           if (calendar.getNumLines().compareTo(c.getNumLines()) > 0) {
                               c.setNumLines(calendar.getNumLines());
                           }
                       }else if(StringUtils.isNotEmpty(c.getLineName()) && c.getLineName().equals(calendar.getLineName()) ){
                           if (calendar.getDefaultqty().compareTo(c.getDefaultqty()) > 0) {
                               c.setDefaultqty(calendar.getDefaultqty());
                           }
                           if (calendar.getNumLines().compareTo(c.getNumLines()) > 0) {
                               c.setNumLines(calendar.getNumLines());
                           }
                       }

                    }
                }
            }
        }
        return list;
    }

}


