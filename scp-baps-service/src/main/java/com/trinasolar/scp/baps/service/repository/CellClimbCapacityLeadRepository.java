package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.dto.CellClimbCapacityLeadDTO;
import com.trinasolar.scp.baps.domain.entity.CellClimbCapacityLead;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 爬坡产能可靠性验证表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-05 08:10:43
 */
@Repository
public interface CellClimbCapacityLeadRepository extends JpaRepository<CellClimbCapacityLead, Long>, QuerydslPredicateExecutor<CellClimbCapacityLead> {
    @Query(value = "SELECT * FROM baps_cell_climb_capacity_lead t WHERE  t.is_deleted=1 and t.cell_type_id=?1 and t.base_place_id=?2 and t.work_shop_id=?3 and t.work_unit_id=?4 and t.line_name=?5  ORDER BY t.updated_time desc",
            nativeQuery = true)
    List<CellClimbCapacityLead> queryByLeadDto(Long cellTypeId, Long basePlaceId, Long workShopId, Long workUnitId, String lineName);
}
