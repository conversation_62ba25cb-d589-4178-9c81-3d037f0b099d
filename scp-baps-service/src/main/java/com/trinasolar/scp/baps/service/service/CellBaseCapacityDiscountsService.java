package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDiscountsDTO;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityDiscountsQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacityDiscountsSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * IE产能打折（人力）表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
public interface CellBaseCapacityDiscountsService {
    /**
     * 分页获取IE产能打折（人力）表
     *
     * @param query 查询对象
     * @return IE产能打折（人力）表分页对象
     */
    Page<CellBaseCapacityDiscountsDTO> queryByPage(CellBaseCapacityDiscountsQuery query);

    /**
     * 根据主键获取IE产能打折（人力）表详情
     *
     * @param id 主键
     * @return IE产能打折（人力）表详情
     */
        CellBaseCapacityDiscountsDTO queryById(Long id);

    /**
     * 保存或更新IE产能打折（人力）表
     *
     * @param saveDTO IE产能打折（人力）表保存对象
     * @return IE产能打折（人力）表对象
     */
    CellBaseCapacityDiscountsDTO save(CellBaseCapacityDiscountsSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除IE产能打折（人力）表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellBaseCapacityDiscountsQuery query, HttpServletResponse response);
    void ieConfirm(List<Long> ids,int tag);
    void planConfirm(List<Long> ids,int tag);
    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

