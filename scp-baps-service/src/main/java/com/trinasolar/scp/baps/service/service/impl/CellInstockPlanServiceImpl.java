package com.trinasolar.scp.baps.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanALowDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanSummaryDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineALowDTO;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlan;
import com.trinasolar.scp.baps.domain.entity.QCellInstockPlan;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanRepository;
import com.trinasolar.scp.baps.service.service.CellInstockPlanService;
import com.trinasolar.scp.baps.service.service.CellInstockPlanVersionService;
import com.trinasolar.scp.baps.service.service.CellItemCodeService;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 入库计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:13:25
 */
@Slf4j
@Service("cellInstockPlanService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class CellInstockPlanServiceImpl implements CellInstockPlanService {
    private static final QCellInstockPlan qCellInstockPlan = QCellInstockPlan.cellInstockPlan;

    private final CellInstockPlanDEConvert convert;

    private final CellInstockPlanRepository repository;

    private final JPAQueryFactory jpaQueryFactory;

    @Autowired
    @Lazy
    private CellInstockPlanVersionService versionService;

    @Autowired
    @Lazy
    private CellInstockPlanService cellInstockPlanService;

    @Autowired
    @Lazy
    private CellItemCodeService cellItemCodeService;

    @Override
    public Page<CellInstockPlanDTO> queryByPage(CellInstockPlanQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellInstockPlan> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellInstockPlanQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellInstockPlan.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getOrderCode())) {
            booleanBuilder.and(qCellInstockPlan.orderCode.eq(query.getOrderCode()));
        }
        if (StringUtils.isNotEmpty(query.getSourceType())) {
            booleanBuilder.and(qCellInstockPlan.sourceType.eq(query.getSourceType()));
        }
        if (StringUtils.isNotEmpty(query.getDemandVersion())) {
            booleanBuilder.and(qCellInstockPlan.demandVersion.eq(query.getDemandVersion()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellInstockPlan.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellInstockPlan.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellInstockPlan.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellInstockPlan.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellInstockPlan.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellInstockPlan.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qCellInstockPlan.workunit.eq(query.getWorkunit()));
        }
        if (query.getWorkunitId() != null) {
            booleanBuilder.and(qCellInstockPlan.workunitId.eq(query.getWorkunitId()));
        }
        if (StringUtils.isNotEmpty(query.getLineName())) {
            booleanBuilder.and(qCellInstockPlan.lineName.eq(query.getLineName()));
        }
        if (query.getNumberLine() != null) {
            booleanBuilder.and(qCellInstockPlan.numberLine.eq(query.getNumberLine()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellInstockPlan.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellInstockPlan.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qCellInstockPlan.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qCellInstockPlan.aesthetics.eq(query.getAesthetics()));
        }
        if (query.getIsTransparentDoubleGlass() != null) {
            booleanBuilder.and(qCellInstockPlan.isTransparentDoubleGlass.eq(query.getIsTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellInstockPlan.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            booleanBuilder.and(qCellInstockPlan.cellSource.eq(query.getCellSource()));
        }
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qCellInstockPlan.productionGrade.eq(query.getProductionGrade()));
        }
        if (StringUtils.isNotEmpty(query.getRegionalCountry())) {
            booleanBuilder.and(qCellInstockPlan.regionalCountry.eq(query.getRegionalCountry()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qCellInstockPlan.itemCode.eq(query.getItemCode()));
        }
        if (StringUtils.isNotEmpty(query.getDemandBasePlace())) {
            booleanBuilder.and(qCellInstockPlan.demandBasePlace.eq(query.getDemandBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getIsSpecialRequirement())) {
            booleanBuilder.and(qCellInstockPlan.isSpecialRequirement.eq(query.getIsSpecialRequirement()));
        }
        if (StringUtils.isNotEmpty(query.getLowResistance())) {
            booleanBuilder.and(qCellInstockPlan.lowResistance.eq(query.getLowResistance()));
        }
        if (StringUtils.isNotEmpty(query.getCellMfrs())) {
            booleanBuilder.and(qCellInstockPlan.cellMfrs.eq(query.getCellMfrs()));
        }
        if (StringUtils.isNotEmpty(query.getSilverPulpMfrs())) {
            booleanBuilder.and(qCellInstockPlan.silverPulpMfrs.eq(query.getSilverPulpMfrs()));
        }
        if (query.getDemandQty() != null) {
            booleanBuilder.and(qCellInstockPlan.demandQty.eq(query.getDemandQty()));
        }
        if (query.getEndTime() != null) {
            booleanBuilder.and(qCellInstockPlan.endTime.eq(query.getEndTime()));
        }
        if (query.getStartTime() != null) {
            booleanBuilder.and(qCellInstockPlan.startTime.eq(query.getStartTime()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellInstockPlan.month.eq(query.getMonth()));
        }
        if (query.getCellMv() != null) {
            booleanBuilder.and(qCellInstockPlan.cellMv.eq(query.getCellMv()));
        }
        if (StringUtils.isNotEmpty(query.getFinalVersion())) {
            booleanBuilder.and(qCellInstockPlan.finalVersion.eq(query.getFinalVersion()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qCellInstockPlan.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qCellInstockPlan.remark.eq(query.getRemark()));
        }
        if (query.getOldQtyPc() != null) {
            booleanBuilder.and(qCellInstockPlan.oldQtyPc.eq(query.getOldQtyPc()));
        }
        if (query.getQtyPc() != null) {
            booleanBuilder.and(qCellInstockPlan.qtyPc.eq(query.getQtyPc()));
        }
        if (query.getDemandSummaryLinesId() != null) {
            booleanBuilder.and(qCellInstockPlan.demandSummaryLinesId.eq(query.getDemandSummaryLinesId()));
        }
        if (StringUtils.isNotEmpty(query.getSiMfrs())) {
            booleanBuilder.and(qCellInstockPlan.siMfrs.eq(query.getSiMfrs()));
        }
        if (query.getIsSiMfrs() != null) {
            booleanBuilder.and(qCellInstockPlan.isSiMfrs.eq(query.getIsSiMfrs()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconMaterialManufacturer())) {
            booleanBuilder.and(qCellInstockPlan.siliconMaterialManufacturer.eq(query.getSiliconMaterialManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getScreenPlateMfrs())) {
            booleanBuilder.and(qCellInstockPlan.screenPlateMfrs.eq(query.getScreenPlateMfrs()));
        }
        if (query.getStartEfficiency() != null) {
            booleanBuilder.and(qCellInstockPlan.startEfficiency.eq(query.getStartEfficiency()));
        }
        if (query.getMaxEfficiency() != null) {
            booleanBuilder.and(qCellInstockPlan.maxEfficiency.eq(query.getMaxEfficiency()));
        }
        if (StringUtils.isNotEmpty(query.getSpecialOrderNo())) {
            booleanBuilder.and(qCellInstockPlan.specialOrderNo.eq(query.getSpecialOrderNo()));
        }
        if (query.getDemandDate() != null) {
            booleanBuilder.and(qCellInstockPlan.demandDate.eq(query.getDemandDate()));
        }
        if (query.getIsWaferGrade() != null) {
            booleanBuilder.and(qCellInstockPlan.isWaferGrade.eq(query.getIsWaferGrade()));
        }
        if (StringUtils.isNotEmpty(query.getWaferGrade())) {
            booleanBuilder.and(qCellInstockPlan.waferGrade.eq(query.getWaferGrade()));
        }
        if (query.getIsASplit() != null) {
            booleanBuilder.and(qCellInstockPlan.isASplit.eq(query.getIsASplit()));
        }
        if (query.getProcessCategoryPriority() != null) {
            booleanBuilder.and(qCellInstockPlan.processCategoryPriority.eq(query.getProcessCategoryPriority()));
        }
        if (StringUtils.isNotEmpty(query.getProcessCategory())) {
            booleanBuilder.and(qCellInstockPlan.processCategory.eq(query.getProcessCategory()));
        }
        if (query.getIsProcessCategory() != null) {
            booleanBuilder.and(qCellInstockPlan.isProcessCategory.eq(query.getIsProcessCategory()));
        }
        if (query.getIsHandProcessCategory() != null) {
            booleanBuilder.and(qCellInstockPlan.isHandProcessCategory.eq(query.getIsHandProcessCategory()));
        }
        if (query.getGap() != null) {
            booleanBuilder.and(qCellInstockPlan.gap.eq(query.getGap()));
        }
        if (StringUtils.isNotEmpty(query.getDemandRemark())) {
            booleanBuilder.and(qCellInstockPlan.demandRemark.eq(query.getDemandRemark()));
        }
        if (StringUtils.isNotEmpty(query.getGradeRule())) {
            booleanBuilder.and(qCellInstockPlan.gradeRule.eq(query.getGradeRule()));
        }
        if (StringUtils.isNotEmpty(query.getVerificationMark())) {
            booleanBuilder.and(qCellInstockPlan.verificationMark.eq(query.getVerificationMark()));
        }
        if (StringUtils.isNotEmpty(query.getDemandSource())) {
            booleanBuilder.and(qCellInstockPlan.demandSource.eq(query.getDemandSource()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryMaterialCode())) {
            booleanBuilder.and(qCellInstockPlan.batteryMaterialCode.eq(query.getBatteryMaterialCode()));
        }
        if (query.getOldStartTime() != null) {
            booleanBuilder.and(qCellInstockPlan.oldStartTime.eq(query.getOldStartTime()));
        }
        if (query.getOldEndTime() != null) {
            booleanBuilder.and(qCellInstockPlan.oldEndTime.eq(query.getOldEndTime()));
        }
        if (query.getSchedulingFromId() != null) {
            booleanBuilder.and(qCellInstockPlan.schedulingFromId.eq(query.getSchedulingFromId()));
        }
        if (query.getPlanLineFromId() != null) {
            booleanBuilder.and(qCellInstockPlan.planLineFromId.eq(query.getPlanLineFromId()));
        }
        if (query.getParentId() != null) {
            booleanBuilder.and(qCellInstockPlan.parentId.eq(query.getParentId()));
        }
        if (query.getBbomId() != null) {
            booleanBuilder.and(qCellInstockPlan.bbomId.eq(query.getBbomId()));
        }
        if (query.getConfirmPlan() != null) {
            booleanBuilder.and(qCellInstockPlan.confirmPlan.eq(query.getConfirmPlan()));
        }
        if (query.getOldMonth() != null) {
            booleanBuilder.and(qCellInstockPlan.oldMonth.eq(query.getOldMonth()));
        }
        if (query.getIsAestheticsSplit() != null) {
            booleanBuilder.and(qCellInstockPlan.isAestheticsSplit.eq(query.getIsAestheticsSplit()));
        }
    }

    @Override
    public CellInstockPlanDTO queryById(Long id) {
        CellInstockPlan queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellInstockPlanDTO save(CellInstockPlanSaveDTO saveDTO) {
        CellInstockPlan newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellInstockPlan());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellInstockPlanQuery query, HttpServletResponse response) {
        List<CellInstockPlanDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "入库计划表", "入库计划表", excelPara.getSimpleHeader(), excelData);
    }

    /**
     * 获取国内海外版本入库计划数据(依据finalVersion)
     *
     * @param query
     * @return
     */
    @Override
    @Cacheable(cacheNames = "CellInstockPlanService_queryByFinalVersion", key = "#query.month + '_' + #query.isOversea+ '_' + #query.cellsType+ '_' + #query.finalVersion")
    public List<CellInstockPlanDTO> queryByFinalVersion(CellInstockPlanQuery query) {

        QCellInstockPlan cellPlanLine = QCellInstockPlan.cellInstockPlan;
        JPAQuery<CellInstockPlan> where = jpaQueryFactory.select(cellPlanLine).
                from(cellPlanLine).
                where(cellPlanLine.month.eq(query.getMonth())
                );
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            where.where(cellPlanLine.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            where.where(cellPlanLine.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getFinalVersion())) {
            where.where(cellPlanLine.finalVersion.eq(query.getFinalVersion()));
        }
        where.orderBy(cellPlanLine.startTime.asc());
        List<CellInstockPlan> datas = where.fetch();
        return convert.toDto(datas);
    }

    /**
     * 获取国内海外版本入库计划数据
     *
     * @param query
     * @return
     */
    @Override
    public List<CellInstockPlanDTO> query(CellInstockPlanQuery query) {
        Pair<String, String> versions = null;
        if (StringUtils.isNotEmpty(query.getVersion())) {
            versions = new ImmutablePair<>(query.getVersion(), null);
        } else {
            versions = getLastVersion(query);
        }
        if (versions.getLeft() == null && versions.getRight() == null) {
            return Lists.newArrayList();
        }
        List<CellInstockPlanDTO> cellInstockPlanDTOS = new LinkedList<>();
        if (versions.getLeft() != null) {
            List<CellInstockPlanDTO> left = cellInstockPlanService.queryCacheByVersion(versions.getLeft());
            cellInstockPlanDTOS.addAll(left);
        }
        if (versions.getRight() != null) {
            List<CellInstockPlanDTO> right = cellInstockPlanService.queryCacheByVersion(versions.getRight());
            cellInstockPlanDTOS.addAll(right);
        }

        return cellInstockPlanDTOS
                .stream().filter(i -> {
                    if (StringUtils.isNotEmpty(query.getBasePlace()) && !i.getBasePlace().equals(query.getBasePlace())) {
                        return false;
                    }
                    if (StringUtils.isNotEmpty(query.getWorkshop()) && !i.getWorkshop().equals(query.getWorkshop())) {
                        return false;
                    }
                    return !StringUtils.isNotEmpty(query.getCellsType()) || i.getCellsType().equals(query.getCellsType());
                }).collect(Collectors.toList());
    }

    /**
     * 获取版本号
     *
     * @param query
     * @return
     */
    public Pair<String, String> getLastVersion(CellInstockPlanQuery query) {

        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getLastVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getLastVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getLastVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }

    }

    @Override
    public Page<CellInstockPlanALowDTO> queryAByPage(CellInstockPlanQuery query) {
        List<CellPlanLineALowDTO> result = calc(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "version").and(Sort.by(Sort.Direction.ASC, "id"));
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        int startIndex = (query.getPageNumber() - 1) * query.getPageSize();
        int toIndex = startIndex + pageable.getPageSize();
        toIndex = toIndex > result.size() ? result.size() : toIndex;
        return new PageImpl(result.subList(startIndex, toIndex), pageable, result.size());
    }

    @Override
    @Cacheable(cacheNames = "CellInstockPlanService_queryCacheByVersion", key = "#p0", unless = "#result == null", condition = "#p0!=null")
    public List<CellInstockPlanDTO> queryCacheByVersion(String version) {
        QCellInstockPlan qCellPlanLine = QCellInstockPlan.cellInstockPlan;
        JPAQuery<CellInstockPlan> where = jpaQueryFactory.select(qCellPlanLine).
                from(qCellPlanLine).where(qCellPlanLine.version.eq(version));
        List<CellInstockPlan> datas = where.fetch();
        return datas.stream().map(convert::toDto).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", key = "#p0",  condition = "#p0!=null")
    public void clearCacheByVersion(String version) {
        log.info("clear CellInstockPlan cache by version:{}",version);
    }

    private List<CellPlanLineALowDTO> calc(CellInstockPlanQuery query) {
        return null;
    }

    private String getLastVersion(String month, String isOversea) {

        QCellInstockPlan cellInstockPlan = QCellInstockPlan.cellInstockPlan;

        String version = jpaQueryFactory.select(cellInstockPlan.version.max()).from(cellInstockPlan).where(
                cellInstockPlan.month.eq(month)
        ).where(
                cellInstockPlan.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    /**
     * 获取版本号
     *
     * @param query
     * @return
     */
    public Pair<String, String> getFinalVersion(CellInstockPlanQuery query) {

        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        //获取上个月
        if ("Y".equals(query.getMinusMonthsFlag())) {
            month = DateUtil.getMonth(LocalDate.now().minusMonths(1));
        }
        String isOversea = OverseaConstant.INLAND;
        String InVersion = getFinalVersion(month, isOversea);
        isOversea = OverseaConstant.OVERSEA;
        String outVersion = getFinalVersion(month, isOversea);
        return new ImmutablePair<>(InVersion, outVersion);
    }

    private String getFinalVersion(String month, String isOversea) {
        QCellInstockPlan cellInstockPlan = QCellInstockPlan.cellInstockPlan;

        String version = jpaQueryFactory.select(cellInstockPlan.finalVersion.max()).from(cellInstockPlan).where(
                cellInstockPlan.month.eq(month)
        ).where(
                cellInstockPlan.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }


    @Override
    public List<CellInstockPlanSummaryDTO> findSummaryByMonth(CellInstockPlanQuery query) {
        Assert.notNull(query.getIsOverseaId());
        Assert.notNull(query.getMonth());
        //获取最新版本号
        String lastVersion = versionService.findMaxVersion(query.getIsOverseaId(), query.getMonth());
        lastVersion = StringUtils.isNotEmpty(lastVersion) ? lastVersion : "-1";
        JPAQuery<CellInstockPlanSummaryDTO> jpaQuery = jpaQueryFactory.select(
                        Projections.fields(
                                CellInstockPlanSummaryDTO.class,
                                qCellInstockPlan.isOverseaId.as("isOverseaId"),
                                qCellInstockPlan.isOversea.as("isOversea"),
                                qCellInstockPlan.cellsTypeId.as("cellTypeId"),
                                qCellInstockPlan.cellsType.as("cellType"),
                                qCellInstockPlan.productionGrade.as("productionGrade"),
                                qCellInstockPlan.transparentDoubleGlassId.as("transparentDoubleGlassId"),
                                qCellInstockPlan.transparentDoubleGlass.as("transparentDoubleGlass"),
                                qCellInstockPlan.regionalCountryId.as("regionalCountryId"),
                                qCellInstockPlan.regionalCountry.as("regionalCountry"),
                                qCellInstockPlan.aestheticsId.as("aestheticsId"),
                                qCellInstockPlan.aesthetics.as("aesthetics"),
                                qCellInstockPlan.cellSourceId.as("cellSourceId"),
                                qCellInstockPlan.cellSource.as("cellSource"),
                                qCellInstockPlan.hTraceId.as("hTraceId"),
                                qCellInstockPlan.hTrace.as("hTrace"),
                                qCellInstockPlan.month.as("month"),
                                qCellInstockPlan.mainGridSpace.as("mainGridSpace"),
                                qCellInstockPlan.qtyPc.sum().as("quantity")
                        )
                )
                .from(qCellInstockPlan)
                .where(qCellInstockPlan.isDeleted.eq(DeleteEnum.NO.getCode()))
                .where(qCellInstockPlan.isOverseaId.eq(query.getIsOverseaId()))
                .where(qCellInstockPlan.month.eq(query.getMonth()))
                .where(qCellInstockPlan.finalVersion.eq(lastVersion))
                .groupBy(qCellInstockPlan.isOverseaId,
                        qCellInstockPlan.isOversea,
                        qCellInstockPlan.cellsType,
                        qCellInstockPlan.cellsTypeId,
                        qCellInstockPlan.productionGrade,
                        qCellInstockPlan.transparentDoubleGlass,
                        qCellInstockPlan.transparentDoubleGlassId,
                        qCellInstockPlan.regionalCountry,
                        qCellInstockPlan.regionalCountryId,
                        qCellInstockPlan.aesthetics,
                        qCellInstockPlan.aestheticsId,
                        qCellInstockPlan.cellSource,
                        qCellInstockPlan.cellSourceId,
                        qCellInstockPlan.hTrace,
                        qCellInstockPlan.hTraceId,
                        qCellInstockPlan.month,
                        qCellInstockPlan.mainGridSpace);
        List<CellInstockPlanSummaryDTO> fetch = jpaQuery.fetch();
        return fetch;
    }

    @Override
    public List<CellInstockPlanDTO> findByCondtition(Long isOverseaId, String planVersion, LocalDate startDate, LocalDate endDate) {
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qCellInstockPlan.isOverseaId.eq(isOverseaId));
        builder.and(qCellInstockPlan.version.eq(planVersion));
        builder.and(qCellInstockPlan.startTime.goe(startDate.atTime(LocalTime.MAX)));
        builder.and(qCellInstockPlan.endTime.loe(endDate.atTime(LocalTime.MAX)));
        List<CellInstockPlan> data = IterableUtils.toList(repository.findAll(builder));
        return convert.toDto(data);
    }

    @Override
    public void getItemCodesByCellInstockPlan(IdsDTO idsDTO) {
        List<CellInstockPlan> allById = repository.findAllById(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        Map<Long, String> itemCodesByCellInstockPlan = cellItemCodeService.getItemCodesByCellInstockPlan(allById);
        log.info("查询料号结果:{}", itemCodesByCellInstockPlan);

    }
}
