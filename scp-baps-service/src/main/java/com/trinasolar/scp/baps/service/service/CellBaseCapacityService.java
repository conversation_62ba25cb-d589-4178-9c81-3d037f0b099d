package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDTO;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacitySaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * IE产能表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
public interface CellBaseCapacityService  {
    /**
     * 分页获取IE产能表
     *
     * @param query 查询对象
     * @return IE产能表分页对象
     */
    Page<CellBaseCapacityDTO> queryByPage(CellBaseCapacityQuery query);

    /**
     * 根据主键获取IE产能表详情
     *
     * @param id 主键
     * @return IE产能表详情
     */
    CellBaseCapacityDTO queryById(Long id);

    /**
     * 保存或更新IE产能表
     *
     * @param saveDTO IE产能表保存对象
     * @return IE产能表对象
     */
    CellBaseCapacityDTO save(CellBaseCapacitySaveDTO saveDTO);

    /**
     * 根据主键逻辑删除IE产能表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(CellBaseCapacityQuery query, HttpServletResponse response);


    Map<String, List<CellBaseCapacityDTO>> queryForDm(CellBaseCapacityQuery query);


    BigDecimal findByCondition(Long isOverseaId, String workshop, LocalDate startDate, LocalDate endDate, String productCategory, String crystalType);
    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

