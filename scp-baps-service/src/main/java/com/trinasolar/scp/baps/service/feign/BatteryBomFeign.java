package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(value = FeignConstant.SCP_BATTERY_BOM_API, path = "/scp-battery-bom-api")
public interface BatteryBomFeign {

    /**
     * 查询电池编码和类型下拉列表
     *
     * @return 查询结果
     */
    @PostMapping("/battery-type-main/queryBatteryCodeTypeAll")
    @ApiOperation(value = "查询电池编码和类型下拉列表", notes = "查询电池编码和类型下拉列表")
    public ResponseEntity<Results<List<BatteryTypeMainDTO>>> queryBatteryCodeTypeAll();
}
