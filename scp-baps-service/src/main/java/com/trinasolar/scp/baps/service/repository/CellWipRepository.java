package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellWip;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 工单生成预览
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Repository
public interface CellWipRepository extends JpaRepository<CellWip, Long>, QuerydslPredicateExecutor<CellWip> {
}
