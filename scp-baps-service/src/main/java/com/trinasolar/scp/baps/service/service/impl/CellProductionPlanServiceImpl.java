package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellPlanLineDEConvert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.convert.CellProductionPlanDEConvert;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanQuery;
import com.trinasolar.scp.baps.domain.query.MaterielMatchHeaderQuery;
import com.trinasolar.scp.baps.domain.save.CellProductionPlanSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.feign.BbomFeign;
import com.trinasolar.scp.baps.service.repository.CellPlanLineRepository;
import com.trinasolar.scp.baps.service.repository.CellProductionPlanRepository;
import com.trinasolar.scp.baps.service.service.CellProductionPlanService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 投产计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Slf4j
@Service("cellProductionPlanService")
@RequiredArgsConstructor
public class CellProductionPlanServiceImpl implements CellProductionPlanService {
    private static final QCellProductionPlan qCellProductionPlan = QCellProductionPlan.cellProductionPlan;
    private final CellProductionPlanDEConvert convert;
    private final CellProductionPlanRepository repository;
    private final JPAQueryFactory jpaQueryFactory;
    private final BbomFeign bbomFeign;
    private final CellPlanLineRepository cellPlanLineRepository;

    private  final QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;

    private  final CellPlanLineDEConvert cellPlanLineDEConvert;

    @Override
    public Page<CellProductionPlanDTO> queryByPage(CellProductionPlanQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellProductionPlan> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellProductionPlanQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellProductionPlan.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellProductionPlan.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellProductionPlan.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellProductionPlan.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellProductionPlan.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellProductionPlan.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellProductionPlan.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qCellProductionPlan.workunit.eq(query.getWorkunit()));
        }
        if (query.getWorkunitId() != null) {
            booleanBuilder.and(qCellProductionPlan.workunitId.eq(query.getWorkunitId()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellProductionPlan.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellProductionPlan.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (query.getLineName() != null) {
            booleanBuilder.and(qCellProductionPlan.lineName.eq(query.getLineName()));
        }
        if (query.getNumberLine() != null) {
            booleanBuilder.and(qCellProductionPlan.numberLine.eq(query.getNumberLine()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qCellProductionPlan.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qCellProductionPlan.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellProductionPlan.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            booleanBuilder.and(qCellProductionPlan.cellSource.eq(query.getCellSource()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellProductionPlan.month.eq(query.getMonth()));
        }
        if (query.getCellMv() != null) {
            booleanBuilder.and(qCellProductionPlan.cellMv.eq(query.getCellMv()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qCellProductionPlan.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qCellProductionPlan.itemCode.eq(query.getItemCode()));
        }
        if (query.getStartTime() != null) {
            booleanBuilder.and(qCellProductionPlan.startTime.eq(query.getStartTime()));
        }
        if (query.getEndTime() != null) {
            booleanBuilder.and(qCellProductionPlan.endTime.eq(query.getEndTime()));
        }
        if (query.getQtyPc() != null) {
            booleanBuilder.and(qCellProductionPlan.qtyPc.eq(query.getQtyPc()));
        }
        if (query.getBbomId() != null) {
            booleanBuilder.and(qCellProductionPlan.bbomId.eq(query.getBbomId()));
        }
        if (query.getFromId() != null) {
            booleanBuilder.and(qCellProductionPlan.fromId.eq(query.getFromId()));
        }
    }

    @Override
    public CellProductionPlanDTO queryById(Long id) {
        CellProductionPlan queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellProductionPlanDTO save(CellProductionPlanSaveDTO saveDTO) {
        CellProductionPlan newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellProductionPlan());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    /**
     * 获取国内海外版本数据
     *
     * @param query
     * @param versions
     * @return
     */
    @Override
    public List<CellProductionPlanDTO> query(CellProductionPlanQuery query, Pair<String, String> versions) {
        QCellProductionPlan cellProductionPlan = QCellProductionPlan.cellProductionPlan;
        JPAQuery<CellProductionPlan> where = jpaQueryFactory.select(cellProductionPlan).
                from(cellProductionPlan).
                where(
                        cellProductionPlan.month.eq(query.getMonth())
                );
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            where.where(cellProductionPlan.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            where.where(cellProductionPlan.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            where.where(cellProductionPlan.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            where.where(cellProductionPlan.cellsType.eq(query.getCellsType()));
        }
        if (Objects.nonNull(versions)) {
            if (Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getRight())) {
                where.where(cellProductionPlan.version.in(versions.getLeft(), versions.getRight()));
            } else if (Objects.nonNull(versions.getLeft())) {
                where.where(cellProductionPlan.version.eq(versions.getLeft()));
            } else if (Objects.nonNull(versions.getRight())) {
                where.where(cellProductionPlan.version.eq(versions.getRight()));
            }
        }
        where.orderBy(cellProductionPlan.isOversea.asc(), cellProductionPlan.startTime.asc());
        List<CellProductionPlan> datas = where.fetch();
        return convert.toDto(datas);
    }


    @Override
    @SneakyThrows
    public void export(CellProductionPlanQuery query, HttpServletResponse response) {
        List<CellProductionPlanDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "投产计划", "投产计划", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    public List<CellProductionPlanDTO> listForMatchItem(CellProductionPlanQuery query) {
        BooleanBuilder booleanBuilder = buildMatchItemQuery(query);
        Iterable<CellProductionPlan> all = repository.findAll(booleanBuilder);
        return convert.toDto(Lists.newArrayList(all));
    }

    private BooleanBuilder buildMatchItemQuery(CellProductionPlanQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qCellProductionPlan.month.eq(query.getMonth()));
        booleanBuilder.and(qCellProductionPlan.basePlace.eq(query.getBasePlace()));
        booleanBuilder.and(qCellProductionPlan.workshop.eq(query.getWorkshop()));
        booleanBuilder.and(qCellProductionPlan.workunit.eq(query.getWorkunit()));
        //booleanBuilder.and(qCellProductionPlan.numberLine.eq(query.getNumberLine()));
        booleanBuilder.and(qCellProductionPlan.cellsType.eq(query.getCellsType()));
        if (null == query.getNumberLine()) {
            booleanBuilder.and(qCellProductionPlan.numberLine.isNull());
        } else {
            booleanBuilder.and(qCellProductionPlan.numberLine.eq(query.getNumberLine()));
        }
        if (StringUtils.isBlank(query.getHTrace()) || "无".equals(query.getHTrace())) {
            booleanBuilder.and(qCellProductionPlan.hTrace.isNull().or(qCellProductionPlan.hTrace.isEmpty()).or(qCellProductionPlan.hTrace.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isBlank(query.getAesthetics()) || "无".equals(query.getAesthetics())) {
            booleanBuilder.and(qCellProductionPlan.aesthetics.isNull().or(qCellProductionPlan.aesthetics.isEmpty()).or(qCellProductionPlan.aesthetics.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isBlank(query.getTransparentDoubleGlass()) || "无".equals(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellProductionPlan.transparentDoubleGlass.isNull().or(qCellProductionPlan.transparentDoubleGlass.isEmpty()).or(qCellProductionPlan.transparentDoubleGlass.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isBlank(query.getCellSource()) || "无".equals(query.getCellSource())) {
            booleanBuilder.and(qCellProductionPlan.cellSource.isNull().or(qCellProductionPlan.cellSource.isEmpty()).or(qCellProductionPlan.cellSource.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.cellSource.eq(query.getCellSource()));
        }
        /**************************新增字段***********************************/
        if (StringUtils.isBlank(query.getIsSpecialRequirement()) || "无".equals(query.getIsSpecialRequirement())) {
            booleanBuilder.and(qCellProductionPlan.isSpecialRequirement.isNull().or(qCellProductionPlan.isSpecialRequirement.isEmpty()).or(qCellProductionPlan.isSpecialRequirement.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.isSpecialRequirement.eq(query.getIsSpecialRequirement()));
        }
        if (StringUtils.isBlank(query.getCellMfrs()) || "无".equals(query.getCellMfrs())) {
            booleanBuilder.and(qCellProductionPlan.cellMfrs.isNull().or(qCellProductionPlan.cellMfrs.isEmpty()).or(qCellProductionPlan.cellMfrs.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.cellMfrs.eq(query.getCellMfrs()));
        }
        if (StringUtils.isBlank(query.getRegionalCountry()) || "无".equals(query.getRegionalCountry())) {
            booleanBuilder.and(qCellProductionPlan.regionalCountry.isNull().or(qCellProductionPlan.regionalCountry.isEmpty()).or(qCellProductionPlan.regionalCountry.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.regionalCountry.eq(query.getRegionalCountry()));
        }
        if (StringUtils.isBlank(query.getSiMfrs()) || "无".equals(query.getSiMfrs())) {
            booleanBuilder.and(qCellProductionPlan.siMfrs.isNull().or(qCellProductionPlan.siMfrs.isEmpty()).or(qCellProductionPlan.siMfrs.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.siMfrs.eq(query.getSiMfrs()));
        }
        if (StringUtils.isBlank(query.getScreenPlateMfrs()) || "无".equals(query.getScreenPlateMfrs())) {
            booleanBuilder.and(qCellProductionPlan.screenPlateMfrs.isNull().or(qCellProductionPlan.screenPlateMfrs.isEmpty()).or(qCellProductionPlan.screenPlateMfrs.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.screenPlateMfrs.eq(query.getScreenPlateMfrs()));
        }
        if (StringUtils.isBlank(query.getSilverPulpMfrs()) || "无".equals(query.getSilverPulpMfrs())) {
            booleanBuilder.and(qCellProductionPlan.silverPulpMfrs.isNull().or(qCellProductionPlan.silverPulpMfrs.isEmpty()).or(qCellProductionPlan.silverPulpMfrs.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.silverPulpMfrs.eq(query.getSilverPulpMfrs()));
        }
        if (StringUtils.isBlank(query.getLowResistance()) || "无".equals(query.getLowResistance())) {
            booleanBuilder.and(qCellProductionPlan.lowResistance.isNull().or(qCellProductionPlan.lowResistance.isEmpty()).or(qCellProductionPlan.lowResistance.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.lowResistance.eq(query.getLowResistance()));
        }
        if (StringUtils.isBlank(query.getDemandBasePlace()) || "无".equals(query.getDemandBasePlace())) {
            booleanBuilder.and(qCellProductionPlan.demandBasePlace.isNull().or(qCellProductionPlan.demandBasePlace.isEmpty()).or(qCellProductionPlan.demandBasePlace.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.demandBasePlace.eq(query.getDemandBasePlace()));
        }
        if (StringUtils.isBlank(query.getWaferGrade()) || "无".equals(query.getWaferGrade())) {
            booleanBuilder.and(qCellProductionPlan.waferGrade.isNull().or(qCellProductionPlan.waferGrade.isEmpty()).or(qCellProductionPlan.waferGrade.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.waferGrade.eq(query.getWaferGrade()));
        }
        if (StringUtils.isBlank(query.getProcessCategory()) || "无".equals(query.getProcessCategory())) {
            booleanBuilder.and(qCellProductionPlan.processCategory.isNull().or(qCellProductionPlan.processCategory.isEmpty()).or(qCellProductionPlan.processCategory.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.processCategory.eq(query.getProcessCategory()));
        }
        if (StringUtils.isBlank(query.getProductionGrade()) || "无".equals(query.getProductionGrade())) {
            booleanBuilder.and(qCellProductionPlan.productionGrade.isNull().or(qCellProductionPlan.productionGrade.isEmpty()).or(qCellProductionPlan.productionGrade.eq("无")));
        } else {
            booleanBuilder.and(qCellProductionPlan.productionGrade.eq(query.getProductionGrade()));
        }
        return booleanBuilder;
    }

    @Override
    public Page<CellProductionPlanDTO> queryTicketOpening(CellProductionPlanQuery query) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //获取最新版本的投产明细
        //左国内 右国外
        Pair<String, String> stringPair= getFinalVersion(query);
        List<CellPlanLineDTO> domesticCellInstockPlan= getDomesticCellInstockPlan(stringPair.getLeft(),query);
        List<CellPlanLineDTO> abroadCellInstockPlan= getAbroadCellInstockPlan(stringPair.getRight(),query);
        domesticCellInstockPlan.addAll(abroadCellInstockPlan);
        List<CellProductionPlanSummaryDTO> summaryDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(domesticCellInstockPlan)) {
            //过滤掉5A料号为空的
            domesticCellInstockPlan.stream().filter(x -> StringUtils.isNotEmpty(x.getItemCode())).collect(Collectors.toList());
            //分组汇总
            summaryDTOList = groupByProductionPlan(domesticCellInstockPlan);
        }
        return new PageImpl(summaryDTOList.subList((query.getPageNumber() - 1) * query.getPageSize(), Math.min(query.getPageNumber() * query.getPageSize(), summaryDTOList.size())), pageable, summaryDTOList.size());

    }

    //分组汇总 纵向转横向
    private List<CellProductionPlanSummaryDTO> groupByProductionPlan(List<CellPlanLineDTO> productionPlanDTOList) {
        List<CellProductionPlanSummaryDTO> res = new ArrayList<>();
        Map<String, List<CellPlanLineDTO>> collectByGroupField = productionPlanDTOList.stream().collect(Collectors.groupingBy(i -> StringUtils.join(
                        i.getCellsType(), "/",
                        i.getAesthetics(), "/",
                        i.getTransparentDoubleGlass(), "/",
                        i.getHTrace(), "/",
                        i.getCellSource(), "/",
                        i.getWaferGrade(), "/",
                        i.getIsSpecialRequirement(), "/",
                        i.getScreenPlateMfrs(), "/",
                        i.getSiMfrs(), "/",
                        i.getCellMfrs(), "/",
                        i.getSilverPulpMfrs(), "/",
                        i.getLowResistance(), "/",
                        i.getDemandBasePlace(), "/",
                        i.getItemCode(), "/",
                        //i.getQtyPc(), "/",
                        i.getBasePlace(), "/",
                        i.getWorkshop(), "/",
                        i.getWorkunit(), "/",
                        i.getMonth(), "/",
                        i.getProductionGrade(), "/",
                        i.getIsOversea(), "/"
                )
        ));
        for (Map.Entry<String, List<CellPlanLineDTO>> entry : collectByGroupField.entrySet()) {
            List<CellPlanLineDTO> entryValue = entry.getValue();
            CellPlanLineDTO cellPlanLineDTO = entryValue.get(0);
            CellProductionPlanSummaryDTO item = convert.detailsToSummary(cellPlanLineDTO);
            item.setQtyPc(entryValue.stream().map(planDto -> Optional.ofNullable(planDto.getQtyPc()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
            res.add(item);
        }

        Map<String, List<CellPlanLineDTO>> collectByDate = productionPlanDTOList.stream().collect(Collectors.groupingBy(item -> StringUtils.join(
                        item.getCellsType(), "/",
                        item.getAesthetics(), "/",
                        item.getTransparentDoubleGlass(), "/",
                        item.getHTrace(), "/",
                        item.getCellSource(), "/",
                        item.getWaferGrade(), "/",
                        item.getIsSpecialRequirement(), "/",
                        item.getScreenPlateMfrs(), "/",
                        item.getSiMfrs(), "/",
                        item.getCellMfrs(), "/",
                        item.getSilverPulpMfrs(), "/",
                        item.getLowResistance(), "/",
                        item.getDemandBasePlace(), "/",
                        item.getItemCode(), "/",
                        //item.getQtyPc(), "/",
                        item.getBasePlace(), "/",
                        item.getWorkshop(), "/",
                        item.getWorkunit(), "/",
                        item.getMonth(), "/",
                        item.getProductionGrade(), "/",
                        item.getIsOversea(), "/"
                )
        ));
        // 1-[q,b,c,e]
        res.stream().forEach(item -> {
            String itemKey = StringUtils.join(
                    item.getCellsType(), "/",
                    item.getAesthetics(), "/",
                    item.getTransparentDoubleGlass(), "/",
                    item.getHTrace(), "/",
                    item.getCellSource(), "/",
                    item.getWaferGrade(), "/",
                    item.getIsSpecialRequirement(), "/",
                    item.getScreenPlateMfrs(), "/",
                    item.getSiMfrs(), "/",
                    item.getCellMfrs(), "/",
                    item.getSilverPulpMfrs(), "/",
                    item.getLowResistance(), "/",
                    item.getDemandBasePlace(), "/",
                    item.getItemCode(), "/",
                    //item.getQtyPc(), "/",
                    item.getBasePlace(), "/",
                    item.getWorkshop(), "/",
                    item.getWorkunit(), "/",
                    item.getMonth(), "/",
                    item.getProductionGrade(), "/",
                    item.getIsOversea(), "/"
            );
            List<String> subList = new ArrayList<>();
            Map<String, BigDecimal> subMap = new HashMap();
            //获取每一天下面有多少数量
            for (Map.Entry<String, List<CellPlanLineDTO>> entry : collectByDate.entrySet()) {
                List<CellPlanLineDTO> list = entry.getValue();
                Map<String, List<CellPlanLineDTO>> planMap = list.stream().collect(Collectors.groupingBy(CellPlanLineDTO::getStartTimeStr));
                planMap.forEach((startTimeStr, dataList) ->{
                    if (entry.getKey().indexOf(itemKey) != -1) {
                        subList.add(startTimeStr);
                        subMap.put(startTimeStr, dataList.stream().map(planDto -> Optional.ofNullable(planDto.getQtyPc()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
                    }
                });
            }
            item.setSubList(subList.stream().sorted().collect(Collectors.toList()));
            item.setSubMap(subMap);
        });
        return res;
    }

    private void buildWhereByTicket(BooleanBuilder booleanBuilder, CellProductionPlanQuery query) {
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellProductionPlan.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellProductionPlan.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellProductionPlan.month.eq(query.getMonth()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellProductionPlan.cellsType.eq(query.getCellsType()));
        }
        booleanBuilder.and(qCellProductionPlan.finalVersion.isNotNull());
        booleanBuilder.and(qCellProductionPlan.version.isNotNull());
    }

    /**
     * 国内外版本
     *
     * @param query
     * @return
     */
    public List<CellProductionPlanDTO> queryByFirst(CellProductionPlanQuery query) {
        String version = queryMaxVersion(query);
        log.info("投产版本：" + version);
        if (version == null) {
            return null;
        }
        //查询最大版本号对应的数据
        JPAQuery<CellProductionPlan> whereData = jpaQueryFactory.select(
                qCellProductionPlan
        ).from(qCellProductionPlan).where(
                qCellProductionPlan.month.eq(query.getMonth())
        );
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            whereData.where(qCellProductionPlan.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            whereData.where(qCellProductionPlan.cellSource.eq(query.getCellSource()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            whereData.where(qCellProductionPlan.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            whereData.where(qCellProductionPlan.cellsType.eq(query.getCellsType()));
        }
        List<CellProductionPlan> fetch = whereData.where(qCellProductionPlan.version.eq(version)).fetch();

        return convert.toDto(fetch);
    }

    public String queryMaxVersion(CellProductionPlanQuery query) {
        //求最大版本号
        JPAQuery<String> where = jpaQueryFactory.select(
                qCellProductionPlan.version.max()
        ).from(qCellProductionPlan).where(
                qCellProductionPlan.month.eq(query.getMonth())
        ).where(
                qCellProductionPlan.version.isNotNull()
        );

        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            where.where(qCellProductionPlan.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            where.where(qCellProductionPlan.cellSource.eq(query.getCellSource()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            where.where(qCellProductionPlan.basePlace.eq(query.getBasePlace()));
        }
        String version = where.fetchFirst();
        return version;
    }

    //排产发版调用接口
    @Override
    public void summaryGroupByCellProductionPlan(List<CellProductionPlanDTO> planDTOList) {
        Map<String, List<CellProductionPlanDTO>> listMap = planDTOList.stream().collect(Collectors.groupingBy(CellProductionPlanDTO::filedGroup));
        //先查询数据相同的 删除 新增 保留指定过的数据
        extractedDeleteByQuery(listMap);
    }
    //排产发版调用接口
    public void summaryGroupByCellProductionPlan(CellProductionPlanQuery query){
        //月份必传
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qCellProductionPlan.month.eq(query.getMonth()));
        List<CellProductionPlanDTO> planDTOList=convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
        Map<String,List<CellProductionPlanDTO>>listMap=planDTOList.stream().collect(Collectors.groupingBy(CellProductionPlanDTO::filedGroup));
       //先查询数据相同的 删除 新增 保留指定过的数据
        extractedDeleteByQuery(listMap);
    }
    private void extractedDeleteByQuery(Map<String, List<CellProductionPlanDTO>> listMap) {
        listMap.entrySet().stream().forEach(entry -> {
            MaterielMatchHeaderQuery headerQuery = getMaterielMatchHeaderQuery(entry);
            //查询唯一
            List<MaterielMatchHeaderDTO>matchHeaderDTOList=bbomFeign.queryList(headerQuery).getBody().getData();
            if(CollectionUtils.isNotEmpty(matchHeaderDTOList)){
                //删除头  删除行
                //一个头下面多个行
                List<Map<String,MaterielMatchLineDTO>> matchLineListMap=new ArrayList<>();
                bbomFeign.deleteByHeadId(matchHeaderDTOList);
                MaterielMatchHeaderDTO headerDTO = convertQuery(headerQuery);
                //新增头
                //新增行
               // headerDTO.setPlanDTOList(entry.getValue());
                headerDTO.setMatchLineListMap(matchLineListMap);
                bbomFeign.addMatchInfo(headerDTO);
            } else {
                MaterielMatchHeaderDTO headerDTO = convertQuery(headerQuery);
                //新增头
                //新增行
             //   headerDTO.setPlanDTOList(entry.getValue());
                headerDTO.setMatchLineListMap(new ArrayList<>());
                bbomFeign.addMatchInfo(headerDTO);
            }
        });
    }

    private static MaterielMatchHeaderQuery getMaterielMatchHeaderQuery(Map.Entry<String, List<CellProductionPlanDTO>> entry) {
        String[] splitKey = entry.getKey().split("/");
        //根据维度查询bbom_materiel_match_header 是否存在
        MaterielMatchHeaderQuery headerQuery = new MaterielMatchHeaderQuery();
        headerQuery.setBasePlace(StringUtils.isNotEmpty(splitKey[0]) ? splitKey[0] : "无");
        headerQuery.setWorkshop(StringUtils.isNotEmpty(splitKey[1]) ? splitKey[1] : "无");
        headerQuery.setWorkunit(StringUtils.isNotEmpty(splitKey[2]) ? splitKey[2] : "无");
        headerQuery.setBatteryType(StringUtils.isNotEmpty(splitKey[3]) ? splitKey[3] : "无");
        headerQuery.setHTrace(StringUtils.isNotEmpty(splitKey[4]) ? splitKey[4] : "无");
        headerQuery.setAesthetics(StringUtils.isNotEmpty(splitKey[5]) ? splitKey[5] : "无");
        headerQuery.setTransparentDoubleGlass(StringUtils.isNotEmpty(splitKey[6]) ? splitKey[6] : "无");
        headerQuery.setSpecialArea(StringUtils.isNotEmpty(splitKey[7]) ? splitKey[7] : "无");
        headerQuery.setPcsSourceType(StringUtils.isNotEmpty(splitKey[8]) ? splitKey[8] : "无");
        headerQuery.setPcsSourceLevel(StringUtils.isNotEmpty(splitKey[9]) ? splitKey[9] : "无");
        headerQuery.setBatteryManufacturer(StringUtils.isNotEmpty(splitKey[10]) ? splitKey[10] : "无");
        headerQuery.setIsSpecialRequirements(StringUtils.isNotEmpty(splitKey[11]) ? splitKey[11] : "无");
        headerQuery.setSiliconMaterialManufacturer(StringUtils.isNotEmpty(splitKey[12]) ? splitKey[12] : "无");
        headerQuery.setScreenManufacturer(StringUtils.isNotEmpty(splitKey[13]) ? splitKey[13] : "无");
        headerQuery.setSilverSlurryManufacturer(StringUtils.isNotEmpty(splitKey[14]) ? splitKey[14] : "无");
        headerQuery.setLowResistance(StringUtils.isNotEmpty(splitKey[15]) ? splitKey[15] : "无");
        headerQuery.setDemandPlace(StringUtils.isNotEmpty(splitKey[16]) ? splitKey[16] : "无");
        headerQuery.setProcessCategory(StringUtils.isNotEmpty(splitKey[17]) ? splitKey[17] : "无");
        headerQuery.setMonth(splitKey[18]);
        headerQuery.setProductionGrade(StringUtils.isNotEmpty(splitKey[19]) ? splitKey[19] : "无");
        headerQuery.setLine(new BigDecimal(splitKey[20]));
        return headerQuery;
    }

    //query转换dto
    public MaterielMatchHeaderDTO convertQuery(MaterielMatchHeaderQuery headerQuery) {
        MaterielMatchHeaderDTO headerDTO = new MaterielMatchHeaderDTO();
        headerDTO.setBasePlace(StringUtils.isNotEmpty(headerQuery.getBasePlace()) ? headerQuery.getBasePlace() : "无");
        headerDTO.setWorkshop(StringUtils.isNotEmpty(headerQuery.getWorkshop()) ? headerQuery.getWorkshop() : "无");
        headerDTO.setWorkunit(StringUtils.isNotEmpty(headerQuery.getWorkunit()) ? headerQuery.getWorkunit() : "无");
        headerDTO.setBatteryType(StringUtils.isNotEmpty(headerQuery.getBatteryType()) ? headerQuery.getBatteryType() : "无");
        headerDTO.setHTrace(StringUtils.isNotEmpty(headerQuery.getHTrace()) ? headerQuery.getHTrace() : "无");
        headerDTO.setAesthetics(StringUtils.isNotEmpty(headerQuery.getAesthetics()) ? headerQuery.getAesthetics() : "无");
        headerDTO.setTransparentDoubleGlass(StringUtils.isNotEmpty(headerQuery.getTransparentDoubleGlass()) ? headerQuery.getTransparentDoubleGlass() : "无");
        headerDTO.setSpecialArea(StringUtils.isNotEmpty(headerQuery.getSpecialArea()) ? headerQuery.getSpecialArea() : "无");
        headerDTO.setPcsSourceType(StringUtils.isNotEmpty(headerQuery.getPcsSourceType()) ? headerQuery.getPcsSourceType() : "无");
        headerDTO.setPcsSourceLevel(StringUtils.isNotEmpty(headerQuery.getPcsSourceLevel()) ? headerQuery.getPcsSourceLevel() : "无");
        headerDTO.setBatteryManufacturer(StringUtils.isNotEmpty(headerQuery.getBatteryManufacturer()) ? headerQuery.getBatteryManufacturer() : "无");
        headerDTO.setIsSpecialRequirements(StringUtils.isNotEmpty(headerQuery.getIsSpecialRequirements()) ? headerQuery.getIsSpecialRequirements() : "无");
        headerDTO.setSiliconMaterialManufacturer(StringUtils.isNotEmpty(headerQuery.getSiliconMaterialManufacturer()) ? headerQuery.getSiliconMaterialManufacturer() : "无");
        headerDTO.setScreenManufacturer(StringUtils.isNotEmpty(headerQuery.getScreenManufacturer()) ? headerQuery.getScreenManufacturer() : "无");
        headerDTO.setSilverSlurryManufacturer(StringUtils.isNotEmpty(headerQuery.getSilverSlurryManufacturer()) ? headerQuery.getSilverSlurryManufacturer() : "无");
        headerDTO.setLowResistance(StringUtils.isNotEmpty(headerQuery.getLowResistance()) ? headerQuery.getLowResistance() : "无");
        headerDTO.setDemandPlace(StringUtils.isNotEmpty(headerQuery.getDemandPlace()) ? headerQuery.getDemandPlace() : "无");
        headerDTO.setProcessCategory(StringUtils.isNotEmpty(headerQuery.getProcessCategory()) ? headerQuery.getProcessCategory() : "无");
        headerDTO.setMonth(headerQuery.getMonth());
        headerDTO.setLine(headerQuery.getLine());
        headerDTO.setProductionGrade(StringUtils.isNotEmpty(headerQuery.getProductionGrade()) ? headerQuery.getProductionGrade() : "无");
        return headerDTO;
    }

    //bom进行5A料号匹配后进行回改投产计划数据
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeDataBy5AMatch(Cell5AItemCodeListDto dto) {
        List<CellProductionPlan> datas=new ArrayList<>();
        List<CellPlanLine> cellPlanLineDatas=new ArrayList<>();
        List<Cell5AItemCodeDto> itemDtos = dto.getItemDtos();
        for (Cell5AItemCodeDto itemCodeDto : itemDtos) {
            //对每个投产数据进行拆分处理
            long id = itemCodeDto.getId(); //投产Id
            //获取拆后的明细数据
            List<Cell5AItemCodeDetailsDto> detailsDtos = itemCodeDto.getDetailsDtos();
            if (CollectionUtils.isEmpty(detailsDtos)) continue;
            if (detailsDtos.size() > 1) {
                //拆了
                //拆后的明细数据处理
                boolean isSplite=false;
                boolean first=true;
                CellProductionPlan   cellProductionPlanFirst=null;
                CellPlanLine cellPlanLineFirst=null;
                for (Cell5AItemCodeDetailsDto detailsDto : detailsDtos) {
                    //判断是否已经在投产中拆过
                    if (first){
                        Long bbomId = detailsDto.getBbomId();
                        CellProductionPlan   cellProductionPlan = repository.selectByBbomid(bbomId);
                        if (Objects.nonNull(cellProductionPlan)) {
                            isSplite=true;
                            cellProductionPlan.setItemCode(detailsDto.getItemCode());
                            datas.add(cellProductionPlan);
                            //同步考虑入库计划
                            CellPlanLine cellPlanLine=cellPlanLineRepository.selectByBbomid(bbomId);
                            if (Objects.nonNull(cellPlanLine)){
                                cellPlanLine.setItemCode(detailsDto.getItemCode());
                                cellPlanLineDatas.add(cellPlanLine);
                            }


                        }

                    }
                    if (!isSplite) {
                        //如果拆分没有执行，进行拆分存储操作
                        if (first){
                            cellProductionPlanFirst=repository.getOne(id);
                            cellProductionPlanFirst.setItemCode(detailsDto.getItemCode());
                            cellProductionPlanFirst.setQtyPc(detailsDto.getQtyPc());
                            cellProductionPlanFirst.setNumberLine(detailsDto.getNumberLine());
                            cellProductionPlanFirst.setBbomId(detailsDto.getBbomId());
                            datas.add(cellProductionPlanFirst);
                            //同步考虑入库计划
                            cellPlanLineFirst=cellPlanLineRepository.getOne(cellProductionPlanFirst.getFromId());
                            if (Objects.nonNull(cellPlanLineFirst)){
                                cellPlanLineFirst.setItemCode(detailsDto.getItemCode());
                                cellPlanLineFirst.setQtyPc(detailsDto.getQtyPc());
                                cellPlanLineFirst.setNumberLine(detailsDto.getNumberLine());
                                cellPlanLineFirst.setBbomId(detailsDto.getBbomId());
                                cellPlanLineDatas.add(cellPlanLineFirst);
                            }
                        }else{
                            CellProductionPlan cellProductionPlan=new CellProductionPlan();
                            BeanUtil.copyProperties(cellProductionPlanFirst,cellProductionPlan,"id");
                            cellProductionPlan.setId(null);
                            cellProductionPlan.setItemCode(detailsDto.getItemCode());
                            cellProductionPlan.setQtyPc(detailsDto.getQtyPc());
                            cellProductionPlan.setNumberLine(detailsDto.getNumberLine());
                            cellProductionPlan.setBbomId(detailsDto.getBbomId());
                            datas.add(cellProductionPlan);
                            //同步考虑入库计划
                            CellPlanLine cellPlanLine=new CellPlanLine();
                            BeanUtil.copyProperties(cellPlanLineFirst,cellPlanLine,"id");
                            cellPlanLine.setId(null);
                            cellPlanLine.setItemCode(detailsDto.getItemCode());
                            cellPlanLine.setQtyPc(detailsDto.getQtyPc());
                            cellPlanLine.setNumberLine(detailsDto.getNumberLine());
                            cellPlanLine.setBbomId(detailsDto.getBbomId());
                            cellPlanLineDatas.add(cellPlanLine);

                        }

                    } else {
                        //如果拆分已经执行过了，进行修改操作
                        repository.updateItemCode(detailsDto.getBbomId(),detailsDto.getItemCode());
                        //同步考虑入库计划
                        cellPlanLineRepository.updateItemCode(detailsDto.getBbomId(),detailsDto.getItemCode());
                    }
                    first=false;
                }
            } else {
                //没拆，执行修改操作，修改5A料号属性
                CellProductionPlan cellProductionPlan = repository.getOne(id);
                cellProductionPlan.setItemCode(detailsDtos.get(0).getItemCode());
                cellProductionPlan.setBbomId(detailsDtos.get(0).getBbomId());
                datas.add(cellProductionPlan);
                //同步考虑入库计划
                CellPlanLine cellPlanLine = cellPlanLineRepository.getOne(cellProductionPlan.getFromId());
                cellPlanLine.setItemCode(detailsDtos.get(0).getItemCode());
                cellPlanLine.setBbomId(detailsDtos.get(0).getBbomId());
                cellPlanLineDatas.add(cellPlanLine);
            }


        }
        repository.saveAll(datas);
        cellPlanLineRepository.saveAll(cellPlanLineDatas);
    }
    /**
     * 获取版本号
     * @param query
     * @return
     */
    public Pair<String,String> getFinalVersion(CellProductionPlanQuery query) {

        String month=query.getMonth();
        if (StringUtils.isEmpty(month)){
            month= DateUtil.getMonth(LocalDate.now());
        }
        String isOversea= OverseaConstant.INLAND;
        String InVersion= getFinalVersion(month,isOversea);
        isOversea=OverseaConstant.OVERSEA;
        String outVersion= getFinalVersion(month,isOversea);
        return new ImmutablePair<>(InVersion,outVersion);
    }
    private String getFinalVersion(String month, String isOversea){
        QCellInstockPlan cellInstockPlan = QCellInstockPlan.cellInstockPlan;

        String version = jpaQueryFactory.select(cellInstockPlan.finalVersion.max()).from(cellInstockPlan).where(
                cellInstockPlan.month.eq(month)
        ).where(
                cellInstockPlan.isOversea.eq(isOversea)
        ).fetchFirst();
        return  version;
    }
    //获取国内数据
    public List<CellPlanLineDTO>getDomesticCellInstockPlan(String finalVersion,CellProductionPlanQuery query){
        if(StringUtils.isNotEmpty(finalVersion)){
            JPAQuery<CellPlanLine>where=jpaQueryFactory.select(qCellPlanLine).from(qCellPlanLine)
                    .where(qCellPlanLine.finalVersion.eq(finalVersion).and(qCellPlanLine.isOversea.eq("国内")));
             if(StringUtils.isNotEmpty(query.getBasePlace())){
                where.where(qCellPlanLine.basePlace.eq(query.getBasePlace()));
             }
            if(StringUtils.isNotEmpty(query.getWorkshop())){
                where.where(qCellPlanLine.workshop.eq(query.getWorkshop()));
            }
            if(StringUtils.isNotEmpty(query.getCellsType())){
                where.where(qCellPlanLine.cellsType.eq(query.getCellsType()));
            }
            return cellPlanLineDEConvert.toDto(where.fetch());
        }
        return new ArrayList<>();
    }
    //获取国外数据
    public List<CellPlanLineDTO>getAbroadCellInstockPlan(String finalVersion,CellProductionPlanQuery query){
        if(StringUtils.isNotEmpty(finalVersion)){
            JPAQuery<CellPlanLine>where=jpaQueryFactory.select(qCellPlanLine).from(qCellPlanLine)
                    .where(qCellPlanLine.finalVersion.eq(finalVersion).and(qCellPlanLine.isOversea.eq("海外")));
            if(StringUtils.isNotEmpty(query.getBasePlace())){
                where.where(qCellPlanLine.basePlace.eq(query.getBasePlace()));
            }
            if(StringUtils.isNotEmpty(query.getWorkshop())){
                where.where(qCellPlanLine.workshop.eq(query.getWorkshop()));
            }
            if(StringUtils.isNotEmpty(query.getCellsType())){
                where.where(qCellPlanLine.cellsType.eq(query.getCellsType()));
            }
            return cellPlanLineDEConvert.toDto(where.fetch());
        }
        return new ArrayList<>();
    }
}
