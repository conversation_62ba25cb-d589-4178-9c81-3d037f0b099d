package com.trinasolar.scp.baps.service.service;
import com.trinasolar.scp.baps.domain.dto.MainGridSpacingRuleDTO;
import com.trinasolar.scp.baps.domain.query.MainGridSpacingRuleQuery;
import com.trinasolar.scp.baps.domain.save.MainGridSpacingRuleSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池主栅间距规则 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-17 08:55:14
 */
public interface MainGridSpacingRuleService {
    /**
     * 分页获取电池主栅间距规则
     *
     * @param query 查询对象
     * @return 电池主栅间距规则分页对象
     */
    Page<MainGridSpacingRuleDTO> queryByPage(MainGridSpacingRuleQuery query);

    /**
     * 根据主键获取电池主栅间距规则详情
     *
     * @param id 主键
     * @return 电池主栅间距规则详情
     */
    MainGridSpacingRuleDTO queryById(Long id);

    /**
     * 保存或更新电池主栅间距规则
     *
     * @param saveDTO 电池主栅间距规则保存对象
     * @return 电池主栅间距规则对象
     */
    MainGridSpacingRuleDTO save(MainGridSpacingRuleSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池主栅间距规则
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(MainGridSpacingRuleQuery query, HttpServletResponse response);

    void importData(MultipartFile multipartFile);

    MainGridSpacingRuleDTO applyRule(MainGridSpacingRuleQuery query);

    List<MainGridSpacingRuleDTO> getAllRules(MainGridSpacingRuleQuery query);
}

