package com.trinasolar.scp.baps.service.feign;


import com.trinasolar.scp.baps.domain.dto.system.CarbonCertCodeDTO;
import com.trinasolar.scp.baps.domain.dto.system.CarbonCertHeaderDTO;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * carbon服务feign调用
 *
 * @author: zsj
 * @create: 2025年2月21日09:12:08
 */
@FeignClient(value = FeignConstant.SCP_CERT_API, path = "/scp-cert-api")
public interface CarbonFeign {

    @PostMapping("/carbon-cert/findlist")
    @ApiOperation(value = "法碳证书查询信息", notes = "法碳证书查询信息")
    ResponseEntity<Results<List<CarbonCertHeaderDTO>>> findList(@RequestBody CarbonCertCodeDTO carbonCertCodeDTO);
}
