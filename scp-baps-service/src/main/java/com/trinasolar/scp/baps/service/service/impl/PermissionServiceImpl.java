package com.trinasolar.scp.baps.service.service.impl;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.feign.SystemFeign;
import com.trinasolar.scp.baps.service.service.PermissionService;
import com.trinasolar.scp.common.api.base.DataPrivilegeDTO;
import com.trinasolar.scp.common.api.base.DataPrivilegeQuery;
import com.trinasolar.scp.common.api.util.UserUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
@Service("permissionService")
@AllArgsConstructor
@Slf4j
@CacheConfig(cacheManager = "caffeineCacheManager")
public class PermissionServiceImpl implements PermissionService {
    private final SystemFeign systemFeign;

    @Override
    @Cacheable(cacheNames = "PermissionService_getBasePlaceDataPrivilegeDTOS")
    public List<DataPrivilegeDTO> getBasePlaceDataPrivilegeDTOS() {
        String userId= UserUtil.getUser().getId();
        DataPrivilegeQuery dataPrivilegeQuery = new DataPrivilegeQuery();
        dataPrivilegeQuery.setUserId(userId);
        dataPrivilegeQuery.setPrivilegeType(LovHeaderCodeConstant.BASE_PLACE);
        List<DataPrivilegeDTO> privilegeDTOList = systemFeign.queryDataPrivilege(dataPrivilegeQuery).getBody().getData();

        return privilegeDTOList;
    }
}
