package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellReturnOrderDTO;
import com.trinasolar.scp.baps.domain.convert.CellReturnOrderDEConvert;
import com.trinasolar.scp.baps.domain.dto.bbom.ItemsDTO;
import com.trinasolar.scp.baps.domain.dto.aps.ReturnDataSaveDTO;
import com.trinasolar.scp.baps.domain.entity.CellReturnOrder;
import com.trinasolar.scp.baps.domain.entity.QCellReturnOrder;
import com.trinasolar.scp.baps.domain.excel.CellLossExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellReturnOrderExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellReturnOrderQuery;
import com.trinasolar.scp.baps.domain.save.CellReturnOrderSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.feign.BbomFeign;
import com.trinasolar.scp.baps.service.repository.CellReturnOrderRepository;
import com.trinasolar.scp.baps.service.service.CellReturnOrderService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * 返司
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Slf4j
@Service("cellReturnOrderService")
@RequiredArgsConstructor
public class CellReturnOrderServiceImpl implements CellReturnOrderService {
    private static final QCellReturnOrder qCellReturnOrder = QCellReturnOrder.cellReturnOrder;
    private final CellReturnOrderDEConvert convert;

    private final CellReturnOrderRepository repository;
    private final ApsFeign apsFeign;

    private final BbomFeign bbomFeign;

    @Override
    public Page<CellReturnOrderDTO> queryByPage(CellReturnOrderQuery query) {
        query= convert.toCellReturnOrderQuery(query,MyThreadLocal.get().getLang());
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query=convert.toCellReturnOrderQuery(query,MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellReturnOrder> page = repository.findAll(booleanBuilder, pageable);
        List<CellReturnOrderDTO> cellReturnOrderDTOS = convert.toDto(page.getContent());

        return new PageImpl(cellReturnOrderDTOS, page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellReturnOrderQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellReturnOrder.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getShipperIsOversea())){
            booleanBuilder.and(qCellReturnOrder.shipperIsOversea.eq(query.getShipperIsOversea()));
        }
        if(StringUtils.isNotEmpty(query.getReceiverIsOversea())){
            booleanBuilder.and(qCellReturnOrder.receiverIsOversea.eq(query.getReceiverIsOversea()));
        }
        if(StringUtils.isNotEmpty(query.getShipperBasePlace())){
            booleanBuilder.and(qCellReturnOrder.shipperBasePlace.eq(query.getShipperBasePlace()));
        }
        if(StringUtils.isNotEmpty(query.getReceiverBasePlace())){
            booleanBuilder.and(qCellReturnOrder.receiverBasePlace.eq(query.getReceiverBasePlace()));
        }
        if(StringUtils.isNotEmpty(query.getCellsType())){
            booleanBuilder.and(qCellReturnOrder.cellsType.eq(query.getCellsType()));
        }
        if(StringUtils.isNotEmpty(query.getItemFivea())){
            booleanBuilder.and(qCellReturnOrder.itemFivea.eq(query.getItemFivea()));
        }
        if (query.getWorkCell() != null) {
            booleanBuilder.and(qCellReturnOrder.workCell.eq(query.getWorkCell()));
        }
        if(StringUtils.isNotEmpty(query.getNote())){
            booleanBuilder.and(qCellReturnOrder.note.eq(query.getNote()));
        }
        if(StringUtils.isNotEmpty(query.getMonth())){
            booleanBuilder.and(qCellReturnOrder.month.eq(query.getMonth()));
        }
        if (query.getDate() != null) {
            booleanBuilder.and(qCellReturnOrder.date.eq(query.getDate()));
        }
        if (query.getCellQty() != null) {
            booleanBuilder.and(qCellReturnOrder.cellQty.eq(query.getCellQty()));
        }
        if (query.getReceiverAccount() != null) {
            booleanBuilder.and(qCellReturnOrder.receiverAccount.eq(query.getReceiverAccount()));
        }
    }

    @Override
    public CellReturnOrderDTO queryById(Long id) {
        CellReturnOrder queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellReturnOrderDTO save(CellReturnOrderSaveDTO saveDTO) {
        CellReturnOrder newObj = Optional.ofNullable(saveDTO.getId())
            .map(id -> repository.getOne(saveDTO.getId()))
            .orElse(new CellReturnOrder());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellReturnOrderQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellReturnOrderDTO> dtos = queryByPage(query).getContent();

        // dto数据转为ExcelData数据
        List<CellReturnOrderExcelDTO> excelDtos = convert.toExcelDTO(dtos);
        ExcelPara excelPara =  query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(excelDtos, excelPara);
        // 导出调用excelUtils
        String fileName= BapsMessgeHelper.getMessage("export.returnorder.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);
    }
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellReturnOrderExcelDTO> excelDtos=  ExcelUtils.readExcel(multipartFile.getInputStream(),null, CellReturnOrderExcelDTO.class,excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        List<CellReturnOrderSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        saveDTOS=convert.toCellReturnOrderSaveDTOCnName(saveDTOS);
        // 先删除之前的数据,再保存新的数据
        repository.deleteAll();
        List<CellReturnOrderDTO> dtos= Lists.newArrayList();
        saveDTOS.stream().forEach(item->{
           dtos.add( save(item));
        });
        //数据同步给发货计划
       List<ReturnDataSaveDTO> returnDataSaveDTOS= convert.toReturnDataDto(dtos);
       returnDataSaveDTOS.forEach(item->{
           item.setWorkshop("*");
       });
       ResponseEntity<Results<String>> resultsResponseEntity = apsFeign.saveReturnDataList(returnDataSaveDTOS);
       if(!resultsResponseEntity.getBody().isSuccess()){
           throw new BizException("同步APS失败！");
       }
    }
    public void checkInput(List<CellReturnOrderExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors=new ArrayList<>();
        excelDTOS.stream().forEach(excelDTO -> {
            //验证国内国外
            if (StringUtils.isEmpty( excelDTO.getShipperIsOversea())){
                String message =BapsMessgeHelper.getMessage("returnorder.import.shipperisoversea.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getShipperIsOversea());
            if (lovLineDTO == null) {
                String message =BapsMessgeHelper.getMessage("returnorder.import.shipperisoversea.not.exists",new Object[]{i[0]});
                errors.add(message);

            }
            if (StringUtils.isEmpty( excelDTO.getReceiverIsOversea())){
                String message =BapsMessgeHelper.getMessage("returnorder.import.receiverisoversea.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            LovLineDTO lovLineDTO2 = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getReceiverIsOversea());
            if (lovLineDTO2 == null) {
                String message =BapsMessgeHelper.getMessage("returnorder.import.receiverisoversea.not.exists",new Object[]{i[0]});
                errors.add(message);

            }
            //验证电池类型
            if (StringUtils.isEmpty( excelDTO.getCellsType())){
                String message =BapsMessgeHelper.getMessage("returnorder.import.cellstype.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            LovLineDTO lovLineDTO7 = LovUtils.getByName(LovHeaderCodeConstant.APS_BATTERY_TYPE, excelDTO.getCellsType());
            if (lovLineDTO7 == null) {
                String message =BapsMessgeHelper.getMessage("returnorder.import.cellstype.not.exists",new Object[]{i[0],excelDTO.getCellsType()});
                errors.add(message);
            }
            if (StringUtils.isEmpty( excelDTO.getShipperBasePlace())){
                String message =BapsMessgeHelper.getMessage("returnorder.import.shipperbaseplace.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            if (StringUtils.isEmpty( excelDTO.getReceiverBasePlace())){
                String message =BapsMessgeHelper.getMessage("returnorder.import.receiverbaseplace.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            //验证生产基地
            LovLineDTO   lovLineDTO_BasePlace = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getShipperBasePlace());
            if (lovLineDTO_BasePlace == null) {
                String message =BapsMessgeHelper.getMessage("returnorder.import.shipperbaseplace.not.exists",new Object[]{i[0],excelDTO.getShipperBasePlace()});
                errors.add(message);
            }
            //验证生产基地
            LovLineDTO   lovLineDTO_BasePlace2 = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getReceiverBasePlace());
            if (lovLineDTO_BasePlace2 == null) {
                String message =BapsMessgeHelper.getMessage("returnorder.import.receiverbaseplace.not.exists",new Object[]{i[0],excelDTO.getReceiverBasePlace()});
                errors.add(message);
            }
            //验证账套
            if (StringUtils.isEmpty( excelDTO.getReceiverAccount())){
                String message =BapsMessgeHelper.getMessage("returnorder.import.receiveraccount.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            LovLineDTO   lovLineDTO_Account = LovUtils.getByName(LovHeaderCodeConstant.INVENTORY_ORGANIZATION, excelDTO.getReceiverAccount());
            if (Objects.isNull(lovLineDTO_Account)){
                String message =BapsMessgeHelper.getMessage("returnorder.import.receiveraccount.not.exists",new Object[]{i[0],excelDTO.getReceiverAccount()});
                errors.add(message);
            }
            if (null==excelDTO.getCellQty()){
                String message =BapsMessgeHelper.getMessage("returnorder.import.cellqty.not.null",new Object[]{i[0]});
                errors.add(message);
            }else if(!NumberUtil.isNumber(String.valueOf(excelDTO.getCellQty()))){
                String message =BapsMessgeHelper.getMessage("returnorder.import.cellqty.not.number",new Object[]{i[0]});
                errors.add(message);
            }
            if (StringUtils.isEmpty(excelDTO.getItemFivea())){
                String message =BapsMessgeHelper.getMessage("returnorder.import.itemcode.not.null",new Object[]{i[0]});
                errors.add(message);
            }else {
                ItemsDTO itemsDTO=new ItemsDTO();
                itemsDTO.setItemCode(excelDTO.getItemFivea());
                ItemsDTO items=  bbomFeign.findOneByItemCode(itemsDTO).getBody().getData();
                if(null==items){
                    String message =BapsMessgeHelper.getMessage("returnorder.import.itemcode.not.exists",new Object[]{i[0],excelDTO.getItemFivea()});
                    errors.add(message);
                }else{
                    excelDTO.setItemFiveaId(items.getItemId());
                }
            }
            if (null==excelDTO.getWorkCell()){
                String message =BapsMessgeHelper.getMessage("returnorder.import.workcell.not.null",new Object[]{i[0]});
                errors.add(message);
            } else if (!NumberUtil.isNumber(String.valueOf(excelDTO.getWorkCell()))) {
                String message =BapsMessgeHelper.getMessage("returnorder.import.workcell.not.number",new Object[]{i[0]});
                errors.add(message);
            }
            if (StringUtils.isEmpty(excelDTO.getNote())){
                String message =BapsMessgeHelper.getMessage("returnorder.import.note.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            if (StringUtils.isEmpty(excelDTO.getMonth())){
                String message =BapsMessgeHelper.getMessage("returnorder.import.month.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            if (null==excelDTO.getDate()){
                String message =BapsMessgeHelper.getMessage("returnorder.import.date.note.null",new Object[]{i[0]});
                errors.add(message);
            }
            i[0]++;
        });
        if (errors.size()>0){
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }

    }


}
