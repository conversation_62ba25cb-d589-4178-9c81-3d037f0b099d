package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellInStockPlanRemarkGroupDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineContrastDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellTypeRuleDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalQuery;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanTotalSaveDTO;
import com.trinasolar.scp.common.api.util.DataColumn;
import com.trinasolar.scp.common.api.util.exStrategy.CellStyleModel;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 入库计划汇总表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-26 11:20:51
 */
public interface CellInstockPlanTotalService {
    /**
     * 分页获取入库计划汇总表
     *
     * @param query 查询对象
     * @return 入库计划汇总表分页对象
     */
    Page<CellInstockPlanTotalDTO> queryByPage(CellInstockPlanTotalQuery query, Pair<String, String> versions);

    /**
     * 获取版本号
     * @param query
     * @param sendedEmailVersionFlag flase = 获取当前最新版本  true = 获取已发送邮件的最新版本
     * @return
     */
    Pair<String, String> getSendedEmailOrLastVersion(CellInstockPlanTotalQuery query, Boolean sendedEmailVersionFlag);

    /**
     * 根据主键获取入库计划汇总表详情
     *
     * @param id 主键
     * @return 入库计划汇总表详情
     */
        CellInstockPlanTotalDTO queryById(Long id);

    /**
     * 保存或更新入库计划汇总表
     *
     * @param saveDTO 入库计划汇总表保存对象
     * @return 入库计划汇总表对象
     */
    CellInstockPlanTotalDTO save(CellInstockPlanTotalSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除入库计划汇总表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellInstockPlanTotalQuery query, HttpServletResponse response);

    void confirm(CellInstockPlanTotalQuery query);

    void email(CellInstockPlanTotalQuery query);

    /**
     * 获取投产计划最新版本
     * @param query
     * @return
     */
    String queryMaxVersion(CellInstockPlanTotalQuery query);

    /**
     * 获取最新版本的数据
     * @param query
     * @return
     */
    List<CellInstockPlanTotalDTO> queryByFirst(CellInstockPlanTotalQuery query);

    /**
     * 获取投产计划最新3个版本
     * @param query
     * @return
     */
    List<String> queryThreeMaxVersion(CellInstockPlanTotalQuery query);

    List<CellInstockPlanTotalDTO> query(CellInstockPlanTotalQuery query,String month,Long isOversea,String version);
   /**
     * 依据电池转换规则改变电池类型名称
     * @param dtos
     * @return
     */
    List<CellInstockPlanTotalDTO> changeDtoCellTypeName(List<CellInstockPlanTotalDTO> dtos);

    List<CellInstockPlanTotalDTO> changeDtoCellTypeName(List<CellInstockPlanTotalDTO> dtos, List<CellTypeRuleDTO> ruleDTOList);

    /**
     * 依据电池转换规则改变电池类型名称
     * @param dto
     * @return
     */
     CellInstockPlanTotalDTO changeDtoCellTypeName(CellInstockPlanTotalDTO dto, List<CellTypeRuleDTO> rules);

    /**
     * 获取两个月内（当月 + 次月）已排产的电池计划中涉及的电池料号
     * @return
     */
    List<String> queryTwoMonthAllItemCodes();

    /**
     * 判断上个版本改动点
     */
    List<CellPlanLineContrastDTO> changeContrast(CellInstockPlanTotalDTO source, CellInstockPlanTotalDTO target) throws NoSuchFieldException, IllegalAccessException;

    List<CellStyleModel> differentialJudgment(List<DataColumn> columns, List<CellInstockPlanTotalDTO> dtos, List<CellInstockPlanTotalDTO> sendedEmailDTOS);

    List<CellInStockPlanRemarkGroupDTO> queryRemarkList(CellInstockPlanTotalQuery query);

    void saveOrUpdateRemark(List<CellInStockPlanRemarkGroupDTO> dtoList);
}

