package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.convert.CellFineMidDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellTypeMidDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellFineDEConvert;
import com.trinasolar.scp.baps.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.baps.domain.dto.CellFineDTO;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.excel.CellFineExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellFineQuery;
import com.trinasolar.scp.baps.domain.save.CellFineSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellFineMidRepository;
import com.trinasolar.scp.baps.service.repository.CellFineMonthRepository;
import com.trinasolar.scp.baps.service.repository.CellFineRepository;
import com.trinasolar.scp.baps.service.repository.CellTypeMidRepository;
import com.trinasolar.scp.baps.service.service.BatteryBomService;
import com.trinasolar.scp.baps.service.service.CellFineService;
import com.trinasolar.scp.baps.service.service.ConfigCellGoodService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.baps.domain.dto.ConfigCellGoodDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import javax.servlet.http.HttpServletResponse;

/**
 * 电池良率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Slf4j
@Service("cellFineService")
@RequiredArgsConstructor
public class CellFineServiceImpl implements CellFineService {
    private static final QCellFine qCellFine = QCellFine.cellFine;

    private final CellFineDEConvert convert;
    private final CellTypeMidDEConvert cellTypeMidDEConvert;
    private final CellFineMidDEConvert cellFineMidDEConvert;
    private final CellFineRepository repository;
    private final CellTypeMidRepository cellTypeMidRepository;
    private final CellFineMidRepository cellFineMidRepository;
    private final BatteryBomService batteryBomService;
    private final ConfigCellGoodService configCellGoodService;
    private final CellFineMonthRepository cellFineMonthRepository;

    @Override
    public Page<CellFineDTO> queryByPage(CellFineQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query= convert.toCellFineQueryCn(query,MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellFine> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<CellFine> queryList(CellFineQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Iterable<CellFine> cellFineIterable = repository.findAll(booleanBuilder);
        List<CellFine> fineList = IterableUtils.toList(cellFineIterable);
        fineList.stream().forEach(x -> {
            for (int i = 1; i < 13; i++) {
                BigDecimal decimal = ReflectUtil.invoke(x, "getM" + i);
                ReflectUtil.invoke(x, "setM" + i, null == decimal ? BigDecimal.ZERO : decimal);
            }
        });
        return IterableUtils.toList(cellFineIterable);
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellFineQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellFine.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellFine.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellFine.cellsType.eq(query.getCellsType()));
        }
        if (CollectionUtils.isNotEmpty(query.getCellsTypeList())) {
            booleanBuilder.and(qCellFine.cellsType.in(query.getCellsTypeList()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellFine.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellFine.workshop.eq(query.getWorkshop()));
        }
        if (query.getYear() != null) {
            booleanBuilder.and(qCellFine.year.eq(query.getYear()));
        }
        if (query.getM1() != null) {
            booleanBuilder.and(qCellFine.m1.eq(query.getM1()));
        }
        if (query.getM2() != null) {
            booleanBuilder.and(qCellFine.m2.eq(query.getM2()));
        }
        if (query.getM3() != null) {
            booleanBuilder.and(qCellFine.m3.eq(query.getM3()));
        }
        if (query.getM4() != null) {
            booleanBuilder.and(qCellFine.m4.eq(query.getM4()));
        }
        if (query.getM5() != null) {
            booleanBuilder.and(qCellFine.m5.eq(query.getM5()));
        }
        if (query.getM6() != null) {
            booleanBuilder.and(qCellFine.m6.eq(query.getM6()));
        }
        if (query.getM7() != null) {
            booleanBuilder.and(qCellFine.m7.eq(query.getM7()));
        }
        if (query.getM8() != null) {
            booleanBuilder.and(qCellFine.m8.eq(query.getM8()));
        }
        if (query.getM9() != null) {
            booleanBuilder.and(qCellFine.m9.eq(query.getM9()));
        }
        if (query.getM10() != null) {
            booleanBuilder.and(qCellFine.m10.eq(query.getM10()));
        }
        if (query.getM11() != null) {
            booleanBuilder.and(qCellFine.m11.eq(query.getM11()));
        }
        if (query.getM12() != null) {
            booleanBuilder.and(qCellFine.m12.eq(query.getM12()));
        }
    }

    @Override
    public CellFineDTO queryById(Long id) {
        CellFine queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellFineDTO save(CellFineSaveDTO saveDTO) {
        CellFine newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new CellFine());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellFineQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellFineDTO> dtos = queryByPage(query).getContent();

        // dto数据转为ExcelData数据
        List<CellFineExcelDTO> datas = convert.toExcelDTO(dtos);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellFineExcelDTO.class, datas, JSON.toJSONString(query), "电池良率表", response);

    }

    /**
     * 读取第第三方接口电池类型和电池良率的接口后进行数据整合
     *
     * @return
     */
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData() {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        List<Integer> years = DateUtil.getTwoOrThreeYearsByNow(LocalDate.now());
        //1、获取电池类型基础数据（其它微服务）保存到电池类型mid表
        List<BatteryTypeMainDTO> batteryTypesList = batteryBomService.queryBatteryCodeTypeAll();

        //1.1 先删除原来的数据
        cellTypeMidRepository.deleteAll();
        //1.2 保存数据到电池类型mid表
        List<CellTypeMid> cellTypeMids = cellTypeMidDEConvert.toCellTypeMid(batteryTypesList);
        cellTypeMidRepository.saveAll(cellTypeMids);
        //2、获取报价系统电池良率(其它微服务)保存到电池良率mid表
        List<ConfigCellGoodDTO> configCellGoodDTOS = configCellGoodService.getConfigCellGoods(years);
        //2.1 先删除原数据

        years.stream().forEach(year -> {
            cellFineMidRepository.deleteByYear(year);
        });
        //2.2、电池良率中间表数据填充 baps_cell_fine_mid
        List<CellFineMid> cellFineMids = cellFineMidDEConvert.toCellFineMid(configCellGoodDTOS);
        cellFineMidRepository.saveAll(cellFineMids);
        //3、构建cell_fine数据（依据以上两表构建）
        //3.1 依据两个中间表获组合数据
        List<CellFine> cellFines = repository.getCellFineFromMid();
        //该集合存储给aps用的数据 一个 CellFine 按月拆成多条存储
        List<CellFineMonth> cellFineMonths = new ArrayList<>();
        cellFines.stream().forEach((cellFine) -> {
            Long cellsTypeId = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, cellFine.getCellsType()).getLovLineId();
            Long isOverseaId = LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, cellFine.getIsOversea()).getLovLineId();

            LovLineDTO lovLineDTO = LovUtils.getByName(com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant.WORK_SHOP, cellFine.getWorkshop());
            Long workshopId = lovLineDTO.getLovLineId();
            String basePlaceIdStr = lovLineDTO.getAttribute1();//车间所属基地id
            if (basePlaceIdStr != null) {
                lovLineDTO = LovUtils.get(Long.parseLong(basePlaceIdStr));
                if (lovLineDTO != null) {
                    cellFine.setBasePlaceId(lovLineDTO.getLovLineId());
                    cellFine.setBasePlace(lovLineDTO.getLovName());
                }
            }
            //依据车间获取基地信息
            //  cellFine.setBasePlaceId(basePlaceId);
            cellFine.setIsOverseaId(isOverseaId);
            cellFine.setWorkshopid(workshopId);
            cellFine.setCellsTypeId(cellsTypeId);
            for (int m = 1; m <= 12; m++) {
                Method method = ReflectUtil.getMethod(CellFine.class, "getM" + m);
                Object value = ReflectUtil.invoke(cellFine, method);
                if (value != null) {
                    CellFineMonth cellFineMonth = convert.toCellFineMonth(cellFine);
                    cellFineMonth.setMonth(m);
                    cellFineMonth.setYield(new BigDecimal(value.toString()));
                    cellFineMonths.add(cellFineMonth);
                }

            }

        });
        //3.2删除原有数据
        years.stream().forEach(year -> {
            repository.deleteByYear(year);
        });
        years.stream().forEach(year -> {
            cellFineMonthRepository.deleteByYear(year);
        });
        //3.3 重新添加数据
        repository.saveAll(cellFines);
        cellFineMonthRepository.saveAll(cellFineMonths);


    }

    @Override
    public List<CellFineDTO> list(CellFineQuery query) {
        BooleanBuilder builder = new BooleanBuilder();
        buildWhere(builder, query);
        List<CellFine> dataList = IterableUtils.toList(repository.findAll(builder));
        List<CellFineDTO> resultList = convert.toDto(dataList);
        return resultList;
    }
}
