package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.SiliconSplitRuleDTO;
import com.trinasolar.scp.baps.domain.entity.SiliconSplitRule;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.SiliconSplitRuleQuery;
import com.trinasolar.scp.baps.domain.save.SiliconSplitRuleSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 硅片拆分规则 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 07:44:54
 */
public interface SiliconSplitRuleService   {
    /**
     * 分页获取硅片拆分规则
     *
     * @param query 查询对象
     * @return 硅片拆分规则分页对象
     */
    Page<SiliconSplitRuleDTO> queryByPage(SiliconSplitRuleQuery query);

    /**
     * 根据主键获取硅片拆分规则详情
     *
     * @param id 主键
     * @return 硅片拆分规则详情
     */
        SiliconSplitRuleDTO queryById(Long id);

    /**
     * 保存或更新硅片拆分规则
     *
     * @param saveDTO 硅片拆分规则保存对象
     * @return 硅片拆分规则对象
     */
    SiliconSplitRuleDTO save(SiliconSplitRuleSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除硅片拆分规则
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(SiliconSplitRuleQuery query, HttpServletResponse response);

    /**
     * 获取某月份指定类型的硅片分类规则

     * @param type
     * @return
     */
    List<SiliconSplitRule> getRulesByMonthType(CellPlanLineQuery query, String type);

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

