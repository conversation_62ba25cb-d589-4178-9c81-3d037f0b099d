package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellProductionPlan;
import org.mapstruct.Mappings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 投产计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Repository
public interface CellProductionPlanRepository extends JpaRepository<CellProductionPlan, Long>, QuerydslPredicateExecutor<CellProductionPlan> {
   @Query(value = "from CellProductionPlan c where c.bbomId = :bbomId")
    public CellProductionPlan selectByBbomid(@Param("bbomId") Long bbomId);
   @Modifying
    @Query(value = "update CellProductionPlan c set  c.itemCode = :itemCode where c.bbomId = :bbomId")
   public  void updateItemCode(@Param("bbomId") Long bbomId,@Param("itemCode") String itemCode);
}
