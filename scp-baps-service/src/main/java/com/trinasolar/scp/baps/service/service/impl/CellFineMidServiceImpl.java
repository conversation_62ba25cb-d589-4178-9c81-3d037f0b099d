package com.trinasolar.scp.baps.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellFineMidDTO;
import com.trinasolar.scp.baps.domain.convert.CellFineMidDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellFineMid;
import com.trinasolar.scp.baps.domain.entity.QCellFineMid;
import com.trinasolar.scp.baps.domain.query.CellFineMidQuery;
import com.trinasolar.scp.baps.domain.save.CellFineMidSaveDTO;
import com.trinasolar.scp.baps.service.repository.CellFineMidRepository;
import com.trinasolar.scp.baps.service.service.CellFineMidService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import javax.servlet.http.HttpServletResponse;

/**
 * 电池良率中间表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Slf4j
@Service("cellFineMidService")
@RequiredArgsConstructor
public class CellFineMidServiceImpl implements CellFineMidService {
    private static final QCellFineMid qCellFineMid = QCellFineMid.cellFineMid;

    private final CellFineMidDEConvert convert;

    private final CellFineMidRepository repository;

    @Override
    public Page<CellFineMidDTO> queryByPage(CellFineMidQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellFineMid> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellFineMidQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellFineMid.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getIsOversea())){
            booleanBuilder.and(qCellFineMid.isOversea.eq(query.getIsOversea()));
        }
        if (query.getYear() != null) {
            booleanBuilder.and(qCellFineMid.year.eq(query.getYear()));
        }
        if(StringUtils.isNotEmpty(query.getParameterType())){
            booleanBuilder.and(qCellFineMid.parameterType.eq(query.getParameterType()));
        }
        if(StringUtils.isNotEmpty(query.getProductType())){
            booleanBuilder.and(qCellFineMid.productType.eq(query.getProductType()));
        }
        if(StringUtils.isNotEmpty(query.getProductCategory())){
            booleanBuilder.and(qCellFineMid.productCategory.eq(query.getProductCategory()));
        }
        if(StringUtils.isNotEmpty(query.getMainGrid())){
            booleanBuilder.and(qCellFineMid.mainGrid.eq(query.getMainGrid()));
        }
        if(StringUtils.isNotEmpty(query.getCrystalType())){
            booleanBuilder.and(qCellFineMid.crystalType.eq(query.getCrystalType()));
        }
        if(StringUtils.isNotEmpty(query.getCrystalSpec())){
            booleanBuilder.and(qCellFineMid.crystalSpec.eq(query.getCrystalSpec()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qCellFineMid.workshop.eq(query.getWorkshop()));
        }
        if (query.getM1() != null) {
            booleanBuilder.and(qCellFineMid.m1.eq(query.getM1()));
        }
        if (query.getM2() != null) {
            booleanBuilder.and(qCellFineMid.m2.eq(query.getM2()));
        }
        if (query.getM3() != null) {
            booleanBuilder.and(qCellFineMid.m3.eq(query.getM3()));
        }
        if (query.getM4() != null) {
            booleanBuilder.and(qCellFineMid.m4.eq(query.getM4()));
        }
        if (query.getM5() != null) {
            booleanBuilder.and(qCellFineMid.m5.eq(query.getM5()));
        }
        if (query.getM6() != null) {
            booleanBuilder.and(qCellFineMid.m6.eq(query.getM6()));
        }
        if (query.getM7() != null) {
            booleanBuilder.and(qCellFineMid.m7.eq(query.getM7()));
        }
        if (query.getM8() != null) {
            booleanBuilder.and(qCellFineMid.m8.eq(query.getM8()));
        }
        if (query.getM9() != null) {
            booleanBuilder.and(qCellFineMid.m9.eq(query.getM9()));
        }
        if (query.getM10() != null) {
            booleanBuilder.and(qCellFineMid.m10.eq(query.getM10()));
        }
        if (query.getM11() != null) {
            booleanBuilder.and(qCellFineMid.m11.eq(query.getM11()));
        }
        if (query.getM12() != null) {
            booleanBuilder.and(qCellFineMid.m12.eq(query.getM12()));
        }
    }

    @Override
    public CellFineMidDTO queryById(Long id) {
        CellFineMid queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellFineMidDTO save(CellFineMidSaveDTO saveDTO) {
        CellFineMid newObj = Optional.ofNullable(saveDTO.getId())
            .map(id -> repository.getOne(saveDTO.getId()))
            .orElse(new CellFineMid());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellFineMidQuery query, HttpServletResponse response) {
       List<CellFineMidDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "电池良率中间表", "电池良率中间表", excelPara.getSimpleHeader(), excelData);
    }
}
