package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CalcDemandDto;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanFinishDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanFinishTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanTotalDTO;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanFinish;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanFinishQuery;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanFinishTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanTotalQuery;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanFinishSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

/**
 * 实际入库表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
public interface CellInstockPlanFinishService {
    /**
     * 分页获取实际入库表
     *
     * @param query 查询对象
     * @return 实际入库表分页对象
     */
    Page<CellInstockPlanFinishDTO> queryByPage(CellInstockPlanFinishQuery query);

    /**
     * 根据主键获取实际入库表详情
     *
     * @param id 主键
     * @return 实际入库表详情
     */
        CellInstockPlanFinishDTO queryById(Long id);

    /**
     * 保存或更新实际入库表
     *
     * @param saveDTO 实际入库表保存对象
     * @return 实际入库表对象
     */
    CellInstockPlanFinishDTO save(CellInstockPlanFinishSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除实际入库表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellInstockPlanFinishTotalQuery query, HttpServletResponse response);
    void synDataFromErpWipIssue(LocalDate startDate, LocalDate endDate);

    Page<CellInstockPlanFinishTotalDTO> query(CellInstockPlanFinishTotalQuery query);

    List<CellInstockPlanFinishDTO> findByCondition(Long isOverseaId, LocalDate actualCoverageDate);

    List<String> findLocatorCode();
}

