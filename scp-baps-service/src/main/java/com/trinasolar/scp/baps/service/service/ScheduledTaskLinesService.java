package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.ScheduledTaskLinesDTO;
import com.trinasolar.scp.baps.domain.query.ScheduledTaskLinesQuery;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ScheduledTaskLinesService {

    Page<ScheduledTaskLinesDTO> queryByPage(ScheduledTaskLinesQuery query);

    void exportToExcel(ScheduledTaskLinesQuery query, HttpServletResponse response);


}
