package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellPlanLineTotal;
import com.trinasolar.scp.baps.domain.entity.CellProductionPlanTotal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 入库计划汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Repository
public interface CellPlanLineTotalRepository extends JpaRepository<CellPlanLineTotal, Long>, QuerydslPredicateExecutor<CellPlanLineTotal> {
    @Query(value = "from CellPlanLineTotal total where total.version = :version and total.month = :month")
    List<CellPlanLineTotal> selectByVersion(@Param("version") String version,@Param("month") String month);
}
