package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.ClassCodeDTO;
import com.trinasolar.scp.baps.domain.dto.MwCoefficientDTO;
import com.trinasolar.scp.baps.domain.dto.aps.*;
import com.trinasolar.scp.baps.domain.dto.erp.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.baps.domain.query.ClassCodeQuery;
import com.trinasolar.scp.baps.domain.query.MwCoefficientQuery;
import com.trinasolar.scp.baps.domain.query.aps.CellBooksForBapsQuery;
import com.trinasolar.scp.baps.domain.query.aps.ModuleBasePlaceQuery;
import com.trinasolar.scp.baps.domain.query.aps.RecordTransitionQuery;
import com.trinasolar.scp.baps.domain.query.erp.ErpAlternateDesignatorQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@FeignClient(value = FeignConstant.SCP_APS_API, path = "/scp-aps-api", configuration = LanguageHeaderInterceptor.class)
public interface ApsFeign {

    /**
     * 查询工单分类
     * 查询条件根据排产车间
     *
     * @return 查询结果
     */
    @PostMapping("dp-schedule-lines/erp/classCode")
    @ApiOperation(value = "查询工单分类", notes = "查询工单分类")
    ResponseEntity<Results<List<ClassCodeDTO>>> queryClassCode(@RequestBody ClassCodeQuery query);

    /**
     * 查询BOM替代项
     * 查询条件根据车间+库存组织ID
     *
     * @return 查询结果
     */
    @PostMapping("/erp_alternate_designator/alternate_designator_code")
    @ApiOperation(value = "查询BOM替代项", notes = "查询BOM替代项")
    ResponseEntity<Results<List<ErpAlternateDesignatorDTO>>> getAlternateDesignatorCode(@RequestBody ErpAlternateDesignatorQuery query);

    /**
     * 根据电池类型获取电池WM系数
     */
    @PostMapping("/mw-coefficient/list")
    @ApiOperation(value = " 根据电池类型获取电池WM系数")
    ResponseEntity<Results<List<MwCoefficientDTO>>> findMwCoefficient(@RequestBody MwCoefficientQuery query);

    @PostMapping("/cell-production-plan/save-list")
    @ApiOperation(value = "新增或更新数据")
    ResponseEntity<Results<String>> saveList(@RequestBody List<CellPlanShippableSaveListDTO> saveList);

    @PostMapping("/cell-shipping-days/list")
    @ApiOperation(value = "运输天数列表", notes = "获得运输天数列表")
    ResponseEntity<Results<List<CellShippingDaysDTO>>> list(@RequestBody CellShippingDaysQuery query);

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/record-transition/page")
    @ApiOperation(value = "电池档位对应关系分页列表", notes = "获得电池档位对应关系分页列表")
    ResponseEntity<Results<PageFeign<RecordTransitionDTO>>> queryByPage(@RequestBody RecordTransitionQuery query);

    /**
     * 页列表
     *
     * @param materialNoList 筛选条件
     * @return 查询结果
     */
    @PostMapping("/cell-relation/queryByMaterialNoList")
    @ApiOperation(value = "(aop)电池系列与料号关系列表", notes = "(aop)电池系列与料号关系列表")
    ResponseEntity<Results<List<CellRelationDTO>>> queryByMaterialNoList(@RequestBody List<String> materialNoList);

    /**
     * 新增或更新数据返司数据
     *
     * @param saveList save实体
     * @return 新增或更新数据返司数据
     */
    @PostMapping("/return-data/save-list")
    @ApiOperation(value = "新增或更新返司数据")
    public ResponseEntity<Results<String>> saveReturnDataList(@Valid @RequestBody List<ReturnDataSaveDTO> saveList);

    @PostMapping("/power-supply-aop/list")
    @ApiOperation(value = "AOP供应能力维护列表", notes = "获得AOP供应能力维护列表")
    public ResponseEntity<Results<List<PowerSupplyAopDTO>>> powerSupplyAopList(@RequestBody PowerSupplyAopQuery query);

    @PostMapping("/erp-wip-discrete/getclasscodeforbaps")
    @ApiOperation(value = "获得class_code依据wip_entity_name", notes = "获得class_code依据wip_entity_name")
    public ResponseEntity<Results<Map<String, String>>> getEntityNameToClassCodeMap(@RequestBody List<String> names) ;
    @PostMapping("/cell-books/query-for-baps")
    @ApiOperation(value = "帐套信息(用于获取车间货位关系)", notes = "获得帐套信息(用于获取车间货位关系)")
    public ResponseEntity<Results<List<CellBooksDTO>>> getCellLocatorWorkshopRelation(@RequestBody CellBooksForBapsQuery query)  ;

    @PostMapping("/module-base-place/allDataList")
    @ApiOperation(value = " 获取所有数据")
    ResponseEntity<Results<List<ModuleBasePlaceDTO>>> allDataList();

    @PostMapping("/module-base-place/page")
    @ApiOperation(value = "车间信息", notes = "车间信息")
    ResponseEntity<Results<PageFeign<ModuleBasePlaceDTO>>> findModuleBasePlaceList(@RequestBody ModuleBasePlaceQuery query);

}
