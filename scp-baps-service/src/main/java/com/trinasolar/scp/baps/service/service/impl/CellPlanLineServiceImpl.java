package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellPlanLineDEConvert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.dto.aps.ModuleBasePlaceDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.BatteryScreenPlateWorkshopDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.BatterySiliconWaferDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.ItemsDTO;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlan;
import com.trinasolar.scp.baps.domain.entity.CellPlanLine;
import com.trinasolar.scp.baps.domain.entity.QCellPlanLine;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineSiliconTotalListQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineSiliconTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanQuery;
import com.trinasolar.scp.baps.domain.query.aps.ModuleBasePlaceQuery;
import com.trinasolar.scp.baps.domain.query.bbom.BatteryScreenPlateWorkshopQuery;
import com.trinasolar.scp.baps.domain.query.bbom.ItemsNewQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineSaveDTO;
import com.trinasolar.scp.baps.domain.utils.*;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.feign.BbomFeign;
import com.trinasolar.scp.baps.service.feign.PageFeign;
import com.trinasolar.scp.baps.service.feign.SystemFeign;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanRepository;
import com.trinasolar.scp.baps.service.repository.CellPlanLineRepository;
import com.trinasolar.scp.baps.service.service.CellPlanLineService;
import com.trinasolar.scp.baps.service.service.CellPlanLineTotalService;
import com.trinasolar.scp.baps.service.service.CellPlanLineVersionService;
import com.trinasolar.scp.baps.service.service.PermissionService;
import com.trinasolar.scp.common.api.base.DataPrivilegeDTO;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 投产计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Slf4j
@Service("cellPlanLineService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class CellPlanLineServiceImpl implements CellPlanLineService {
    private static final QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;

    private final CellPlanLineDEConvert convert;

    private final CellPlanLineRepository repository;

    private final CellInstockPlanDEConvert instockConvert;

    private final CellInstockPlanRepository instockRepository;

    private final JPAQueryFactory jpaQueryFactory;

    private final BbomFeign bbomFeign;

    private final SystemFeign systemFeign;

    @Autowired
    @Lazy
    private CellPlanLineVersionService versionService;

    private final PermissionService permissionService;

    @Autowired
    @Lazy
    private CellPlanLineService cellPlanLineService;
    @Autowired
    @Lazy
    private CellPlanLineTotalService cellPlanLineTotalService;
    @Autowired
    private ApsFeign apsFeign;

    @Autowired
    @Qualifier("itemCodeMatchThreadPool")
    ExecutorService itemCodeMatchThreadPool;

    @Override
    public Page<CellPlanLineDTO> queryByPage(CellPlanLineQuery query) {

        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellPlanLine> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<CellPlanLineDTO> queryForMrp(CellPlanLineQuery query, Pair<String, String> versions, CellPlanLineQuery nextQuery, Pair<String, String> nextVersions) {
        //本月数据
        QCellPlanLine cellPlanLine = QCellPlanLine.cellPlanLine;
        JPAQuery<CellPlanLine> where = jpaQueryFactory.select(cellPlanLine).
                from(cellPlanLine).
                where(cellPlanLine.oldMonth.eq(query.getMonth())
                );
        where.where(cellPlanLine.month.eq(query.getMonth()));
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            where.where(cellPlanLine.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            where.where(cellPlanLine.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            where.where(cellPlanLine.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            where.where(cellPlanLine.cellsType.eq(query.getCellsType()));
        }
        if (Objects.nonNull(versions)) {
            if (Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getRight())) {
                where.where(cellPlanLine.finalVersion.in(versions.getLeft(), versions.getRight()));
            } else if (Objects.nonNull(versions.getLeft())) {
                where.where(cellPlanLine.finalVersion.eq(versions.getLeft()));
            } else if (Objects.nonNull(versions.getRight())) {
                where.where(cellPlanLine.finalVersion.eq(versions.getRight()));
            } else {
                where.where(cellPlanLine.id.eq(-1L));
            }
        }
        where.orderBy(cellPlanLine.isOversea.asc(), cellPlanLine.startTime.asc());
        List<CellPlanLine> datas = where.fetch();
        //次月数据

        JPAQuery<CellPlanLine> nextWhere = jpaQueryFactory.select(cellPlanLine).
                from(cellPlanLine).
                where(cellPlanLine.oldMonth.eq(nextQuery.getMonth())
                );
        nextWhere.where(cellPlanLine.month.eq(query.getMonth()));
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            nextWhere.where(cellPlanLine.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            nextWhere.where(cellPlanLine.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            nextWhere.where(cellPlanLine.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            nextWhere.where(cellPlanLine.cellsType.eq(query.getCellsType()));
        }
        if (Objects.nonNull(nextVersions)) {
            if (Objects.nonNull(nextVersions.getLeft()) && Objects.nonNull(nextVersions.getRight())) {
                nextWhere.where(cellPlanLine.finalVersion.in(nextVersions.getLeft(), nextVersions.getRight()));
            } else if (Objects.nonNull(nextVersions.getLeft())) {
                nextWhere.where(cellPlanLine.finalVersion.eq(nextVersions.getLeft()));
            } else if (Objects.nonNull(nextVersions.getRight())) {
                nextWhere.where(cellPlanLine.finalVersion.eq(nextVersions.getRight()));
            } else {
                nextWhere.where(cellPlanLine.id.eq(-1L));
            }
        }
        nextWhere.orderBy(cellPlanLine.isOversea.asc(), cellPlanLine.startTime.asc());
        List<CellPlanLine> nextDatas = nextWhere.fetch();
        datas.addAll(nextDatas);
        return convert.toDto(datas);
    }

    @Override
    public List<CellPlanLineDTO> queryByVersion(Pair<String, String> versions) {
        List<CellPlanLineDTO> result = new LinkedList<>();
        if (StringUtils.isNotBlank(versions.getLeft())) {
            List<CellPlanLineDTO> cellPlanLineDTOS = cellPlanLineService.queryByVersion(versions.getLeft());
            result.addAll(cellPlanLineDTOS);
        }
        if (StringUtils.isNotBlank(versions.getRight())) {
            List<CellPlanLineDTO> cellPlanLineDTOS = cellPlanLineService.queryByVersion(versions.getRight());
            result.addAll(cellPlanLineDTOS);
        }
        return result;
    }

    @Override
    public List<CellPlanLineDTO> findByCondition(Long isOverseaId, Map<String, String> versionMap) {
        List<CellPlanLineDTO> result = Lists.newArrayList();
        versionMap.forEach((month, version) -> {
            LocalDateTime oldStartTime = DateUtil.month2BeginLocalDate(month).atTime(LocalTime.MIN);
            LocalDateTime oldEndTime = DateUtil.month2EndLocalDate(month).atTime(LocalTime.MAX);
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(qCellPlanLine.isOverseaId.eq(isOverseaId));
            builder.and(qCellPlanLine.oldMonth.eq(month));
            builder.and(qCellPlanLine.version.eq(version));
            builder.and(qCellPlanLine.oldStartTime.between(oldStartTime, oldEndTime));
            List<CellPlanLine> dataList = IterableUtils.toList(repository.findAll(builder));
            result.addAll(convert.toDto(dataList));
        });
        //过滤日期在查询范围内的数据
        LocalDate beginDate = versionMap.keySet().stream().sorted().findFirst().map(DateUtil::month2BeginLocalDate).get();
        LocalDate endDate = versionMap.keySet().stream().sorted(Comparator.reverseOrder()).findFirst().map(DateUtil::month2EndLocalDate).get();
        return result.stream().filter(r -> DateUtil.validMonth(r.getStartTime().toLocalDate(), beginDate, endDate)).collect(Collectors.toList());
    }

    @Override
    public List<CellPlanLineDTO> findByCondition(Long isOverseaId, String month, String version) {
        LocalDateTime startTime = DateUtil.month2BeginLocalDate(month).atTime(LocalTime.MIN);
        LocalDateTime endTime = DateUtil.month2EndLocalDate(month).atTime(LocalTime.MAX);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qCellPlanLine.isOverseaId.eq(isOverseaId));
        builder.and(qCellPlanLine.oldMonth.eq(month));
        builder.and(qCellPlanLine.version.eq(version));
        builder.and(qCellPlanLine.oldStartTime.between(startTime, endTime));
        List<CellPlanLine> dataList = IterableUtils.toList(repository.findAll(builder));
        return convert.toDto(dataList);
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellPlanLineQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellPlanLine.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getOrderCode())) {
            booleanBuilder.and(qCellPlanLine.orderCode.eq(query.getOrderCode()));
        }
        if (StringUtils.isNotEmpty(query.getSourceType())) {
            booleanBuilder.and(qCellPlanLine.sourceType.eq(query.getSourceType()));
        }
        if (StringUtils.isNotEmpty(query.getDemandVersion())) {
            booleanBuilder.and(qCellPlanLine.demandVersion.eq(query.getDemandVersion()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellPlanLine.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellPlanLine.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellPlanLine.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellPlanLine.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellPlanLine.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellPlanLine.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qCellPlanLine.workunit.eq(query.getWorkunit()));
        }
        if (query.getWorkunitId() != null) {
            booleanBuilder.and(qCellPlanLine.workunitId.eq(query.getWorkunitId()));
        }
        if (query.getLineName() != null) {
            booleanBuilder.and(qCellPlanLine.lineName.eq(query.getLineName()));
        }
        if (query.getNumberLine() != null) {
            booleanBuilder.and(qCellPlanLine.numberLine.eq(query.getNumberLine()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellPlanLine.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellPlanLine.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qCellPlanLine.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qCellPlanLine.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellPlanLine.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            booleanBuilder.and(qCellPlanLine.cellSource.eq(query.getCellSource()));
        }
        if (StringUtils.isNotEmpty(query.getRegionalCountry())) {
            booleanBuilder.and(qCellPlanLine.regionalCountry.eq(query.getRegionalCountry()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qCellPlanLine.itemCode.eq(query.getItemCode()));
        }
        if (StringUtils.isNotEmpty(query.getDemandBasePlace())) {
            booleanBuilder.and(qCellPlanLine.demandBasePlace.eq(query.getDemandBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getIsSpecialRequirement())) {
            booleanBuilder.and(qCellPlanLine.isSpecialRequirement.eq(query.getIsSpecialRequirement()));
        }
        if (StringUtils.isNotEmpty(query.getLowResistance())) {
            booleanBuilder.and(qCellPlanLine.lowResistance.eq(query.getLowResistance()));
        }
        if (StringUtils.isNotEmpty(query.getCellMfrs())) {
            booleanBuilder.and(qCellPlanLine.cellMfrs.eq(query.getCellMfrs()));
        }
        if (StringUtils.isNotEmpty(query.getSilverPulpMfrs())) {
            booleanBuilder.and(qCellPlanLine.silverPulpMfrs.eq(query.getSilverPulpMfrs()));
        }
        if (query.getDemandQty() != null) {
            booleanBuilder.and(qCellPlanLine.demandQty.eq(query.getDemandQty()));
        }
        if (query.getEndTime() != null) {
            booleanBuilder.and(qCellPlanLine.endTime.eq(query.getEndTime()));
        }
        if (query.getStartTime() != null) {
            booleanBuilder.and(qCellPlanLine.startTime.eq(query.getStartTime()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellPlanLine.month.eq(query.getMonth()));
        }
        if (query.getCellMv() != null) {
            booleanBuilder.and(qCellPlanLine.cellMv.eq(query.getCellMv()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qCellPlanLine.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qCellPlanLine.remark.eq(query.getRemark()));
        }
        if (query.getQtyPc() != null) {
            booleanBuilder.and(qCellPlanLine.qtyPc.eq(query.getQtyPc()));
        }
        if (query.getFinalVersion() != null) {
            booleanBuilder.and(qCellPlanLine.finalVersion.eq(query.getFinalVersion()));
        }
        if (query.getBbomId() != null) {
            booleanBuilder.and(qCellPlanLine.bbomId.eq(query.getBbomId()));
        }
        if (query.getFromId() != null) {
            booleanBuilder.and(qCellPlanLine.fromId.eq(query.getFromId()));
        }
        if (query.getParentId() != null) {
            booleanBuilder.and(qCellPlanLine.parentId.eq(query.getParentId()));
        }
        if (query.getOldEndTime() != null) {
            booleanBuilder.and(qCellPlanLine.oldEndTime.eq(query.getOldEndTime()));
        }
        if (query.getOldStartTime() != null) {
            booleanBuilder.and(qCellPlanLine.oldStartTime.eq(query.getOldStartTime()));
        }
        if (query.getConfirmPlan() != null) {
            booleanBuilder.and(qCellPlanLine.confirmPlan.eq(query.getConfirmPlan()));
        }
        if (query.getOldMonth() != null) {
            booleanBuilder.and(qCellPlanLine.oldMonth.eq(query.getOldMonth()));
        }
        if (query.getHChangeFlag() != null) {
            booleanBuilder.and(qCellPlanLine.hChangeFlag.eq(query.getHChangeFlag()));
        }
    }

    @Override
    public CellPlanLineDTO queryById(Long id) {
        CellPlanLine queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellPlanLineDTO  save(CellPlanLineSaveDTO saveDTO) {
        if ("单晶_P型_210_双面_12BB_二分".equals(saveDTO.getCellsType())) {
            log.error("加工类型拆分之前的数据saveDTO{}",JSON.toJSONString(saveDTO));
        }
        CellPlanLine newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellPlanLine());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        if ("单晶_P型_210_双面_12BB_二分".equals(saveDTO.getCellsType())) {
            log.error("加工类型拆分之后保存的数据newObj{}",JSON.toJSONString(newObj));
        }
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @Cacheable(cacheNames = "CellPlanLineService_queryByVersion", key = "#p0", unless = "#result == null", condition = "#p0!=null")
    public List<CellPlanLineDTO> queryByVersion(String version) {
        QCellPlanLine cellPlanLine = QCellPlanLine.cellPlanLine;
        JPAQuery<CellPlanLine> where = jpaQueryFactory.select(cellPlanLine).
                from(cellPlanLine).
                where(
                        cellPlanLine.version.eq(version)
                );

        List<CellPlanLineDTO> cellPlanLines = convert.toDto(where.fetch());
        return cellPlanLines;
    }

    @Override
    @CacheEvict(cacheNames = "CellPlanLineService_queryByVersion", key = "#version")
    public void clearCacheByVersion(String version) {
        log.info("clear cache by version:{}", version);
    }

    @Override
    public List<CellPlanLineDTO> queryNo5AData(CellPlanLineQuery query) {
        QCellPlanLine cellPlanLine = QCellPlanLine.cellPlanLine;
        JPAQuery<CellPlanLine> where = jpaQueryFactory.select(cellPlanLine).
                from(cellPlanLine).
                where(cellPlanLine.oldMonth.eq(query.getMonth())
                );
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            where.where(cellPlanLine.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            where.where(cellPlanLine.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            where.where(cellPlanLine.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            where.where(cellPlanLine.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            where.where(cellPlanLine.version.eq(query.getVersion()));
        } else {
            Pair<String, String> versions = getLastVersion(query);
            if (Objects.nonNull(versions)) {
                if (Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getRight())) {
                    where.where(cellPlanLine.version.in(versions.getLeft(), versions.getRight()));
                } else if (Objects.nonNull(versions.getLeft())) {
                    where.where(cellPlanLine.version.eq(versions.getLeft()));
                } else if (Objects.nonNull(versions.getRight())) {
                    where.where(cellPlanLine.version.eq(versions.getRight()));
                }
            }
        }
        where.where(
                cellPlanLine.itemCode.isNull()
        );

        where.orderBy(cellPlanLine.isOversea.asc(), cellPlanLine.startTime.asc());
        List<CellPlanLine> datas = where.fetch();
        return convert.toDto(datas);
    }

    /**
     * 获取国内海外版本投产计划数据
     *
     * @param query
     * @param versions
     * @return
     */
    @Override
    public List<CellPlanLineDTO> query(CellPlanLineQuery query, Pair<String, String> versions) {
        List<CellPlanLineDTO> cellPlanLineDTOS = cellPlanLineService.queryByVersion(versions);
        return cellPlanLineDTOS.parallelStream()
                .filter(i -> {
                    if (!i.getOldMonth().equals(query.getMonth())) {
                        return false;
                    }
                    if (StringUtils.isNotEmpty(query.getIsOversea()) && !i.getIsOversea().equals(query.getIsOversea())) {
                        return false;
                    }
                    if (StringUtils.isNotEmpty(query.getBasePlace()) && !i.getBasePlace().equals(query.getBasePlace())) {
                        return false;
                    }
                    if (StringUtils.isNotEmpty(query.getWorkshop()) && !i.getWorkshop().equals(query.getWorkshop())) {
                        return false;
                    }
                    return StringUtils.isBlank(query.getCellsType()) || i.getCellsType().equals(query.getCellsType());
                }).sorted(Comparator
                        .comparing(CellPlanLineDTO::getIsOversea)
                        .thenComparing(CellPlanLineDTO::getStartTime)
                )
                .collect(Collectors.toList());
    }

    /**
     * 获取国内海外版本投产计划确认版数据
     *
     * @param query
     * @param versions
     * @return
     */
    @Override
    public List<CellPlanLineDTO> queryConfirm(CellPlanLineQuery query, Pair<String, String> versions) {
        List<CellPlanLineDTO> datas = new ArrayList<>();
        QCellPlanLine cellPlanLine = QCellPlanLine.cellPlanLine;
        JPAQuery<CellPlanLine> where = jpaQueryFactory.select(cellPlanLine).
                from(cellPlanLine).
                where(cellPlanLine.oldMonth.eq(query.getMonth())
                );
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            where.where(cellPlanLine.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            where.where(cellPlanLine.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            where.where(cellPlanLine.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            where.where(cellPlanLine.cellsType.eq(query.getCellsType()));
        }
        if (Objects.nonNull(versions)) {
            if (Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getRight())) {
                where.where(cellPlanLine.version.in(versions.getLeft(), versions.getRight()));
            } else if (Objects.nonNull(versions.getLeft())) {
                where.where(cellPlanLine.version.eq(versions.getLeft()));
            } else if (Objects.nonNull(versions.getRight())) {
                where.where(cellPlanLine.version.eq(versions.getRight()));
            } else {
                return datas;
            }
        } else {
            return datas;
        }
        where.orderBy(cellPlanLine.isOversea.asc(), cellPlanLine.startTime.asc());
        return convert.toDto(where.fetch());
    }

    /**
     * 查询确认的数据
     *
     * @param query
     * @return
     */
    @Override
    public List<CellPlanLineDTO> queryConfirmDatas(CellPlanLineQuery query) {
        QCellPlanLine cellPlanLine = QCellPlanLine.cellPlanLine;
        JPAQuery<CellPlanLine> where = jpaQueryFactory.select(cellPlanLine).
                from(cellPlanLine).
                where(cellPlanLine.oldMonth.eq(query.getMonth())
                );
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            where.where(cellPlanLine.isOversea.eq(query.getIsOversea()));
            String version = getLastFinalVersion(query.getMonth(), query.getIsOversea());

            if (StringUtils.isNotEmpty(version)) {
                where.where(cellPlanLine.finalVersion.eq(version));
            } else {
                throw new BizException("baps_month_unconfirmed_data_error");
            }

        } else {
            Pair<String, String> versions = getLastFinalVersion(query);
            if (Objects.nonNull(versions)) {
                if (Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getRight())) {
                    where.where(cellPlanLine.finalVersion.in(versions.getLeft(), versions.getRight()));
                } else if (Objects.nonNull(versions.getLeft())) {
                    where.where(cellPlanLine.finalVersion.eq(versions.getLeft()));
                } else if (Objects.nonNull(versions.getRight())) {
                    where.where(cellPlanLine.finalVersion.eq(versions.getRight()));
                } else {
                    throw new BizException("baps_month_unconfirmed_data_error");
                }
            }
        }

        where.orderBy(cellPlanLine.isOversea.asc(), cellPlanLine.startTime.asc());
        List<CellPlanLine> datas = where.fetch();
        return convert.toDto(datas);
    }

    /**
     * 获取国内海外版本投产计划数据
     *
     * @param query
     * @return
     */
    @Override
    public List<CellPlanLineDTO> query(CellPlanLineQuery query) {
        Pair<String, String> versions = getLastVersion(query);
        if (StringUtils.isNotBlank(query.getVersion())) {
            versions = new ImmutablePair<>(query.getVersion(), null);
        }
        return query(query, versions);
    }

    @Override
    public List<CellPlanLineSplitDTO> querySplitList(CellPlanLineQuery query) {
        List<CellPlanLineDTO> cellPlanLineDTOList = cellPlanLineService.query(query);
        if (CollectionUtils.isEmpty(cellPlanLineDTOList)){
            return Lists.newArrayList();
        }
        Map<String, List<CellPlanLineDTO>> cellPlanLineSplitMap = cellPlanLineDTOList.stream()
                .filter(dto -> !ProcessCategoryConstant.WU.equals(dto.getHTrace()))
                .filter(dto -> StringUtils.isEmpty(dto.getProcessCategory()))
                .collect(Collectors.groupingBy(CellPlanLineDTO::splitGroup));
        List<CellPlanLineSplitDTO> cellPlanLineDTOS = new ArrayList<>(cellPlanLineSplitMap.size());
        AtomicInteger orderNo = new AtomicInteger(1);
        cellPlanLineSplitMap.forEach((splitGroupKey, cellPlanLineList) -> {
            CellPlanLineDTO cellPlanLineDTO = cellPlanLineList.get(0);
            CellPlanLineSplitDTO cellPlanLineSplitDTO = new CellPlanLineSplitDTO();
            cellPlanLineSplitDTO.setWorkshop(cellPlanLineDTO.getWorkshop());
            cellPlanLineSplitDTO.setCellsType(cellPlanLineDTO.getCellsType());
            cellPlanLineSplitDTO.setHTrace(cellPlanLineDTO.getHTrace());
            cellPlanLineSplitDTO.setSupplyMethod(cellPlanLineDTO.getSupplyMethod());
            cellPlanLineSplitDTO.setCellSource(cellPlanLineDTO.getCellSource());
            cellPlanLineSplitDTO.setCertCode(cellPlanLineDTO.getCertCode());
            cellPlanLineSplitDTO.setMonth(cellPlanLineDTO.getMonth());
            cellPlanLineSplitDTO.setOrderNo(String.valueOf(orderNo.get()));
            orderNo.getAndIncrement();
            cellPlanLineDTOS.add(cellPlanLineSplitDTO);
        });
        return cellPlanLineDTOS;
    }


    @Override
    public List<CellPlanLineDTO> queryManyMonth(CellPlanLineQuery query) {
        QCellPlanLine cellPlanLine = QCellPlanLine.cellPlanLine;

        JPAQuery<CellPlanLine> where = jpaQueryFactory.select(cellPlanLine).
                from(cellPlanLine);

        Pair<String, String> versions = getLastVersion(query);
        if (Objects.nonNull(versions)) {
            if (Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getRight())) {
                where.where(cellPlanLine.version.in(versions.getLeft(), versions.getRight()));
            } else if (Objects.nonNull(versions.getLeft())) {
                where.where(cellPlanLine.version.eq(versions.getLeft()));
            } else if (Objects.nonNull(versions.getRight())) {
                where.where(cellPlanLine.version.eq(versions.getRight()));
            }
        } else {
            return new ArrayList<>();
        }

        where.orderBy(cellPlanLine.isOversea.asc(), cellPlanLine.startTime.asc());
        List<CellPlanLine> datas = where.fetch();
        return convert.toDto(datas);
    }

    @Override
    public Pair<Boolean, Boolean> checkHandle(CellPlanLineQuery query) {
        Pair<Boolean, Boolean> result = new ImmutablePair<>(null, null);
        Pair<String, String> versions = getLastVersion(query);
        if (Objects.nonNull(versions)) {
            if (Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getRight())) {
                //判断国内外版本是否处理过
                Boolean left = checkHandleByOversea(query.getMonth(), versions.getLeft(), OverseaConstant.INLAND);
                Boolean right = checkHandleByOversea(query.getMonth(), versions.getRight(), OverseaConstant.OVERSEA);
                result = new ImmutablePair<>(left, right);
            } else if (Objects.nonNull(versions.getLeft())) {
                //判断国内版本是否处理过
                Boolean left = checkHandleByOversea(query.getMonth(), versions.getLeft(), OverseaConstant.INLAND);
                result = new ImmutablePair<>(left, null);
            } else if (Objects.nonNull(versions.getRight())) {
                Boolean right = checkHandleByOversea(query.getMonth(), versions.getRight(), OverseaConstant.OVERSEA);
                result = new ImmutablePair<>(null, right);
            }


        }
        return result;
    }

    /**
     * 判断是否对投产数据开始时间结束时间处理过
     *
     * @param month
     * @param version
     * @param isOversea
     * @return
     */
    private Boolean checkHandleByOversea(String month, String version, String isOversea) {
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;
        JPAQuery<CellPlanLine> where = jpaQueryFactory.select(qCellPlanLine).
                from(qCellPlanLine).
                where(qCellPlanLine.oldMonth.eq(month)).where(
                        qCellPlanLine.isOversea.eq(isOversea)
                ).where(
                        qCellPlanLine.version.eq(version)

                );
        CellPlanLine cellPlanLine = where.fetchFirst();
        if (Objects.nonNull(cellPlanLine.getOldStartTime())) {
            //已经处理过（投产时间已经处理过）
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    @Override
    @SneakyThrows
    public void export(CellPlanLineQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellPlanLineDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("没有数据");
        }
        // dto数据转为ExcelData数据
        List<CellPlanLineExcelDTO> datas = convert.toExcelDTO(dtos);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellPlanLineExcelDTO.class, datas, JSON.toJSONString(query), "入库计划表", response);
    }

    /**
     * 获取用于手动指定的投产计划数据
     *
     * @param listQuery
     * @return
     */
    @Override
    public Page<CellPlanLineDTO> getHandCellPlanLines(CellPlanLineSiliconTotalListQuery listQuery) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(listQuery.getPageNumber() - 1, listQuery.getPageSize(), sort);
        CellPlanLineQuery query = new CellPlanLineQuery();
        query.setMonth(listQuery.getMonth());
        String isOversea = MapStrutUtil.getNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, listQuery.getIsOversea());
        query.setIsOversea(isOversea);
        List<CellPlanLineSiliconTotalQuery> queryItems = listQuery.getQueryItems();
        queryItems = queryItems.stream().map(item -> {
            return convert.toCellPlanLineSiliconTotalQueryCnName(item, oldLang);
        }).collect(Collectors.toList());
        Pair<String, String> lastVersion = getLastVersion(query);
        if (lastVersion == null || lastVersion.getLeft() == null && lastVersion.getRight() == null) {
            return PageableExecutionUtils.getPage(new ArrayList<>(), pageable, () -> 0);
        }
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        if (StringUtils.isNotEmpty(lastVersion.getRight()) && StringUtils.isNotEmpty(lastVersion.getLeft())) {
            booleanBuilder.and(
                    qCellPlanLine.version.in(lastVersion.getLeft(), lastVersion.getRight())
            );
        } else if (StringUtils.isNotEmpty(lastVersion.getLeft())) {
            booleanBuilder.and(
                    qCellPlanLine.version.eq(lastVersion.getLeft())
            );
        } else {
            booleanBuilder.and(
                    qCellPlanLine.version.eq(lastVersion.getRight())
            );
        }
        // ( 多条件) or ( 多条件)  or ( 多条件)

        BooleanBuilder booleanBuilderAll = new BooleanBuilder();
        queryItems.stream().forEach(item -> {
            BooleanBuilder itemBooleanBuilder = new BooleanBuilder();
            itemBooleanBuilder.and(qCellPlanLine.basePlace.eq(item.getBasePlace()));
            itemBooleanBuilder.and(qCellPlanLine.workshop.eq(item.getWorkshop()));
            itemBooleanBuilder.and(qCellPlanLine.cellsType.eq(item.getCellType()));
            itemBooleanBuilder.and(qCellPlanLine.month.eq(item.getMonth()));
            if (StringUtils.isNotEmpty(item.getHTrace())) {
                itemBooleanBuilder.and(
                        qCellPlanLine.hTrace.eq(item.getHTrace())
                );
            } else {
                itemBooleanBuilder.and(
                        qCellPlanLine.hTrace.isNull());
            }
            if (StringUtils.isNotEmpty(item.getProductionGrade())) {
                itemBooleanBuilder.and(
                        qCellPlanLine.productionGrade.eq(item.getProductionGrade()));
            } else {
                itemBooleanBuilder.and(
                        qCellPlanLine.productionGrade.isNull());
            }
            if (StringUtils.isNotEmpty(item.getProcessCategory())) {
                itemBooleanBuilder.and(
                        qCellPlanLine.processCategory.eq(item.getProcessCategory()));
            } else {
                itemBooleanBuilder.and(
                        qCellPlanLine.processCategory.isNull());
            }
            if (StringUtils.isNotEmpty(item.getWaferGrade())) {
                itemBooleanBuilder.and(
                        qCellPlanLine.waferGrade.eq(item.getWaferGrade()));
            } else {
                itemBooleanBuilder.and(
                        qCellPlanLine.waferGrade.isNull());
            }
            if (StringUtils.isNotEmpty(item.getSiMfrs())) {
                itemBooleanBuilder.and(
                        qCellPlanLine.siMfrs.eq(item.getSiMfrs()));
            } else {
                itemBooleanBuilder.and(
                        qCellPlanLine.siMfrs.isNull());
            }
            if (StringUtils.isNotEmpty(item.getTransparentDoubleGlass())) {
                itemBooleanBuilder.and(
                        qCellPlanLine.transparentDoubleGlass.eq(item.getTransparentDoubleGlass()));
            } else {
                itemBooleanBuilder.and(
                        qCellPlanLine.transparentDoubleGlass.isNull());
            }
            List<Integer> days = item.getDays();

            if (CollectionUtils.isNotEmpty(days)) {
                Integer[] daysArray = days.toArray(new Integer[0]);
                itemBooleanBuilder.and(
                        qCellPlanLine.startTime.dayOfMonth().in(daysArray));
            }
            booleanBuilderAll.or(itemBooleanBuilder);
        });
        booleanBuilder.and(booleanBuilderAll);
        JPAQuery<CellPlanLine> jpaQuery = jpaQueryFactory.select(qCellPlanLine).from(qCellPlanLine).where(booleanBuilder);


        long count = jpaQuery.fetchCount();
        if (pageable.isPaged()) {
            jpaQuery.offset((int) pageable.getOffset());
            jpaQuery.limit(pageable.getPageSize());
        }
        List<CellPlanLine> datas = jpaQuery.fetch();
        List<CellPlanLineDTO> cellPlanLineDTOS = convert.toDto(datas);
        //翻译
        MyThreadLocal.get().setLang(oldLang);
        cellPlanLineDTOS = convert.toCellPlanLineDTONameFromCNName(cellPlanLineDTOS);
        return PageableExecutionUtils.getPage(cellPlanLineDTOS, pageable, () -> count);
    }

    @Override
    public Pair<String, String> getLastFinalVersion(CellPlanLineQuery query) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getLastFinalVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getLastFinalVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getLastFinalVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }
    }

    /**
     * 获取版本号
     *
     * @param query
     * @return
     */
    @Override
    public Pair<String, String> getLastVersion(CellPlanLineQuery query) {

        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getLastVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getLastVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getLastVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }

    }

    private String getLastFinalVersion(String month, String isOversea) {
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;
        String version = jpaQueryFactory.select(qCellPlanLine.finalVersion.max()).from(qCellPlanLine).where(
                qCellPlanLine.oldMonth.eq(month)
        ).where(
                qCellPlanLine.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    private String getLastVersion(String month, String isOversea) {
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;

        String version = jpaQueryFactory.select(qCellPlanLine.version.max()).from(qCellPlanLine).where(
                qCellPlanLine.oldMonth.eq(month)
        ).where(
                qCellPlanLine.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    //投产发版调用接口
    @Override
    public void summaryGroupByCellPlanLine(List<CellPlanLineDTO> planDTOList, String month) {
        Map<Long, List<CellPlanLineDTO>> collect = planDTOList.stream().collect(Collectors.groupingBy(CellPlanLineDTO::getIsOverseaId));

        collect.entrySet().stream().forEach(longListEntry -> {
            Map<String, List<CellPlanLineDTO>> listMap = new HashMap<>();
            Long overseaId = longListEntry.getKey();
            List<CellPlanLineDTO> value = longListEntry.getValue();
            listMap.put(month + "_" + overseaId, value);
            extractedDeleteByQuery(listMap);
        });


    }

    private void extractedDeleteByQuery(Map<String, List<CellPlanLineDTO>> listMap) {
        listMap.keySet().stream().forEach(key -> {
            MaterielMatchHeaderDTO headerDTO = new MaterielMatchHeaderDTO();
            //新增头
            headerDTO.setMonthAndOversea(key);
            //新增行
            headerDTO.setPlanDTOList(listMap.get(key));
            if(CollectionUtils.isNotEmpty(listMap.get(key))){
                headerDTO.setPlanType(listMap.get(key).get(0).getPlanType());
            }
            log.info("发送bom数据---{}",JSON.toJSONString(headerDTO));
            ResponseEntity<Results<Object>> response = bbomFeign.addMatchInfo(headerDTO);
            if (response != null && response.getBody() != null && !response.getBody().isSuccess()) {
                throw new BizException("数据同步给bom失败");
            }
        });
    }

    //bom进行5A料号匹配后进行回改投产计划数据进行料号匹配
    @Override
    public void changeDataBy5AMatch(Cell5AItemCodeListDto dto) {
        String ids = dto.getItemDtos().stream().map(ele -> String.valueOf(ele.getId())).collect(Collectors.joining(","));
        try {
            log.info("changeDataBy5AMatch ...start ids：{}", ids);
            List<Cell5AItemCodeDto> itemDtos = dto.getItemDtos();
            ResponseEntity<Results<List<ModuleBasePlaceDTO>>> listByBasePlace = apsFeign.allDataList();
            List<ModuleBasePlaceDTO> result = listByBasePlace.getBody().getData();
            if(CollectionUtils.isNotEmpty(result)){
                result = result.stream().filter(item -> item.getProductType().equals("CELL")).collect(Collectors.toList());
            }
            Map<String, String> workShopOrgIdMap = result.stream().collect(Collectors.toMap(ModuleBasePlaceDTO::getWorkshop, ModuleBasePlaceDTO::getErpCode, (ol, ne) -> ol));
            Map<String, ItemsDTO> itemsDTOMap = new ConcurrentHashMap<>();
            CountDownLatch countDownLatch = new CountDownLatch(itemDtos.size());
            CellPlanLineServiceImpl beanHandle = SpringContextUtils.getBean(CellPlanLineServiceImpl.class);
            for (Cell5AItemCodeDto itemCodeDto : itemDtos) {
                //对每个投产数据进行拆分处理
                long id = itemCodeDto.getId(); //投产Id
                //获取拆后的明细数据
                List<Cell5AItemCodeDetailsDto> detailsDtos = itemCodeDto.getDetailsDtos();
                if (CollectionUtils.isEmpty(detailsDtos)) continue;

                itemCodeMatchThreadPool.execute(() -> {
                    try {
                        if(PlanConstant.INSTOCK_TYPE.equals(itemCodeDto.getPlanType())){
                            beanHandle.changeDataBy5AMatchInstockTypeHandle(id, detailsDtos, workShopOrgIdMap, itemsDTOMap);
                        }else{
                            beanHandle.changeDataBy5AMatchPlanTypeHandle(id, detailsDtos, workShopOrgIdMap, itemsDTOMap);
                        }
                    } catch (Exception ex) {
                        log.info("changeDataBy5AMatch id：{} itemCodeMatchThreadPool exception：{}", id, JSON.toJSON(ex));
                        throw ex;
                    } finally {
                        countDownLatch.countDown();
                    }
                });

            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            log.info("changeDataBy5AMatch ...end ids：{}", ids);
        } catch (Exception ex) {
            log.info("changeDataBy5AMatch ids：{} exception：{}", ids, JSON.toJSONString(ex));
            throw ex;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeDataBy5AMatchInstockTypeHandle(long id,
                                                     List<Cell5AItemCodeDetailsDto> detailsDtos,
                                                     Map<String, String> workShopOrgIdMap,
                                                     Map<String, ItemsDTO> itemsDTOMap) {
        CellInstockPlan sourceCellPlanLine = instockRepository.getOne(id);
        CellInstockPlanDTO old = instockConvert.toDto(sourceCellPlanLine);
        boolean first = true;
        for (Cell5AItemCodeDetailsDto detailsDto : detailsDtos) {
            Long bbomId = detailsDto.getBbomId();
            CellInstockPlan cellProductionPlan = instockRepository.selectByBbomid(bbomId);
            if (cellProductionPlan == null) {
                if (first) {
                    cellProductionPlan = sourceCellPlanLine;
                    first = false;
                } else {
                    cellProductionPlan = instockConvert.toEntity(old);
                    cellProductionPlan.setId(null);
                }
            }
            ItemsDTO itemsDTO = getItemsDTO(workShopOrgIdMap, cellProductionPlan.getWorkshop(), detailsDto, itemsDTOMap);
            if (Objects.nonNull(itemsDTO)) {
                cellProductionPlan.setItemDesc(itemsDTO.getItemDesc());
                cellProductionPlan.setLifecycleState(itemsDTO.getLifecycleState());
                cellProductionPlan.setIsTemporaryOutput(itemsDTO.getIsTemporaryOutput());
            }
            cellProductionPlan.setItemCode(detailsDto.getItemCode());
            cellProductionPlan.setQtyPc(detailsDto.getQtyPc());
            cellProductionPlan.setNumberLine(detailsDto.getNumberLine());
            cellProductionPlan.setBbomId(detailsDto.getBbomId());
            //料号匹配传输：背面细栅、正面细栅、硅片厚度、硅片尺寸
            cellProductionPlan.setBackFineGrid(detailsDto.getBackFineGrid());
            cellProductionPlan.setFrontFineGrid(detailsDto.getFrontFineGrid());
            cellProductionPlan.setSiliconWaferSize(detailsDto.getSiliconWaferSize());
            cellProductionPlan.setSiliconWaferThickness(detailsDto.getSiliconWaferThickness());
            instockRepository.save(cellProductionPlan);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeDataBy5AMatchPlanTypeHandle(long id,
                                                  List<Cell5AItemCodeDetailsDto> detailsDtos,
                                                  Map<String, String> workShopOrgIdMap,
                                                  Map<String, ItemsDTO> itemsDTOMap) {
        CellPlanLine sourceCellPlanLine = repository.getOne(id);
        CellPlanLineDTO old = convert.toDto(sourceCellPlanLine);
        boolean first = true;
        for (Cell5AItemCodeDetailsDto detailsDto : detailsDtos) {
            Long bbomId = detailsDto.getBbomId();
            CellPlanLine cellProductionPlan = repository.selectByBbomid(bbomId);
            if (cellProductionPlan == null) {
                if (first) {
                    cellProductionPlan = sourceCellPlanLine;
                    first = false;
                } else {
                    cellProductionPlan = convert.toEntity(old);
                    cellProductionPlan.setId(null);
                }
            }
            ItemsDTO itemsDTO = getItemsDTO(workShopOrgIdMap, cellProductionPlan.getWorkshop(), detailsDto, itemsDTOMap);
            if (Objects.nonNull(itemsDTO)) {
                cellProductionPlan.setItemDesc(itemsDTO.getItemDesc());
                cellProductionPlan.setLifecycleState(itemsDTO.getLifecycleState());
                cellProductionPlan.setIsTemporaryOutput(itemsDTO.getIsTemporaryOutput());
            }
            cellProductionPlan.setItemCode(detailsDto.getItemCode());
            cellProductionPlan.setQtyPc(detailsDto.getQtyPc());
            cellProductionPlan.setNumberLine(detailsDto.getNumberLine());
            cellProductionPlan.setBbomId(detailsDto.getBbomId());
            //料号匹配传输：背面细栅、正面细栅、硅片厚度、硅片尺寸
            cellProductionPlan.setBackFineGrid(detailsDto.getBackFineGrid());
            cellProductionPlan.setFrontFineGrid(detailsDto.getFrontFineGrid());
            cellProductionPlan.setSiliconWaferSize(detailsDto.getSiliconWaferSize());
            cellProductionPlan.setSiliconWaferThickness(detailsDto.getSiliconWaferThickness());
            repository.save(cellProductionPlan);
        }
    }

    private ItemsDTO getItemsDTO(Map<String, String> workShopOrgIdMap, String cellProductionPlan, Cell5AItemCodeDetailsDto detailsDto, Map<String, ItemsDTO> itemsDTOMap) {
        String orgId = workShopOrgIdMap.get(cellProductionPlan);
        String groupKey = String.join(orgId, detailsDto.getItemCode());
        ItemsDTO itemsDTO = itemsDTOMap.get(groupKey);
        if (Objects.nonNull(itemsDTO)) {
            return itemsDTO;
        } else {
            ItemsNewQuery query = new ItemsNewQuery();
            query.setOrganizationId(Long.decode(orgId));
            query.setItemCodes(Collections.singletonList(detailsDto.getItemCode()));
            Results<Map<String, ItemsDTO>> response = bbomFeign.queryByItemCodeAll(query).getBody();
            Map<String, ItemsDTO> data = response.getData();
            itemsDTO = data.get(detailsDto.getItemCode());
            itemsDTOMap.put(groupKey, itemsDTO);
            return itemsDTO;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeDataBy5AMatchv2(Cell5AItemCodeListDto dto) {
        List<CellPlanLine> datas = new ArrayList<>();
        List<Cell5AItemCodeDto> itemDtos = dto.getItemDtos();
        for (Cell5AItemCodeDto itemCodeDto : itemDtos) {
            //对每个投产数据进行拆分处理
            long id = itemCodeDto.getId(); //投产Id
            //获取拆后的明细数据
            List<Cell5AItemCodeDetailsDto> detailsDtos = itemCodeDto.getDetailsDtos();
            if (CollectionUtils.isEmpty(detailsDtos)) continue;
            if (detailsDtos.size() > 1) {
                //拆了
                //拆后的明细数据处理
                boolean isSplite = false;
                boolean first = true;
                CellPlanLine cellProductionPlanFirst = null;
                for (Cell5AItemCodeDetailsDto detailsDto : detailsDtos) {
                    //判断是否已经在投产中拆过
                    if (first) {
                        Long bbomId = detailsDto.getBbomId();
                        CellPlanLine cellProductionPlan = repository.selectByBbomid(bbomId);
                        if (Objects.nonNull(cellProductionPlan)) {
                            isSplite = true;
                            cellProductionPlan.setItemCode(detailsDto.getItemCode());
                            datas.add(cellProductionPlan);
                        }

                    }
                    if (!isSplite) {
                        //如果拆分没有执行，进行拆分存储操作
                        if (first) {
                            cellProductionPlanFirst = repository.getOne(id);
                            cellProductionPlanFirst.setItemCode(detailsDto.getItemCode());
                            cellProductionPlanFirst.setQtyPc(detailsDto.getQtyPc());
                            cellProductionPlanFirst.setNumberLine(detailsDto.getNumberLine());
                            cellProductionPlanFirst.setBbomId(detailsDto.getBbomId());
                            datas.add(cellProductionPlanFirst);

                        } else {
                            CellPlanLine cellProductionPlan = new CellPlanLine();
                            BeanUtil.copyProperties(cellProductionPlanFirst, cellProductionPlan, "id");
                            cellProductionPlan.setId(null);
                            cellProductionPlan.setItemCode(detailsDto.getItemCode());
                            cellProductionPlan.setQtyPc(detailsDto.getQtyPc());
                            cellProductionPlan.setNumberLine(detailsDto.getNumberLine());
                            cellProductionPlan.setBbomId(detailsDto.getBbomId());
                            datas.add(cellProductionPlan);
                        }

                    } else {
                        //如果拆分已经执行过了，进行修改操作
                        // repository.updateItemCode(detailsDto.getBbomId(),detailsDto.getItemCode());

                    }
                    first = false;
                }
            } else {
                //没拆，执行修改操作，修改5A料号属性
                CellPlanLine cellProductionPlan = repository.getOne(id);
                cellProductionPlan.setItemCode(detailsDtos.get(0).getItemCode());
                cellProductionPlan.setBbomId(detailsDtos.get(0).getBbomId());
                datas.add(cellProductionPlan);
            }
        }
        repository.saveAll(datas);

    }

    @Override
    public Page<CellPlanLineDTO> queryTicketOpening(CellPlanLineQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //获取最新版本的投产明细
        //左国内 右国外
        Pair<String, String> stringPair = getFinalVersion(query);
        List<CellPlanLineDTO> domesticCellInstockPlan = getDomesticCellInstockPlan(stringPair.getLeft(), query);
        List<CellPlanLineDTO> abroadCellInstockPlan = getAbroadCellInstockPlan(stringPair.getRight(), query);
        domesticCellInstockPlan.addAll(abroadCellInstockPlan);
        //分组汇总
        List<CellProductionPlanSummaryDTO> summaryDTOList = groupByProductionPlan(domesticCellInstockPlan);
        //排序国内海外>生产基地>生产车间>生产单元>电池类型
        List<CellProductionPlanSummaryDTO> summaryDTOSortList = summaryDTOList.stream()
                .sorted(Comparator.comparing(CellProductionPlanSummaryDTO::getIsOversea)
                        .thenComparing(CellProductionPlanSummaryDTO::getBasePlace)
                        .thenComparing(CellProductionPlanSummaryDTO::getWorkshop)
                        .thenComparing(CellProductionPlanSummaryDTO::getWorkunit, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellProductionPlanSummaryDTO::getCellsType))
                .collect(Collectors.toList());
        summaryDTOSortList = filterByUser(summaryDTOSortList);
        List<CellProductionPlanSummaryDTO> cellProductionPlanSummaryDTOS = summaryDTOSortList.subList((query.getPageNumber() - 1) * query.getPageSize(), Math.min(query.getPageNumber() * query.getPageSize(), summaryDTOSortList.size()));
        //翻译
        MyThreadLocal.get().setLang(oldLang);
        cellProductionPlanSummaryDTOS = convert.toCellProductionPlanSummaryDTONameFromCNName(cellProductionPlanSummaryDTOS);
        return new PageImpl(cellProductionPlanSummaryDTOS, pageable, summaryDTOSortList.size());

    }

    private List<CellProductionPlanSummaryDTO> filterByUser(List<CellProductionPlanSummaryDTO> summaryDTOList) {
        List<DataPrivilegeDTO> privilegeDTOList = permissionService.getBasePlaceDataPrivilegeDTOS();

        //对summaryDTOList过滤，如果CellProductionPlanSummaryDTO的basePlaceId在privilegeDTOList与DataPrivilegeDTO中的getDataId相等，则保留，否则过滤
        //如果 privilegeDTOList中DataPrivilegeDTO的getDataId有一个等于-1就全部保留
        if (privilegeDTOList.stream().anyMatch(privilegeDTO -> privilegeDTO.getDataId().equals(-1L))) {
            return summaryDTOList;
        }
        summaryDTOList = summaryDTOList.stream().filter(summaryDTO -> {
            return privilegeDTOList.stream().anyMatch(privilegeDTO -> privilegeDTO.getDataId().equals(summaryDTO.getBasePlaceId()));
        }).collect(Collectors.toList());
        return summaryDTOList;

    }

    //分组汇总 纵向转横向
    private List<CellProductionPlanSummaryDTO> groupByProductionPlan(List<CellPlanLineDTO> productionPlanDTOList) {
        List<CellProductionPlanSummaryDTO> res = new ArrayList<>();
        Map<String, List<CellPlanLineDTO>> collectByGroupField = productionPlanDTOList.stream().collect(Collectors.groupingBy(i -> StringUtils.join(
                        i.getCellsType(), "/",
                        i.getAesthetics(), "/",
                        i.getTransparentDoubleGlass(), "/",
                        i.getHTrace(), "/",
                        i.getCellSource(), "/",
                        i.getWaferGrade(), "/",
                        i.getIsSpecialRequirement(), "/",
                        i.getScreenPlateMfrs(), "/",
                        i.getSiMfrs(), "/",
                        i.getCellMfrs(), "/",
                        i.getSilverPulpMfrs(), "/",
                        i.getLowResistance(), "/",
                        i.getDemandBasePlace(), "/",
                        i.getItemCode(), "/",
                        i.getBasePlace(), "/",
                        i.getWorkshop(), "/",
                        i.getWorkunit(), "/",
                        i.getMonth(), "/",
                        i.getProductionGrade(), "/",
                        i.getIsOversea(), "/",
                        i.getRegionalCountry(), "/",
                        i.getVerificationMark(), "/",
                        i.getSourceType(), "/",
                        StringUtils.isNotEmpty(i.getVerificationMark()) ? i.getLineName() : "", "/"
                )
        ));
        for (Map.Entry<String, List<CellPlanLineDTO>> entry : collectByGroupField.entrySet()) {
            String key = entry.getKey();
            List<CellPlanLineDTO> entryValue = entry.getValue();
            CellPlanLineDTO cellPlanLineDTO = entryValue.get(0);
            CellProductionPlanSummaryDTO item = convert.detailsToSummary(cellPlanLineDTO);
            String[] ketSplit = key.split("/", 24);//指定长度，不然有bug
            if (StringUtils.isNotEmpty(ketSplit[21])) {
                item.setLineName(ketSplit[21].contains("可靠性") ? item.getLineName() : "");
            } else {
                item.setLineName("");
            }
            item.setQtyPc(entryValue.stream().map(planDto -> Optional.ofNullable(planDto.getQtyPc()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
            res.add(item);
        }

        Map<String, List<CellPlanLineDTO>> collectByDate = productionPlanDTOList.stream().collect(Collectors.groupingBy(item -> StringUtils.join(
                        item.getCellsType(), "/",
                        item.getAesthetics(), "/",
                        item.getTransparentDoubleGlass(), "/",
                        item.getHTrace(), "/",
                        item.getCellSource(), "/",
                        item.getWaferGrade(), "/",
                        item.getIsSpecialRequirement(), "/",
                        item.getScreenPlateMfrs(), "/",
                        item.getSiMfrs(), "/",
                        item.getCellMfrs(), "/",
                        item.getSilverPulpMfrs(), "/",
                        item.getLowResistance(), "/",
                        item.getDemandBasePlace(), "/",
                        item.getItemCode(), "/",
                        item.getBasePlace(), "/",
                        item.getWorkshop(), "/",
                        item.getWorkunit(), "/",
                        item.getMonth(), "/",
                        item.getProductionGrade(), "/",
                        item.getIsOversea(), "/",
                        item.getRegionalCountry(), "/",
                        item.getVerificationMark(), "/",
                        item.getSourceType(), "/",
                        StringUtils.isNotEmpty(item.getVerificationMark()) ? item.getLineName() : "", "/"
                )
        ));
        // 1-[q,b,c,e]
        res.stream().forEach(item -> {
            String itemKey = StringUtils.join(
                    item.getCellsType(), "/",
                    item.getAesthetics(), "/",
                    item.getTransparentDoubleGlass(), "/",
                    item.getHTrace(), "/",
                    item.getCellSource(), "/",
                    item.getWaferGrade(), "/",
                    item.getIsSpecialRequirement(), "/",
                    item.getScreenPlateMfrs(), "/",
                    item.getSiMfrs(), "/",
                    item.getCellMfrs(), "/",
                    item.getSilverPulpMfrs(), "/",
                    item.getLowResistance(), "/",
                    item.getDemandBasePlace(), "/",
                    item.getItemCode(), "/",
                    item.getBasePlace(), "/",
                    item.getWorkshop(), "/",
                    item.getWorkunit(), "/",
                    item.getMonth(), "/",
                    item.getProductionGrade(), "/",
                    item.getIsOversea(), "/",
                    item.getRegionalCountry(), "/",
                    item.getVerificationMark(), "/",
                    item.getSourceType(), "/",
                    StringUtils.isNotEmpty(item.getVerificationMark()) ? item.getLineName() : "", "/"
            );
            List<String> subList = new ArrayList<>();
            Map<String, BigDecimal> subMap = new HashMap();
            //获取每一天下面有多少数量
            for (Map.Entry<String, List<CellPlanLineDTO>> entry : collectByDate.entrySet()) {
                List<CellPlanLineDTO> list = entry.getValue();
                Map<String, List<CellPlanLineDTO>> planMap = list.stream().collect(Collectors.groupingBy(CellPlanLineDTO::getStartTimeStr));
                planMap.forEach((planKey, dataList) ->{
                    if (entry.getKey().indexOf(itemKey) != -1) {
                        subList.add(planKey);
                        subMap.put(planKey, dataList.stream().map(planDto -> Optional.ofNullable(planDto.getQtyPc()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
                    }
                });
            }
            item.setSubList(subList.stream().sorted().collect(Collectors.toList()));
            item.setSubMap(subMap);
        });
        return res;
    }

    /**
     * 获取版本号
     *
     * @param query
     * @return
     */
    public Pair<String, String> getFinalVersion(CellPlanLineQuery query) {

        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        String isOversea = OverseaConstant.INLAND;
        String InVersion = getFinalVersion(month, isOversea);
        isOversea = OverseaConstant.OVERSEA;
        String outVersion = getFinalVersion(month, isOversea);
        return new ImmutablePair<>(InVersion, outVersion);
    }

    private String getFinalVersion(String month, String isOversea) {
        String version = jpaQueryFactory.select(qCellPlanLine.finalVersion.max()).from(qCellPlanLine).where(
                qCellPlanLine.oldMonth.eq(month)
        ).where(
                qCellPlanLine.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    //获取国内数据
    public List<CellPlanLineDTO> getDomesticCellInstockPlan(String finalVersion, CellPlanLineQuery query) {
        if (StringUtils.isNotEmpty(finalVersion)) {
            JPAQuery<CellPlanLine> where = jpaQueryFactory.select(qCellPlanLine).from(qCellPlanLine)
                    .where(qCellPlanLine.finalVersion.eq(finalVersion).and(qCellPlanLine.isOversea.eq("国内")));
            if (StringUtils.isNotEmpty(query.getBasePlace())) {
                where.where(qCellPlanLine.basePlace.eq(query.getBasePlace()));
            }
            if (StringUtils.isNotEmpty(query.getWorkshop())) {
                where.where(qCellPlanLine.workshop.eq(query.getWorkshop()));
            }
            if (StringUtils.isNotEmpty(query.getCellsType())) {
                where.where(qCellPlanLine.cellsType.eq(query.getCellsType()));
            }
            if (StringUtils.isNotEmpty(query.getMonth())) {
                where.where(qCellPlanLine.oldMonth.eq(query.getMonth()));
            }
            return convert.toDto(where.fetch());
        }
        return new ArrayList<>();
    }

    //获取国外数据
    public List<CellPlanLineDTO> getAbroadCellInstockPlan(String finalVersion, CellPlanLineQuery query) {
        if (StringUtils.isNotEmpty(finalVersion)) {
            JPAQuery<CellPlanLine> where = jpaQueryFactory.select(qCellPlanLine).from(qCellPlanLine)
                    .where(qCellPlanLine.finalVersion.eq(finalVersion).and(qCellPlanLine.isOversea.eq("海外")));
            if (StringUtils.isNotEmpty(query.getBasePlace())) {
                where.where(qCellPlanLine.basePlace.eq(query.getBasePlace()));
            }
            if (StringUtils.isNotEmpty(query.getWorkshop())) {
                where.where(qCellPlanLine.workshop.eq(query.getWorkshop()));
            }
            if (StringUtils.isNotEmpty(query.getCellsType())) {
                where.where(qCellPlanLine.cellsType.eq(query.getCellsType()));
            }
            if (StringUtils.isNotEmpty(query.getMonth())) {
                where.where(qCellPlanLine.month.eq(query.getMonth()));
            }
            return convert.toDto(where.fetch());
        }
        return new ArrayList<>();
    }

    /**
     * 工单预览保存投产明细查询使用
     */
    public List<CellPlanLineDTO> queryMaxVersionByCellWip(CellPlanLineQuery query) {
        //1.具有转换功能的对象
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTime = "";
        String endTime = "";
        //如果日期拼接包含多个 拆分第一个到开始时间 截取最后一个
        if (query.getStrTime().indexOf(",") > 0) {
            String firstIndexOf = String.valueOf(query.getStrTime().indexOf(","));
            startTime = query.getStrTime().substring(0, Integer.parseInt(firstIndexOf));
            endTime = query.getStrTime().substring(query.getStrTime().lastIndexOf(",") + 1);
        } else {
            startTime = query.getStrTime();
        }
        //查询最大版本号对应的数据
        JPAQuery<CellPlanLine> whereData = jpaQueryFactory.select(
                qCellPlanLine
        ).from(qCellPlanLine).where(
                qCellPlanLine.month.eq(query.getMonth())
        );
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            whereData.where(qCellPlanLine.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            whereData.where(qCellPlanLine.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            whereData.where(qCellPlanLine.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            whereData.where(qCellPlanLine.startTime.goe(LocalDate.parse(StringUtils.join(startTime, " 00:00:00"), df).atStartOfDay())
                    .and(qCellPlanLine.endTime.loe(LocalDate.parse(StringUtils.join(endTime, " 23:59:59"), df).atStartOfDay())));
        } else {
            whereData.where(qCellPlanLine.startTime.goe(LocalDate.parse(StringUtils.join(startTime, " 00:00:00"), df).atStartOfDay())
                    .and(qCellPlanLine.endTime.loe(LocalDate.parse(StringUtils.join(startTime, " 23:59:59"), df).atStartOfDay())));
        }
        whereData.where(qCellPlanLine.finalVersion.eq(query.getFinalVersion()));

        List<CellPlanLine> fetch = whereData.fetch();
        return convert.toDto(fetch);
    }


    @Override
    public List<CellInstockPlanSummaryDTO> findSummaryByMonth(CellPlanLineQuery query) {
        Assert.notNull(query.getIsOverseaId());
        Assert.notNull(query.getMonth());
        //获取最新版本号
        String lastVersion = versionService.findMaxVersion(query.getIsOverseaId(), query.getMonth());
        lastVersion = StringUtils.isNotEmpty(lastVersion) ? lastVersion : "-1";
        JPAQuery<CellInstockPlanSummaryDTO> jpaQuery = jpaQueryFactory.select(
                        Projections.fields(
                                CellInstockPlanSummaryDTO.class,
                                qCellPlanLine.isOverseaId.as("isOverseaId"),
                                qCellPlanLine.isOversea.as("isOversea"),
                                qCellPlanLine.cellsTypeId.as("cellTypeId"),
                                qCellPlanLine.cellsType.as("cellType"),
                                qCellPlanLine.transparentDoubleGlassId.as("transparentDoubleGlassId"),
                                qCellPlanLine.transparentDoubleGlass.as("transparentDoubleGlass"),
                                qCellPlanLine.regionalCountryId.as("regionalCountryId"),
                                qCellPlanLine.regionalCountry.as("regionalCountry"),
                                qCellPlanLine.aestheticsId.as("aestheticsId"),
                                qCellPlanLine.aesthetics.as("aesthetics"),
                                qCellPlanLine.cellSourceId.as("cellSourceId"),
                                qCellPlanLine.cellSource.as("cellSource"),
                                qCellPlanLine.hTraceId.as("hTraceId"),
                                qCellPlanLine.hTrace.as("hTrace"),
                                qCellPlanLine.oldMonth.as("month"),
                                qCellPlanLine.qtyPc.sum().as("quantity")
                        )
                )
                .from(qCellPlanLine)
                .where(qCellPlanLine.isDeleted.eq(DeleteEnum.NO.getCode()))
                .where(qCellPlanLine.isOverseaId.eq(query.getIsOverseaId()))
                .where(qCellPlanLine.oldMonth.eq(query.getMonth()))
                .where(qCellPlanLine.version.eq(lastVersion))
                .groupBy(qCellPlanLine.isOverseaId,
                        qCellPlanLine.isOversea,
                        qCellPlanLine.cellsType,
                        qCellPlanLine.cellsTypeId,
                        qCellPlanLine.transparentDoubleGlass,
                        qCellPlanLine.transparentDoubleGlassId,
                        qCellPlanLine.regionalCountry,
                        qCellPlanLine.regionalCountryId,
                        qCellPlanLine.aesthetics,
                        qCellPlanLine.aestheticsId,
                        qCellPlanLine.cellSource,
                        qCellPlanLine.cellSourceId,
                        qCellPlanLine.hTrace,
                        qCellPlanLine.hTraceId,
                        qCellPlanLine.oldMonth);
        List<CellInstockPlanSummaryDTO> fetch = jpaQuery.fetch();
        return fetch;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveChangeLines(List<CellPlanLineDTO> changeLines) {
        if (CollectionUtils.isNotEmpty(changeLines)) {
            //计算oldQty等数据
            cellPlanLineTotalService.calcOldQtyPc(changeLines);
            //入库
            List<CellPlanLine> cellPlanLines = convert.toEntity(changeLines);
            repository.saveAll(cellPlanLines);
        }
    }

 /*    public void match7AInfo(CellPlanLineQuery query) {
        List<CellPlanLineDTO> cellPlanLineDTOS = query(query);
        if (CollectionUtils.isNotEmpty(cellPlanLineDTOS)) {
            Set<CellPlanLineDTO> updateCellPlanLineDTOS = new HashSet<>();
            BatteryScreenPlateWorkshopQuery screenPlateWorkshopQuery = new BatteryScreenPlateWorkshopQuery();
            screenPlateWorkshopQuery.setPageNumber(1);
            screenPlateWorkshopQuery.setPageSize(GlobalConstant.max_page_size);
            ResponseEntity<Results<PageFeign<BatteryScreenPlateWorkshopDTO>>> resultsResponseEntity = bbomFeign.queryBatteryScreenPlateList(screenPlateWorkshopQuery);
            List<BatteryScreenPlateWorkshopDTO> batteryScreenPlateWorkshopDTOS = resultsResponseEntity.getBody().getData().getContent();
            ResponseEntity<Results<List<BatterySiliconWaferDTO>>> batterySiliconWaferEntity = bbomFeign.queryBatterySiliconWaferList();
            List<BatterySiliconWaferDTO> batterySiliconWaferDTOS = batterySiliconWaferEntity.getBody().getData();
            if (CollectionUtils.isNotEmpty(batteryScreenPlateWorkshopDTOS)) {
                cellPlanLineDTOS.forEach(cellPlanLine -> {
                    for(BatteryScreenPlateWorkshopDTO batteryScreenPlateWorkshopDTO: batteryScreenPlateWorkshopDTOS){
                        if(cellPlanLine.getCellsType().equals(batteryScreenPlateWorkshopDTO.getBatteryCode())
                                && cellPlanLine.getMainGridSpace().equals(batteryScreenPlateWorkshopDTO.getMainGridSpace())
                                && cellPlanLine.getWorkshop().equals(batteryScreenPlateWorkshopDTO.getWorkshop())
                                && cellPlanLine.getBasePlace().equals(batteryScreenPlateWorkshopDTO.getBasePlace())){
                            if (StringUtils.isNotEmpty(cellPlanLine.getTransparentDoubleGlass()) && "单玻".equals(cellPlanLine.getTransparentDoubleGlass()) && "Y".equals(batteryScreenPlateWorkshopDTO.getSingleGlassFlag())) {
                                if((Objects.isNull(batteryScreenPlateWorkshopDTO.getEffectiveStartDate()) || batteryScreenPlateWorkshopDTO.getEffectiveStartDate().isBefore(cellPlanLine.getStartTime().toLocalDate())
                                        && (Objects.isNull(batteryScreenPlateWorkshopDTO.getEffectiveEndDate()) || batteryScreenPlateWorkshopDTO.getEffectiveEndDate().isAfter(cellPlanLine.getStartTime().toLocalDate())))){
                                    cellPlanLine.setBackFineGrid(batteryScreenPlateWorkshopDTO.getNegativeElectrodeScreenFineGrid());
                                    cellPlanLine.setFrontFineGrid(batteryScreenPlateWorkshopDTO.getPositiveElectrodeScreenFineGrid());
                                    updateCellPlanLineDTOS.add(cellPlanLine);
                                }
                            }else {
                                if((Objects.isNull(batteryScreenPlateWorkshopDTO.getEffectiveStartDate()) || batteryScreenPlateWorkshopDTO.getEffectiveStartDate().isBefore(cellPlanLine.getStartTime().toLocalDate())
                                        && (Objects.isNull(batteryScreenPlateWorkshopDTO.getEffectiveEndDate()) || batteryScreenPlateWorkshopDTO.getEffectiveEndDate().isAfter(cellPlanLine.getStartTime().toLocalDate())))){
                                    cellPlanLine.setBackFineGrid(batteryScreenPlateWorkshopDTO.getNegativeElectrodeScreenFineGrid());
                                    cellPlanLine.setFrontFineGrid(batteryScreenPlateWorkshopDTO.getPositiveElectrodeScreenFineGrid());
                                    updateCellPlanLineDTOS.add(cellPlanLine);
                                }
                            }
                        }
                    }
                });
            }

            if(CollectionUtils.isNotEmpty(batterySiliconWaferDTOS)){
                cellPlanLineDTOS.forEach(cellPlanLine -> {
                    for(BatterySiliconWaferDTO batterySiliconWaferDTO: batterySiliconWaferDTOS){
                        if(cellPlanLine.getCellsType().equals(batterySiliconWaferDTO.getBatteryCode())
                                && cellPlanLine.getWorkshop().equals(batterySiliconWaferDTO.getWorkshop())
                                && cellPlanLine.getWorkunit().equals(batterySiliconWaferDTO.getWorkunit())
                                && cellPlanLine.getBasePlace().equals(batterySiliconWaferDTO.getBasePlace())){
                            if ("DT".equals(cellPlanLine.getCellSource()) && "Y".equals(batterySiliconWaferDTO.getLowCarbonFlag())) {
                                if((Objects.isNull(batterySiliconWaferDTO.getEffectiveStartDate()) || batterySiliconWaferDTO.getEffectiveStartDate().isBefore(cellPlanLine.getStartTime().toLocalDate())
                                        && (Objects.isNull(batterySiliconWaferDTO.getEffectiveEndDate()) || batterySiliconWaferDTO.getEffectiveEndDate().isAfter(cellPlanLine.getStartTime().toLocalDate())))){
                                    cellPlanLine.setSiliconWaferThickness(batterySiliconWaferDTO.getWaferThickness());
                                    cellPlanLine.setSiliconWaferSize(batterySiliconWaferDTO.getWaferCategory());
                                    updateCellPlanLineDTOS.add(cellPlanLine);
                                }
                            }else {
                                if((Objects.isNull(batterySiliconWaferDTO.getEffectiveStartDate()) || batterySiliconWaferDTO.getEffectiveStartDate().isBefore(cellPlanLine.getStartTime().toLocalDate())
                                        && (Objects.isNull(batterySiliconWaferDTO.getEffectiveEndDate()) || batterySiliconWaferDTO.getEffectiveEndDate().isAfter(cellPlanLine.getStartTime().toLocalDate())))){
                                    cellPlanLine.setSiliconWaferThickness(batterySiliconWaferDTO.getWaferThickness());
                                    cellPlanLine.setSiliconWaferSize(batterySiliconWaferDTO.getWaferCategory());
                                    updateCellPlanLineDTOS.add(cellPlanLine);
                                }
                            }
                        }
                    }
                });
            }
            if(CollectionUtils.isNotEmpty(updateCellPlanLineDTOS)){
                repository.saveAll(convert.toCopyEntityFromDto(new ArrayList<>(updateCellPlanLineDTOS)));
            }
        }
    } */

    @Override
    @Transactional(rollbackFor =Exception.class)
    public void match7AInfo(CellPlanLineQuery query) {
        try {
            query.setIsOversea(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()));
            List<CellPlanLineDTO> cellPlanLineDTOS = query(query);
            if (CollectionUtils.isNotEmpty(cellPlanLineDTOS)) {
                Map<Long,CellPlanLineDTO>  updateCellPlanLineDTOS = new HashMap<>();

                // 获取电池筛选板车间信息
                ResponseEntity<Results<PageFeign<BatteryScreenPlateWorkshopDTO>>> screenPlateResponse = bbomFeign.queryBatteryScreenPlateList(createScreenPlateQuery());
                List<BatteryScreenPlateWorkshopDTO> batteryScreenPlateWorkshopDTOS = null;
                if (screenPlateResponse != null || screenPlateResponse.getBody() != null || screenPlateResponse.getBody().getData() != null) {
                    batteryScreenPlateWorkshopDTOS = screenPlateResponse.getBody().getData().getContent();
                }

                // 获取电池硅片信息
                ResponseEntity<Results<List<BatterySiliconWaferDTO>>> siliconWaferResponse = bbomFeign.queryBatterySiliconWaferList();
                List<BatterySiliconWaferDTO> batterySiliconWaferDTOS = null;
                if (siliconWaferResponse != null || siliconWaferResponse.getBody() != null || siliconWaferResponse.getBody().getData() != null) {
                    batterySiliconWaferDTOS = siliconWaferResponse.getBody().getData();
                }
                // 处理电池筛选板车间信息
                processBatteryScreenPlate(cellPlanLineDTOS, batteryScreenPlateWorkshopDTOS, updateCellPlanLineDTOS);

                // 处理电池硅片信息
                processBatterySiliconWafer(cellPlanLineDTOS, batterySiliconWaferDTOS, updateCellPlanLineDTOS);

                // 保存更新后的数据
                if (!updateCellPlanLineDTOS.isEmpty()) {
                    ArrayList<CellPlanLineDTO> cellPlanLineDTOS1 = new ArrayList<>(updateCellPlanLineDTOS.values());
                    repository.saveAll(convert.toCopyEntityFromDto(cellPlanLineDTOS1));
                }
            }
        } catch (Exception e) {
            // 记录异常日志并处理
            log.error("Error in match7AInfo", e);
            throw new BizException("match7AInfo 失败！");
        }
    }

    private BatteryScreenPlateWorkshopQuery createScreenPlateQuery() {
        BatteryScreenPlateWorkshopQuery query = new BatteryScreenPlateWorkshopQuery();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        return query;
    }

    private <T> List<T> getBodyData(ResponseEntity<Results<?>> response) {
        if (response == null || response.getBody() == null || response.getBody().getData() == null) {
            return Collections.emptyList();
        }
        return (List<T>) response.getBody().getData();
    }

    private void processBatteryScreenPlate(List<CellPlanLineDTO> cellPlanLineDTOS, List<BatteryScreenPlateWorkshopDTO> batteryScreenPlateWorkshopDTOS, Map<Long,CellPlanLineDTO> updateCellPlanLineDTOS) {
        if (CollectionUtils.isNotEmpty(batteryScreenPlateWorkshopDTOS)) {
            Map<String, List<BatteryScreenPlateWorkshopDTO>> workshopMapBySingleGlassFlag = batteryScreenPlateWorkshopDTOS.stream().filter(p->"Y".equals(p.getSingleGlassFlag()))
                    .collect(Collectors.groupingBy(
                            dto -> dto.getBatteryName() + dto.getMainGridSpace() + dto.getWorkshop() + dto.getBasePlace()));
            Map<String, List<BatteryScreenPlateWorkshopDTO>> workshopMapByNoSingleGlassFlag= batteryScreenPlateWorkshopDTOS.stream().filter(p->"N".equals(p.getSingleGlassFlag()))
                    .collect(Collectors.groupingBy(
                            dto -> dto.getBatteryName() + dto.getMainGridSpace() + dto.getWorkshop() + dto.getBasePlace()));
            cellPlanLineDTOS.forEach(cellPlanLine -> {
                String key = cellPlanLine.getCellsType() + cellPlanLine.getMainGridSpace() + cellPlanLine.getWorkshop() + cellPlanLine.getBasePlace();
                List<BatteryScreenPlateWorkshopDTO> workshopDTOs ="单玻".equals(cellPlanLine.getTransparentDoubleGlass()) ? workshopMapBySingleGlassFlag.getOrDefault(key, Collections.emptyList()) : workshopMapByNoSingleGlassFlag.getOrDefault(key, Collections.emptyList());
                Optional<BatteryScreenPlateWorkshopDTO> first = workshopDTOs.stream().filter(workshopDTO ->
                        isWithinEffectiveDate(workshopDTO.getEffectiveStartDate(), workshopDTO.getEffectiveEndDate(), cellPlanLine.getStartTime().toLocalDate())).findFirst();
                if(first.isPresent()){
                    setFineGrid(cellPlanLine, first.get());
                    updateCellPlanLineDTOS.put(cellPlanLine.getId(), cellPlanLine);
                }
            });
        }
    }

    private void processBatterySiliconWafer(List<CellPlanLineDTO> cellPlanLineDTOS, List<BatterySiliconWaferDTO> batterySiliconWaferDTOS, Map<Long,CellPlanLineDTO> updateCellPlanLineDTOS) {
        if (CollectionUtils.isNotEmpty(batterySiliconWaferDTOS)) {
            Map<String, List<BatterySiliconWaferDTO>> waferMapByDT = batterySiliconWaferDTOS.stream().filter(p->"Y".equals(p.getLowCarbonFlag()))
                    .collect(Collectors.groupingBy(
                            dto -> dto.getBatteryName() + dto.getWorkshop() + dto.getWorkunit() + dto.getBasePlace()));
            Map<String, List<BatterySiliconWaferDTO>> waferMapByNotDT = batterySiliconWaferDTOS.stream().filter(p->"N".equals(p.getLowCarbonFlag()))
                    .collect(Collectors.groupingBy(
                            dto -> dto.getBatteryName() + dto.getWorkshop() + dto.getWorkunit() + dto.getBasePlace()));

            cellPlanLineDTOS.forEach(cellPlanLine -> {
                String key = cellPlanLine.getCellsType() + cellPlanLine.getWorkshop() + cellPlanLine.getWorkunit() + cellPlanLine.getBasePlace();
                List<BatterySiliconWaferDTO> waferDTOs =  "DT".equals(cellPlanLine.getCellSource()) ? waferMapByDT.getOrDefault(key, Collections.emptyList()) : waferMapByNotDT.getOrDefault(key, Collections.emptyList());
                Optional<BatterySiliconWaferDTO> first = waferDTOs.stream().filter(waferDTO -> isWithinEffectiveDate(waferDTO.getEffectiveStartDate(), waferDTO.getEffectiveEndDate(), cellPlanLine.getStartTime().toLocalDate())).findFirst();
                if (first.isPresent()) {
                    setSiliconWaferProperties(cellPlanLine, first.get());
                    updateCellPlanLineDTOS.put(cellPlanLine.getId(), cellPlanLine);
                }
            });
        }
    }

    private boolean isWithinEffectiveDate(LocalDate effectiveStartDate,LocalDate effectiveEndDate, LocalDate startTime) {
        return (effectiveStartDate == null || !effectiveStartDate.isAfter(startTime)) && (effectiveEndDate == null || !effectiveEndDate.isBefore(startTime));
    }

    private void setFineGrid(CellPlanLineDTO cellPlanLine, BatteryScreenPlateWorkshopDTO workshopDTO) {
        cellPlanLine.setBackFineGrid(workshopDTO.getNegativeElectrodeScreenFineGrid());
        cellPlanLine.setFrontFineGrid(workshopDTO.getPositiveElectrodeScreenFineGrid());
    }

    private void setSiliconWaferProperties(CellPlanLineDTO cellPlanLine, BatterySiliconWaferDTO waferDTO) {
        cellPlanLine.setSiliconWaferThickness(waferDTO.getWaferThickness());
        cellPlanLine.setSiliconWaferSize(waferDTO.getWaferCategory());
    }


}
