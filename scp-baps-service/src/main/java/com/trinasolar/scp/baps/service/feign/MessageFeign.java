package com.trinasolar.scp.baps.service.feign;
import com.trinasolar.scp.baps.domain.dto.message.MsgReceiverDto;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = FeignConstant.DPA_MESSAGE_SERVICE, path = "/scp-dpa-message-api"/*,url = "https://scpapitest.trinasolar.com"*/)
public interface MessageFeign {
    @PostMapping("/message/send")
    Object sendMsg(@RequestBody MsgReceiverDto msgReceiverDto);
}

