package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.convert.CalendarDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellBaseCapacityDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellBomManufacturingDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDTO;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityMonthDiscountsDTO;
import com.trinasolar.scp.baps.domain.entity.Calendar;
import com.trinasolar.scp.baps.domain.entity.CellBaseCapacity;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturing;
import com.trinasolar.scp.baps.domain.entity.QCellBaseCapacity;
import com.trinasolar.scp.baps.domain.excel.CellBaseCapacityExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.excel.*;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacitySaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CalendarRepository;
import com.trinasolar.scp.baps.service.repository.CellBaseCapacityRepository;
import com.trinasolar.scp.baps.service.repository.CellBomManufacturingRepository;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.baps.service.service.system.SystemService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * IE产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Slf4j
@Service("cellBaseCapacityService")
@RequiredArgsConstructor
public class CellBaseCapacityServiceImpl implements CellBaseCapacityService {
    private static final QCellBaseCapacity qCellBaseCapacity = QCellBaseCapacity.cellBaseCapacity;

    private final CellBaseCapacityDEConvert convert;

    private final CellBaseCapacityRepository repository;

    private final CalendarRepository calendarRepository;

    private final CalendarService calendarService;

    private final SystemService systemService;
    private final CellBaseCapacityMonthDiscountsService cellBaseCapacityMonthDiscountsService;

    private final CellBomManufacturingRepository cellBomManufacturingRepository;
    private final CellBomManufacturingService cellBomManufacturingService;

    private final CellBomManufacturingDEConvert cellBomManufacturingDEConvert;

    private final CalendarDEConvert calendarDEConvert;
    private final CellResourceService cellResourceService;

    @Override
    public Page<CellBaseCapacityDTO> queryByPage(CellBaseCapacityQuery query) {

        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.ASC, "workunit", "lineName", "cellsType", "startTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellBaseCapacity> page = repository.findAll(booleanBuilder, pageable);
        List<CellBaseCapacityDTO> content = convert.toDto(page.getContent());
        content = convert.toDtoNameById(content);
        return new PageImpl(content, page.getPageable(), page.getTotalElements());
    }

    @Override
    public Map<String, List<CellBaseCapacityDTO>> queryForDm(CellBaseCapacityQuery query) {
        Map<String, List<CellBaseCapacityDTO>> map = Maps.newHashMap();
        List<String> monthList = query.getMonthList();
        if (CollectionUtils.isNotEmpty(monthList)) {
            for (String month : monthList) {
                BooleanBuilder booleanBuilder = new BooleanBuilder();
                query.setMonth(month);
                buildWhere(booleanBuilder, query);
                List<CellBaseCapacity> data = IterableUtils.toList(repository.findAll(booleanBuilder));
                List<CellBaseCapacityDTO> cellBaseCapacityDTOS = convert.toDto(data);
                if (CollectionUtils.isNotEmpty(cellBaseCapacityDTOS)) {
                    //计算ie打折
                    List<CellBaseCapacityDTO> computeDiscounts = computeDiscounts(month, cellBaseCapacityDTOS);
                    map.put(month, computeDiscounts);
                }
            }
        }
        return map;
    }

    /**
     * 计算产能打折，计算到天
     *
     * @param month
     * @param cellBaseCapacityDTOS
     * @return
     */
    private List<CellBaseCapacityDTO> computeDiscounts(String month, List<CellBaseCapacityDTO> cellBaseCapacityDTOS) {
        //产能打折
        List<CellBaseCapacityMonthDiscountsDTO> discountsList = cellBaseCapacityMonthDiscountsService.findByMonth(month);
        Map<String, CellBaseCapacityMonthDiscountsDTO> discountsMap = discountsList.stream().collect(Collectors.toMap(k -> StringUtils.join(k.getIsOverseaId(), k.getBasePlaceId(), k.getWorkshopId()), Function.identity()));
        return cellBaseCapacityDTOS.stream().map(item -> {
            String key = StringUtils.join(item.getIsOverseaId(), item.getBasePlaceId(), item.getWorkshopid());
            //获取当前月份中开始和结束日期
            Pair<String, String> monthRange = item.getMonthRange(month);
            //获取两个时间之间的所有日期集合
            List<LocalDate> days = DateUtil.getDays(monthRange.getLeft(), monthRange.getRight());
            CellBaseCapacityMonthDiscountsDTO discounts = discountsMap.getOrDefault(key, new CellBaseCapacityMonthDiscountsDTO());
            return days.stream().map(day -> {
                int dayOfMonth = day.getDayOfMonth();
                Object fieldValue = ReflectUtil.getFieldValue(discounts, String.format("d%s", dayOfMonth));
                BigDecimal bigDecimal = Optional.ofNullable((BigDecimal) fieldValue).orElse(BigDecimal.ONE);
                CellBaseCapacityDTO clone = SerializationUtils.clone(item);
                BigDecimal singleCapacity = Optional.ofNullable(clone.getSingleCapacity()).orElse(BigDecimal.ZERO);
                clone.setSingleCapacity(singleCapacity.multiply(bigDecimal));
                return clone;
            }).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 计算产能打折，计算到天
     *
     * @param monthList
     * @param workshop
     * @param cellBaseCapacityDTOS
     * @return
     */
    private List<CellBaseCapacityDTO> computeDiscounts(List<String> monthList, String workshop, List<CellBaseCapacityDTO> cellBaseCapacityDTOS) {
        return monthList.stream().map(month -> {
            //产能打折
            List<CellBaseCapacityMonthDiscountsDTO> discountsList = cellBaseCapacityMonthDiscountsService.findByMonth(month);
            Map<String, CellBaseCapacityMonthDiscountsDTO> discountsMap = discountsList.stream().filter(k -> workshop.equals(k.getWorkshop())).collect(Collectors.toMap(k -> StringUtils.join(k.getIsOverseaId(), k.getBasePlaceId(), k.getWorkshopId(), k.getWorkunitId()), Function.identity()));
            return cellBaseCapacityDTOS.stream().map(item -> {
                String key = StringUtils.join(item.getIsOverseaId(), item.getBasePlaceId(), item.getWorkshopid(), item.getWorkunitid());
                //获取当前月份中开始和结束日期
                Pair<String, String> monthRange = item.getMonthRange(month);
                //获取两个时间之间的所有日期集合
                List<LocalDate> days = DateUtil.getDays(monthRange.getLeft(), monthRange.getRight());
                CellBaseCapacityMonthDiscountsDTO discounts = discountsMap.getOrDefault(key, new CellBaseCapacityMonthDiscountsDTO());
                return days.stream().map(day -> {
                    int dayOfMonth = day.getDayOfMonth();
                    Object fieldValue = ReflectUtil.getFieldValue(discounts, String.format("d%s", dayOfMonth));
                    BigDecimal bigDecimal = Optional.ofNullable((BigDecimal) fieldValue).orElse(BigDecimal.ONE);
                    CellBaseCapacityDTO clone = SerializationUtils.clone(item);
                    BigDecimal singleCapacity = Optional.ofNullable(clone.getSingleCapacity()).orElse(BigDecimal.ZERO);
                    clone.setSingleCapacity(singleCapacity.multiply(bigDecimal));
                    clone.setLocalDate(day);
                    return clone;
                }).collect(Collectors.toList());
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }


    @Override
    public BigDecimal findByCondition(Long isOverseaId, String workshop, LocalDate startDate, LocalDate endDate, String productCategory, String crystalType) {
        //月份集合
        List<String> monthList = DateUtil.getMonthList(startDate, endDate);
        //电池类型值集
        Map<Long, LovLineDTO> lovMap = systemService.findByLovCode(LovHeaderCodeConstant.BATTERY_TYPE, LovLineDTO::getLovLineId, Function.identity());
        String productCategoryId = LovUtils.get(LovHeaderCodeConstant.PRODUCT_CATEGORY, productCategory).getLovLineId().toString();
        String crystalTypeId = LovUtils.get(LovHeaderCodeConstant.CRYSTAL_TYPE, crystalType).getLovLineId().toString();
        //获取对应品类、型号下的电池类型
        List<Long> cellTypeIdList = lovMap.values().stream().filter(l -> StringUtils.isNoneBlank(l.getAttribute2(), l.getAttribute3()) && StringUtils.equals(l.getAttribute2(), crystalTypeId) && StringUtils.equals(l.getAttribute3(), productCategoryId)).map(LovLineDTO::getLovLineId).collect(Collectors.toList());
        CellBaseCapacityQuery query = new CellBaseCapacityQuery();
        query.setIsOverseaId(isOverseaId);
        query.setCellsTypeIdList(cellTypeIdList);
        query.setWorkshop(workshop);
        query.setBeginDate(startDate);
        query.setEndDate(endDate);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        //查询IE
        List<CellBaseCapacity> data = IterableUtils.toList(repository.findAll(booleanBuilder));
        List<CellBaseCapacityDTO> cellBaseCapacityDTOS = convert.toDto(data);
        if (CollectionUtils.isNotEmpty(cellBaseCapacityDTOS)) {
            //计算ie打折
            List<CellBaseCapacityDTO> computeDiscounts = computeDiscounts(monthList, workshop, cellBaseCapacityDTOS);
            //获日期范围下的日期
            List<CellBaseCapacityDTO> dataList = computeDiscounts.stream().filter(k -> startDate.compareTo(k.getLocalDate()) <= 0 && endDate.compareTo(k.getLocalDate()) >= 0).collect(Collectors.toList());
            return dataList.stream().map(e -> (e.getUsageLine().multiply(e.getSingleCapacity()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return BigDecimal.ZERO;
    }


    private void buildWhere(BooleanBuilder booleanBuilder, CellBaseCapacityQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellBaseCapacity.id.eq(query.getId()));
        }
        if (query.getStartTime() != null) {
            booleanBuilder.and(qCellBaseCapacity.startTime.eq(query.getStartTime()));
        }
        if (query.getEndTime() != null) {
            booleanBuilder.and(qCellBaseCapacity.endTime.eq(query.getEndTime()));
        }
        if (query.getBeginDate() != null && query.getEndDate() != null) {
            booleanBuilder.and((qCellBaseCapacity.startTime.loe(query.getBeginDate()).and(qCellBaseCapacity.endTime.goe(query.getBeginDate())))
                    .or(qCellBaseCapacity.startTime.loe(query.getEndDate()).and(qCellBaseCapacity.endTime.goe(query.getEndDate()))));
        }
        if (Objects.nonNull(query.getCellsTypeId())) {
            booleanBuilder.and(qCellBaseCapacity.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (CollectionUtils.isNotEmpty(query.getCellsTypeIdList())) {
            booleanBuilder.and(qCellBaseCapacity.cellsTypeId.in(query.getCellsTypeIdList()));
        }
        if (Objects.nonNull(query.getIsOverseaId())) {
            booleanBuilder.and(qCellBaseCapacity.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (Objects.nonNull(query.getBasePlaceId())) {
            booleanBuilder.and(qCellBaseCapacity.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (Objects.nonNull(query.getWorkshopid())) {
            booleanBuilder.and(qCellBaseCapacity.workshopid.eq(query.getWorkshopid()));
        }
        if (Objects.nonNull(query.getWorkunitid())) {
            booleanBuilder.and(qCellBaseCapacity.workunitid.eq(query.getWorkunitid()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellBaseCapacity.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellBaseCapacity.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellBaseCapacity.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellBaseCapacity.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qCellBaseCapacity.workunit.eq(query.getWorkunit()));
        }
        if (StringUtils.isNotEmpty(query.getLineName())) {
            booleanBuilder.and(qCellBaseCapacity.lineName.eq(query.getLineName()));
        }
        if (query.getNumberLine() != null) {
            booleanBuilder.and(qCellBaseCapacity.numberLine.eq(query.getNumberLine()));
        }
        if (query.getUsageLine() != null) {
            booleanBuilder.and(qCellBaseCapacity.usageLine.eq(query.getUsageLine()));
        }
        if (query.getSingleCapacity() != null) {
            booleanBuilder.and(qCellBaseCapacity.singleCapacity.eq(query.getSingleCapacity()));
        }
        if (StringUtils.isNotEmpty(query.getUnit())) {
            booleanBuilder.and(qCellBaseCapacity.unit.eq(query.getUnit()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            //按月份查询
            LocalDate firstDate = DateUtil.month2BeginLocalDate(query.getMonth());
            LocalDate lastDate = DateUtil.month2EndLocalDate(query.getMonth());

            booleanBuilder.and((qCellBaseCapacity.startTime.loe(firstDate).and(qCellBaseCapacity.endTime.goe(firstDate)))
                    .or(qCellBaseCapacity.startTime.loe(lastDate).and(qCellBaseCapacity.endTime.goe(lastDate))));

        }
    }

    @Override
    public CellBaseCapacityDTO queryById(Long id) {
        CellBaseCapacity queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellBaseCapacityDTO save(CellBaseCapacitySaveDTO saveDTO) {
        CellBaseCapacity newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new CellBaseCapacity());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);
        CellBaseCapacityDTO result = this.queryById(newObj.getId());

        //同步bom和生产日历
        if (result != null) {
            //添加对应新bom的数据
            CellBomManufacturing cellBomManufacturing = cellBomManufacturingDEConvert.fromCellBaseCapacity(result);
            cellBomManufacturing.setIeorgrade(0);
            cellBomManufacturing.setId(null);
            CellBomManufacturingRepository.setDefault(cellBomManufacturing);//设置默认值
            cellBomManufacturingRepository.save(cellBomManufacturing);
            //添加对应生产日历数据
            cellBomManufacturing = cellBomManufacturingRepository.getOne(cellBomManufacturing.getId());
            List<Calendar> calendars = calendarDEConvert.fromCellBomManufacturingList(cellBomManufacturing);
            //同一生产单元同一日期有多条重复数据。需在写入表前按生产单元、日期归集数据，归集时标准产线数和资源量取最大值
            //查到重复数据
            List<Calendar> deduplications = calendarService.deduplication(cellBomManufacturing, calendars);
            //删除重复数据
            calendarRepository.deleteInBatch(deduplications);
            calendarRepository.saveAll(calendars);

        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> {
            repository.deleteById(id);
            //同步删除bom中原有对应数据
            cellBomManufacturingRepository.deleteByFromId(id);
            //同步删除生产日历中原有对应数据
            calendarRepository.deleteByFromId(id);
        });
        cellBomManufacturingService.bomDataMake(0);
        cellResourceService.makeData(0);
    }


    @Override
    @SneakyThrows
    public void export(CellBaseCapacityQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellBaseCapacityDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara =  query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(dtos, excelPara);
        // 导出调用excelUtils
        String fileName=BapsMessgeHelper.getMessage("export.cellbasecapacity.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);

    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellBaseCapacityExcelDTO> excelDtos=  ExcelUtils.readExcel(multipartFile.getInputStream(),null,CellBaseCapacityExcelDTO.class,excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        setScale(excelDtos,4);
        //验证数据
        checkInput(excelDtos);
        List<CellBaseCapacitySaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        saveDTOS = convert.toCellBaseCapacitySaveDTOCnNameById(saveDTOS);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        cellBomManufacturingRepository.deleteByIeOrGrade(0);
        calendarRepository.deleteByIeOrGrade(0);
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        saveAll(saveDTOS);
        cellBomManufacturingService.bomDataMake(0);
        cellResourceService.makeData(0);

    }
    private void setScale(List<CellBaseCapacityExcelDTO> dtos,int scale) {
         dtos.stream().forEach(dto -> {
             if (dto.getSingleCapacity() != null){
                 dto.setSingleCapacity( dto.getSingleCapacity().setScale(scale, BigDecimal.ROUND_HALF_UP));
             }
         });
    }
    private void saveAll(List<CellBaseCapacitySaveDTO> saveDTOS) {
        //保存Ie产能
        List<CellBaseCapacity> cellBaseCapacities = convert.toEntityFromSaveDTO(saveDTOS);
        repository.saveAll(cellBaseCapacities);
        List<CellBaseCapacityDTO> dtos = convert.toDto(cellBaseCapacities);
        //生成bom
        List<CellBomManufacturing> allBoms = new ArrayList<>();
        dtos.stream().forEach(dto -> {
            //添加对应新bom的数据
            CellBomManufacturing cellBomManufacturing = cellBomManufacturingDEConvert.fromCellBaseCapacity(dto);
            cellBomManufacturing.setIeorgrade(0);
            cellBomManufacturing.setId(null);
            CellBomManufacturingRepository.setDefault(cellBomManufacturing);//设置默认值
            allBoms.add(cellBomManufacturing);

        });
        //存储bom
        cellBomManufacturingRepository.saveAll(allBoms);
        //生成生产日历
        List<Calendar> allCalendars = new ArrayList<>();
        allBoms.stream().forEach(cellBomManufacturing -> {
            //添加对应生产日历数据
            List<Calendar> calendars = calendarDEConvert.fromCellBomManufacturingList(cellBomManufacturing);
            allCalendars.addAll(calendars);
        });
        //生产日历去重
        //对allCalendars分组，生产单元相同，时间相同的为一组
        Map<String, List<Calendar>> allMap = allCalendars.stream().collect(Collectors.groupingBy(
                item -> com.google.common.base.Joiner.on(",").useForNull("null").join(item.getWorkunit(), item.getDate().toString(), item.getLineName())
        ));
        List<Calendar> saveCalendars = new ArrayList<>();
        allMap.forEach((k, v) -> {

            //同一生产单元、同产线同一日期有多条重复数据。需在写入表前按生产单元、日期归集数据，归集时标准产线数和资源量取最大值
            //获取v中Defaultqty的最大值
            BigDecimal maxDefaultqty = v.stream().map(Calendar::getDefaultqty).max(BigDecimal::compareTo).get();
            //获取v中numLines的最大值
            BigDecimal maxNumLines = v.stream().map(Calendar::getNumLines).max(BigDecimal::compareTo).get();
            Calendar calendar = v.get(0);
            calendar.setDefaultqty(maxDefaultqty);
            calendar.setNumLines(maxNumLines);
            CalendarRepository.setDefault(calendar);//设置默认值
            saveCalendars.add(calendar);
        });
        //保存生产日历
        calendarRepository.saveAll(saveCalendars);

    }


    public void checkInput(List<CellBaseCapacityExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors = new ArrayList<>();
        Map<String, LovLineDTO> allYesOrNoMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.YES_OR_NO);
        for (CellBaseCapacityExcelDTO excelDTO : excelDTOS) {
            checkNullField(allYesOrNoMap, excelDTO, i[0]);
            //验证电池类型
            LovLineDTO lovLineDTOCellType = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType());
            if (lovLineDTOCellType == null) {
                String message = BapsMessgeHelper.getMessage("the.row.cellstype.not.exists", new Object[]{i[0],excelDTO.getCellsType()});
                errors.add(message);
            }
            //验证国内海外
            LovLineDTO lovLineDTOOversea = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOversea());
            if (lovLineDTOOversea == null) {
                String message = BapsMessgeHelper.getMessage("the.row.isoversea.not.exists", new Object[]{i[0],excelDTO.getIsOversea()});
                errors.add(message);
            }
            //验证生产基地
            LovLineDTO lovLineDTOBasePlace = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace());
            if (lovLineDTOBasePlace == null) {
              String message = BapsMessgeHelper.getMessage("the.row.baseplace.not.exists", new Object[]{i[0],excelDTO.getBasePlace()});
               errors.add(message);
            }
            //验证基地与国内海外的关系
            if (lovLineDTOOversea != null && lovLineDTOBasePlace != null) {
                if (!(lovLineDTOBasePlace.getAttribute2() != null && lovLineDTOBasePlace.getAttribute2().equals(lovLineDTOOversea.getLovLineId().toString()))) {
                    String message = BapsMessgeHelper.getMessage("the.row.baseplace.not.in.isoversea", new Object[]{i[0],excelDTO.getBasePlace(), excelDTO.getIsOversea()});
                    errors.add(message);
                }
            }
            //验证生产车间
            LovLineDTO lovLineDTOWorkShop = LovUtils.getByLang(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop(), MyThreadLocal.get().getLang());
            if (lovLineDTOWorkShop == null) {
                String message = BapsMessgeHelper.getMessage("the.row.workshop.not.exists", new Object[]{i[0],excelDTO.getWorkshop()});
                errors.add(message);
            }
            //验证车间与基地的关系
            if (lovLineDTOWorkShop != null && lovLineDTOBasePlace != null) {
                if (!(lovLineDTOWorkShop.getAttribute1() != null && lovLineDTOWorkShop.getAttribute1().equals(lovLineDTOBasePlace.getLovLineId().toString()))) {
                    String message = BapsMessgeHelper.getMessage("the.row.workshop.not.in.baseplace", new Object[]{i[0],excelDTO.getWorkshop(), excelDTO.getBasePlace()});
                    errors.add(message);
                }
            }
            //验证生产单元
            LovLineDTO lovLineDTOWorkUnit = LovUtils.getByName(LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit());
            if (lovLineDTOWorkUnit == null) {
                String message = BapsMessgeHelper.getMessage("the.row.workunit.not.exists", new Object[]{i[0],excelDTO.getWorkunit()});
                errors.add(message);
            }
            if (lovLineDTOWorkShop != null && lovLineDTOWorkUnit != null) {
                //验证单元与车间的关系
                if (!(lovLineDTOWorkUnit.getAttribute2() != null && lovLineDTOWorkUnit.getAttribute2().equals(lovLineDTOWorkShop.getLovLineId().toString()))) {
                    String message = BapsMessgeHelper.getMessage("the.row.workunit.not.in.workshop", new Object[]{i[0],excelDTO.getWorkunit(), excelDTO.getWorkshop()});
                    errors.add(message);
                }
            }
            i[0]++;
        }
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }
        checkTimeUnque(excelDTOS);
    }

    private void checkTimeUnque(List<CellBaseCapacityExcelDTO> excelDTOS) {
        Joiner joiner = Joiner.on(",").skipNulls();
        Map<String, List<CellBaseCapacityExcelDTO>> collect = excelDTOS.stream().collect(Collectors.groupingBy(i -> {
            return joiner.join(i.getCellsType(), i.getIsOversea(), i.getBasePlace(), i.getWorkshop(), i.getWorkunit());
        }));
        List<String> errors = new ArrayList<>();
        collect.entrySet().forEach(item -> {
            String key = item.getKey();
            List<CellBaseCapacityExcelDTO> values = item.getValue();
            List<TimeRange> ranges = values.stream().map(v -> {
                return new TimeRange(v.getStartTime(), v.getEndTime());
            }).collect(Collectors.toList());
            Boolean isOverlap = hasOverlap(ranges);
            if (isOverlap) {
                String message = BapsMessgeHelper.getMessage("ie.import.not.unique");
                errors.add(key + message);
            }
        });
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining("\n"));
            throw new BizException(errorString);
        }
    }


    private boolean hasOverlap(List<TimeRange> timeRanges) {
        for (int i = 0; i < timeRanges.size(); i++) {
            TimeRange range1 = timeRanges.get(i);
            for (int j = i + 1; j < timeRanges.size(); j++) {
                TimeRange range2 = timeRanges.get(j);
                if (range1.overlaps(range2)) {
                    return true; // 有重叠部分，返回true
                }
            }
        }
        return false; // 没有重叠部分，返回false
    }

    private void checkNullField(Map<String, LovLineDTO> allYesOrNoMap, CellBaseCapacityExcelDTO excelDTO, int i) {
        if (StringUtils.isBlank(excelDTO.getCellsType()) || StringUtils.isBlank(excelDTO.getIsOversea()) ||
                StringUtils.isBlank(excelDTO.getBasePlace()) || StringUtils.isBlank(excelDTO.getWorkshop()) || StringUtils.isBlank(excelDTO.getWorkunit())
                || StringUtils.isBlank(excelDTO.getIsSingleGlass()) || StringUtils.isBlank(excelDTO.getIsDt()) || StringUtils.isBlank(excelDTO.getIsRegionalCountry()) || StringUtils.isBlank(excelDTO.getIsHChangeFlag()) || StringUtils.isBlank(excelDTO.getIsHTrace())) {
            throw new BizException("ie.import.the.row.is.not.empty",i);
        }

        if (!(StringUtils.equalsAny(excelDTO.getIsSingleGlass(), "Y", "N")
                && StringUtils.equalsAny(excelDTO.getIsDt(), "Y", "N")
                && StringUtils.equalsAny(excelDTO.getIsRegionalCountry(), "Y", "N")
                && StringUtils.equalsAny(excelDTO.getIsHChangeFlag(), "Y", "N")
                && StringUtils.equalsAny(excelDTO.getIsHTrace(), "Y", "N"))
        ) {
            throw new BizException("ie.import.the.row.must.y.or.n");
        }

    }

}
