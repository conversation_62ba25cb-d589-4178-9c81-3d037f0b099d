package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.SiliconSliceSupplyLinesDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface SiliconSliceSupplyLinesService {
    public List<SiliconSliceSupplyLinesDTO> getSupplyByMonth(String month);

    /**
     * 传入月份集合，获取每个月的供应数据
     *
     * @param months        月份集合
     * @param inventoryDate 库存日期
     * @return Map<String, Map < String, Map < LocalDate, Map < String, BigDecimal>>> key1:料号 key2:基地 key3:日期 key4: 加工类型 value:供应量
     */
    Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> getSupplyByMonths(List<String> months, LocalDate inventoryDate);
}
