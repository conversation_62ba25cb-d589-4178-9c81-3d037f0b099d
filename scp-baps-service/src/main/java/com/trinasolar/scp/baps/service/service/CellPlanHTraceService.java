package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellPlanHTraceDTO;
import com.trinasolar.scp.baps.domain.dto.HTraceLineDTO;
import com.trinasolar.scp.baps.domain.dto.HTraceResponse;
import com.trinasolar.scp.baps.domain.dto.HTraceResult;
import com.trinasolar.scp.baps.domain.query.CellPlanHTraceQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: CellPlanHTraceService
 * @date 2024/6/5 10:04
 */
public interface CellPlanHTraceService {

    List<CellPlanHTraceDTO> list(CellPlanHTraceQuery query);

    void export(CellPlanHTraceQuery query, HttpServletResponse response);

    List<HTraceLineDTO> importData(MultipartFile multipartFile);

    HTraceResponse compute(CellPlanHTraceQuery query);

    void confirmData(List<HTraceLineDTO> hTraceLineList);
}
