package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.PowerEfficiencyDTO;
import com.trinasolar.scp.baps.domain.query.PowerEfficiencyQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.base.PageFeign;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@FeignClient(value = FeignConstant.SCP_APS_API, path = "/scp-aps-api")
public interface PowerEfficiencyFeign {

    /**
     * 全年效率值分页列表
     *
     * @param query
     * @return
     */
    @PostMapping("/power-efficiency/page")
    ResponseEntity<Results<List<PowerEfficiencyDTO>>> queryByPage(@RequestBody PowerEfficiencyQuery query);
}
