package com.trinasolar.scp.baps.service.service.impl;

import com.trinasolar.scp.baps.domain.entity.User;
import com.trinasolar.scp.baps.service.repository.UserRepository;
import com.trinasolar.scp.baps.service.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/22
 */
@Service("userService")
public class UserServiceImpl  implements UserService {

    @Autowired
    UserRepository userRepository;
    @Override
    public List<User> list() {
        return userRepository.findAll();
    }
}
