package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellParallelLossDTO;
import com.trinasolar.scp.baps.domain.query.CellParallelLossQuery;
import com.trinasolar.scp.baps.domain.save.CellParallelLossSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 产能并行损失表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
public interface CellParallelLossService {
    /**
     * 分页获取产能并行损失表
     *
     * @param query 查询对象
     * @return 产能并行损失表分页对象
     */
    Page<CellParallelLossDTO> queryByPage(CellParallelLossQuery query);

    /**
     * 根据主键获取产能并行损失表详情
     *
     * @param id 主键
     * @return 产能并行损失表详情
     */
        CellParallelLossDTO queryById(Long id);

    /**
     * 保存或更新产能并行损失表
     *
     * @param saveDTO 产能并行损失表保存对象
     * @return 产能并行损失表对象
     */
    CellParallelLossDTO save(CellParallelLossSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除产能并行损失表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellParallelLossQuery query, HttpServletResponse response);


    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

