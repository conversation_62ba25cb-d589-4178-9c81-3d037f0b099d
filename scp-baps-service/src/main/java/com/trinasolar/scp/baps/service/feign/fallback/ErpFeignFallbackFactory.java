package com.trinasolar.scp.baps.service.feign.fallback;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.baps.domain.dto.erp.ERPResponse;
import com.trinasolar.scp.baps.domain.dto.erp.ESBResponse;
import com.trinasolar.scp.baps.domain.dto.erp.WipWorkOrderDTO;
import com.trinasolar.scp.baps.domain.query.erp.SyncWipIssueQuery;
import com.trinasolar.scp.baps.service.feign.ERPFeign;
import feign.hystrix.FallbackFactory;

import java.util.List;

/**
 * @ClassName: ErpFeignFallbackFactory.java, Created by IntelliJ IDEA
 * @Author: huangzhihui
 * @Date: 2024/01/17
 * @Version :1.0
 **/
public class ErpFeignFallbackFactory implements FallbackFactory<ERPFeign> {
    @Override
    public ERPFeign create(Throwable cause) {
        return new ERPFeign() {
            @Override
            public ESBResponse<ERPResponse> wipAdd(WipWorkOrderDTO wipWorkOrderDTO) {
                return null;
            }


        };
    }
}
