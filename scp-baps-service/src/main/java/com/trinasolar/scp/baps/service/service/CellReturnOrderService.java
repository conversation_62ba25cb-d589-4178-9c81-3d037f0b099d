package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellReturnOrderDTO;
import com.trinasolar.scp.baps.domain.query.CellReturnOrderQuery;
import com.trinasolar.scp.baps.domain.save.CellReturnOrderSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 返司 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
public interface CellReturnOrderService  {
    /**
     * 分页获取返司
     *
     * @param query 查询对象
     * @return 返司分页对象
     */
    Page<CellReturnOrderDTO> queryByPage(CellReturnOrderQuery query);

    /**
     * 根据主键获取返司详情
     *
     * @param id 主键
     * @return 返司详情
     */
        CellReturnOrderDTO queryById(Long id);

    /**
     * 保存或更新返司
     *
     * @param saveDTO 返司保存对象
     * @return 返司对象
     */
    CellReturnOrderDTO save(CellReturnOrderSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除返司
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellReturnOrderQuery query, HttpServletResponse response);

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

