package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.baps.domain.query.CellDailyBalanceQuery;

import java.util.List;

public interface DemandPlanLinesService {
    List<BatteryDemandPlanLinesDTO> getCellDemandPlanByMonth(CellDailyBalanceQuery query);

    List<BatteryDemandPlanLinesDTO> getCellDemandPlanByMonthAndIsOversea(String month, String isOversea);
}
