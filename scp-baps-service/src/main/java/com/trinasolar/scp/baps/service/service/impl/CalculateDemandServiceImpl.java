package com.trinasolar.scp.baps.service.service.impl;

import com.trinasolar.scp.baps.domain.constant.CommonConstant;
import com.trinasolar.scp.baps.domain.dto.ActualInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.CalculateDemandParams;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanFinishDTO;
import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MathUtils;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.baps.service.service.bdm.BatteryDemandService;
import com.trinasolar.scp.common.api.annotation.validator.ImportConvertUtils;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.YesOrNoEnum;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: CalculateDemandServiceImpl
 * @date 2024/6/15 09:42
 */
@Slf4j
@Service("calculateDemandService")
@AllArgsConstructor
public class CalculateDemandServiceImpl implements CalculateDemandService {

    private final BatteryDemandService batteryDemandService;
    private final CellInstockPlanService cellInstockPlanService;
    private final ActualInstockPlanService actualInstockPlanService;
    private final DemandPlanLinesApsService demandPlanLinesApsService;
    private final CellInstockPlanFinishService cellInstockPlanFinishService;
    private final CellInstockPlanVersionService cellInstockPlanVersionService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calculateDemand(CalculateDemandParams params) {
        String month = params.getMonth();
        Long isOverseaId = params.getIsOverseaId();
        String planVersion = params.getPlanVersion();
        LocalDate planLockDate = params.getPlanLockDate();
        LocalDate actualCoverageDate = params.getActualCoverageDate();
        //校验数据
        verify(params);
        //批次号
        String batchNo = params.batchNo();
        //ERP入库数据
        List<CellInstockPlanFinishDTO> erpData = findCellInstockPlanFinish(isOverseaId, actualCoverageDate);
        //入库计划数据
        List<CellInstockPlanDTO> planData = findCellInstockPlans(isOverseaId, planVersion, actualCoverageDate, planLockDate);
        //转换为入库数据并保存批次
        List<ActualInstockPlanDTO> supplyData = integrateData(batchNo, planVersion, erpData, planData);
        //需求数据
        List<BatteryDemandPlanLinesDTO> demandPlan = findDemandPlan(isOverseaId, month);
        //冲销需求
        deductDemand(planLockDate, demandPlan, supplyData);
        //将剩余有需求数量的存入表中
        saveDemandPlan(batchNo, isOverseaId, actualCoverageDate, planLockDate, demandPlan);
    }

    /**
     * 保存需求计划
     *
     * @param batchNo
     * @param isOverseaId
     * @param actualCoverageDate
     * @param planLockDate
     * @param demandPlan
     */
    private void saveDemandPlan(String batchNo, Long isOverseaId, LocalDate actualCoverageDate, LocalDate planLockDate, List<BatteryDemandPlanLinesDTO> demandPlan) {
        List<BatteryDemandPlanLinesDTO> saveList = demandPlan.stream().filter(k -> !MathUtils.checkIsZero(k.getDemandQty())).collect(Collectors.toList());
        demandPlanLinesApsService.saveDemandPlan(batchNo, isOverseaId, actualCoverageDate, planLockDate, saveList);
    }

    /**
     * 冲销需求
     *
     * @param planLockDate
     * @param demandPlan
     * @param supplyData
     */
    private void deductDemand(LocalDate planLockDate, List<BatteryDemandPlanLinesDTO> demandPlan, List<ActualInstockPlanDTO> supplyData) {
        //低碳、单玻 & 主栅间距  冲减
        deductSpecialDemand(demandPlan, supplyData);
        //透明双玻、美学、小区域、H追溯冲减
        deductLockDemand(planLockDate, demandPlan, supplyData);
    }

    /**
     * 透明双玻、美学、小区域、H追溯冲减
     *
     * @param planLockDate
     * @param demandData
     * @param supplyData
     */
    private void deductLockDemand(LocalDate planLockDate, List<BatteryDemandPlanLinesDTO> demandData, List<ActualInstockPlanDTO> supplyData) {
        //过滤dt和单玻的需求
        Long dtId = LovUtils.get(LovHeaderCodeConstant.CELL_SOURCE, CommonConstant.DT).getLovLineId();
        Long glassId = LovUtils.get(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS, CommonConstant.GLASS).getLovLineId();
        //排序
        List<LocalDate> dateList = demandData.stream().map(BatteryDemandPlanLinesDTO::getDemandDate).distinct().sorted().collect(Collectors.toList());
        //需求分组
        Map<LocalDate, List<BatteryDemandPlanLinesDTO>> demandMap = demandData.stream().filter(k -> !Objects.equals(dtId, k.getPcsSourceTypeId()) && !Objects.equals(glassId, k.getTransparentDoubleGlassId())).collect(Collectors.groupingBy(BatteryDemandPlanLinesDTO::getDemandDate));
        //供应排出单波dt
        List<ActualInstockPlanDTO> supplyList = supplyData.stream().filter(k -> !Objects.equals(dtId, k.getCellSourceId()) && !Objects.equals(glassId, k.getTransparentDoubleGlassId())).collect(Collectors.toList());
        //按锁定日期前进行精准分配，不兼容
        List<LocalDate> beforeList = dateList.stream().filter(date -> !date.isAfter(planLockDate.plusDays(5))).collect(Collectors.toList());
        allocatedQuantity(beforeList, BatteryDemandPlanLinesDTO::compatibleSign, demandMap, ActualInstockPlanDTO::compatibleSign, supplyList);
        //兼容分配，美学+双玻
        allocatedQuantity(dateList, BatteryDemandPlanLinesDTO::compatibleAestheticsAndDoubleGlass, demandMap, ActualInstockPlanDTO::compatibleAestheticsAndDoubleGlass, supplyList);
        //兼容分配，双玻
        allocatedQuantity(dateList, BatteryDemandPlanLinesDTO::compatibleDoubleGlass, demandMap, ActualInstockPlanDTO::compatibleDoubleGlass, supplyList);
        //兼容分配，美学
        allocatedQuantity(dateList, BatteryDemandPlanLinesDTO::compatibleAesthetics, demandMap, ActualInstockPlanDTO::compatibleAesthetics, supplyList);
        //兼容分配，小区域
        allocatedQuantity(dateList, BatteryDemandPlanLinesDTO::compatibleRegionalCountry, demandMap, ActualInstockPlanDTO::compatibleRegionalCountry, supplyList);
        //兼容分配，H追溯
        allocatedQuantity(dateList, BatteryDemandPlanLinesDTO::compatibleHTrace, demandMap, ActualInstockPlanDTO::compatibleHTrace, supplyList);
    }


    /**
     * 低碳 & 单玻 & 主栅间距 冲减
     *
     * @param demandList
     * @param supplyData
     */
    private void deductSpecialDemand(List<BatteryDemandPlanLinesDTO> demandList, List<ActualInstockPlanDTO> supplyData) {
        //过滤dt和单玻的需求
        Long dtId = LovUtils.get(LovHeaderCodeConstant.CELL_SOURCE, CommonConstant.DT).getLovLineId();
        Long glassId = LovUtils.get(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS, CommonConstant.GLASS).getLovLineId();
        Map<LocalDate, List<BatteryDemandPlanLinesDTO>> demandMap = demandList.stream().filter(k -> Objects.equals(dtId, k.getPcsSourceTypeId()) || Objects.equals(glassId, k.getTransparentDoubleGlassId())).collect(Collectors.groupingBy(BatteryDemandPlanLinesDTO::getDemandDate));
        //分配
        allocatedQuantity(IterableUtils.toList(demandMap.keySet()), BatteryDemandPlanLinesDTO::compatibleDtAndGlass, demandMap, ActualInstockPlanDTO::compatibleDtAndGlassAndMain, supplyData);
    }


    /**
     * 分配数量
     *
     * @param dateList
     * @param demandSign
     * @param demandMap
     * @param supplyDataList
     */
    private void allocatedQuantity(List<LocalDate> dateList, Function<BatteryDemandPlanLinesDTO, String> demandSign, Map<LocalDate, List<BatteryDemandPlanLinesDTO>> demandMap, Function<ActualInstockPlanDTO, String> supplySign, List<ActualInstockPlanDTO> supplyDataList) {
        //按传入的关键字分组
        Map<String, List<ActualInstockPlanDTO>> compatibleSupplyMap = supplyDataList.stream().collect(Collectors.groupingBy(supplySign));
        //排序循环分配
        dateList.sort(Comparator.naturalOrder());
        dateList.forEach(date -> {
            List<BatteryDemandPlanLinesDTO> demandPlanLines = demandMap.get(date);
            if(CollectionUtils.isEmpty(demandPlanLines)){
                //防止空指针
                demandPlanLines = Lists.newArrayList();
            }
            demandPlanLines.stream().filter(demand -> !MathUtils.checkIsZero(demand.getDemandQty())).forEach(item -> {
            //需求量
            BigDecimal demandQty = item.getDemandQty();
            //基地
            Long basePlace = item.getBasePlaceId()==null?0L:item.getBasePlaceId();

            String sign = demandSign.apply(item);
            if (compatibleSupplyMap.containsKey(sign)) {
                List<ActualInstockPlanDTO> supplyList = compatibleSupplyMap.get(sign);
                //数量不为0 && 按时间排序 && 当前需求相同基地优先
                List<ActualInstockPlanDTO> availableSupplyList = supplyList.stream()
                        .filter(supply -> !MathUtils.checkIsZero(supply.getQtyPc()))
                        .sorted(Comparator.comparing(ActualInstockPlanDTO::localDate)
                                .thenComparing(ActualInstockPlanDTO::getBasePlaceId, Comparator.comparingInt(k -> basePlace.equals(k) ? Integer.MIN_VALUE : Integer.MAX_VALUE))
                                .thenComparing(ActualInstockPlanDTO::getAesthetics, Comparator.comparingInt(k -> !StringUtils.equals(CommonConstant.NON, k) ? Integer.MIN_VALUE : Integer.MAX_VALUE))
                                .thenComparing(ActualInstockPlanDTO::getTransparentDoubleGlass, Comparator.comparingInt(k -> !StringUtils.equals(CommonConstant.NON, k) ? Integer.MIN_VALUE : Integer.MAX_VALUE))
                                .thenComparing(ActualInstockPlanDTO::getRegionalCountry, Comparator.comparingInt(k -> StringUtils.isNotBlank(k) ? Integer.MIN_VALUE : Integer.MAX_VALUE))
                                .thenComparing(ActualInstockPlanDTO::getHTrace, Comparator.comparingInt(k -> !StringUtils.equals(CommonConstant.NON, k) ? Integer.MIN_VALUE : Integer.MAX_VALUE))
                        )
                        .collect(Collectors.toList());
                for (ActualInstockPlanDTO supply : availableSupplyList) {
                    BigDecimal qtyPc = supply.getQtyPc();
                    if (!MathUtils.checkIsZero(qtyPc, demandQty)) {
                        if (qtyPc.compareTo(demandQty) >= 0) {
                            supply.setQtyPc(qtyPc.subtract(demandQty));
                            demandQty = BigDecimal.ZERO;
                            item.setDemandQty(demandQty);
                        } else {
                            demandQty = demandQty.subtract(qtyPc);
                            item.setDemandQty(demandQty);
                            supply.setQtyPc(BigDecimal.ZERO);
                        }
                        item.setMainGridSpace(supply.getMainGridSpace());
                    }
                }
            }
        });

        });
    }


    /**
     * erp入库数据
     *
     * @param isOverseaId
     * @param actualCoverageDate
     * @return
     */
    private List<CellInstockPlanFinishDTO> findCellInstockPlanFinish(Long isOverseaId, LocalDate actualCoverageDate) {
        List<CellInstockPlanFinishDTO> dataList = cellInstockPlanFinishService.findByCondition(isOverseaId, actualCoverageDate);
        dataList.forEach(data -> {
            if (YesOrNoEnum.YES.getCode().equals(data.getIsRegionalCountry())) {
                data.setRegionalCountry(data.getIsRegionalCountry());
            }
        });
        return dataList;
    }

    /**
     * 入库计划数据
     *
     * @param isOverseaId
     * @param planVersion
     * @param startDate
     * @param endDate
     * @return
     */
    private List<CellInstockPlanDTO> findCellInstockPlans(Long isOverseaId, String planVersion, LocalDate startDate, LocalDate endDate) {
        List<CellInstockPlanDTO> dataList = cellInstockPlanService.findByCondtition(isOverseaId, planVersion, startDate, endDate);
        return dataList;
    }

    private List<BatteryDemandPlanLinesDTO> findDemandPlan(Long isOverseaId, String month) {
        List<BatteryDemandPlanLinesDTO> demandPlan = batteryDemandService.findDemandPlan(isOverseaId, month).stream().filter(k -> StringUtils.isNotBlank(k.getBatteryName())).collect(Collectors.toList());
        //转译id
        ImportConvertUtils.convertField(demandPlan);
        return demandPlan;
    }

    private List<ActualInstockPlanDTO> integrateData(String batchNo, String planVersion, List<CellInstockPlanFinishDTO> erpData, List<CellInstockPlanDTO> planData) {
        List<ActualInstockPlanDTO> dataList = actualInstockPlanService.integrateData(batchNo, planVersion, erpData, planData);
        //过滤掉A-电池片
        Long lovLineId = LovUtils.get(LovHeaderCodeConstant.PRODUCTION_GRADE, CommonConstant.A_SUB).getLovLineId();
        return dataList.stream().filter(k -> !Objects.equals(lovLineId, k.getProductionGradeId())).collect(Collectors.toList());
    }

    /**
     * 校验数据
     *
     * @param params
     */
    private void verify(CalculateDemandParams params) {
        String month = params.getMonth();
        Long isOverseaId = params.getIsOverseaId();
        String planVersion = params.getPlanVersion();
        String convertMonth = DateUtil.getMonth(params.getActualCoverageDate());
        String lockMonth = DateUtil.getMonth(params.getPlanLockDate());
        //三个月份必须一致
        if (!StringUtils.equals(month, convertMonth) || !StringUtils.equals(month, lockMonth)) {
            throw new BizException("baps_month_consistency_verify");
        }
        //校验版本号
        boolean exist = cellInstockPlanVersionService.checkVersion(isOverseaId, month, planVersion);
        if (!exist) {
            throw new BizException("baps_plan_version_error");
        }
    }
}
