package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellFineMid;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 电池良率中间表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Repository
public interface CellFineMidRepository extends JpaRepository<CellFineMid, Long>, QuerydslPredicateExecutor<CellFineMid> {
    @Modifying
    @Query(value = "delete from CellFineMid  cellFineMid  where cellFineMid.year = :year")
    void  deleteByYear(@Param("year") Integer year);
}
