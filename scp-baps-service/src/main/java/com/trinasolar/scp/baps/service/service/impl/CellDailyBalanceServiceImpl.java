package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellDailyBalanceDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellDailyBalanceDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.OverseaPurchasePlanDTO;
import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.baps.domain.entity.CellDailyBalance;
import com.trinasolar.scp.baps.domain.entity.QCellDailyBalance;
import com.trinasolar.scp.baps.domain.entity.QCellInstockPlan;
import com.trinasolar.scp.baps.domain.excel.CellDailyBalanceExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellDailyBalanceQuery;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.save.CellDailyBalanceSaveDTO;
import com.trinasolar.scp.baps.domain.utils.*;
import com.trinasolar.scp.baps.service.repository.CellDailyBalanceRepository;
import com.trinasolar.scp.baps.service.service.CellDailyBalanceService;
import com.trinasolar.scp.baps.service.service.CellInstockPlanService;
import com.trinasolar.scp.baps.service.service.DemandPlanLinesService;
import com.trinasolar.scp.baps.service.service.OverseaPurchasePlanService;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 每日结存报表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-12 06:19:24
 */
@Slf4j
@Service("cellDailyBalanceService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class CellDailyBalanceServiceImpl implements CellDailyBalanceService {
    private static final QCellDailyBalance qCellDailyBalance = QCellDailyBalance.cellDailyBalance;
    private final CellDailyBalanceDEConvert convert;
    private final CellDailyBalanceRepository repository;
    private final JPAQueryFactory jpaQueryFactory;
    private final CellInstockPlanService cellInstockPlanService;
    private final OverseaPurchasePlanService overseaPurchasePlanService;
    private final DemandPlanLinesService demandPlanLinesService;
    private String oldLang=LovHeaderCodeConstant.LANGUAGE_CN;

    private static final Joiner groupKeyJoiner = Joiner.on(",").useForNull("");

    @Autowired
    @Lazy
    private CellDailyBalanceService cellDailyBalanceService;
    @Override
    public Page<CellDailyBalanceDTO> queryByPage(CellDailyBalanceQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        oldLang=MyThreadLocal.get().getLang();
        query=convert.toCellDailyBalanceQuery(query, oldLang);
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        List<CellDailyBalanceDTO> datas = new ArrayList<>();
        if (StringUtils.isNotEmpty(query.getIsOversea())) {//当传入国内海外字段时
            datas.addAll(query(query));
        } else {//没选国内海外字段时，查出国内和海外的数据
            query.setIsOversea(OverseaConstant.INLAND);
            datas.addAll(query(query));
            query.setIsOversea(OverseaConstant.OVERSEA);
            datas.addAll(query(query));
        }
        translate(datas);
        return new PageImpl(datas.subList((query.getPageNumber() - 1) * query.getPageSize(), Math.min(query.getPageNumber() * query.getPageSize(), datas.size())), pageable, datas.size());
    }

    /**
     * 翻译
     * @param datas
     */
    private void translate( List<CellDailyBalanceDTO> datas){
        MyThreadLocal.get().setLang(oldLang);
        datas.stream().forEach(item->{
            String isoversea = item.getIsOversea();
            if (StringUtils.isNotEmpty(isoversea)){
                isoversea= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,isoversea);
                item.setIsOversea(isoversea);
            }
            String cellTypeName = item.getCellType();
            if (StringUtils.isNotEmpty(cellTypeName)){
                cellTypeName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,cellTypeName);
                item.setCellType(cellTypeName);
            }

            String hTrace = item.getHTrace();
            if (StringUtils.isNotEmpty(hTrace)){
                hTrace= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,hTrace);
                item.setHTrace(hTrace);
            }
            String dt = item.getDt();
            if (StringUtils.isNotEmpty(dt)){
                dt= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.CELL_SOURCE,dt);
                item.setDt(dt);
            }
            String transparentDoubleGlass = item.getTransparentDoubleGlass();
            if (StringUtils.isNotEmpty(transparentDoubleGlass)){
                transparentDoubleGlass= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,transparentDoubleGlass);
                item.setTransparentDoubleGlass(transparentDoubleGlass);
            }
            String aesthetics = item.getAesthetics();
            if (StringUtils.isNotEmpty(aesthetics)){
                aesthetics= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,aesthetics);
                item.setAesthetics(aesthetics);
            }

        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CellDailyBalanceDTO> query(CellDailyBalanceQuery query) {
        //一、基本数据准备
        //1.1、计算电池需求计划数据
        List<CellDailyBalanceDTO> demandData= calcCellDemandPlan(query);
        //1.2、计算电池自制计划数据
        List<CellDailyBalanceDTO> selfData= calcSelfMakePlans(query);
        //1.3、计算电池外购计划数据
        List<CellDailyBalanceDTO> outData=  calcOverseaPurchasePlan(query);
        //二、数据预处理->构建map方便查询与计算
        //2.1对自制数据构建一个map方便计算
        LinkedHashMap<String, CellDailyBalanceDTO> selfDataMap = selfData.stream().collect(Collectors.toMap(
                entity -> {
                    return groupKeyJoiner.join(entity.getIsOversea(), entity.getCellType(),
                            entity.getHTrace(),
                            entity.getDt(),
                            entity.getAesthetics(),
                            entity.getTransparentDoubleGlass()

                    );
                },
                Function.identity(),
                (existing, replacement) -> existing,
                LinkedHashMap::new
        ));
        //2.2对外购数据构建一个map方便查询使用
        LinkedHashMap<String, CellDailyBalanceDTO> outDataMap = outData.stream().collect(Collectors.toMap(
                entity -> {
                    return groupKeyJoiner.join(entity.getIsOversea(), entity.getCellType(),
                            entity.getHTrace(),
                            entity.getDt(),
                            entity.getAesthetics(),
                            entity.getTransparentDoubleGlass());
                },
                Function.identity(),
                (existing, replacement) -> existing,
                LinkedHashMap::new
        ));
        //2.3 获取下个月第一天需求map
        Map<String, BigDecimal> nextMonthDemandData = nextMonthDemand(query);
        //三、数据计算需求、自制、外购合并并计算到天结存、周转天数
        List<CellDailyBalanceDTO> finalDatas = calcData(query, demandData, selfDataMap, outDataMap, nextMonthDemandData);
        return finalDatas;
    }

    /**
     * 数据计算需求、自制、外购合并并计算到天结存、周转天数
     * @param query
     * @param demandData
     * @param selfDataMap
     * @param outDataMap
     * @param nextMonthDemandData
     * @return
     */
    private List<CellDailyBalanceDTO> calcData(CellDailyBalanceQuery query, List<CellDailyBalanceDTO> demandData, LinkedHashMap<String, CellDailyBalanceDTO> selfDataMap, LinkedHashMap<String, CellDailyBalanceDTO> outDataMap, Map<String, BigDecimal> nextMonthDemandData) {
        //用于存储最终数据集合
        List<CellDailyBalanceDTO> finalDatas = Lists.newArrayList();
        //1、依据需求组装
        demandData.stream().forEach(entity -> {
            //电池需求
            finalDatas.add(entity);
            String selfKey = groupKeyJoiner.join(entity.getIsOversea(), entity.getCellType(),
                    entity.getHTrace(),
                    entity.getDt(),
                    entity.getAesthetics(),
                    entity.getTransparentDoubleGlass());
            //电池自制
            CellDailyBalanceDTO selfValue = selfDataMap.get(selfKey);
            if (selfValue == null) {
                //考虑没有自制的情况
                selfValue = convert.toDeepCopyDto(entity);
                selfValue.setProject(CellDailyBalanceConstant.MADE_BY_ONESELF);
                setZero(selfValue);

            } else {
                selfDataMap.remove(selfKey);
            }
            finalDatas.add(selfValue);
            //电池外购
            String outKey = groupKeyJoiner.join(entity.getIsOversea(), entity.getCellType(),
                    entity.getHTrace(),
                    entity.getDt(),
                    entity.getAesthetics(),
                    entity.getTransparentDoubleGlass());
            CellDailyBalanceDTO outValue = outDataMap.get(outKey);
            if (outValue == null) {
                //考虑没有外购的情况
                outValue = convert.toDeepCopyDto(entity);
                outValue.setProject(CellDailyBalanceConstant.OUTSOURCING);
                setZero(outValue);
            } else {
                //电池如果具有各种属性的不能外购（特殊情况）
                if (
                        (StringUtils.isNotEmpty(entity.getHTrace()) && !Objects.equals(entity.getHTrace(), "无"))
                                ||
                                (StringUtils.isNotEmpty(entity.getDt()) && !Objects.equals(entity.getDt(), "无"))
                                ||
                                (StringUtils.isNotEmpty(entity.getAesthetics()) && !Objects.equals(entity.getAesthetics(), "无"))
                                ||
                                (StringUtils.isNotEmpty(entity.getTransparentDoubleGlass()) && !Objects.equals(entity.getTransparentDoubleGlass(), "无"))
                ) {
                    outValue = convert.toDeepCopyDto(entity);
                    outValue.setProject(CellDailyBalanceConstant.OUTSOURCING);
                    setZero(outValue);
                } else {
                    outDataMap.remove(outKey);
                }
            }
            finalDatas.add(outValue);
            //到天结存-》到天结存=前一日结存+今日电池自产+今日电池外购-今日电池需求
            CellDailyBalanceDTO goodsOnHandValue = convert.toDeepCopyDto(entity);
            goodsOnHandValue.setProject(CellDailyBalanceConstant.GOODS_ON_HAND);
            setZero(goodsOnHandValue);
            //周转天数-》今日到天库存/次日需求
            CellDailyBalanceDTO turnoverDaysValue = convert.toDeepCopyDto(entity);
            turnoverDaysValue.setProject(CellDailyBalanceConstant.TURNOVER_DAYS);
            setZero(turnoverDaysValue);
            //计算每天库存
            BigDecimal yesterday = BigDecimal.ZERO;
            Integer totalDays = DateUtil.getDaysForMonth(query.getMonth());
            for (int day = 1; day <= totalDays; day++) {
                //昨日结存
                BigDecimal balance = yesterday;
                //今日自产
                BigDecimal self = ReflectUtil.invoke(selfValue, "getD" + day);
                //今日外购
                BigDecimal out = ReflectUtil.invoke(outValue, "getD" + day);
                //今日需求
                BigDecimal demand = ReflectUtil.invoke(entity, "getD" + day);
                //今日结存
                balance = balance.add(self).add(out).subtract(demand);
                ReflectUtil.invoke(goodsOnHandValue, "setD" + day, balance);
                yesterday = balance;
                //周转天数-》今日到天库存/次日需求
                BigDecimal turnoverValue = BigDecimal.ZERO;
                if (day < totalDays) {
                    //次日需求
                    BigDecimal nextDemand = ReflectUtil.invoke(entity, "getD" + (day + 1));
                    if (nextDemand.equals(BigDecimal.ZERO)) {
                        turnoverValue = BigDecimal.ZERO;
                    } else {
                        turnoverValue = balance.divide(nextDemand, 4, RoundingMode.HALF_UP);
                    }

                } else {
                    //下个月第一天需求
                    BigDecimal nextDemand = nextMonthDemandData.get(selfKey);
                    if (nextDemand.equals(BigDecimal.ZERO)) {
                        turnoverValue = BigDecimal.ZERO;
                    } else {
                        turnoverValue = balance.divide(nextDemand, 4, RoundingMode.HALF_UP);
                    }
                }
                ReflectUtil.invoke(turnoverDaysValue, "setD" + day, turnoverValue);

            }
            finalDatas.add(goodsOnHandValue);
            finalDatas.add(turnoverDaysValue);


        });
        //2、特殊情况考虑（有自产无对应需求数据情况）
        selfDataMap.keySet().stream().forEach(key -> {
            CellDailyBalanceDTO selfValue = selfDataMap.get(key);
            CellDailyBalanceDTO demandValue = convert.toDeepCopyDto(selfValue);
            demandValue.setProject(CellDailyBalanceConstant.CELL_DEMAND);
            setZero(demandValue);
            finalDatas.add(demandValue);
            finalDatas.add(selfValue);
            //电池外购
            String outKey = groupKeyJoiner.join(selfValue.getIsOversea() + selfValue.getCellType(),
                    selfValue.getHTrace(),
                    selfValue.getDt(),
                    selfValue.getAesthetics(),
                    selfValue.getTransparentDoubleGlass()) ;
            CellDailyBalanceDTO outValue = outDataMap.get(outKey);
            if (outValue == null) {
                outValue = convert.toDeepCopyDto(selfValue);
                outValue.setProject(CellDailyBalanceConstant.OUTSOURCING);
                setZero(outValue);
            } else {
                //电池如果具有各种属性的不能外购
                if (
                        (StringUtils.isNotEmpty(selfValue.getHTrace()) && !Objects.equals(selfValue.getHTrace(), "无"))
                                ||
                                (StringUtils.isNotEmpty(selfValue.getDt()) && !Objects.equals(selfValue.getDt(), "无"))
                                ||
                                (StringUtils.isNotEmpty(selfValue.getAesthetics()) && !Objects.equals(selfValue.getAesthetics(), "无"))
                                ||
                                (StringUtils.isNotEmpty(selfValue.getTransparentDoubleGlass()) && !Objects.equals(selfValue.getTransparentDoubleGlass(), "无"))
                ) {
                    outValue = convert.toDeepCopyDto(selfValue);
                    outValue.setProject(CellDailyBalanceConstant.OUTSOURCING);
                    setZero(outValue);
                } else {
                    outDataMap.remove(outKey);
                }
            }
            finalDatas.add(outValue);
            //到天结存-》到天结存=前一日结存+今日电池自产+今日电池外购-今日电池需求
            CellDailyBalanceDTO goodsOnHandValue = convert.toDeepCopyDto(selfValue);
            goodsOnHandValue.setProject(CellDailyBalanceConstant.GOODS_ON_HAND);
            setZero(goodsOnHandValue);
            //周转天数-》今日到天库存/次日需求
            CellDailyBalanceDTO turnoverDaysValue = convert.toDeepCopyDto(selfValue);
            turnoverDaysValue.setProject(CellDailyBalanceConstant.TURNOVER_DAYS);
            setZero(turnoverDaysValue);
            //计算每天库存
            BigDecimal yesterday = BigDecimal.ZERO;
            Integer totalDays = DateUtil.getDaysForMonth(query.getMonth());
            for (int day = 1; day <= totalDays; day++) {
                //昨日结存
                BigDecimal balance = yesterday;
                //今日自产
                BigDecimal self = ReflectUtil.invoke(selfValue, "getD" + day);
                //今日外购
                BigDecimal out = ReflectUtil.invoke(outValue, "getD" + day);
                //今日需求
                BigDecimal demand = ReflectUtil.invoke(demandValue, "getD" + day);
                //今日结存
                balance = balance.add(self).add(out).subtract(demand);
                ReflectUtil.invoke(goodsOnHandValue, "setD" + day, balance);
                yesterday = balance;
                //周转天数-》今日到天库存/次日需求
                BigDecimal turnoverValue = BigDecimal.ZERO;
                if (day < totalDays) {
                    //次日需求
                    BigDecimal nextDemand = ReflectUtil.invoke(demandValue, "getD" + (day + 1));
                    if (nextDemand.equals(BigDecimal.ZERO)) {
                        turnoverValue = BigDecimal.ZERO;
                    } else {
                        turnoverValue = balance.divide(nextDemand, 4, RoundingMode.HALF_UP);
                    }

                } else {
                    //下个月第一天需求
                    BigDecimal nextDemand = nextMonthDemandData.get(key);
                    if (nextDemand == null) nextDemand = BigDecimal.ZERO;
                    if (nextDemand.equals(BigDecimal.ZERO)) {
                        turnoverValue = BigDecimal.ZERO;
                    } else {
                        turnoverValue = balance.divide(nextDemand, 4, RoundingMode.HALF_UP);
                    }
                }
                ReflectUtil.invoke(turnoverDaysValue, "setD" + day, turnoverValue);

            }
            finalDatas.add(goodsOnHandValue);
            finalDatas.add(turnoverDaysValue);


        });
        //3、特殊情况考虑（有外购无自产和需求情况）
        outDataMap.keySet().stream().forEach(key -> {
            CellDailyBalanceDTO outValue = outDataMap.get(key);
            CellDailyBalanceDTO demandValue = convert.toDeepCopyDto(outValue);
            demandValue.setProject(CellDailyBalanceConstant.CELL_DEMAND);
            setZero(demandValue);
            finalDatas.add(demandValue);
            CellDailyBalanceDTO selfValue = convert.toDeepCopyDto(outValue);
            selfValue.setProject(CellDailyBalanceConstant.MADE_BY_ONESELF);
            setZero(selfValue);
            finalDatas.add(selfValue);
            finalDatas.add(outValue);
            //到天结存-》到天结存=前一日结存+今日电池自产+今日电池外购-今日电池需求
            CellDailyBalanceDTO goodsOnHandValue = convert.toDeepCopyDto(selfValue);
            goodsOnHandValue.setProject(CellDailyBalanceConstant.GOODS_ON_HAND);
            setZero(goodsOnHandValue);
            //周转天数-》今日到天库存/次日需求
            CellDailyBalanceDTO turnoverDaysValue = convert.toDeepCopyDto(selfValue);
            turnoverDaysValue.setProject(CellDailyBalanceConstant.TURNOVER_DAYS);
            setZero(turnoverDaysValue);
            //计算每天库存
            BigDecimal yesterday = BigDecimal.ZERO;
            Integer totalDays = DateUtil.getDaysForMonth(query.getMonth());
            for (int day = 1; day <= totalDays; day++) {
                //昨日结存
                BigDecimal balance = yesterday;
                //今日自产
                BigDecimal self = ReflectUtil.invoke(selfValue, "getD" + day);
                //今日外购
                BigDecimal out = ReflectUtil.invoke(outValue, "getD" + day);
                //今日需求
                BigDecimal demand = ReflectUtil.invoke(demandValue, "getD" + day);
                //今日结存
                balance = balance.add(self).add(out).subtract(demand);
                ReflectUtil.invoke(goodsOnHandValue, "setD" + day, balance);
                yesterday = balance;
                //周转天数-》今日到天库存/次日需求
                BigDecimal turnoverValue = BigDecimal.ZERO;
                if (day < totalDays) {
                    //次日需求
                    BigDecimal nextDemand = ReflectUtil.invoke(demandValue, "getD" + (day + 1));
                    if (nextDemand.equals(BigDecimal.ZERO)) {
                        turnoverValue = BigDecimal.ZERO;
                    } else {
                        turnoverValue = balance.divide(nextDemand, 4, RoundingMode.HALF_UP);
                    }

                } else {
                    //下个月第一天需求
                    BigDecimal nextDemand = nextMonthDemandData.get(key);
                    if (nextDemand == null) {
                        nextDemand = BigDecimal.ZERO;
                    }
                    if (nextDemand.equals(BigDecimal.ZERO)) {
                        turnoverValue = BigDecimal.ZERO;
                    } else {
                        turnoverValue = balance.divide(nextDemand, 4, RoundingMode.HALF_UP);
                    }
                }
                ReflectUtil.invoke(turnoverDaysValue, "setD" + day, turnoverValue);

            }
            finalDatas.add(goodsOnHandValue);
            finalDatas.add(turnoverDaysValue);
        });
        return finalDatas;
    }

    /**
     * 获取下个月第一天需求
     *
     * @param query
     * @return
     */
    private Map<String, BigDecimal> nextMonthDemand(CellDailyBalanceQuery query) {
        Map<String, BigDecimal> map = new HashMap<>();
        List<BatteryDemandPlanLinesDTO> dtos = demandPlanLinesService.getCellDemandPlanByMonth(query);
        Map<String, List<BatteryDemandPlanLinesDTO>> collect = dtos.stream().collect(Collectors.groupingBy(item -> {
            return groupKeyJoiner.join(item.getDomesticOverseaName(),
                    item.getBatteryName(),
                    item.getHTraceName(),
                    item.getPcsSourceTypeName(),
                    item.getAestheticsName(),
                    item.getTransparentDoubleGlassName()
                   );
        }));
        collect.entrySet().forEach(item -> {
            String key = item.getKey();
            AtomicReference<BigDecimal> qty = new AtomicReference<>(BigDecimal.ZERO);
            item.getValue().stream().forEach(demandPlanLinesDTO -> {
                LocalDate localDate = demandPlanLinesDTO.getDemandDate();
                Integer day = localDate.getDayOfMonth();
                if (day.equals(1)) {
                    if (demandPlanLinesDTO.getDemandQty() != null) {
                        qty.set(qty.get().add(demandPlanLinesDTO.getDemandQty()));
                    }
                }
            });
            map.put(key, qty.get());
        });

        return map;
    }

    private void setZero(CellDailyBalanceDTO balance) {
        balance.setD1(BigDecimal.ZERO);
        balance.setD2(BigDecimal.ZERO);
        balance.setD3(BigDecimal.ZERO);
        balance.setD4(BigDecimal.ZERO);
        balance.setD5(BigDecimal.ZERO);
        balance.setD6(BigDecimal.ZERO);
        balance.setD7(BigDecimal.ZERO);
        balance.setD8(BigDecimal.ZERO);
        balance.setD9(BigDecimal.ZERO);
        balance.setD10(BigDecimal.ZERO);
        balance.setD11(BigDecimal.ZERO);
        balance.setD12(BigDecimal.ZERO);
        balance.setD13(BigDecimal.ZERO);
        balance.setD14(BigDecimal.ZERO);
        balance.setD15(BigDecimal.ZERO);
        balance.setD16(BigDecimal.ZERO);
        balance.setD17(BigDecimal.ZERO);
        balance.setD18(BigDecimal.ZERO);
        balance.setD19(BigDecimal.ZERO);
        balance.setD20(BigDecimal.ZERO);
        balance.setD21(BigDecimal.ZERO);
        balance.setD22(BigDecimal.ZERO);
        balance.setD23(BigDecimal.ZERO);
        balance.setD24(BigDecimal.ZERO);
        balance.setD25(BigDecimal.ZERO);
        balance.setD26(BigDecimal.ZERO);
        balance.setD27(BigDecimal.ZERO);
        balance.setD28(BigDecimal.ZERO);
        balance.setD29(BigDecimal.ZERO);
        balance.setD30(BigDecimal.ZERO);
        balance.setD31(BigDecimal.ZERO);
    }


    /**
     * 计算自制数据：来源入库计划)
     *
     * @return
     */
    @Override

    public  List<CellDailyBalanceDTO> calcSelfMakePlans(CellDailyBalanceQuery query) {
        //获取入库计划表的finalVersion(已经确认的)
        String version = cellDailyBalanceService.getMaxVersionByMonthAndIsOversea(query.getMonth(), query.getIsOversea());
        //获取每日库存表-自制数据的from_version

        //对没有进行汇总的进行汇总计算
        List<CellDailyBalanceDTO> dtos = Lists.newArrayList();
        if (StringUtils.isNotEmpty(version) ) {
            CellInstockPlanQuery cellInstockPlanQuery = new CellInstockPlanQuery();
            cellInstockPlanQuery.setMonth(query.getMonth());
            cellInstockPlanQuery.setFinalVersion(version);
            cellInstockPlanQuery.setIsOversea(query.getIsOversea());
            cellInstockPlanQuery.setCellsType(query.getCellsType());

            List<CellInstockPlanDTO> datas = cellInstockPlanService.queryByFinalVersion(cellInstockPlanQuery);
            Map<String, List<CellInstockPlanDTO>> collect = datas.stream().collect(Collectors.groupingBy(item -> {
                return groupKeyJoiner.join(
                        item.getIsOversea(),
                        item.getCellsType(),
                        item.getHTrace(),
                        item.getAesthetics(),
                        item.getTransparentDoubleGlass(),
                        item.getCellSource());
            }));
            for (Map.Entry<String, List<CellInstockPlanDTO>> item: collect.entrySet())
            {
                CellDailyBalanceDTO dto = null;
                for (CellInstockPlanDTO value : item.getValue()) {
                    if (dto == null) {
                        dto = convert.cellInstockPlanToDto(value);
                        dto.setCellType(value.getCellsType());
                        dto.setDt(value.getCellSource());
                        dto.setProject(CellDailyBalanceConstant.MADE_BY_ONESELF);
                        setZero(dto);
                    }
                    int day = value.getStartTime().getDayOfMonth();
                    Class cls = CellDailyBalanceDTO.class;
                    Method getMethod = ReflectUtil.getMethod(cls, "getD" + day);
                    Method setMethod = ReflectUtil.getMethod(cls, "setD" + day, BigDecimal.class);
                    Object val = ReflectUtil.invoke(dto, getMethod);
                    if (val == null) {
                        ReflectUtil.invoke(dto, setMethod, value.getQtyPc());
                    } else {
                        BigDecimal addVal = ((BigDecimal) val).add(value.getQtyPc());
                        ReflectUtil.invoke(dto, setMethod, addVal);
                    }
                }
                dto.setMonth(cellInstockPlanQuery.getMonth());
                dto.setFromVersion(version);
                dtos.add(dto);
            }

        }
        dtos= getSortedCellDailyBalanceDTOS(dtos);
        return dtos;
    }

    @Override
    @Cacheable(cacheNames = "CellDailyBalanceService_getMaxVersionByMonthAndIsOversea", key = "#p0+'_'+#p1")
    public String getMaxVersionByMonthAndIsOversea(String month, String isOversea) {
        QCellInstockPlan qCellInstockPlan = QCellInstockPlan.cellInstockPlan;
        JPAQuery<String> where = jpaQueryFactory.select(qCellInstockPlan.finalVersion.max()).from(qCellInstockPlan).where(
                qCellInstockPlan.month.eq(month)
        ).where(
                qCellInstockPlan.finalVersion.isNotNull()
        ).where(
                qCellInstockPlan.isOversea.eq(isOversea)
        );
        String version = where.fetchFirst();
        return version;
    }

    /**
     * 计算外购数据：来源外购计划（其它接口）
     *
     * @return
     */
    @Override

    public   List<CellDailyBalanceDTO> calcOverseaPurchasePlan(CellDailyBalanceQuery query) {
        List<OverseaPurchasePlanDTO> list = overseaPurchasePlanService.listByMonthAndIsOversea(query.getMonth(), query.getIsOversea());
        list=Optional.ofNullable(list).orElse(Lists.newArrayList());
        if (StringUtils.isNotBlank(query.getCellsType())){
            list = list.stream().filter(item -> item.getCellTypeName().equals(query.getCellsType())).collect(Collectors.toList());
        }
        List<CellDailyBalanceDTO> datas = new ArrayList<>();
        Map<String, Map<String, List<OverseaPurchasePlanDTO>>> collect = list.stream().collect(
                Collectors.groupingBy(OverseaPurchasePlanDTO::getIsOverseaName,
                        Collectors.groupingBy(OverseaPurchasePlanDTO::getCellTypeName
                        ))
        );
        List<CellDailyBalanceDTO> finalDatas = datas;
        collect.entrySet().stream().forEach(overseaEntry -> {
            String isoversea = overseaEntry.getKey();
            overseaEntry.getValue().entrySet().forEach(cellTypeEntry -> {
                String cellType = cellTypeEntry.getKey();
                CellDailyBalanceDTO dto = new CellDailyBalanceDTO();
                dto.setIsOversea(isoversea);
                dto.setCellType(cellType);
                dto.setMonth(query.getMonth());
                dto.setProject(CellDailyBalanceConstant.OUTSOURCING);
                setZero(dto);
                cellTypeEntry.getValue().stream().forEach(overseaPurchasePlanDTO -> {
                    Integer day = overseaPurchasePlanDTO.getDay();
                    BigDecimal value = overseaPurchasePlanDTO.getQuantity();
                    if (value != null) {
                        BigDecimal val = ReflectUtil.invoke(dto, "getD" + day);
                        if (val == null) {
                            ReflectUtil.invoke(dto, "setD" + day, value);
                        } else {
                            ReflectUtil.invoke(dto, "setD" + day, val.add(value));
                        }
                    }


                });
                dto.setHTrace("无");
                dto.setAesthetics("无");
                dto.setDt("无");
                dto.setTransparentDoubleGlass("无");
                finalDatas.add(dto);

            });
        });
      datas=  getSortedCellDailyBalanceDTOS(finalDatas);
        return  datas;


    }

    /**
     * 计算电池需求数据：来源电池需求接口
     *
     * @param query
     */
    @Override
    public List<CellDailyBalanceDTO> calcCellDemandPlan(CellDailyBalanceQuery query) {
        List<CellDailyBalanceDTO> cellDailyBalanceDTOS = new ArrayList<>();
        List<BatteryDemandPlanLinesDTO> dtos = demandPlanLinesService.getCellDemandPlanByMonth(query);
        if (StringUtils.isNotBlank(query.getCellsType())){
            dtos = dtos.stream().filter(item -> {
                return item.getBatteryName().equals(query.getCellsType());
            }).collect(Collectors.toList());
        }
        Map<String, List<BatteryDemandPlanLinesDTO>> collect = dtos.stream().collect(Collectors.groupingBy(item -> {
            return groupKeyJoiner.join(item.getDomesticOverseaName(), item.getBatteryName(),
                    item.getHTraceName(), item.getAestheticsName(),
                    item.getTransparentDoubleGlassName(), item.getPcsSourceTypeName());
        }));
        for(Map.Entry<String, List<BatteryDemandPlanLinesDTO>> item:collect.entrySet())
         {
            String key = item.getKey();
            String[] values = key.split(",", 6);
            CellDailyBalanceDTO cellDailyBalanceDTO = new CellDailyBalanceDTO();
            cellDailyBalanceDTO.setIsOversea(values[0]);
            cellDailyBalanceDTO.setCellType(values[1]);
            cellDailyBalanceDTO.setMonth(query.getMonth());
            cellDailyBalanceDTO.setHTrace(values[2]);
            cellDailyBalanceDTO.setAesthetics(values[3]);
            cellDailyBalanceDTO.setTransparentDoubleGlass(values[4]);
            cellDailyBalanceDTO.setDt(values[5]);//低碳
            cellDailyBalanceDTO.setProject(CellDailyBalanceConstant.CELL_DEMAND);
            setZero(cellDailyBalanceDTO);
            item.getValue().stream().forEach(demandPlanLinesDTO -> {
                LocalDate localDate = demandPlanLinesDTO.getDemandDate();
                Integer day = localDate.getDayOfMonth();
                Method setMethod = ReflectUtil.getMethod(CellDailyBalanceDTO.class, "setD" + day, BigDecimal.class);
                Method getMethod = ReflectUtil.getMethod(CellDailyBalanceDTO.class, "getD" + day);
                Object value = ReflectUtil.invoke(cellDailyBalanceDTO, getMethod);
                if (value == null) {
                    ReflectUtil.invoke(cellDailyBalanceDTO, setMethod, demandPlanLinesDTO.getDemandQty());
                } else {
                    ReflectUtil.invoke(cellDailyBalanceDTO, setMethod, ((BigDecimal) value).add(demandPlanLinesDTO.getDemandQty()));
                }
            });
            cellDailyBalanceDTOS.add(cellDailyBalanceDTO);
        }
        cellDailyBalanceDTOS = getSortedCellDailyBalanceDTOS(cellDailyBalanceDTOS);
        return cellDailyBalanceDTOS;

    }

    private List<CellDailyBalanceDTO> getSortedCellDailyBalanceDTOS(List<CellDailyBalanceDTO> cellDailyBalanceDTOS) {
        if (CollectionUtils.isNotEmpty(cellDailyBalanceDTOS)){
            //cellDailyBalanceDTOS排序依据isOversea cellType hTrace dt aesthetics transparentDoubleGlass,排序后转成list
            cellDailyBalanceDTOS = cellDailyBalanceDTOS.stream().sorted(
                    Comparator.comparing(CellDailyBalanceDTO::getIsOversea,Comparator.nullsLast(Comparator.naturalOrder())).
                            thenComparing(CellDailyBalanceDTO::getCellType,Comparator.nullsLast(Comparator.naturalOrder())).
                            thenComparing(CellDailyBalanceDTO::getHTrace,Comparator.nullsLast(Comparator.naturalOrder())).
                            thenComparing(CellDailyBalanceDTO::getDt,Comparator.nullsLast(Comparator.naturalOrder())).
                            thenComparing(CellDailyBalanceDTO::getAesthetics,Comparator.nullsLast(Comparator.naturalOrder())).
                            thenComparing(CellDailyBalanceDTO::getTransparentDoubleGlass,Comparator.nullsLast(Comparator.naturalOrder()))).
                    collect(Collectors.toList());



        }
        return cellDailyBalanceDTOS;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellDailyBalanceQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellDailyBalance.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellDailyBalance.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellDailyBalance.isOversea.eq(query.getIsOversea()));
        }
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qCellDailyBalance.cellTypeId.eq(query.getCellTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellDailyBalance.cellType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellDailyBalance.month.eq(query.getMonth()));
        }
        if (StringUtils.isNotEmpty(query.getProject())) {
            booleanBuilder.and(qCellDailyBalance.project.eq(query.getProject()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qCellDailyBalance.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getDt())) {
            booleanBuilder.and(qCellDailyBalance.dt.eq(query.getDt()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qCellDailyBalance.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellDailyBalance.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (query.getD1() != null) {
            booleanBuilder.and(qCellDailyBalance.d1.eq(query.getD1()));
        }
        if (query.getD2() != null) {
            booleanBuilder.and(qCellDailyBalance.d2.eq(query.getD2()));
        }
        if (query.getD3() != null) {
            booleanBuilder.and(qCellDailyBalance.d3.eq(query.getD3()));
        }
        if (query.getD4() != null) {
            booleanBuilder.and(qCellDailyBalance.d4.eq(query.getD4()));
        }
        if (query.getD5() != null) {
            booleanBuilder.and(qCellDailyBalance.d5.eq(query.getD5()));
        }
        if (query.getD6() != null) {
            booleanBuilder.and(qCellDailyBalance.d6.eq(query.getD6()));
        }
        if (query.getD7() != null) {
            booleanBuilder.and(qCellDailyBalance.d7.eq(query.getD7()));
        }
        if (query.getD8() != null) {
            booleanBuilder.and(qCellDailyBalance.d8.eq(query.getD8()));
        }
        if (query.getD9() != null) {
            booleanBuilder.and(qCellDailyBalance.d9.eq(query.getD9()));
        }
        if (query.getD10() != null) {
            booleanBuilder.and(qCellDailyBalance.d10.eq(query.getD10()));
        }
        if (query.getD11() != null) {
            booleanBuilder.and(qCellDailyBalance.d11.eq(query.getD11()));
        }
        if (query.getD12() != null) {
            booleanBuilder.and(qCellDailyBalance.d12.eq(query.getD12()));
        }
        if (query.getD13() != null) {
            booleanBuilder.and(qCellDailyBalance.d13.eq(query.getD13()));
        }
        if (query.getD14() != null) {
            booleanBuilder.and(qCellDailyBalance.d14.eq(query.getD14()));
        }
        if (query.getD15() != null) {
            booleanBuilder.and(qCellDailyBalance.d15.eq(query.getD15()));
        }
        if (query.getD16() != null) {
            booleanBuilder.and(qCellDailyBalance.d16.eq(query.getD16()));
        }
        if (query.getD17() != null) {
            booleanBuilder.and(qCellDailyBalance.d17.eq(query.getD17()));
        }
        if (query.getD18() != null) {
            booleanBuilder.and(qCellDailyBalance.d18.eq(query.getD18()));
        }
        if (query.getD19() != null) {
            booleanBuilder.and(qCellDailyBalance.d19.eq(query.getD19()));
        }
        if (query.getD20() != null) {
            booleanBuilder.and(qCellDailyBalance.d20.eq(query.getD20()));
        }
        if (query.getD21() != null) {
            booleanBuilder.and(qCellDailyBalance.d21.eq(query.getD21()));
        }
        if (query.getD22() != null) {
            booleanBuilder.and(qCellDailyBalance.d22.eq(query.getD22()));
        }
        if (query.getD23() != null) {
            booleanBuilder.and(qCellDailyBalance.d23.eq(query.getD23()));
        }
        if (query.getD24() != null) {
            booleanBuilder.and(qCellDailyBalance.d24.eq(query.getD24()));
        }
        if (query.getD25() != null) {
            booleanBuilder.and(qCellDailyBalance.d25.eq(query.getD25()));
        }
        if (query.getD26() != null) {
            booleanBuilder.and(qCellDailyBalance.d26.eq(query.getD26()));
        }
        if (query.getD27() != null) {
            booleanBuilder.and(qCellDailyBalance.d27.eq(query.getD27()));
        }
        if (query.getD28() != null) {
            booleanBuilder.and(qCellDailyBalance.d28.eq(query.getD28()));
        }
        if (query.getD29() != null) {
            booleanBuilder.and(qCellDailyBalance.d29.eq(query.getD29()));
        }
        if (query.getD30() != null) {
            booleanBuilder.and(qCellDailyBalance.d30.eq(query.getD30()));
        }
        if (query.getD31() != null) {
            booleanBuilder.and(qCellDailyBalance.d31.eq(query.getD31()));
        }
    }

    @Override
    public CellDailyBalanceDTO queryById(Long id) {
        CellDailyBalance queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellDailyBalanceDTO save(CellDailyBalanceSaveDTO saveDTO) {
        CellDailyBalance newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellDailyBalance());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(CellDailyBalanceQuery query, HttpServletResponse response) {
        Page<CellDailyBalanceDTO> cellDailyBalanceDTOS = queryByPage(query);
        List<CellDailyBalanceDTO> datas =Optional.ofNullable( cellDailyBalanceDTOS.getContent()).orElse(new ArrayList<>());
        // dto数据转为ExcelData数据
        List<CellDailyBalanceExcelDTO> cellDailyBalanceExcelDTOS = convert.toExcelDTO(datas);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellDailyBalanceExcelDTO.class, cellDailyBalanceExcelDTOS, JSON.toJSONString(query), "每日结存报表", response);
    }
}
