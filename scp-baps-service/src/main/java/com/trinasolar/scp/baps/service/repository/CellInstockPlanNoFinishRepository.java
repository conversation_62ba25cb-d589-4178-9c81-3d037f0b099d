package com.trinasolar.scp.baps.service.repository;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanNoFinish;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;
/**
 * 实际入库表（待转化）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
@Repository
public interface CellInstockPlanNoFinishRepository extends JpaRepository<CellInstockPlanNoFinish, Long>, QuerydslPredicateExecutor<CellInstockPlanNoFinish> {

}
