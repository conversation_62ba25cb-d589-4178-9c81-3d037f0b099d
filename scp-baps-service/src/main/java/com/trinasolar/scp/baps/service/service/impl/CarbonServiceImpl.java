package com.trinasolar.scp.baps.service.service.impl;

import com.trinasolar.scp.baps.domain.dto.system.CarbonCertCodeDTO;
import com.trinasolar.scp.baps.domain.dto.system.CarbonCertHeaderDTO;
import com.trinasolar.scp.baps.service.feign.CarbonFeign;
import com.trinasolar.scp.baps.service.service.CarbonService;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.http.ResponseEntity;


import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("CarbonServiceImpl")
@RequiredArgsConstructor
public class CarbonServiceImpl implements CarbonService {
    private final CarbonFeign carbonFeign;

    @Override
    public List<CarbonCertHeaderDTO> findList(List<String> certCodeList) {
        CarbonCertCodeDTO carbonCertCodeDTO = new CarbonCertCodeDTO();
        carbonCertCodeDTO.setCertCodeList(certCodeList);
        ResponseEntity<Results<List<CarbonCertHeaderDTO>>> findList = carbonFeign.findList(carbonCertCodeDTO);
        assert findList.getBody() != null;
        if(CollectionUtils.isNotEmpty((findList.getBody().getData()))){
            return findList.getBody().getData().stream().filter(i -> "1".equals(i.getStatusCode())).collect(Collectors.toList());
        }
        return null;
    }
}
