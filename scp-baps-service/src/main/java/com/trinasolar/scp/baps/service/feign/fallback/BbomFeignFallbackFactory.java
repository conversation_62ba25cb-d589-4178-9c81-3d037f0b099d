package com.trinasolar.scp.baps.service.feign.fallback;


import com.trinasolar.scp.baps.domain.dto.MainGridSpacingRuleDTO;
import com.trinasolar.scp.baps.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.baps.domain.dto.MaterielMatchLineDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.BatteryScreenPlateWorkshopDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.BatterySiliconWaferDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.ItemsDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.LowEfficiencyCellPercentDTO;
import com.trinasolar.scp.baps.domain.query.MainGridSpacingRuleQuery;
import com.trinasolar.scp.baps.domain.query.MaterielMatchHeaderQuery;
import com.trinasolar.scp.baps.domain.query.bbom.BatteryScreenPlateWorkshopQuery;
import com.trinasolar.scp.baps.domain.query.bbom.ItemsQuery;
import com.trinasolar.scp.baps.domain.query.bbom.ItemsNewQuery;
import com.trinasolar.scp.baps.domain.query.bbom.LowEfficiencyCellPercentQuery;
import com.trinasolar.scp.baps.service.feign.BbomFeign;
import com.trinasolar.scp.baps.service.feign.PageFeign;
import com.trinasolar.scp.common.api.util.Results;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * scp-bom-api服务降级处理
 *
 * <AUTHOR>
 * @date 2022年4月26日12:01:48
 */
@Component
@Slf4j
public class BbomFeignFallbackFactory implements FallbackFactory<BbomFeign> {
    @Override
    public BbomFeign create(Throwable cause) {
        return new BbomFeign() {

            @Override
            public ResponseEntity<Results<List<MaterielMatchHeaderDTO>>> queryList(MaterielMatchHeaderQuery query) {
                log.warn("【BbomFeign-queryList】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Object>> deleteByHeadId(List<MaterielMatchHeaderDTO> headerDTOList) {
                log.warn("【BbomFeign-deleteByHeadId】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<MaterielMatchLineDTO>>> queryMatchLineByHeaderId(MaterielMatchHeaderDTO headerDTO) {
                log.warn("【BbomFeign-queryMatchLineByHeaderId】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Object>> addMatchInfo(MaterielMatchHeaderDTO headerDTO) {
                log.warn("【BbomFeign-addMatchInfo】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<ItemsDTO>> findOneByItemCode(ItemsDTO itemsDTO) {
                log.warn("【BbomFeign-findOneByItemCode】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<MainGridSpacingRuleDTO>>> getAllRules(MainGridSpacingRuleQuery query) {
                log.warn("【BbomFeign:main-grid-spacing-rule:getAllRules】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Map<String, ItemsDTO>>> queryByItemCodeAll(ItemsNewQuery query) {
                log.warn("【BbomFeign:main-grid-spacing-rule:queryByItemCodeAll】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Map<String, ItemsDTO>>> queryByItemCodeAllNew(ItemsNewQuery query) {
                log.warn("【BbomFeign:main-grid-spacing-rule:queryByItemCodeAllNew】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<LowEfficiencyCellPercentDTO>>> list(LowEfficiencyCellPercentQuery query) {
                log.warn("【BbomFeign-list】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ItemsDTO>>> findReworkItemCode(ItemsQuery query) {
                log.warn("【BbomFeign:items:findItemsForBaps】查询物料信息给baps发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Object>> allMatchItem(MaterielMatchHeaderQuery query) {
                log.warn("【BbomFeign:items:allMatchItem】baps发生异常：{}", cause.getMessage());
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<PageFeign<BatteryScreenPlateWorkshopDTO>>> queryBatteryScreenPlateList(BatteryScreenPlateWorkshopQuery query) {
                log.warn("【BbomFeign:BatteryScreenPlateWorkshopDTO:queryByPage】baps发生异常：{}", cause.getMessage());
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<BatterySiliconWaferDTO>>> queryBatterySiliconWaferList() {
                log.warn("【BbomFeign:BatterySiliconWaferDTO:getAllByCache】baps发生异常：{}", cause.getMessage());
                return Results.createFailRes();
            }

            /*@Override
            public ResponseEntity<Results<Object>> addMatchLineInfo(MaterielMatchHeaderDTO headerDTO) {
                log.warn("【BbomFeign-addMatchLineInfo】发生异常：{}", cause);
                return Results.createFailRes();
            }*/

        };
    }
}
