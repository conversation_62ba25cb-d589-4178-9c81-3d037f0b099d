package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellPlanQtyFluctuationCoefficientDTO;
import com.trinasolar.scp.baps.domain.entity.CellPlanQtyFluctuationCoefficient;
import com.trinasolar.scp.baps.domain.query.CellPlanQtyFluctuationCoefficientQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanQtyFluctuationCoefficientSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 投产计划浮动系数 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-30 01:49:53
 */
public interface CellPlanQtyFluctuationCoefficientService {
    /**
     * 分页获取投产计划浮动系数
     *
     * @param query 查询对象
     * @return 投产计划浮动系数分页对象
     */
    Page<CellPlanQtyFluctuationCoefficientDTO> queryByPage(CellPlanQtyFluctuationCoefficientQuery query);

    /**
     * 根据主键获取投产计划浮动系数详情
     *
     * @param id 主键
     * @return 投产计划浮动系数详情
     */
        CellPlanQtyFluctuationCoefficientDTO queryById(Long id);

    /**
     * 保存或更新投产计划浮动系数
     *
     * @param saveDTO 投产计划浮动系数保存对象
     * @return 投产计划浮动系数对象
     */
    CellPlanQtyFluctuationCoefficientDTO save(CellPlanQtyFluctuationCoefficientSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除投产计划浮动系数
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellPlanQtyFluctuationCoefficientQuery query, HttpServletResponse response);

    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

