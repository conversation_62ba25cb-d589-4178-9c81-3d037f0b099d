package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellFineMonthDTO;
import com.trinasolar.scp.baps.domain.convert.CellFineMonthDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellFineMonth;
import com.trinasolar.scp.baps.domain.entity.QCellFineMonth;
import com.trinasolar.scp.baps.domain.query.CellFineMonthQuery;
import com.trinasolar.scp.baps.domain.excel.CellFineMonthExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellFineMonthQuery;
import com.trinasolar.scp.baps.domain.save.CellFineMonthSaveDTO;
import com.trinasolar.scp.baps.service.repository.CellFineMonthRepository;
import com.trinasolar.scp.baps.service.service.CellFineMonthService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.LocalDateConverter;
import com.trinasolar.scp.common.api.util.LocalDateTimeConverter;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 电池良率月拆分表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-05 05:51:04
 */
@Slf4j
@Service("cellFineMonthService")
@RequiredArgsConstructor
public class CellFineMonthServiceImpl implements CellFineMonthService {
    private static final QCellFineMonth qCellFineMonth = QCellFineMonth.cellFineMonth;

    private final CellFineMonthDEConvert convert;

    private final CellFineMonthRepository repository;

    @Override
    public Page<CellFineMonthDTO> queryByPage(CellFineMonthQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellFineMonth> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellFineMonthQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellFineMonth.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getIsOversea())){
            booleanBuilder.and(qCellFineMonth.isOversea.eq(query.getIsOversea()));
        }
        if(StringUtils.isNotEmpty(query.getCellType())){
            booleanBuilder.and(qCellFineMonth.cellType.eq(query.getCellType()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlace())){
            booleanBuilder.and(qCellFineMonth.basePlace.eq(query.getBasePlace()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qCellFineMonth.workshop.eq(query.getWorkshop()));
        }
        if (query.getYear() != null) {
            booleanBuilder.and(qCellFineMonth.year.eq(query.getYear()));
        }
        if (query.getMonth() != null) {
            booleanBuilder.and(qCellFineMonth.month.eq(query.getMonth()));
        }
        if (query.getYield() != null) {
            booleanBuilder.and(qCellFineMonth.yield.eq(query.getYield()));
        }
    }

    @Override
    public CellFineMonthDTO queryById(Long id) {
        CellFineMonth queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellFineMonthDTO save(CellFineMonthSaveDTO saveDTO) {
        CellFineMonth newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new CellFineMonth());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellFineMonthQuery query, HttpServletResponse response) {
       List<CellFineMonthDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "电池良率月拆分表", "电池良率月拆分表", excelPara.getSimpleHeader(), excelData);
    }
}
