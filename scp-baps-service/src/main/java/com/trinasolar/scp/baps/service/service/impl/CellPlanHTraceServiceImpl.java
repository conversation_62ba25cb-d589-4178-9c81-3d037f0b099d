package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.google.common.collect.Maps;
import com.trinasolar.scp.baps.domain.HTraceWarnType;
import com.trinasolar.scp.baps.domain.constant.CommonConstant;
import com.trinasolar.scp.baps.domain.convert.CellPlanHTraceDEConvert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.dto.bbom.ItemsDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.BmrpSafetyStockDaysDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.SiliconSlicePurchasePlanDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.TjOnHandDTO;
import com.trinasolar.scp.baps.domain.factory.HTraceFactory;
import com.trinasolar.scp.baps.domain.query.CellPlanHTraceQuery;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MathUtils;
import com.trinasolar.scp.baps.domain.utils.excel.ExcelHead;
import com.trinasolar.scp.baps.domain.utils.excel.ExcelMain;
import com.trinasolar.scp.baps.domain.utils.excel.ExcelUtil;
import com.trinasolar.scp.baps.domain.utils.excel.OperateRowData;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.baps.service.service.bmrp.BatteryMrpService;
import com.trinasolar.scp.baps.service.service.system.SystemService;
import com.trinasolar.scp.common.api.annotation.validator.ExportConvertUtils;
import com.trinasolar.scp.common.api.annotation.validator.ImportConvertUtils;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.YesOrNoEnum;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: CellPlanHTraceServiceImpl
 * @date 2024/6/5 10:14
 */
@Slf4j
@Service("cellPlanHTraceService")
@RequiredArgsConstructor
public class CellPlanHTraceServiceImpl implements CellPlanHTraceService {
    private final CellPlanHTraceDEConvert convert;
    private final SystemService systemService;
    private final BatteryMrpService batteryMrpService;
    private final BatteryBomService batteryBomService;
    private final CellPlanLineService cellPlanLineService;
    private final CellPlanLineVersionService versionService;
    private final CellBaseCapacityService cellBaseCapacityService;
    private final CellGradeCapacityService cellGradeCapacityService;


    @Override
    public List<CellPlanHTraceDTO> list(CellPlanHTraceQuery query) {
        List<CellPlanLineDTO> dataList = findByCondition(query);
        //转换
        List<CellPlanHTraceDTO> cellPlanHTraceList = convert.toHTraceDTO(dataList);
        //排序
        cellPlanHTraceList.sort(Comparator.comparing(CellPlanHTraceDTO::getIsOverseaId).thenComparing(CellPlanHTraceDTO::getBasePlaceId).thenComparing(CellPlanHTraceDTO::getWorkshopId).thenComparing(CellPlanHTraceDTO::getOldMonth, Comparator.comparingInt(m -> Integer.parseInt(m))));
        return cellPlanHTraceList;
    }

    /**
     * 根据查询条件查询
     *
     * @param query
     * @return
     */
    private List<CellPlanLineDTO> findByCondition(CellPlanHTraceQuery query) {
        //转译查询条件
        ImportConvertUtils.convertField(query);
        //获取最大版本号
        Map<String, String> versionMap = versionService.findMaxVersion(query.getIsOverseaId(), query.getMonthList());
        if (MapUtils.isNotEmpty(versionMap)) {
            //根据版本查询排产数据
            List<CellPlanLineDTO> dataList = cellPlanLineService.findByCondition(query.getIsOverseaId(), versionMap);
            return dataList;
        }
        return Lists.newArrayList();
    }

    @Override
    public HTraceResponse compute(CellPlanHTraceQuery query) {
        //校验是否存在当前月份
        LocalDate now = LocalDate.now();
        String month = DateUtil.getMonth(now);
        if (StringUtils.equalsAny(month, query.getBeginMonth(), query.getEndMonth())) {
            Long lovLineId = LovUtils.get(LovHeaderCodeConstant.CELL_SOURCE, CommonConstant.DT).getLovLineId();
            //多获取一个月的排产数据，为了安全库存处理
            String newMonth = DateUtil.getNewMonth(query.getEndMonth(), 1);
            query.setMonthList(Arrays.asList(query.getBeginMonth(), query.getEndMonth(), newMonth));
            List<CellPlanLineDTO> dataList = findByCondition(query);
            //将从数据库中查处的投产数据中H转换标记剔除，认为都是正常H数据，防止和当前计算的H兼容标记冲突
            dataList.forEach(CellPlanLineDTO::clearHChangeFlag);
            //当前日期之前的需求 || 低碳 数据
            List<CellPlanLineDTO> dtList = dataList.stream().filter(k -> !newMonth.equals(k.getOldMonth()) && (Objects.equals(k.getCellSourceId(), lovLineId) || now.isAfter(k.getStartTime().toLocalDate()))).collect(Collectors.toList());
            //删掉DT和之前的数据，剩下的进行兼容
            dataList.removeAll(dtList);
            //获取安全库存数据，并转换计划数据
            List<HTracePlan> hTracePlanList = safetyStockList(dataList, query);
            //剔除多出的一个月数据
            dataList = dataList.stream().filter(k -> !newMonth.equals(k.getOldMonth())).collect(Collectors.toList());
            hTracePlanList = hTracePlanList.stream().filter(k -> !newMonth.equals(k.getOldMonth())).collect(Collectors.toList());
            //获取供应数据
            List<HTraceSupply> hTraceSupplyList = siliconSliceSupply(dataList);
            //需求对比供应，H转常规
            List<HTraceDayResult> dayResults = computeData(hTracePlanList, hTraceSupplyList);
            //生成兼容数据
            generateCompatibleData(dayResults, dataList);
            //添加DT、历史数据
            dataList.addAll(dtList);
            //列传行
            List<HTraceLineDTO> cellPlanLines = convertResult(dataList);
            //转换返回类型
            List<HTraceResult> results = convertDayResult(dayResults);
            //排序
            results.sort(Comparator.comparing(HTraceResult::getProductCategory).thenComparing(HTraceResult::getCrystalType).thenComparing(HTraceResult::getHTrace).thenComparing(HTraceResult::getType, Comparator.comparingInt(HTraceWarnType::sort)).thenComparing(HTraceResult::getWorkshop));
            cellPlanLines.sort(Comparator.comparing(HTraceLineDTO::getMonth).thenComparing(HTraceLineDTO::getCellsType));
            //转译
            ExportConvertUtils.convertField(results);
            return new HTraceResponse(results, cellPlanLines);
        }
        throw new BizException("baps_include_current_month_error");
    }

    private List<HTraceLineDTO> convertResult(List<CellPlanLineDTO> dataList) {
        List<String> subList = dataList.stream().map(CellPlanLineDTO::getStartTime).map(LocalDateTime::getDayOfMonth).distinct().sorted().map(String::valueOf).collect(Collectors.toList());
        //分组汇总
        Map<HTraceLineDTO, List<CellPlanLineDTO>> groupMap = dataList.stream().collect(Collectors.groupingBy(HTraceLineDTO::groupBy));
        groupMap.forEach((line, list) -> {
            Map<String, BigDecimal> subMap = list.stream().collect(Collectors.groupingBy(k -> String.valueOf(k.getStartTime().getDayOfMonth()), Collectors.reducing(BigDecimal.ZERO, CellPlanLineDTO::getQtyPc, BigDecimal::add)));
            line.setSubList(subList);
            line.setSubMap(subMap);
        });
        return IterableUtils.toList(groupMap.keySet());
    }

    /**
     * 转换返回类型
     *
     * @param dayResults
     * @return
     */
    private List<HTraceResult> convertDayResult(List<HTraceDayResult> dayResults) {
        List<String> dayList = dayResults.stream().map(HTraceDayResult::getDay).distinct().sorted().map(String::valueOf).collect(Collectors.toList());
        Map<HTraceResult, List<HTraceDayResult>> groupMap = dayResults.stream().collect(Collectors.groupingBy(HTraceResult::groupBy));
        groupMap.forEach((result, list) -> {
            Map<String, BigDecimal> subMap = list.stream().collect(Collectors.toMap(k -> String.valueOf(k.getDay()), HTraceDayResult::getQuantity));
            result.setSubList(dayList);
            result.setSubMap(subMap);
        });
        return IterableUtils.toList(groupMap.keySet());
    }

    /**
     * 按对比结果生成兼容数据
     *
     * @param results
     * @param dataList
     */
    private void generateCompatibleData(List<HTraceDayResult> results, List<CellPlanLineDTO> dataList) {
        Long lovLineId = LovUtils.get(LovHeaderCodeConstant.H_TRACE, CommonConstant.H).getLovLineId();
        Map<String, List<CellPlanLineDTO>> planMap = dataList.stream().collect(Collectors.groupingBy(CellPlanLineDTO::compatibleSign));
        List<HTraceDayResult> compatibleData = results.stream().filter(k -> HTraceWarnType.COMPATIBLE.getCode().equals(k.getType())).collect(Collectors.toList());
        compatibleData.forEach(data -> {
            BigDecimal quantity = data.getQuantity();
            String compatibleSign = data.compatibleSign();
            List<CellPlanLineDTO> planLineList = planMap.get(compatibleSign);
            planLineList.sort(Comparator.comparing(CellPlanLineDTO::getQtyPc));
            for (CellPlanLineDTO planLine : planLineList) {
                BigDecimal compatibleQuantity;
                BigDecimal qtyPc = planLine.getQtyPc();
                if (qtyPc.compareTo(quantity) >= 0) {
                    compatibleQuantity = quantity;
                    planLine.setQtyPc(qtyPc.subtract(quantity));
                } else {
                    compatibleQuantity = qtyPc;
                    planLine.setQtyPc(BigDecimal.ZERO);
                    quantity = quantity.subtract(qtyPc);
                }
                //生成数据
                CellPlanLineDTO clone = SerializationUtils.clone(planLine);
                clone.setId(null);
                clone.setHChangeFlag(YesOrNoEnum.YES.getCode());
                clone.setQtyPc(compatibleQuantity);
                clone.setHTrace(CommonConstant.H);
                clone.setHTraceId(lovLineId);
                dataList.add(clone);
            }
        });
    }

    /**
     * 计算兼容
     *
     * @param hTracePlanList
     * @param hTraceSupplyList
     */
    private List<HTraceDayResult> computeData(List<HTracePlan> hTracePlanList, List<HTraceSupply> hTraceSupplyList) {
        //初始化工厂
        HTraceFactory.init();
        Map<String, LovLineDTO> lovMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BDM_HTRACE_MAPPING);
        //共用车间集合
        List<String> shareWorkshopList = lovMap.values().stream().map(LovLineDTO::getAttribute1).distinct().filter(v -> v.contains(",")).map(v -> v.split(",")).map(Arrays::asList).map(l -> l.stream().map(v -> LovUtils.getById(v).getLovValue()).collect(Collectors.joining(","))).collect(Collectors.toList());
        //设置需求的共用车间
        setupShareWorkshop(shareWorkshopList, hTracePlanList);
        //需求
        Map<LocalDate, Map<String, Map<String, Map<String, List<HTracePlan>>>>> demandDateMap = hTracePlanList.stream().sorted(Comparator.comparing(HTracePlan::getPlanDate)).collect(Collectors.groupingBy(HTracePlan::getPlanDate, Collectors.groupingBy(HTracePlan::dailySign, Collectors.groupingBy(HTracePlan::getShareWorkshop, Collectors.groupingBy(HTracePlan::getHTrace)))));
        //供应
        Map<LocalDate, Map<String, Map<String, Map<String, HTraceSupply>>>> supplyDateMap = hTraceSupplyList.stream().sorted(Comparator.comparing(HTraceSupply::getSupplyDate)).collect(Collectors.groupingBy(HTraceSupply::getSupplyDate, Collectors.groupingBy(HTraceSupply::dailySign, Collectors.groupingBy(HTraceSupply::getWorkshop, Collectors.toMap(HTraceSupply::getHTrace, Function.identity())))));
        //天集合
        List<LocalDate> dateCollection = IterableUtils.toList(CollectionUtils.union(demandDateMap.keySet(), supplyDateMap.keySet()));
        dateCollection.sort(Comparator.naturalOrder());
        for (LocalDate date : dateCollection) {
            Map<String, Map<String, Map<String, List<HTracePlan>>>> demandMap = demandDateMap.getOrDefault(date, Maps.newHashMap());
            Map<String, Map<String, Map<String, HTraceSupply>>> supplyMap = supplyDateMap.getOrDefault(date, Maps.newHashMap());
            //分配
            Collection<String> unionCollection = CollectionUtils.union(demandMap.keySet(), supplyMap.keySet());
            for (String dailySign : unionCollection) {
                Map<String, Map<String, List<HTracePlan>>> hTracePlanMap = demandMap.getOrDefault(dailySign, Maps.newHashMap());
                //判断供应数据
                if (supplyMap.containsKey(dailySign)) {
                    //供应数据
                    Map<String, Map<String, HTraceSupply>> hTraceSupplyMap = supplyMap.get(dailySign);
                    //排产车间集合
                    Collection<String> workshopCollection = CollectionUtils.union(hTracePlanMap.keySet(), hTraceSupplyMap.keySet());
                    //循环需求中的车间进行分配
                    for (String workshop : workshopCollection) {
                        //需求数据
                        Map<String, List<HTracePlan>> workshopPlanMap = hTracePlanMap.getOrDefault(workshop, Maps.newHashMap());
                        //存在供应
                        if (hTraceSupplyMap.containsKey(workshop)) {
                            Map<String, HTraceSupply> workshopSupplyMap = hTraceSupplyMap.get(workshop);
                            //计算
                            HTraceFactory.compute(workshopSupplyMap, workshopPlanMap);
                        } else {
                            //计算
                            HTraceFactory.shortage(workshopPlanMap);
                        }
                    }
                } else {
                    hTracePlanMap.forEach((workshop, workshopPlanMap) -> {
                        //计算
                        HTraceFactory.shortage(workshopPlanMap);
                    });
                }
            }
        }
        return HTraceFactory.result();
    }

    /**
     * 设置共用车间
     *
     * @param shareWorkshopList
     * @param hTracePlanList
     */
    private void setupShareWorkshop(List<String> shareWorkshopList, List<HTracePlan> hTracePlanList) {
        hTracePlanList.forEach(k -> {
            String workshop = k.getWorkshop();
            String shareWorkshop = shareWorkshopList.stream().filter(share -> share.contains(workshop)).findFirst().orElse(workshop);
            k.setShareWorkshop(shareWorkshop);
        });
    }

    /**
     * 设置品类和型号
     *
     * @param dataList
     */
    private void setup(List<CellPlanLineDTO> dataList) {
        Map<Long, LovLineDTO> lovMap = systemService.findByLovCode(LovHeaderCodeConstant.BATTERY_TYPE, LovLineDTO::getLovLineId, Function.identity());
        dataList.forEach(data -> {
            Long cellTypeId = data.getCellsTypeId();
            if (lovMap.containsKey(cellTypeId)) {
                LovLineDTO lov = lovMap.get(cellTypeId);
                data.setCrystalType(LovUtils.getById(lov.getAttribute2()).getLovValue());
                data.setProductCategory(LovUtils.getById(lov.getAttribute3()).getLovValue());
            }
        });
    }


    /**
     * 获取安全库存数据
     *
     * @param dataList
     * @return
     */
    private List<HTracePlan> safetyStockList(List<CellPlanLineDTO> dataList, CellPlanHTraceQuery query) {
        //国内海外
        Long isOverseaId = query.getIsOverseaId();
        //设置品类、晶体类型，安全库存
        setup(dataList);
        //汇总分组  品类、晶体类型、H追溯、车间
        Map<HTracePlan, BigDecimal> hTracePlanMap = dataList.stream().collect(Collectors.groupingBy(HTracePlan::build, Collectors.reducing(BigDecimal.ZERO, CellPlanLineDTO::getQtyPc, BigDecimal::add)));
        hTracePlanMap.forEach((plan, quantity) -> plan.setQuantity(quantity));
        List<HTracePlan> hTracePlans = IterableUtils.toList(hTracePlanMap.keySet());
        //基地集合
        List<String> basePlaceList = dataList.stream().map(CellPlanLineDTO::getBasePlace).distinct().collect(Collectors.toList());
        //获取安全库存数据
        List<BmrpSafetyStockDaysDTO> safetyStockDaysList = batteryMrpService.safetyStockList(CommonConstant.SILICON, basePlaceList);
        //分组
        Map<String, String> safetyStockMap = safetyStockDaysList.stream().collect(Collectors.toMap(BmrpSafetyStockDaysDTO::sign, BmrpSafetyStockDaysDTO::getSafetyStockDays));
        //按相同维度分组
        Map<String, List<HTracePlan>> groupMap = hTracePlans.stream().collect(Collectors.groupingBy(HTracePlan::sign));
        //根据排产计划获取安全库存数据
        hTracePlans.sort(Comparator.comparing(HTracePlan::getPlanDate));
        for (HTracePlan data : hTracePlans) {
            String basePlace = data.getBasePlace();
            String month = data.getMonth();
            String workshop = data.getWorkshop();
            LocalDate demandDate = data.getPlanDate();
            String productCategory = data.getProductCategory();
            String crystalType = data.getCrystalType();
            String sign = StringUtils.join(basePlace, month);
            if (safetyStockMap.containsKey(sign) || safetyStockMap.containsKey(basePlace)) {
                //安全库存天，默认0
                String days = StringUtils.isNotBlank(safetyStockMap.get(sign)) ? safetyStockMap.get(sign) : safetyStockMap.get(basePlace);
                Long safetyStockDay = Optional.ofNullable(days).map(Long::new).orElse(0L);
                String groupSign = data.sign();
                BigDecimal ieQuantity = BigDecimal.ZERO;
                List<HTracePlan> safetyStockList = groupMap.get(groupSign).stream().filter(k -> k.getPlanDate().isAfter(demandDate)).sorted(Comparator.comparing(HTracePlan::getPlanDate)).limit(safetyStockDay).collect(Collectors.toList());
                //排产条数不等于安全库存天数，需要获取IE产能；非H的不用获取IE
                if (safetyStockList.size() < safetyStockDay) {
                    LocalDate startDate = safetyStockList.stream().sorted(Comparator.comparing(HTracePlan::getPlanDate).reversed()).findFirst().map(HTracePlan::getPlanDate).orElse(demandDate);
                    LocalDate endDate = startDate.plusDays(safetyStockDay - safetyStockList.size());
                    //ie产能
                    ieQuantity = findIePower(isOverseaId, workshop, startDate, endDate, productCategory, crystalType);
                }
                BigDecimal safetyStockQuantity = safetyStockList.stream().map(HTracePlan::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                data.setSafetyStockQuantity(safetyStockQuantity.add(ieQuantity));
            } else {
                data.setSafetyStockQuantity(BigDecimal.ZERO);
            }
        }
        return hTracePlans;
    }

    /**
     * 获取ie产能
     *
     * @param isOverseaId
     * @param workshop
     * @param startDate
     * @param endDate
     * @param productCategory
     * @param crystalType
     * @return
     */
    private BigDecimal findIePower(Long isOverseaId, String workshop, LocalDate startDate, LocalDate endDate, String productCategory, String crystalType) {
        //IE产能+IE打折
        BigDecimal baseQuantity = cellBaseCapacityService.findByCondition(isOverseaId, workshop, startDate, endDate, productCategory, crystalType);
        //IE爬坡
        BigDecimal gradeQuantity = cellGradeCapacityService.findByCondition(isOverseaId, workshop, startDate, endDate, productCategory, crystalType);
        return baseQuantity.add(gradeQuantity);
    }


    /**
     * 硅片供应数据
     *
     * @param cellPlanLineList
     * @return
     */
    private List<HTraceSupply> siliconSliceSupply(List<CellPlanLineDTO> cellPlanLineList) {
        //月份
        List<String> monthList = cellPlanLineList.stream().map(CellPlanLineDTO::getMonth).distinct().collect(Collectors.toList());
        //天玑
        List<TjOnHandDTO> inventoryList = findInventory();
        //硅片供应能力
        List<SiliconSliceSupplyLinesDTO> siliconSliceSupply = findSiliconSliceSupply(monthList);
        //硅片外购
        List<SiliconSlicePurchasePlanDTO> siliconSlicePurchasePlan = findSiliconSlicePurchasePlan(monthList);
        //合并供应
        return generateSupply(inventoryList, siliconSliceSupply, siliconSlicePurchasePlan);
    }

    /**
     * 生成供应数据
     *
     * @param inventoryList
     * @param siliconSliceSupply
     * @param siliconSlicePurchasePlan
     * @return
     */
    private List<HTraceSupply> generateSupply(List<TjOnHandDTO> inventoryList, List<SiliconSliceSupplyLinesDTO> siliconSliceSupply, List<SiliconSlicePurchasePlanDTO> siliconSlicePurchasePlan) {
        Map<String, LovLineDTO> lovMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BDM_HTRACE_MAPPING);
        List<String> workshopList = lovMap.values().stream().map(LovLineDTO::getAttribute1).distinct().filter(v -> v.contains(",")).map(v -> v.split(",")).map(Arrays::asList).map(l -> l.stream().map(v -> LovUtils.getById(v).getLovValue()).collect(Collectors.joining(","))).collect(Collectors.toList());
        //根据料号获取bom属性
        List<String> itemCodeList = inventoryList.stream().map(TjOnHandDTO::getItemCode).distinct().collect(Collectors.toList());
        itemCodeList.addAll(siliconSliceSupply.stream().map(SiliconSliceSupplyLinesDTO::getItemCode).distinct().collect(Collectors.toList()));
        itemCodeList.addAll(siliconSlicePurchasePlan.stream().map(SiliconSlicePurchasePlanDTO::getMaterialNo).distinct().collect(Collectors.toList()));
        itemCodeList = itemCodeList.stream().distinct().collect(Collectors.toList());
        Map<String, ItemsDTO> itemMap = batteryBomService.findItems(itemCodeList);
        //转换属性
        List<HTraceSupply> hTraceSupplyList = inventoryList.stream().map(data -> HTraceSupply.build(data, itemMap.get(data.getItemCode()))).collect(Collectors.toList());
        hTraceSupplyList.addAll(siliconSliceSupply.stream().map(data -> HTraceSupply.build(data, itemMap.get(data.getItemCode()))).flatMap(Collection::stream).collect(Collectors.toList()));
        hTraceSupplyList.addAll(siliconSlicePurchasePlan.stream().map(data -> HTraceSupply.build(data, itemMap.get(data.getMaterialNo()))).flatMap(Collection::stream).collect(Collectors.toList()));
        //合并供应数据
        hTraceSupplyList.forEach(item -> {
            String workshop = item.getWorkshop();
            String workshopStr = workshopList.stream().filter(k -> k.contains(workshop)).findFirst().orElse(workshop);
            item.setWorkshop(workshopStr);
        });
        //合并供应数据
        Map<HTraceSupply, BigDecimal> summaryMap = hTraceSupplyList.stream().collect(Collectors.groupingBy(HTraceSupply::groupBy, Collectors.reducing(BigDecimal.ZERO, HTraceSupply::getQuantity, BigDecimal::add)));
        summaryMap.forEach((supply, quantity) -> {
            supply.setQuantity(quantity);
            supply.assembly();
        });
        LocalDate now = LocalDate.now();
        return IterableUtils.toList(summaryMap.keySet()).stream().filter(k -> now.compareTo(k.getSupplyDate()) <= 0 && StringUtils.equalsAny(k.getHTrace(), CommonConstant.H, CommonConstant.NON)).collect(Collectors.toList());
    }


    /**
     * 获取库存数据
     *
     * @return
     */
    private List<TjOnHandDTO> findInventory() {
        Map<String, LovLineDTO> lovMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BDM_HTRACE_MAPPING);
        List<String> subInventoryCodeList = IterableUtils.toList(lovMap.keySet());
        List<TjOnHandDTO> inventoryList = batteryMrpService.findInventory(subInventoryCodeList, LocalDate.now());
        //设置车间
        inventoryList.forEach(item -> {
            LovLineDTO lovLine = lovMap.get(item.getSubInventoryCode());
            String attribute1 = lovLine.getAttribute1();
            List<String> workshopList = Arrays.asList(attribute1.split(","));
            List<String> workshop = workshopList.stream().map(LovUtils::getById).map(LovLineDTO::getLovValue).collect(Collectors.toList());
            item.setWorkshop(StringUtils.join(workshop, ","));
            item.setWorkshopList(workshop);
            //获取基地
            String basePlace = Optional.ofNullable(LovUtils.getById(lovLine.getAttribute2())).map(LovLineDTO::getLovValue).orElse(null);
            item.setBasePlace(basePlace);
        });
        return inventoryList;
    }

    /**
     * 获取硅片供应能力
     *
     * @param monthList
     * @return
     */
    private List<SiliconSliceSupplyLinesDTO> findSiliconSliceSupply(List<String> monthList) {
        return batteryMrpService.findSupplyLines(monthList);
    }

    /**
     * 获取硅片外购计划
     *
     * @param monthList
     * @return
     */
    private List<SiliconSlicePurchasePlanDTO> findSiliconSlicePurchasePlan(List<String> monthList) {
        return batteryMrpService.findSiliconSlicePurchasePlan(monthList);
    }


    @Override
    @SneakyThrows
    public void export(CellPlanHTraceQuery query, HttpServletResponse response) {
        HTraceResponse computeResult = compute(query);
        //导出excel
        List<HTraceLineDTO> cellPlanLineList = computeResult.getCellPlanLineList();
        HTraceLineDTO dto = CollectionUtils.isNotEmpty(cellPlanLineList) ? cellPlanLineList.get(0) : new HTraceLineDTO();
        //设置动态列
        List<ExcelHead> excelHeads = buildExcelHead(dto);
        List<Map<String, Object>> data = cellPlanLineList.stream().map(HTraceLineDTO::convertMap).collect(Collectors.toList());
        ExcelMain excelMain = ExcelMain.builder()
                .excelHeads(excelHeads)
                .data(data)
                .build();

        ExcelUtil.setExportResponseHeader(response, "H兼容导出");
        ExcelUtil.export(response, excelMain);
    }

    /**
     * 构建excel动态表头
     *
     * @param dto
     * @return
     */
    private List<ExcelHead> buildExcelHead(HTraceLineDTO dto) {
        List<ExcelHead> excelHeads = com.google.common.collect.Lists.newArrayList(
                ExcelHead.builder()
                        .merge(false)
                        .label("国内海外")
                        .prop("isOversea")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("生产基地")
                        .prop("basePlace")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("生产车间")
                        .prop("workshop")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("电池类型")
                        .prop("cellsType")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("片源种类")
                        .prop("cellSource")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("是否电池特殊要求")
                        .prop("isSpecialRequirement")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("硅料厂家")
                        .prop("siMfrs")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("计划版本")
                        .prop("version")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("实际月份")
                        .prop("month")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("排产月份")
                        .prop("oldMonth")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("H追溯")
                        .prop("hTrace")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("是否H兼容")
                        .prop("hChangeFlag")
                        .build()
        );
        if (Objects.nonNull(dto)) {
            dto.getSubList().forEach(sub -> {
                excelHeads.add(
                        ExcelHead.builder()
                                .merge(false)
                                .label(sub)
                                .prop(sub)
                                .build()
                );
            });
        }
        return excelHeads;
    }

    @Override
    public List<HTraceLineDTO> importData(MultipartFile multipartFile) {
        //读取excel
        List<HTraceLineInputDTO> inputList = ExcelUtil.readExcel(multipartFile, HTraceLineInputDTO.class, 12, (operateRowData) -> {
            ReflectUtil.setFieldValue(operateRowData.getRowData(), "day", OperateRowData.parsingDataToInteger(operateRowData.getColumn1()));
            ReflectUtil.setFieldValue(operateRowData.getRowData(), "quantity", OperateRowData.parsingDataToBigDecimal(operateRowData.getColumn2()));
        });
        //转译
        ImportConvertUtils.convertField(inputList);
        //校验数据
        verifyData(inputList);
        //数据维度：国内海外，月份，版本号
        Map<HTraceDimension, List<HTraceLineInputDTO>> dimensionList = inputList.stream().collect(Collectors.groupingBy(HTraceLineInputDTO::dimension));
        //根据维度查询数据
        Map<HTraceDimension, List<CellPlanLineDTO>> cellPlanLineMap = findDataByDimension(IterableUtils.toList(dimensionList.keySet()));
        //按维度进行校验总量是否超出
        dimensionList.forEach((dimension, list) -> {
            List<CellPlanLineDTO> planLineList = cellPlanLineMap.get(dimension);
            //将从数据库中查处的投产数据中H转换标记剔除，认为都是正常H数据，防止和当前计算的H兼容标记冲突
            planLineList.forEach(CellPlanLineDTO::clearHChangeFlag);
            if (CollectionUtils.isNotEmpty(planLineList)) {
                //按天兼容计算
                compatibleData(list, planLineList);
            } else {
                //不存在该维度下的数据
                log.error("数据维度不存在：{}", dimension);
                throw new BizException("baps_data_not_exist");
            }
        });
        List<CellPlanLineDTO> dataList = cellPlanLineMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        //转换数据
        List<HTraceLineDTO> cellPlanLines = convertResult(dataList);
        return cellPlanLines;
    }


    /**
     * 兼容数据
     *
     * @param inputList
     * @param planLineList
     * @return
     */
    private List<CellPlanLineDTO> compatibleData(List<HTraceLineInputDTO> inputList, List<CellPlanLineDTO> planLineList) {
        //记录发生变化的行
        List<CellPlanLineDTO> changeLine = Lists.newArrayList();
        //低碳lovId
        Long dtId = LovUtils.get(LovHeaderCodeConstant.CELL_SOURCE, CommonConstant.DT).getLovLineId();
        //非H id
        Long nonHId = LovUtils.get(LovHeaderCodeConstant.H_TRACE, CommonConstant.NON).getLovLineId();
        //H id
        Long hId = LovUtils.get(LovHeaderCodeConstant.H_TRACE, CommonConstant.H).getLovLineId();
        //校验总量是否超出
        BigDecimal dbQuantity = planLineList.stream().map(CellPlanLineDTO::getQtyPc).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal inputQuantity = inputList.stream().map(HTraceLineInputDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (dbQuantity.compareTo(inputQuantity) != 0) {
            throw new BizException("baps_quantity_verify_failed", inputQuantity, dbQuantity);
        }
        //每天的计划
        Map<String, List<CellPlanLineDTO>> dayPlanLineList = planLineList.stream().collect(Collectors.groupingBy(CellPlanLineDTO::sign));
        Map<String, List<HTraceLineInputDTO>> dayInputList = inputList.stream().collect(Collectors.groupingBy(HTraceLineInputDTO::sign));
        Collection<String> signCollection = CollectionUtils.union(dayPlanLineList.keySet(), dayInputList.keySet());
        //按天进行校验分配
        for (String sign : signCollection) {
            /**************************************** DB中的数据 ******************************************/
            List<CellPlanLineDTO> dbPlanLineList = dayPlanLineList.getOrDefault(sign, Lists.newArrayList());
            //DT不参与分配
            List<CellPlanLineDTO> dtList = dbPlanLineList.stream().filter(k -> Objects.equals(dtId, k.getCellSourceId())).collect(Collectors.toList());
            //H的数据
            List<CellPlanLineDTO> hList = dbPlanLineList.stream().filter(k -> Objects.equals(hId, k.getHTraceId())).collect(Collectors.toList());
            BigDecimal hQuantity = hList.stream().map(CellPlanLineDTO::getQtyPc).reduce(BigDecimal.ZERO, BigDecimal::add);
            //常规的数据
            List<CellPlanLineDTO> nonHList = dbPlanLineList.stream().filter(k -> Objects.equals(nonHId, k.getHTraceId())).collect(Collectors.toList());
            BigDecimal nonHQuantity = nonHList.stream().map(CellPlanLineDTO::getQtyPc).reduce(BigDecimal.ZERO, BigDecimal::add);
            /**************************************** 输入的数据 ******************************************/
            List<HTraceLineInputDTO> inputLineList = dayInputList.getOrDefault(sign, Lists.newArrayList());
            //DT不参与分配
            List<HTraceLineInputDTO> dtInputList = inputLineList.stream().filter(k -> Objects.equals(dtId, k.getCellSourceId())).collect(Collectors.toList());
            //H的数据
            List<HTraceLineInputDTO> hInputList = inputLineList.stream().filter(k -> Objects.equals(hId, k.getHTraceId())).collect(Collectors.toList());
            //兼容数据
            BigDecimal compatibleQuantity = hInputList.stream().filter(k -> StringUtils.equals(YesOrNoEnum.YES.getCode(), k.getHChangeFlag())).map(HTraceLineInputDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            //H数据
            BigDecimal hInputQuantity = hInputList.stream().filter(k -> StringUtils.equals(YesOrNoEnum.NO.getCode(), k.getHChangeFlag()) || StringUtils.isBlank(k.getHChangeFlag())).map(HTraceLineInputDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            //H总数 （H、兼容H）
            BigDecimal hInputTotalQuantity = compatibleQuantity.add(hInputQuantity);
            //常规的数据
            List<HTraceLineInputDTO> nonHInputList = inputLineList.stream().filter(k -> Objects.equals(nonHId, k.getHTraceId())).collect(Collectors.toList());
            BigDecimal nonHInputQuantity = nonHInputList.stream().map(HTraceLineInputDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            /**************************************** 校验 ******************************************/
            //兼容数量不能大于常规总量
            if (compatibleQuantity.compareTo(nonHQuantity) > 0) {
                throw new BizException("baps_compatible_quantity_too_large");
            }
            //输入的H总数 > db中的H总数 && 输入的常规总数 < db中的常规总数
            if (hInputTotalQuantity.compareTo(hQuantity) > 0 && nonHInputQuantity.compareTo(nonHQuantity) < 0) {
                //进行兼容
                if (compatibleQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    //兼容优先分配到同一个车间后，优先同一个电池类型，电池类型优先级：二分 -> 16BB -> 三分
                    nonHList.sort(Comparator.comparing(CellPlanLineDTO::getWorkshop).thenComparing(CellPlanLineDTO::getCellsType, Comparator.comparingInt(c -> c.contains("二分") ? Integer.MIN_VALUE : c.contains("16BB") ? Integer.MIN_VALUE + 1 : c.contains("三分") ? Integer.MIN_VALUE + 2 : Integer.MAX_VALUE)).thenComparing(CellPlanLineDTO::getQtyPc));
                    for (CellPlanLineDTO nonH : nonHList) {
                        BigDecimal qtyPc = nonH.getQtyPc();
                        if (!MathUtils.checkIsZero(compatibleQuantity, qtyPc)) {
                            //从常规中clone行进行修改H追溯字段，并将原行数据数量进行扣减
                            CellPlanLineDTO clone = SerializationUtils.clone(nonH);
                            if (compatibleQuantity.compareTo(qtyPc) >= 0) {
                                compatibleQuantity = compatibleQuantity.subtract(qtyPc);
                                nonH.setQtyPc(BigDecimal.ZERO);
                            } else {
                                clone.setQtyPc(compatibleQuantity);
                                nonH.setQtyPc(qtyPc.subtract(compatibleQuantity));
                                compatibleQuantity = BigDecimal.ZERO;
                            }
                            clone.setId(null);
                            clone.setHChangeFlag(YesOrNoEnum.YES.getCode());
                            clone.setHTrace(CommonConstant.H);
                            clone.setHTraceId(hId);
                            planLineList.add(clone);
                            changeLine.add(clone);
                            changeLine.add(nonH);
                        }
                    }
                }
            } else if (hInputTotalQuantity.compareTo(hQuantity) > 0) {
                if (nonHInputQuantity.compareTo(nonHQuantity) == 0) {
                    throw new BizException("baps_compatible_operation_error");
                } else if (nonHInputQuantity.compareTo(nonHQuantity) > 0) {
                    throw new BizException("baps_compatible_expand_quantity_error");
                }
            } else if (hInputTotalQuantity.compareTo(hQuantity) < 0) {
                throw new BizException("baps_compatible_reduce_quantity_error");
            }
        }
        return changeLine;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmData(List<HTraceLineDTO> hTraceLineList) {
        //数据转换，跟excel对象保持一致
        List<HTraceLineInputDTO> inputList = hTraceLineList.stream().map(HTraceLineInputDTO::build).flatMap(Collection::stream).collect(Collectors.toList());
        //校验数据
        verifyData(inputList);
        //数据维度：国内海外，月份，版本号
        Map<HTraceDimension, List<HTraceLineInputDTO>> dimensionList = inputList.stream().collect(Collectors.groupingBy(HTraceLineInputDTO::dimension));
        //根据维度查询数据
        Map<HTraceDimension, List<CellPlanLineDTO>> cellPlanLineMap = findDataByDimension(IterableUtils.toList(dimensionList.keySet()));
        List<CellPlanLineDTO> changeLines = Lists.newArrayList();
        //按维度进行校验总量是否超出
        dimensionList.forEach((dimension, list) -> {
            List<CellPlanLineDTO> planLineList = cellPlanLineMap.get(dimension);
            if (CollectionUtils.isNotEmpty(planLineList)) {
                //按天兼容计算
                changeLines.addAll(compatibleData(list, planLineList));
            } else {
                //不存在该维度下的数据
                log.error("数据维度不存在：{}", dimension);
                throw new BizException("baps_data_not_exist");
            }
        });
        Assert.notEmpty(changeLines, "baps_no_change_line");
        //变动行入库
        cellPlanLineService.saveChangeLines(changeLines);
    }


    /**
     * 根据维度查询数据
     *
     * @param dimensionList
     * @return
     */
    private Map<HTraceDimension, List<CellPlanLineDTO>> findDataByDimension(List<HTraceDimension> dimensionList) {
        Map<String, String> versionMap = Maps.newHashMap();
        dimensionList.forEach(dimension -> {
            versionMap.put(dimension.getMonth(), dimension.getVersion());
        });
        Long isOversea = dimensionList.stream().map(HTraceDimension::getIsOversea).distinct().findFirst().get();
        List<CellPlanLineDTO> dataList = cellPlanLineService.findByCondition(isOversea, versionMap);
        //分组
        return dataList.stream().collect(Collectors.groupingBy(HTraceDimension::build));
    }

    /**
     * 校验数据
     *
     * @param excelList
     */
    private void verifyData(List<HTraceLineInputDTO> excelList) {
        if (CollectionUtils.isEmpty(excelList)) {
            throw new BizException("baps_data_not_exist");
        }
        //只允许一个国内海外
        long count = excelList.stream().map(HTraceLineInputDTO::getIsOversea).distinct().count();
        if (count > 1) {
            throw new BizException("baps_isOversea_discrepancy_error");
        }
        List<String> errorList = Lists.newArrayList();
        //校验一个月份下只允许存在一个版本
        Map<String, List<String>> countMap = excelList.stream().collect(Collectors.groupingBy(HTraceLineInputDTO::getOldMonth, Collectors.mapping(HTraceLineInputDTO::getVersion, Collectors.toList())));
        countMap.forEach((month, list) -> {
            if (list.stream().distinct().count() > 1) {
                errorList.add(String.format("月份：%s，版本号只允许存在一个！", month));
            }
        });
        if (CollectionUtils.isNotEmpty(errorList)) {
            throw new BizException("baps_data_abnormality", StringUtils.join(errorList, "\n"));
        }
        excelList.forEach(HTraceLineInputDTO::convertDate);
        //转译
        ImportConvertUtils.convertField(excelList);
    }
}
