package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellPlanLine;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineH;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 入库计划H表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Repository
public interface CellPlanLineHRepository extends JpaRepository<CellPlanLineH, Long>, QuerydslPredicateExecutor<CellPlanLineH> {

}
