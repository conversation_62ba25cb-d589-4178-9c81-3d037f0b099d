package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellInstockPlanFinish;
import com.trinasolar.scp.baps.domain.entity.ErpWipIssue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 实际入库表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
@Repository
public interface CellInstockPlanFinishRepository extends JpaRepository<CellInstockPlanFinish, Long>, QuerydslPredicateExecutor<CellInstockPlanFinish> {
    //获取某个库存组织的数据最新更新时间
    @Query(value = "select * from  baps_cell_instock_plan_finish AS tb WHERE tb.organization_id = (:organizationId) AND tb.is_deleted=0 and tb.creation_date IS NOT NULL  ORDER BY  tb.creation_date DESC LIMIT 1", nativeQuery = true)
    CellInstockPlanFinish findLastUpdateObj(@Param("organizationId") Long organizationId);
}
