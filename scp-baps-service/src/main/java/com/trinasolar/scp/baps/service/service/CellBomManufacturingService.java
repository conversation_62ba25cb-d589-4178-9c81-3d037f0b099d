package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellBomManufacturingDTO;
import com.trinasolar.scp.baps.domain.query.CellBomManufacturingQuery;
import com.trinasolar.scp.baps.domain.save.CellBomManufacturingSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 制造BOM表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
public interface CellBomManufacturingService {
    /**
     * 分页获取制造BOM表
     *
     * @param query 查询对象
     * @return 制造BOM表分页对象
     */
    Page<CellBomManufacturingDTO> queryByPage(CellBomManufacturingQuery query);

    /**
     * 根据主键获取制造BOM表详情
     *
     * @param id 主键
     * @return 制造BOM表详情
     */
        CellBomManufacturingDTO queryById(Long id);

    /**
     * 保存或更新制造BOM表
     *
     * @param saveDTO 制造BOM表保存对象
     * @return 制造BOM表对象
     */
    CellBomManufacturingDTO save(CellBomManufacturingSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除制造BOM表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellBomManufacturingQuery query, HttpServletResponse response);

    void bomDataMake(Integer ieorgrade);
}

