package com.trinasolar.scp.baps.service.service.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellBomManufacturingDTO;
import com.trinasolar.scp.baps.domain.convert.CellBomManufacturingDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturing;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturingSummary;
import com.trinasolar.scp.baps.domain.entity.QCellBomManufacturing;
import com.trinasolar.scp.baps.domain.query.CellBomManufacturingQuery;
import com.trinasolar.scp.baps.domain.save.CellBomManufacturingSaveDTO;
import com.trinasolar.scp.baps.service.repository.CellBomManufacturingRepository;
import com.trinasolar.scp.baps.service.repository.CellBomManufacturingSummaryRepository;
import com.trinasolar.scp.baps.service.service.CellBomManufacturingService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

/**
 * 制造BOM表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Slf4j
@Service("cellBomManufacturingService")
@RequiredArgsConstructor
public class CellBomManufacturingServiceImpl implements CellBomManufacturingService {
    private static final QCellBomManufacturing qCellBomManufacturing = QCellBomManufacturing.cellBomManufacturing;

    private final CellBomManufacturingDEConvert convert;

    private final CellBomManufacturingRepository repository;

    private final CellBomManufacturingRepository cellBomManufacturingRepository;
    private final CellBomManufacturingSummaryRepository cellBomManufacturingSummaryRepository;

    private final CellBomManufacturingDEConvert cellBomManufacturingDEConvert;

    @Override
    public Page<CellBomManufacturingDTO> queryByPage(CellBomManufacturingQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellBomManufacturing> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellBomManufacturingQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellBomManufacturing.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getCellsType())){
            booleanBuilder.and(qCellBomManufacturing.cellsType.eq(query.getCellsType()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlace())){
            booleanBuilder.and(qCellBomManufacturing.basePlace.eq(query.getBasePlace()));
        }
        if(StringUtils.isNotEmpty(query.getMonth())){
            booleanBuilder.and(qCellBomManufacturing.month.eq(query.getMonth()));
        }
        if (query.getTotalLine() != null) {
            booleanBuilder.and(qCellBomManufacturing.totalLine.eq(query.getTotalLine()));
        }
        if (query.getUsageLine() != null) {
            booleanBuilder.and(qCellBomManufacturing.usageLine.eq(query.getUsageLine()));
        }
        if (query.getCapacityQuantity() != null) {
            booleanBuilder.and(qCellBomManufacturing.capacityQuantity.eq(query.getCapacityQuantity()));
        }
        if(StringUtils.isNotEmpty(query.getUnit())){
            booleanBuilder.and(qCellBomManufacturing.unit.eq(query.getUnit()));
        }
        if(StringUtils.isNotEmpty(query.getRemark())){
            booleanBuilder.and(qCellBomManufacturing.remark.eq(query.getRemark()));
        }
        if(StringUtils.isNotEmpty(query.getProcessCode())){
            booleanBuilder.and(qCellBomManufacturing.processCode.eq(query.getProcessCode()));
        }
        if (query.getProcessId() != null) {
            booleanBuilder.and(qCellBomManufacturing.processId.eq(query.getProcessId()));
        }
        if(StringUtils.isNotEmpty(query.getInstructionType())){
            booleanBuilder.and(qCellBomManufacturing.instructionType.eq(query.getInstructionType()));
        }
        if(StringUtils.isNotEmpty(query.getInstructionCode())){
            booleanBuilder.and(qCellBomManufacturing.instructionCode.eq(query.getInstructionCode()));
        }
        if(StringUtils.isNotEmpty(query.getVersion())){
            booleanBuilder.and(qCellBomManufacturing.version.eq(query.getVersion()));
        }
        if(StringUtils.isNotEmpty(query.getIsOversea())){
            booleanBuilder.and(qCellBomManufacturing.isOversea.eq(query.getIsOversea()));
        }
        if(StringUtils.isNotEmpty(query.getWorkUnit())){
            booleanBuilder.and(qCellBomManufacturing.workunit.eq(query.getWorkUnit()));
        }
        if(StringUtils.isNotEmpty(query.getManufacturing())){
            booleanBuilder.and(qCellBomManufacturing.manufacturing.eq(query.getManufacturing()));
        }
        if (query.getRate() != null) {
            booleanBuilder.and(qCellBomManufacturing.rate.eq(query.getRate()));
        }
        if (query.getStartDate() != null) {
            booleanBuilder.and(qCellBomManufacturing.startDate.eq(query.getStartDate()));
        }
        if (query.getEndDate() != null) {
            booleanBuilder.and(qCellBomManufacturing.endDate.eq(query.getEndDate()));
        }
        if (query.getQuantity() != null) {
            booleanBuilder.and(qCellBomManufacturing.quantity.eq(query.getQuantity()));
        }
        if (query.getFlag() != null) {
            booleanBuilder.and(qCellBomManufacturing.flag.eq(query.getFlag()));
        }
        if (query.getFinePercent() != null) {
            booleanBuilder.and(qCellBomManufacturing.finePercent.eq(query.getFinePercent()));
        }
        if(StringUtils.isNotEmpty(query.getGradeWorkDate())){
            booleanBuilder.and(qCellBomManufacturing.gradeWorkDate.eq(query.getGradeWorkDate()));
        }
        if(StringUtils.isNotEmpty(query.getLineName())){
            booleanBuilder.and(qCellBomManufacturing.lineName.eq(query.getLineName()));
        }
        if(StringUtils.isNotEmpty(query.getReliabilityCheck())){
            booleanBuilder.and(qCellBomManufacturing.reliabilityCheck.eq(query.getReliabilityCheck()));
        }
    }

    @Override
    public CellBomManufacturingDTO queryById(Long id) {
        CellBomManufacturing queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellBomManufacturingDTO save(CellBomManufacturingSaveDTO saveDTO) {
        CellBomManufacturing newObj = Optional.ofNullable(saveDTO.getId())
            .map(id -> repository.getOne(saveDTO.getId()))
            .orElse(new CellBomManufacturing());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellBomManufacturingQuery query, HttpServletResponse response) {
       List<CellBomManufacturingDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "制造BOM表", "制造BOM表", excelPara.getSimpleHeader(), excelData);
    }
    @Override
    public void bomDataMake(Integer ieorgrade) {
        List<CellBomManufacturingDTO> cellBomManufacturings = cellBomManufacturingDEConvert.toDto( cellBomManufacturingRepository.findByIeOrGrade(ieorgrade));
        Joiner joiner = Joiner.on(",").useForNull("null");
        Map<String, List<CellBomManufacturingDTO>> collect = cellBomManufacturings.stream().collect(Collectors.groupingBy(item -> {
            return joiner.join(item.getCellsType(), item.getWorkUnit(), item.getLineName(), item.getManufacturing(), item.getIeorgrade(), item.getReliabilityCheck(), item.getIsSingleGlass(), item.getIsDt(), item.getIsRegionalCountry(), item.getIsHChangeFlag(), item.getIsHTrace());
        }));
        List<CellBomManufacturingDTO> mergeDtos =  new ArrayList<>();
        for (List<CellBomManufacturingDTO> values : collect.values()) {
            mergeDtos.addAll(   merge(values ));
        }
        List<CellBomManufacturingSummary> cellBomManufacturingSummaries = cellBomManufacturingDEConvert.toCellBomManufacturingSummary(mergeDtos);
        //存储mergeDtos
        cellBomManufacturingSummaryRepository.deleteByIeOrGrade(ieorgrade);
        cellBomManufacturingSummaryRepository.saveAll(cellBomManufacturingSummaries);
    }
    private List<CellBomManufacturingDTO> merge(List<CellBomManufacturingDTO> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return Lists.newArrayList();
        }
        // 对datas依据开始时间升序排序，开始时间相同时依据结束时间升序排序
        datas.sort(Comparator.comparing(CellBomManufacturingDTO::getStartDate).thenComparing(CellBomManufacturingDTO::getEndDate)) ;
        //对datas集合里的连续时间段进行合并，希望得到 2024-02-01~2024-02-05，2024-02-07~2024-02-11
        List<CellBomManufacturingDTO> mergedTimePeriods = new ArrayList<>();
        //开始合并，合并后数据放到mergedTimePeriods集合里
        CellBomManufacturingDTO curTimePeriod = datas.get(0);
        if (datas.size() == 1){
            mergedTimePeriods.add(curTimePeriod);
        }else {
            for (int i = 1; i < datas.size(); i++) {
                CellBomManufacturingDTO nextTimePeriod = datas.get(i);
                if (nextTimePeriod.getStartDate().toLocalDate().isAfter(curTimePeriod.getEndDate().toLocalDate())) {

                        //如果nextTimePeriod不是 curTimePeriod.endTime的后一天，则将当前时间段加入到合并后的集合中
                        mergedTimePeriods.add(curTimePeriod);
                        //将当前时间段更新为下一个时间段
                        curTimePeriod = nextTimePeriod;
                        if (i == datas.size() - 1) {
                            //如果nextTimePeriod是最后一个时间段，则将当前时间段加入到合并后的集合中
                            mergedTimePeriods.add(curTimePeriod);
                        }

                } else {
                    //当前时间段与下一个时间段有连续，则将当前时间段的结束时间更新为下一个时间段的结束时间
                    //如果nextTimePeriod.endTime大于curTimePeriod.endTime，则将当前时间段的结束时间更新为nextTimePeriod.endTime
                    if (nextTimePeriod.getEndDate().toLocalDate().isAfter(curTimePeriod.getEndDate().toLocalDate())) {
                        curTimePeriod.setEndDate(  nextTimePeriod.getEndDate());
                    }
                    if (i == datas.size() - 1) {
                        //如果nextTimePeriod是最后一个时间段，则将当前时间段加入到合并后的集合中
                        mergedTimePeriods.add(curTimePeriod);
                    }
                }
            }
        }
        return mergedTimePeriods;
    }
}
