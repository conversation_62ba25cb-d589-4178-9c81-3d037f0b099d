package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.Cell5AItemCodeListDto;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanSummaryDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineSplitDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineSiliconTotalListQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineSaveDTO;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 入库计划表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
public interface CellPlanLineService {

    /**
     * 根据主键获取入库计划表详情
     *
     * @param id 主键
     * @return 入库计划表详情
     */
        CellPlanLineDTO queryById(Long id);

    /**
     * 保存或更新入库计划表
     *
     * @param saveDTO 入库计划表保存对象
     * @return 入库计划表对象
     */
    CellPlanLineDTO save(CellPlanLineSaveDTO saveDTO);
     Page<CellPlanLineDTO> queryByPage(CellPlanLineQuery query);
    /**
     * 根据主键逻辑删除入库计划表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    List<CellPlanLineDTO> queryByVersion(String version);

    List<CellPlanLineDTO> query(CellPlanLineQuery query, Pair<String, String> versions);


    List<CellPlanLineDTO> queryConfirm(CellPlanLineQuery query, Pair<String, String> versions);

    List<CellPlanLineDTO> queryConfirmDatas(CellPlanLineQuery query);

    List<CellPlanLineDTO>  query(CellPlanLineQuery query);

    List<CellPlanLineSplitDTO> querySplitList(CellPlanLineQuery query);

    List<CellPlanLineDTO> queryManyMonth(CellPlanLineQuery query);

    Pair<Boolean,Boolean>    checkHandle(CellPlanLineQuery query);
    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellPlanLineQuery query, HttpServletResponse response);


    Page<CellPlanLineDTO> getHandCellPlanLines(CellPlanLineSiliconTotalListQuery listQuery);

    Pair<String,String> getLastFinalVersion(CellPlanLineQuery query);

    Pair<String,String>  getLastVersion(CellPlanLineQuery query);

    //排产发版调用接口
    void summaryGroupByCellPlanLine(List<CellPlanLineDTO> planDTOList,String month);

    //bom进行5A料号匹配后进行回改投产计划数据
    @Transactional(rollbackFor = Exception.class)
    void changeDataBy5AMatch(Cell5AItemCodeListDto dto);

    Page<CellPlanLineDTO> queryTicketOpening(CellPlanLineQuery query);

    /**
     * 根据日期、生产基地、生产车间、月份、电池类型 查询明细数据
     */
    List<CellPlanLineDTO>queryMaxVersionByCellWip(CellPlanLineQuery query);


    void clearCacheByVersion(String version);

    List<CellPlanLineDTO>   queryNo5AData(CellPlanLineQuery toCellPlanLineQuery);

    List<CellPlanLineDTO> queryForMrp(CellPlanLineQuery query, Pair<String, String> versions, CellPlanLineQuery nextQuery, Pair<String, String> nextVersions);

    List<CellPlanLineDTO> queryByVersion(Pair<String, String> versions);

    List<CellPlanLineDTO> findByCondition(Long isOverseaId, Map<String, String> versionMap);

    List<CellPlanLineDTO> findByCondition(Long isOverseaId, String month, String version);


    List<CellInstockPlanSummaryDTO> findSummaryByMonth(CellPlanLineQuery query);

    void saveChangeLines(List<CellPlanLineDTO> changeLines);

    void match7AInfo(CellPlanLineQuery query);
}

