package com.trinasolar.scp.baps.service.service.system.impl;

import com.trinasolar.scp.baps.domain.dto.system.OrganizationDefinitionsDTO;
import com.trinasolar.scp.baps.domain.dto.system.OrganizationDefinitionsQuery;
import com.trinasolar.scp.baps.service.feign.SystemFeign;
import com.trinasolar.scp.baps.service.service.system.SystemService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.LovLineQuery;
import com.trinasolar.scp.common.api.enums.YesOrNoEnum;
import com.trinasolar.scp.common.api.util.Results;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: SystemServiceImpl
 * @date 2024/3/1 08:56
 */
@Service("systemService")
@AllArgsConstructor
public class SystemServiceImpl implements SystemService {

    private final SystemFeign systemFeign;

    @Override
    public <R, S> Map<R, S> findByLovCode(String lovCode, Function<LovLineDTO, R> mapper1, Function<LovLineDTO, S> mapper2) {
        ResponseEntity<Results<List<LovLineDTO>>> resultsResponseEntity = systemFeign.queryByCode(LovLineQuery.builder().code(lovCode).build());
        List<LovLineDTO> dataList = resultsResponseEntity.getBody().getData();
        return dataList.stream().collect(Collectors.toMap(mapper1, mapper2));
    }

    @Override
    public List<Long> findOrganizationIds() {
        ResponseEntity<Results<List<OrganizationDefinitionsDTO>>> resultsResponseEntity = systemFeign.listForAps(new OrganizationDefinitionsQuery());
        List<Long> orgIds = resultsResponseEntity.getBody().getData().stream().filter(k -> YesOrNoEnum.YES.getCode().equals(k.getCellsScpFlag())).map(OrganizationDefinitionsDTO::getOrganizationId).collect(Collectors.toList());
        return orgIds;
    }
}
