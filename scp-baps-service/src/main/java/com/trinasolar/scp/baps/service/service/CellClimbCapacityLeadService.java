package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellClimbCapacityLeadDTO;
import com.trinasolar.scp.baps.domain.entity.CellClimbCapacityLead;
import com.trinasolar.scp.baps.domain.query.CellClimbCapacityLeadQuery;
import com.trinasolar.scp.baps.domain.save.CellClimbCapacityLeadSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 爬坡产能可靠性验证表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-05 08:10:43
 */
public interface CellClimbCapacityLeadService {
    /**
     * 分页获取爬坡产能可靠性验证表
     *
     * @param query 查询对象
     * @return 爬坡产能可靠性验证表分页对象
     */
    Page<CellClimbCapacityLeadDTO> queryByPage(CellClimbCapacityLeadQuery query);

    /**
     * 根据主键获取爬坡产能可靠性验证表详情
     *
     * @param id 主键
     * @return 爬坡产能可靠性验证表详情
     */
        CellClimbCapacityLeadDTO queryById(Long id);

    /**
     * 保存或更新爬坡产能可靠性验证表
     *
     * @param saveDTO 爬坡产能可靠性验证表保存对象
     * @return 爬坡产能可靠性验证表对象
     */
    CellClimbCapacityLeadDTO save(CellClimbCapacityLeadSaveDTO saveDTO);

    List<CellClimbCapacityLeadDTO> batchUpdate(List<CellClimbCapacityLeadSaveDTO> saveDTOList);

    /**
     * 根据主键逻辑删除爬坡产能可靠性验证表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellClimbCapacityLeadQuery query, HttpServletResponse response);
    /**
     * 同步爬坡产能数据
     */
    void syncClimbCapacity();
}

