package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellProductionPlanTotal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 投产计划汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Repository
public interface CellProductionPlanTotalRepository extends JpaRepository<CellProductionPlanTotal, Long>, QuerydslPredicateExecutor<CellProductionPlanTotal> {
    @Query(value = "from CellProductionPlanTotal total where total.version = :version")
    List<CellProductionPlanTotal> selectByVersion(@Param("version") String version);
}
