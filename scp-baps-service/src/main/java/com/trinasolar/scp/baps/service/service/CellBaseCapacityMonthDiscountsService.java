package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityMonthDiscountsDTO;
import com.trinasolar.scp.baps.domain.entity.CellBaseCapacityMonthDiscounts;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityMonthDiscountsQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacityMonthDiscountsSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * IE产能打折月度（人力）表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-28 03:34:54
 */
public interface CellBaseCapacityMonthDiscountsService  extends  IExcelImportData {
    /**
     * 分页获取IE产能打折月度（人力）表
     *
     * @param query 查询对象
     * @return IE产能打折月度（人力）表分页对象
     */
    Page<CellBaseCapacityMonthDiscountsDTO> queryByPage(CellBaseCapacityMonthDiscountsQuery query);

    /**
     * 根据主键获取IE产能打折月度（人力）表详情
     *
     * @param id 主键
     * @return IE产能打折月度（人力）表详情
     */
        CellBaseCapacityMonthDiscountsDTO queryById(Long id);

    /**
     * 保存或更新IE产能打折月度（人力）表
     *
     * @param saveDTO IE产能打折月度（人力）表保存对象
     * @return IE产能打折月度（人力）表对象
     */
    CellBaseCapacityMonthDiscountsDTO save(CellBaseCapacityMonthDiscountsSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除IE产能打折月度（人力）表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellBaseCapacityMonthDiscountsQuery query, HttpServletResponse response);

    List<CellBaseCapacityMonthDiscountsDTO> findByMonth(String month);
}

