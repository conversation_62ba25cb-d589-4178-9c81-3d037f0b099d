package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.convert.CellVersionQopIeDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanIe;
import com.trinasolar.scp.baps.domain.entity.CellVersionQopIe;
import com.trinasolar.scp.baps.domain.entity.QCellVersionPlanIe;
import com.trinasolar.scp.baps.domain.entity.QCellVersionQopIe;
import com.trinasolar.scp.baps.domain.excel.CellVersionPlanIeExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellVersionQopIeExcelDTO;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.baps.domain.save.CellVersionQopIeSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.repository.CellVersionQopIeRepository;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.poi.util.LittleEndian;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * qop与ie对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:08
 */
@Slf4j
@Service("cellVersionQopIeService")
@RequiredArgsConstructor
public class CellVersionQopIeServiceImpl implements CellVersionQopIeService {
    private static final QCellVersionQopIe qCellVersionQopIe = QCellVersionQopIe.cellVersionQopIe;
    private final CellVersionQopIeDEConvert convert;
    private final CellVersionQopIeRepository repository;
    private final CellBaseCapacityService cellBaseCapacityService;
    private final CellGradeCapacityService cellGradeCapacityService;
    private final QopDetailsService qopDetailsService;
    private final CellConversionFactorService cellConversionFactorService;

    @Override
    public Page<CellVersionQopIeDTO> queryByPage(CellVersionQopIeQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellVersionQopIe> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellVersionQopIeQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellVersionQopIe.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellVersionQopIe.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOverseaName())) {
            booleanBuilder.and(qCellVersionQopIe.isOverseaName.eq(query.getIsOverseaName()));
        }
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qCellVersionQopIe.cellTypeId.eq(query.getCellTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellTypeName())) {
            booleanBuilder.and(qCellVersionQopIe.cellTypeName.eq(query.getCellTypeName()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellVersionQopIe.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshopName())) {
            booleanBuilder.and(qCellVersionQopIe.workshopName.eq(query.getWorkshopName()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellVersionQopIe.month.eq(query.getMonth()));
        }
        if (query.getRate() != null) {
            booleanBuilder.and(qCellVersionQopIe.rate.eq(query.getRate()));
        }
        if (query.getTarget() != null) {
            booleanBuilder.and(qCellVersionQopIe.target.eq(query.getTarget()));
        }
        if (query.getCapacity() != null) {
            booleanBuilder.and(qCellVersionQopIe.capacity.eq(query.getCapacity()));
        }
        if (query.getGap() != null) {
            booleanBuilder.and(qCellVersionQopIe.gap.eq(query.getGap()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qCellVersionQopIe.remark.eq(query.getRemark()));
        }
    }

    @Override
    public CellVersionQopIeDTO queryById(Long id) {
        CellVersionQopIe queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellVersionQopIeDTO save(CellVersionQopIeSaveDTO saveDTO) {
        CellVersionQopIe newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellVersionQopIe());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellVersionQopIeQuery query, HttpServletResponse response) {
        List<CellVersionQopIeDTO> cellVersionQopIeCollect=new ArrayList<>();
        Map<String, Object> mapData = makeReport(query);

        Object detailsObj=  mapData.get("details");
        if (detailsObj!=null){
            cellVersionQopIeCollect.addAll((List<CellVersionQopIeDTO>)detailsObj);
        }
        Object ieVersionObj=   mapData.get("ieVersion");
        String ieVersion="";
        if (ieVersionObj!=null){
            ieVersion=ieVersionObj.toString();
        }
        Object qopVersionObj=  mapData.get("qopVersion");
        String qopVersion="";
        if (qopVersionObj!=null){
            qopVersion=qopVersionObj.toString();
        }

        // dto数据转为ExcelData数据
        List<CellVersionQopIeExcelDTO> datas = convert.toExcelDTO(cellVersionQopIeCollect);
        ExcelPara excelPara=new ExcelPara();
        excelPara.addColumn("isOverseaName","国内/海外",0, 500.0);
        excelPara.addColumn("workshopName","生产车间",1,200.0);
        excelPara.addColumn("cellTypeName","电池类型",2,200.0);
        excelPara.addColumn("month","月份",3,100.0);
        excelPara.addColumn("ratePercent","qop利用率",4,200.0);
        excelPara.addColumn("target",StringUtils.isNotEmpty(qopVersion)?"QOP目标\n"+qopVersion:"QOP目标",5,100.0);
        excelPara.addColumn("capacity",StringUtils.isNotEmpty(ieVersion)?"IE产能\n"+ieVersion:"IE产能",6,100.0);
        excelPara.addColumn("gap","IE与QOP差异",7,100.0);
        List<List<Object>> excelData = ExcelUtils.getList(datas, excelPara);
        String fileName="版本对比QOP与IE产能";
        String sheetName = fileName;
        fileName = fileName + "_" + DateUtils.formatDate(new Date(), "yyyy-MM-dd+HH:mm:ss");
        ExcelUtils.exportEx(response, fileName, sheetName, excelPara.getSimpleHeader(), excelData);
    }
    private void setZero(CellGradeCapacityDTO dto){
        dto.setD1(BigDecimal.ZERO);
        dto.setD2(BigDecimal.ZERO);
        dto.setD3(BigDecimal.ZERO);
        dto.setD4(BigDecimal.ZERO);
        dto.setD5(BigDecimal.ZERO);
        dto.setD6(BigDecimal.ZERO);
        dto.setD7(BigDecimal.ZERO);
        dto.setD8(BigDecimal.ZERO);
        dto.setD9(BigDecimal.ZERO);
        dto.setD10(BigDecimal.ZERO);
        dto.setD11(BigDecimal.ZERO);
        dto.setD12(BigDecimal.ZERO);
        dto.setD13(BigDecimal.ZERO);
        dto.setD14(BigDecimal.ZERO);
        dto.setD15(BigDecimal.ZERO);
        dto.setD16(BigDecimal.ZERO);
        dto.setD17(BigDecimal.ZERO);
        dto.setD18(BigDecimal.ZERO);
        dto.setD19(BigDecimal.ZERO);
        dto.setD20(BigDecimal.ZERO);
        dto.setD21(BigDecimal.ZERO);
        dto.setD22(BigDecimal.ZERO);
        dto.setD23(BigDecimal.ZERO);
        dto.setD24(BigDecimal.ZERO);
        dto.setD25(BigDecimal.ZERO);
        dto.setD26(BigDecimal.ZERO);
        dto.setD27(BigDecimal.ZERO);
        dto.setD28(BigDecimal.ZERO);
        dto.setD29(BigDecimal.ZERO);
        dto.setD30(BigDecimal.ZERO);
        dto.setD31(BigDecimal.ZERO);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> makeReport(CellVersionQopIeQuery query) {
        String oldLang= MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        LovLineDTO overseaLov = null;
        if (query.getIsOverseaId() == null) {
            //默认读取国内数据
            overseaLov = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, OverseaConstant.INLAND);

        } else {
            overseaLov = LovUtils.get(query.getIsOverseaId());
        }
        query.setIsOverseaId(overseaLov.getLovLineId());
        query.setIsOverseaName(overseaLov.getLovName());
        // 准备电池类型--》电池型号的map集合开始
        //构建电池类型对应的型号map
        //电池类型名--》型号
        Map<String,LovLineDTO> mapTypeToSeries= MapStrutUtil.getMapCelltypeToCellModelLov();
       // 准备电池类型--》电池型号的map集合结束
        Map<String, Object> mapData = new HashMap<>();
        String ieVersion = "V" + query.getMonth() + "01";//IE数据版本

        //一、获取IE产能数据
        CellBaseCapacityQuery cellBaseCapacityQuery = convert.toCellBaseCapacityQuery(query);
        //电池型号Id
        Long cellModelId=  query.getCellTypeId();
        String cellModelName=null;
        if (cellModelId!=null){
            LovLineDTO lovLineDTO = LovUtils.get(cellModelId);
            if (lovLineDTO!=null){
                cellModelName=lovLineDTO.getLovName();
            }
        }
        cellBaseCapacityQuery.setCellsTypeId(null);
        cellBaseCapacityQuery.setWorkshopid(query.getWorkshopId());
        List<CellVersionQopIe> cellVersionQopIes = prepareIeData(query, mapTypeToSeries, cellBaseCapacityQuery, cellModelName);
        //二、获取爬坡产能数据
        //对查到的爬坡产能数据依据国内海外、车间、电池型号分组统计
        CellGradeCapacityQuery cellGradeCapacityQuery = convert.toCellGradeCapacityQuery(query);
        cellGradeCapacityQuery.setCellsTypeId(null);
        cellGradeCapacityQuery.setWorkshopid(query.getWorkshopId());
        List<CellVersionQopIe> cellVersionQopIesByGrade = prepareGradeData(query, mapTypeToSeries, cellModelName, cellGradeCapacityQuery);
        //三、爬坡数据与IE产能数据整合->合并
        List<CellVersionQopIe> ieAndGradeCollect = mergeIeAndGrade(cellVersionQopIes, cellVersionQopIesByGrade);
        //四、获取qop数据
        QopDetailsQuery sopDetailsQuery = convert.toQopDetailsQuery(query);
        List<CellVersionQopIe> cellVersionQopIeListByQop = prepareQopData(query, mapTypeToSeries, cellModelName, sopDetailsQuery);
        //五、对统计后的产能数据和Qop数据合并处理
        List<CellVersionQopIe> cellVersionQopIeCollect = mergeIeAndGradeAndQop(ieAndGradeCollect, cellVersionQopIeListByQop);
        //过滤掉0数据
        cellVersionQopIeCollect= filterZero(cellVersionQopIeCollect);
        //六、生成报表信息基础数据构建最终map
        makeMapResult(query, oldLang, mapData, ieVersion, cellVersionQopIeCollect);
        return mapData;
    }

    private   List<CellVersionQopIe> filterZero(List<CellVersionQopIe> cellVersionQopIeCollect) {
      return   Optional.ofNullable(cellVersionQopIeCollect).orElse(Lists.newArrayList()).stream().filter(cellVersionQopIe -> {
            return Optional.ofNullable(cellVersionQopIe.getCapacity()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO)>0 ||
                   Optional.ofNullable(cellVersionQopIe.getTarget()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO)>0;
        }).collect(Collectors.toList());

    }

    /**
     * 生成报表信息基础数据构建最终map
     * @param query
     * @param oldLang
     * @param mapData
     * @param ieVersion
     * @param cellVersionQopIeCollect
     */
    private void makeMapResult(CellVersionQopIeQuery query, String oldLang, Map<String, Object> mapData, String ieVersion, List<CellVersionQopIe> cellVersionQopIeCollect) {
        AtomicReference<BigDecimal> capacityAll = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> targetCapacityAll = new AtomicReference<>(BigDecimal.ZERO);
        cellVersionQopIeCollect.stream().forEach(cellVersionQopIe -> {

            if (cellVersionQopIe.getCapacity() != null) {
                capacityAll.set(capacityAll.get().add(cellVersionQopIe.getCapacity()));
            }
            if (cellVersionQopIe.getTarget() != null) {

                targetCapacityAll.set(targetCapacityAll.get().add(cellVersionQopIe.getTarget()));
            }
            if (cellVersionQopIe.getTarget() != null && cellVersionQopIe.getCapacity() != null ) {
               if (BigDecimal.ZERO.compareTo(cellVersionQopIe.getTarget())!=0){
                   cellVersionQopIe.setRate(cellVersionQopIe.getCapacity().divide(cellVersionQopIe.getTarget(), 4, RoundingMode.HALF_UP));
               }
                cellVersionQopIe.setGap(cellVersionQopIe.getCapacity().subtract(cellVersionQopIe.getTarget()));
            }
        });
        CellVersionQopIe cellVersionQopIeAll = new CellVersionQopIe();
        cellVersionQopIeAll.setWorkshopName((query.getIsOverseaName() == null ? "" : query.getIsOverseaName()) );
        cellVersionQopIeAll.setCapacity(capacityAll.get());
        cellVersionQopIeAll.setTarget(targetCapacityAll.get());
        if (cellVersionQopIeAll.getTarget() != null && cellVersionQopIeAll.getCapacity() != null) {
          if ( cellVersionQopIeAll.getTarget().compareTo(BigDecimal.ZERO) != 0 ){
              cellVersionQopIeAll.setRate(cellVersionQopIeAll.getCapacity().divide(cellVersionQopIeAll.getTarget(), 4, RoundingMode.HALF_UP));
          }

            cellVersionQopIeAll.setGap(cellVersionQopIeAll.getCapacity().subtract(cellVersionQopIeAll.getTarget()));
        }

        cellVersionQopIeCollect.stream().forEach(cellVersionQopIe -> {
            cellVersionQopIe.setMonth(query.getMonth());
            cellVersionQopIe.setCapacity(null==cellVersionQopIe.getCapacity()?BigDecimal.ZERO:cellVersionQopIe.getCapacity());
            cellVersionQopIe.setGap(null==cellVersionQopIe.getGap()?BigDecimal.ZERO:cellVersionQopIe.getGap());
        });
        List<CellVersionQopIeDTO> details = convert.toDto(cellVersionQopIeCollect);
        CellVersionQopIeDTO firstRow = convert.toDto(cellVersionQopIeAll);
        //对报表基础信息汇总
        //国内外电池类型维度分组

        Map<String, List<CellVersionQopIe>> collectCellVersionQopIe = cellVersionQopIeCollect.stream().collect(Collectors.groupingBy(CellVersionQopIe::getCellTypeName));
        List<CellVersionQopIe> datas = new ArrayList<>();
        collectCellVersionQopIe.entrySet().forEach(cellsTypeCellVersionQopIe -> {
            String celltypeName = cellsTypeCellVersionQopIe.getKey();
            AtomicReference<BigDecimal> capcity = new AtomicReference<>(BigDecimal.ZERO);
            AtomicReference<BigDecimal> targetCapcity = new AtomicReference<>(BigDecimal.ZERO);
            cellsTypeCellVersionQopIe.getValue().stream().forEach(cellVersionQopIe -> {
                if (cellVersionQopIe.getCapacity() != null) {
                    capcity.set(capcity.get().add(cellVersionQopIe.getCapacity()));
                }
                if (cellVersionQopIe.getTarget() != null) {
                    targetCapcity.set(targetCapcity.get().add(cellVersionQopIe.getTarget()));
                }

            });
            CellVersionQopIe cellVersionQopIeByCelltype = new CellVersionQopIe();
            cellVersionQopIeByCelltype.setMonth(query.getMonth());
            cellVersionQopIeByCelltype.setCellTypeName(celltypeName);
            cellVersionQopIeByCelltype.setTarget(targetCapcity.get());
            cellVersionQopIeByCelltype.setCapacity(capcity.get());
            if (targetCapcity.get() != null  && capcity.get() != null) {
                if (targetCapcity.get().compareTo(BigDecimal.ZERO) != 0){
                    cellVersionQopIeByCelltype.setRate(capcity.get().divide(targetCapcity.get(), 2, RoundingMode.HALF_UP));
                }

                cellVersionQopIeByCelltype.setGap(capcity.get().subtract(targetCapcity.get()));
            }
            datas.add(cellVersionQopIeByCelltype);

        });
        mapData.put("ieVersion", ieVersion);
        String qopVersion = "V" + query.getMonth() + "01";
        mapData.put("qopVersion", qopVersion);
        Collections.sort(details, new Comparator<CellVersionQopIeDTO>() {
            @Override
            public int compare(CellVersionQopIeDTO o1, CellVersionQopIeDTO o2) {
                if (Objects.equals(o1.getWorkshopName(),o2.getWorkshopName())){
                    return o1.getCellTypeName().compareTo(o2.getCellTypeName());
                }else{
                    return o1.getWorkshopName().compareTo(o2.getWorkshopName());
                }
            }
        });
        List<CellVersionQopIeDTO> datasByCellType = convert.toDto(datas);
        Collections.sort(datasByCellType, new Comparator<CellVersionQopIeDTO>() {
            @Override
            public int compare(CellVersionQopIeDTO o1, CellVersionQopIeDTO o2) {

                    return o1.getCellTypeName().compareTo(o2.getCellTypeName());


            }
        });
        MyThreadLocal.get().setLang(oldLang);
        //翻译第一行
        String workshopName = firstRow.getWorkshopName();
        workshopName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,workshopName);
        firstRow.setWorkshopName( workshopName);
        details.add(0,firstRow);
        details.addAll(datasByCellType);
        //略过第一行，翻译details
        details.stream().skip(1).forEach(cellVersionQopIeDTO -> {
            String cellTypeName = cellVersionQopIeDTO.getCellTypeName();
            if (StringUtils.isNotEmpty(cellTypeName)){
                cellTypeName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AOP_CELL_SERIES,cellTypeName);
                cellVersionQopIeDTO.setCellTypeName(cellTypeName);
            }
             String myWorkshopName = cellVersionQopIeDTO.getWorkshopName();
            if (StringUtils.isNotEmpty(myWorkshopName)){
                myWorkshopName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,myWorkshopName);
                cellVersionQopIeDTO.setWorkshopName(myWorkshopName);
            }

        });


        mapData.put("details",details);
    }

    /**
     * 对统计后的产能数据和Qop数据合并处理
     * @param ieAndGradeCollect
     * @param cellVersionQopIeListByQop
     * @return
     */
    private List<CellVersionQopIe> mergeIeAndGradeAndQop(List<CellVersionQopIe> ieAndGradeCollect, List<CellVersionQopIe> cellVersionQopIeListByQop) {
        ieAndGradeCollect.stream().forEach(cellVersionQopIe -> {
            int index = cellVersionQopIeListByQop.indexOf(cellVersionQopIe);
            if (index > -1) {
                cellVersionQopIe.setTarget(cellVersionQopIeListByQop.get(index).getTarget());
            }
        });
        List<CellVersionQopIe> collectByQop = cellVersionQopIeListByQop.stream().filter(cellVersionQopIe -> {
            return !ieAndGradeCollect.contains(cellVersionQopIe);
        }).collect(Collectors.toList());
        List<CellVersionQopIe> cellVersionQopIeCollect = Stream.concat(ieAndGradeCollect.stream(), collectByQop.stream()).collect(Collectors.toList());
        return cellVersionQopIeCollect;
    }

    /**
     * 准备Qop数据
     * @param query
     * @param mapTypeToSeries
     * @param cellModelName
     * @param sopDetailsQuery
     * @return
     */
    private List<CellVersionQopIe> prepareQopData(CellVersionQopIeQuery query, Map<String, LovLineDTO> mapTypeToSeries, String cellModelName, QopDetailsQuery sopDetailsQuery) {
        //依据国内海外、车间、电池类型、月份（必须项）查询SOP数据
        sopDetailsQuery.setCellTypeId(null);
        sopDetailsQuery.setCellTypeName(null);
        List<QopDetailsDTO> qopDetailsDTOS = qopDetailsService.queryByPage(sopDetailsQuery);
        if (qopDetailsDTOS == null) {
            throw new BizException(query.getMonth() + "月还没有QOP数据，无法版本对比");
        }else
        {
            qopDetailsDTOS.stream().forEach(item->{
                item.setSopQty(item.getSopQty().setScale(0,RoundingMode.HALF_UP));
                String cellTypeName= item.getCellTypeName();
                LovLineDTO lovLineDTO = mapTypeToSeries.get(cellTypeName);
                if (lovLineDTO==null){
                    throw  new RuntimeException(cellTypeName+"没有对应的电池型号");
                }
                String modelName=lovLineDTO.getLovName();
                Long modelId=lovLineDTO.getLovLineId();
                item.setCellTypeId(modelId);
                item.setCellTypeName(modelName);
            });
            String finalCellModelName2 = cellModelName;
            if (StringUtils.isNotEmpty(cellModelName)){
                qopDetailsDTOS=  qopDetailsDTOS.stream().filter(item->{
                    return item.getCellTypeName().equals(finalCellModelName2);
                }).collect(Collectors.toList());
            }

        }
        //对查到的计划数据依据国内海外、车间、电池类型分组统计
        //
        Map<String, Map<String, Map<String, List<QopDetailsDTO>>>> qopDetailsDTOSCollect =
                qopDetailsDTOS.stream().collect(Collectors.groupingBy(QopDetailsDTO::getIsOverseaName,
                        Collectors.groupingBy(QopDetailsDTO::getWorkshopName,
                                Collectors.groupingBy(QopDetailsDTO::getCellTypeName))));

        List<CellVersionQopIe> cellVersionQopIeListByQop = new ArrayList<>();
        qopDetailsDTOSCollect.entrySet().stream().forEach(entryOversea -> {
            String oversea = entryOversea.getKey();
            entryOversea.getValue().entrySet().stream().forEach(entryWorkshop -> {
                String workshop = entryWorkshop.getKey();
                entryWorkshop.getValue().entrySet().stream().forEach(entryCelltype -> {
                    String celltype = entryCelltype.getKey();
                    CellVersionQopIe cellVersionQopIe = new CellVersionQopIe();
                    cellVersionQopIe.setIsOverseaName(oversea);
                    cellVersionQopIe.setWorkshopName(workshop);
                    cellVersionQopIe.setCellTypeName(celltype);
                    AtomicReference<BigDecimal> qopQty = new AtomicReference<>(BigDecimal.ZERO);
                    entryCelltype.getValue().stream().forEach(qopDetailsDTO -> {
                        BigDecimal qtyThousandPc = qopDetailsDTO.getSopQty();
                        if (qtyThousandPc != null) {
                            qopQty.set(qopQty.get().add(qtyThousandPc));
                        }
                    });
                    cellVersionQopIe.setCapacity(BigDecimal.ZERO);
                    cellVersionQopIe.setTarget(qopQty.get());
                    cellVersionQopIeListByQop.add(cellVersionQopIe);
                });
            });

        });
        return cellVersionQopIeListByQop;
    }

    /**
     * 合并IE和爬坡产能数据
     * @param cellVersionQopIes
     * @param cellVersionQopIesByGrade
     * @return
     */
    private List<CellVersionQopIe> mergeIeAndGrade(List<CellVersionQopIe> cellVersionQopIes, List<CellVersionQopIe> cellVersionQopIesByGrade) {
        cellVersionQopIes.stream().forEach(cellVersionQopIe -> {
            int index = cellVersionQopIesByGrade.indexOf(cellVersionQopIe);
            if (index > -1) {
                CellVersionQopIe cellVersionQopIeInGrade = cellVersionQopIesByGrade.get(index);
                if (cellVersionQopIe.getCapacity() != null) {
                    if (cellVersionQopIeInGrade.getCapacity() != null) {
                        cellVersionQopIe.setCapacity(cellVersionQopIe.getCapacity().add(cellVersionQopIeInGrade.getCapacity()));
                    }
                } else {
                    if (cellVersionQopIeInGrade.getCapacity() != null) {
                        cellVersionQopIe.setCapacity(cellVersionQopIeInGrade.getCapacity());
                    }
                }

            }
        });

        //考虑爬坡数据在IE产能没有对应的特殊情况（也要合并）
        List<CellVersionQopIe> cellGradecollect = cellVersionQopIesByGrade.stream().filter(cellVersionQopIe -> {
            return !cellVersionQopIes.contains(cellVersionQopIe);
        }).collect(Collectors.toList());
        //最终IE和Grade合并后的数据
        List<CellVersionQopIe> ieAndGradeCollect = Stream.concat(cellVersionQopIes.stream(), cellGradecollect.stream()).collect(Collectors.toList());

        ieAndGradeCollect.stream().forEach(item->{
            if (item.getCapacity()!=null){
                item.setCapacity(item.getCapacity().setScale(0,RoundingMode.HALF_UP));
            }

        });
        log.info("ieAndGradeCollect:" + ieAndGradeCollect);
        return ieAndGradeCollect;
    }

    /**
     * 获取爬坡产能数据
     * @param query
     * @param mapTypeToSeries
     * @param cellModelName
     * @param cellGradeCapacityQuery
     * @return
     */
    private List<CellVersionQopIe> prepareGradeData(CellVersionQopIeQuery query, Map<String, LovLineDTO> mapTypeToSeries, String cellModelName, CellGradeCapacityQuery cellGradeCapacityQuery) {
        Page<CellGradeCapacityDTO> pageCellGradeCapacityDTOS = cellGradeCapacityService.queryByPage(cellGradeCapacityQuery);

        List<CellGradeCapacityDTO> cellGradeCapacityDTOS = null;
        if (pageCellGradeCapacityDTOS != null) {
            cellGradeCapacityDTOS = pageCellGradeCapacityDTOS.getContent();
            // 爬坡产能/兆瓦系数
            cellGradeCapacityDTOS.stream().forEach(grade -> {
                CellConversionFactorDTO cellConversionFactorDTO = cellConversionFactorService.queryByCellsType(grade.getIsOversea(),grade.getCellsType());
                if (cellConversionFactorDTO==null) {
                    setZero(grade);
                } else {
                    if (BigDecimal.ZERO.compareTo( cellConversionFactorDTO.getConversionFactor())!=0){
                        for (int i = 1; i <=31 ; i++) {
                          BigDecimal val=   ReflectUtil.invoke(grade,"getD"+i);
                          if (val!=null&&BigDecimal.ZERO.compareTo(val)!=0){
                              ReflectUtil.invoke(grade,"setD"+i,val.divide(cellConversionFactorDTO.getConversionFactor(), 6, RoundingMode.HALF_UP));
                          }
                        }
                    }else {
                        setZero(grade);
                    }

                }

            });
            //电池型号处理
            cellGradeCapacityDTOS.stream().forEach(item->{
                String cellsType=item.getCellsType();
                item.setOldCellsType(cellsType);
                LovLineDTO lovLineDTO= mapTypeToSeries.get(cellsType);
                if (lovLineDTO==null){
                    throw  new RuntimeException(cellsType+"没有对应的电池型号");
                }
                String modelName=lovLineDTO.getLovName();
                Long  modelId=lovLineDTO.getLovLineId();
                item.setCellsType(modelName);
                item.setCellsTypeId(modelId);
            });
            if (StringUtils.isNotEmpty(cellModelName)){
                String finalCellModelName1 = cellModelName;
                cellGradeCapacityDTOS=   cellGradeCapacityDTOS.stream().filter(item->{
                    return item.getCellsType().equals(finalCellModelName1);
                }).collect(Collectors.toList());
            }
        }
        //爬坡数据筛选，爬坡数据没有国内海外的维度，需要自行筛选处理
        if (StringUtils.isNotEmpty(query.getIsOverseaName())) {
            Long id = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOverseaName()).getLovLineId();
            cellGradeCapacityDTOS = cellGradeCapacityDTOS.stream().filter(data -> {
                String basePlace = data.getBasePlace();
                if (StringUtils.isNotEmpty(basePlace)) {
                    LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, basePlace);
                    if (lovLineDTO != null && StringUtils.isNotEmpty(lovLineDTO.getAttribute2())) {
                        return id.toString().equals(lovLineDTO.getAttribute2());
                    }

                }
                return false;
            }).collect(Collectors.toList());
        }

        Map<String, Map<String, List<CellGradeCapacityDTO>>> collectCellGradeCapacityDTOS = cellGradeCapacityDTOS.stream().collect(
                Collectors.groupingBy(CellGradeCapacityDTO::getWorkshop,
                        Collectors.groupingBy(CellGradeCapacityDTO::getCellsType)));
        List<CellVersionQopIe> cellVersionQopIesByGrade = new ArrayList<>();
        collectCellGradeCapacityDTOS.entrySet().stream().forEach(workshopEntry -> {
            workshopEntry.getValue().entrySet().stream().forEach(cellSeriesEntry -> {
                AtomicReference<BigDecimal> maxCapacity = new AtomicReference<>(BigDecimal.ZERO);
                CellVersionQopIe cellVersionQopIe = null;
                Map<String, List<CellGradeCapacityDTO>> oldCellsTypeMap = cellSeriesEntry.getValue().stream().collect(Collectors.groupingBy(CellGradeCapacityDTO::getOldCellsType));
                Set<Map.Entry<String, List<CellGradeCapacityDTO>>> workunitMapEntry =oldCellsTypeMap.entrySet();
                for(Map.Entry<String, List<CellGradeCapacityDTO>> workunitEntry:workunitMapEntry )
                 {
                     AtomicReference<BigDecimal> capacity = new AtomicReference<>(BigDecimal.ZERO);
                    for(CellGradeCapacityDTO cellGradeCapacity: workunitEntry.getValue())
                    {
                        for (int day = 1; day <= 31; day++) {
                            if (cellVersionQopIe==null){
                                cellVersionQopIe= BeanUtil.copyProperties(cellGradeCapacity,CellVersionQopIe.class);
                                cellVersionQopIe.setCellTypeId(cellGradeCapacity.getCellsTypeId());
                                cellVersionQopIe.setWorkshopId(cellGradeCapacity.getWorkshopid());
                                cellVersionQopIe.setWorkshopName(cellGradeCapacity.getWorkshop());
                                cellVersionQopIe.setCellTypeName(cellGradeCapacity.getCellsType());;
                                cellVersionQopIe.setIsOverseaId(cellGradeCapacity.getIsOverseaId());
                                cellVersionQopIe.setIsOverseaName(cellGradeCapacity.getIsOversea());
                            }
                            Method method = ReflectUtil.getMethod(CellGradeCapacityDTO.class, "getD" + day);
                            BigDecimal dayCapacity = ReflectUtil.invoke(cellGradeCapacity, method);
                            if (dayCapacity != null) {
                                if (capacity.get() == null) {
                                    capacity.set(BigDecimal.ZERO);
                                }
                                capacity.set(capacity.get().add(dayCapacity.multiply(cellGradeCapacity.getLineNumber())));
                            }
                        }


                    };
                    if (capacity.get().compareTo(maxCapacity.get())>0){
                        maxCapacity.set(capacity.get());
                    }
                }
                cellVersionQopIe.setTarget(BigDecimal.ZERO);
                cellVersionQopIe.setCapacity(maxCapacity.get());
                cellVersionQopIesByGrade.add(cellVersionQopIe);

            });
        });
        log.info("grade:" + cellVersionQopIesByGrade);
        return cellVersionQopIesByGrade;
    }

    /**
     *  获取IE产能数据
     * @param query
     * @param mapTypeToSeries
     * @param cellBaseCapacityQuery
     * @param cellModelName
     * @return
     */
    private List<CellVersionQopIe> prepareIeData(CellVersionQopIeQuery query, Map<String, LovLineDTO> mapTypeToSeries, CellBaseCapacityQuery cellBaseCapacityQuery, String cellModelName) {
        Page<CellBaseCapacityDTO> pageCellBaseCapacityDTOS = cellBaseCapacityService.queryByPage(cellBaseCapacityQuery);
        List<CellBaseCapacityDTO> cellBaseCapacityDTOS = null;
        if (pageCellBaseCapacityDTOS != null) {
            cellBaseCapacityDTOS = pageCellBaseCapacityDTOS.getContent();
            // IE产能/兆瓦系数
            cellBaseCapacityDTOS.stream().forEach(grade -> {
                CellConversionFactorDTO cellConversionFactorDTO = cellConversionFactorService.queryByCellsType(grade.getIsOversea(),grade.getCellsType());
                if (cellConversionFactorDTO==null) {
                    grade.setSingleCapacity(BigDecimal.ZERO);
                } else {
                    if (BigDecimal.ZERO.compareTo( cellConversionFactorDTO.getConversionFactor())!=0){
                        grade.setSingleCapacity(grade.getSingleCapacity().divide(cellConversionFactorDTO.getConversionFactor(), 6, RoundingMode.HALF_UP));
                    }else {
                        grade.setSingleCapacity(BigDecimal.ZERO);
                    }

                }

            });
            //电池型号准备
            cellBaseCapacityDTOS.stream().forEach(item->{

                String cellsType=item.getCellsType();
                item.setOldCellsType(cellsType);
                LovLineDTO lovLineDTO= mapTypeToSeries.get(cellsType);
                if (lovLineDTO==null){
                    throw  new RuntimeException(cellsType+"没有对应的电池型号");
                }
                String modelName=lovLineDTO.getLovName();
                Long  modelId=lovLineDTO.getLovLineId();
                item.setCellsType(modelName);
                item.setCellsTypeId(modelId);
            });
            String finalCellModelName = cellModelName;
            if (StringUtils.isNotEmpty(finalCellModelName)){
                cellBaseCapacityDTOS=   cellBaseCapacityDTOS.stream().filter(item->{
                    return item.getCellsType().equals(finalCellModelName);
                }).collect(Collectors.toList());
            }

        }
        //依据国内海外、电池类型、车间、月份（必须项）查询IE产能
        //对查到的IE产能数据依据国内海外、车间、电池型号分组统计
        Map<String, Map<String, Map<String, List<CellBaseCapacityDTO>>>> collectCellBaseCapacityDTO = cellBaseCapacityDTOS.stream().collect(
                Collectors.groupingBy(CellBaseCapacityDTO::getIsOversea,
                        Collectors.groupingBy(CellBaseCapacityDTO::getWorkshop,
                                Collectors.groupingBy(CellBaseCapacityDTO::getCellsType))));

        List<CellVersionQopIe> cellVersionQopIes = new ArrayList<>();
        collectCellBaseCapacityDTO.entrySet().stream().forEach(entryIsOversea -> {
            String oversea = entryIsOversea.getKey();
            entryIsOversea.getValue().entrySet().stream().forEach(entryWorkshop -> {
                String workshop = entryWorkshop.getKey();
                entryWorkshop.getValue().entrySet().stream().forEach(entryCellSeries -> {
                    String cellType = entryCellSeries.getKey();
                    CellVersionQopIe cellVersionQopIe = new CellVersionQopIe();
                    cellVersionQopIe.setIsOverseaName(oversea);
                    cellVersionQopIe.setWorkshopName(workshop);
                    cellVersionQopIe.setCellTypeName(cellType);//电池型号
                    AtomicReference<BigDecimal> maxCapacity = new AtomicReference<>(BigDecimal.ZERO);
                    Map<String, List<CellBaseCapacityDTO>> oldCellTypeMap = entryCellSeries.getValue().stream().collect(Collectors.groupingBy(CellBaseCapacityDTO::getOldCellsType));
                    oldCellTypeMap.entrySet().stream().forEach(entryWorkunit -> {
                        String workunit = entryWorkunit.getKey();
                        AtomicReference<BigDecimal> capacity = new AtomicReference<>(BigDecimal.ZERO);
                        entryWorkunit.getValue().stream().forEach(cellBaseCapacityDTO -> {
                            //当前月的第一天
                            LocalDate firstDay = DateUtil.getLocalDate(query.getMonth(), 1);
                            //当前月的最后一天
                            LocalDate lastDay = firstDay.with(TemporalAdjusters.lastDayOfMonth());
                            //Ie产能的开始时间
                            LocalDate startDate = cellBaseCapacityDTO.getStartTime();
                            //Ie产能的结束时间
                            LocalDate endDate = cellBaseCapacityDTO.getEndTime();
                            if (startDate.isBefore(firstDay)) {
                                startDate = firstDay;
                            }
                            if (endDate.isAfter(lastDay)) {
                                endDate = lastDay;
                            }
                            long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
                            if (capacity.get() == null) {
                                capacity.set(BigDecimal.ZERO);
                            }
                            capacity.set(capacity.get().add(cellBaseCapacityDTO.getSingleCapacity().multiply(BigDecimal.valueOf(days)).multiply(cellBaseCapacityDTO.getUsageLine())));

                        });
                        //capacity大于maxCapacity
                        if (capacity.get().compareTo(maxCapacity.get()) > 0) {
                            maxCapacity.set(capacity.get());
                        }
                   });
                    cellVersionQopIe.setTarget(BigDecimal.ZERO);
                    cellVersionQopIe.setCapacity(maxCapacity.get());
                    cellVersionQopIes.add(cellVersionQopIe);
                });
            });
        });
        return cellVersionQopIes;
    }


}
