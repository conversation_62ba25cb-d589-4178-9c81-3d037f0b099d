package com.trinasolar.scp.baps.service.service.impl;

import com.trinasolar.scp.baps.domain.dto.PowerEfficiencyDTO;
import com.trinasolar.scp.baps.domain.query.PowerEfficiencyQuery;
import com.trinasolar.scp.baps.service.feign.PowerEfficiencyCnFeign;
import com.trinasolar.scp.baps.service.feign.PowerEfficiencyFeign;
import com.trinasolar.scp.baps.service.service.CellWorkshopPriorityTargetService;
import com.trinasolar.scp.baps.service.service.PowerEfficiencyService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(value = "powerEfficiencyService")
public class PowerEfficiencyServiceImpl implements PowerEfficiencyService {
    @Autowired
    private PowerEfficiencyFeign powerEfficiencyFeign;
    @Autowired
    private PowerEfficiencyCnFeign powerEfficiencyCNFeign;
    @Lazy
    @Autowired
    private CellWorkshopPriorityTargetService cellWorkshopPriorityTargetService;

    @Override
    public List<PowerEfficiencyDTO> queryByPage() {
        PowerEfficiencyQuery query = new PowerEfficiencyQuery();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        return powerEfficiencyFeign.queryByPage(query).getBody().getData();
    }
    @Override
    public List<PowerEfficiencyDTO> queryByPageCN() {
        PowerEfficiencyQuery query = new PowerEfficiencyQuery();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        return powerEfficiencyCNFeign.queryByPage(query).getBody().getData();
    }
    @Override
    public List<PowerEfficiencyDTO> queryWithRelations(PowerEfficiencyQuery query) {
        return powerEfficiencyFeign.queryByPage(query).getBody().getData();
    }

    @Override
    public void refreshData() {
        cellWorkshopPriorityTargetService.refreshData();
    }
}
