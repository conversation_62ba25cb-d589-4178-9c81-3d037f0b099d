package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.ErpWipIssueDTO;
import com.trinasolar.scp.baps.domain.entity.ErpWipIssue;
import com.trinasolar.scp.baps.domain.query.ErpWipIssueQuery;
import com.trinasolar.scp.baps.domain.save.ErpWipIssueSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Erp实际入库来源表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:23:58
 */
public interface ErpWipIssueService {
    /**
     * 分页获取Erp实际入库来源表
     *
     * @param query 查询对象
     * @return Erp实际入库来源表分页对象
     */
    Page<ErpWipIssueDTO> queryByPage(ErpWipIssueQuery query);

    /**
     * 根据主键获取Erp实际入库来源表详情
     *
     * @param id 主键
     * @return Erp实际入库来源表详情
     */
        ErpWipIssueDTO queryById(Long id);

    /**
     * 保存或更新Erp实际入库来源表
     *
     * @param saveDTO Erp实际入库来源表保存对象
     * @return Erp实际入库来源表对象
     */
    ErpWipIssueDTO save(ErpWipIssueSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除Erp实际入库来源表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(ErpWipIssueQuery query, HttpServletResponse response);
}

