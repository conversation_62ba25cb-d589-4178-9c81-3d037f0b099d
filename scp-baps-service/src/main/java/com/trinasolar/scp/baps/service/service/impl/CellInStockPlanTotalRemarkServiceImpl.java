package com.trinasolar.scp.baps.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.convert.CellInStockPlanTotalRemarkDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellInStockPlanTotalRemarkDTO;
import com.trinasolar.scp.baps.domain.query.CellInStockPlanTotalRemarkQuery;
import com.trinasolar.scp.baps.service.repository.CellInStockPlanTotalRemarkRepository;
import com.trinasolar.scp.baps.service.service.CellInStockPlanTotalRemarkService;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.trinasolar.scp.baps.domain.entity.QCellInStockPlanTotalRemark.cellInStockPlanTotalRemark;

@Service
public class CellInStockPlanTotalRemarkServiceImpl implements CellInStockPlanTotalRemarkService {

    @Resource
    private CellInStockPlanTotalRemarkRepository repository;

    @Resource
    private CellInStockPlanTotalRemarkDEConvert convert;

    @Override
    public List<CellInStockPlanTotalRemarkDTO> queryBy(CellInStockPlanTotalRemarkQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    @Override
    public void save(CellInStockPlanTotalRemarkDTO dto) {
        repository.save(convert.toEntity(dto));
    }

    private BooleanBuilder buildWhere(CellInStockPlanTotalRemarkQuery query) {
        BooleanBuilder builder = new BooleanBuilder();
        if (StringUtils.isNotBlank(query.getMonth())) {
            builder.and(cellInStockPlanTotalRemark.month.eq(query.getMonth()));
        }
        if (StringUtils.isNotBlank(query.getIsOversea())) {
            builder.and(cellInStockPlanTotalRemark.isOversea.eq(query.getIsOversea()));
        }
        if (query.getIsOverseaId() != null) {
            builder.and(cellInStockPlanTotalRemark.isOverseaId.eq(query.getIsOverseaId()));
        }
        return builder;
    }
}
