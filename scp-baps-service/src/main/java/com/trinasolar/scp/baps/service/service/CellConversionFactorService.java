package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellConversionFactorDTO;
import com.trinasolar.scp.baps.domain.query.CellConversionFactorQuery;
import com.trinasolar.scp.baps.domain.save.CellConversionFactorSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 万片与兆瓦折算系数 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-31 10:33:29
 */
public interface CellConversionFactorService  extends IExcelImportData{
    /**
     * 分页获取万片与兆瓦折算系数
     *
     * @param query 查询对象
     * @return 万片与兆瓦折算系数分页对象
     */
    Page<CellConversionFactorDTO> queryByPage(CellConversionFactorQuery query);

    /**
     * 根据主键获取万片与兆瓦折算系数详情
     *
     * @param id 主键
     * @return 万片与兆瓦折算系数详情
     */
        CellConversionFactorDTO queryById(Long id);

    /**
     * 依据电池类型查询
     * @param cellsType 电池类型名
     * @return
     */
    CellConversionFactorDTO queryByCellsType(String isOversea,String cellsType);
    /**
     * 保存或更新万片与兆瓦折算系数
     *
     * @param saveDTO 万片与兆瓦折算系数保存对象
     * @return 万片与兆瓦折算系数对象
     */
    CellConversionFactorDTO save(CellConversionFactorSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除万片与兆瓦折算系数
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellConversionFactorQuery query, HttpServletResponse response);

    /**
     * 更新万片与兆瓦折算系数
     */
    void updateConversionFactor();
    public Map<String,CellConversionFactorDTO> createMapByCelltypeAndIsOversea();

}

