package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellBaseCapacityDiscounts;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * IE产能打折（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Repository
public interface CellBaseCapacityDiscountsRepository extends JpaRepository<CellBaseCapacityDiscounts, Long>, QuerydslPredicateExecutor<CellBaseCapacityDiscounts> {
    @Modifying
    @Query("update  CellBaseCapacityDiscounts  c set c.ieConfirm= :tag WHERE c.id = :id")
  public void updateIeConfirm(@Param("id") Long id ,@Param("tag") int tag);
    @Modifying
    @Query("update  CellBaseCapacityDiscounts  c set c.planConfirm= :tag WHERE c.id = :id")
    public void updatePlanConfirm(@Param("id") Long id ,@Param("tag") int tag);
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query("delete from CellBaseCapacityDiscounts  c WHERE c.fromId = :fromId")
    public void deleteByFromId(@Param("fromId") Long fromId);
}
