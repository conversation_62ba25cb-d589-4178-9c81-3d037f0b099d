package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellTypeRuleDTO;
import com.trinasolar.scp.baps.domain.convert.CellTypeRuleDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellTypeRule;
import com.trinasolar.scp.baps.domain.entity.QCellTypeRule;
import com.trinasolar.scp.baps.domain.excel.CellTypeRuleExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellTypeRuleQuery;
import com.trinasolar.scp.baps.domain.save.CellTypeRuleSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.FileUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellTypeRuleRepository;
import com.trinasolar.scp.baps.service.service.CellTypeRuleService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * 电池类型转化规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-11 01:42:00
 */
@Slf4j
@Service("cellTypeRuleService")
@RequiredArgsConstructor
public class CellTypeRuleServiceImpl implements CellTypeRuleService {
    private static final QCellTypeRule qCellTypeRule = QCellTypeRule.cellTypeRule;

    private final CellTypeRuleDEConvert convert;

    private final CellTypeRuleRepository repository;

    @Override
    public Page<CellTypeRuleDTO> queryByPage(CellTypeRuleQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellTypeRule> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellTypeRuleQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellTypeRule.id.eq(query.getId()));
        }
        if (query.getSortNo() != null) {
            booleanBuilder.and(qCellTypeRule.sortNo.eq(query.getSortNo()));
        }
        if (StringUtils.isNotEmpty(query.getModule())) {
            booleanBuilder.and(qCellTypeRule.module.eq(query.getModule()));
        }
        if (StringUtils.isNotEmpty(query.getIllustrate())) {
            booleanBuilder.and(qCellTypeRule.illustrate.eq(query.getIllustrate()));
        }
        if (StringUtils.isNotEmpty(query.getField())) {
            booleanBuilder.and(qCellTypeRule.field.eq(query.getField()));
        }
        if (StringUtils.isNotEmpty(query.getRule())) {
            booleanBuilder.and(qCellTypeRule.rule.eq(query.getRule()));
        }
        if (StringUtils.isNotEmpty(query.getResult())) {
            booleanBuilder.and(qCellTypeRule.result.eq(query.getResult()));
        }
    }

    @Override
    public CellTypeRuleDTO queryById(Long id) {
        CellTypeRule queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellTypeRuleDTO save(CellTypeRuleSaveDTO saveDTO) {
        CellTypeRule newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellTypeRule());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellTypeRuleQuery query, HttpServletResponse response) {
        List<CellTypeRuleDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);
        String fileName = BapsMessgeHelper.getMessage("export.celltyperule.table.name");
        ExcelUtils.exportExWithLocalDate(response,  fileName, fileName, excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellTypeRuleExcelDTO> excelDtos = ExcelUtils.readExcel(multipartFile.getInputStream(), null, CellTypeRuleExcelDTO.class, excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        //验证数据
        checkInput(excelDtos);
        List<CellTypeRuleSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        saveDTOS.stream().forEach(saveDTO -> {
            save(saveDTO);
        });

    }

    private void checkInput(List<CellTypeRuleExcelDTO> excelDTOS) {
        int i = 1;
        List<String> errors = new ArrayList<>();
        for (CellTypeRuleExcelDTO excelDTO : excelDTOS) {
            checkNullField(excelDTO, i);
            //验证模块
            LovLineDTO lovLineDTOModule = LovUtils.getByName(LovHeaderCodeConstant.BAPS_CELL_TYPE_RULE_MODULE, excelDTO.getModuleName());
            if (lovLineDTOModule == null) {
                String message = BapsMessgeHelper.getMessage("celltyperule.import.module.not.exists",new Object[]{i,excelDTO.getModule()});
                errors.add(message);
            }
            //验证解析字段
            LovLineDTO lovLineDTOField = LovUtils.getByName(LovHeaderCodeConstant.BAPS_CELL_TYPE_RULE_FIELD, excelDTO.getFieldName());
            if (lovLineDTOField == null) {
                String message = BapsMessgeHelper.getMessage("celltyperule.import.field.not.exists",new Object[]{i,excelDTO.getField()});
                errors.add(message);
            }
        }
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }
    }

    private void checkNullField(CellTypeRuleExcelDTO excelDTO, int i) {
        if (StringUtils.isEmpty(excelDTO.getSortNo()) || Optional.ofNullable(!StringUtils.isNumeric(excelDTO.getSortNo())).orElse(false) || StringUtils.isEmpty(excelDTO.getModuleName()) || StringUtils.isEmpty(excelDTO.getFieldName()) || StringUtils.isEmpty(excelDTO.getRule()) || StringUtils.isEmpty(excelDTO.getResult())) {
            String message = BapsMessgeHelper.getMessage("celltyperule.import.row.not.null",new Object[]{i});
            throw new BizException(message);
        }
    }


}
