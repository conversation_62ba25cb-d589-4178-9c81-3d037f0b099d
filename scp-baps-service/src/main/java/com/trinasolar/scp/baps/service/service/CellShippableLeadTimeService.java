package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellShippableLeadTimeDTO;
import com.trinasolar.scp.baps.domain.query.CellShippableLeadTimeQuery;
import com.trinasolar.scp.baps.domain.save.CellShippableLeadTimeSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 可发货计划提前期 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-08 06:14:56
 */
public interface CellShippableLeadTimeService {
    /**
     * 分页获取可发货计划提前期
     *
     * @param query 查询对象
     * @return 可发货计划提前期分页对象
     */
    Page<CellShippableLeadTimeDTO> queryByPage(CellShippableLeadTimeQuery query);

    /**
     * 根据主键获取可发货计划提前期详情
     *
     * @param id 主键
     * @return 可发货计划提前期详情
     */
    CellShippableLeadTimeDTO queryById(Long id);

    /**
     * 保存或更新可发货计划提前期
     *
     * @param saveDTO 可发货计划提前期保存对象
     * @return 可发货计划提前期对象
     */
    CellShippableLeadTimeDTO save(CellShippableLeadTimeSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除可发货计划提前期
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query 查询对象
     * @param response 响应对象
     */
    void export(CellShippableLeadTimeQuery query, HttpServletResponse response);

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

