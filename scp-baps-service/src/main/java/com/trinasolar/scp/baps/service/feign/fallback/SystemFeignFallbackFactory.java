package com.trinasolar.scp.baps.service.feign.fallback;

import com.trinasolar.scp.baps.domain.dto.system.OrganizationDefinitionsDTO;
import com.trinasolar.scp.baps.domain.dto.system.OrganizationDefinitionsQuery;
import com.trinasolar.scp.baps.service.feign.SystemFeign;
import com.trinasolar.scp.common.api.base.DataPrivilegeDTO;
import com.trinasolar.scp.common.api.base.DataPrivilegeQuery;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.LovLineQuery;
import com.trinasolar.scp.common.api.util.Results;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: SystemFeignFallbackFactory
 * @date 2024/7/10 14:06
 */
@Slf4j
@Component
public class SystemFeignFallbackFactory implements FallbackFactory<SystemFeign> {
    @Override
    public SystemFeign create(Throwable cause) {
        return new SystemFeign() {
            @Override
            public ResponseEntity<Results<List<LovLineDTO>>> queryByCode(LovLineQuery lovLineQuery) {
                log.warn("【SystemFeign->queryByCode】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<DataPrivilegeDTO>>> queryDataPrivilege(DataPrivilegeQuery query) {
                log.warn("【SystemFeign->queryDataPrivilege】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<OrganizationDefinitionsDTO>>> listForAps(OrganizationDefinitionsQuery query) {
                log.warn("【SystemFeign->listForAps】发生异常：{}", cause);
                return Results.createFailRes();
            }
        };
    }
}
