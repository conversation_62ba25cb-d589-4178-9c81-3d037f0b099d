package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellDailyBalanceDTO;
import com.trinasolar.scp.baps.domain.query.CellDailyBalanceQuery;
import com.trinasolar.scp.baps.domain.save.CellDailyBalanceSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 每日结存报表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-12 06:19:24
 */
public interface CellDailyBalanceService {
    /**
     * 分页获取每日结存报表
     *
     * @param query 查询对象
     * @return 每日结存报表分页对象
     */
    Page<CellDailyBalanceDTO> queryByPage(CellDailyBalanceQuery query);


    List<CellDailyBalanceDTO> query(CellDailyBalanceQuery query);

    @Transactional(rollbackFor = Exception.class)
    List<CellDailyBalanceDTO> calcSelfMakePlans(CellDailyBalanceQuery query);

    String getMaxVersionByMonthAndIsOversea(String month, String isOversea);

    @Transactional(rollbackFor = Exception.class)
    List<CellDailyBalanceDTO> calcOverseaPurchasePlan(CellDailyBalanceQuery query);

    @Transactional(rollbackFor = Exception.class)
    List<CellDailyBalanceDTO> calcCellDemandPlan(CellDailyBalanceQuery query);

    /**
     * 根据主键获取每日结存报表详情
     *
     * @param id 主键
     * @return 每日结存报表详情
     */
    CellDailyBalanceDTO queryById(Long id);

    /**
     * 保存或更新每日结存报表
     *
     * @param saveDTO 每日结存报表保存对象
     * @return 每日结存报表对象
     */
    CellDailyBalanceDTO save(CellDailyBalanceSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除每日结存报表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query 查询对象
     * @param response 响应对象
     */
    void export(CellDailyBalanceQuery query, HttpServletResponse response);
}

