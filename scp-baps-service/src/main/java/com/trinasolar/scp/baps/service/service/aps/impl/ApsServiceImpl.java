package com.trinasolar.scp.baps.service.service.aps.impl;

import com.trinasolar.scp.baps.domain.dto.aps.*;
import com.trinasolar.scp.baps.domain.query.aps.RecordTransitionQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.service.aps.ApsService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("apsService")
@AllArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class ApsServiceImpl implements ApsService {
    private final ApsFeign feign;

    /**
     * 获取指定电池类型的分档规则(筛选状态为“有效”且电池等级为"Q1"的数据。)
     *
     * @param cellsType
     * @return
     */
    @Override
    public List<RecordTransitionDTO> queryCellMateRules(String... cellsType) {
        RecordTransitionQuery query = new RecordTransitionQuery();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        //筛选状态为“有效”且电池等级为"Q1"的数据。
        query.setStatus("有效");
        query.setCellGrade("Q1");
        if (Objects.nonNull(cellsType) && cellsType.length > 0) {
            query.getCellTypeList().addAll(Arrays.stream(cellsType).collect(Collectors.toList()));
        }
        return feign.queryByPage(query).getBody().getData().getContent();
    }

    @Override
    public List<CellShippingDaysDTO> findCellShippingDays() {
        ResponseEntity<Results<List<CellShippingDaysDTO>>> responseEntity = feign.list(new CellShippingDaysQuery());
        return responseEntity.getBody().getData();
    }

    @Override
    @Cacheable(cacheNames = "ApsService_getPowerSupplyAopDTOS", key = "#month+'_'+#isOverseaName", unless = "#result == null")
    public List<PowerSupplyAopDTO> getPowerSupplyAopDTOS(String month, String isOverseaName) {
        PowerSupplyAopQuery powerSupplyAopQuery = new PowerSupplyAopQuery();
        powerSupplyAopQuery.setPageSize(GlobalConstant.max_page_size);
        powerSupplyAopQuery.setPageNumber(1);
        LovLineDTO lovVersion = LovUtils.getByName(LovHeaderCodeConstant.AOP_VERSION_TYPE, LovHeaderCodeConstant.AOP_VERSION_TYPE_NAME);
        if (Objects.nonNull(lovVersion)) {
            powerSupplyAopQuery.setDataVersion(lovVersion.getLovValue());//目标版
        }
        powerSupplyAopQuery.setSupplyType("自产");
        List<String> monthList = new ArrayList<>();
        monthList.add(month);
        powerSupplyAopQuery.setMonthList(monthList);
        if (Objects.equals(OverseaConstant.INLAND, isOverseaName)) {
            powerSupplyAopQuery.setIsOversea(OverseaConstant.INLAND_VALUE);
        } else {
            powerSupplyAopQuery.setIsOversea(OverseaConstant.OVERSEA_VALUE);
        }
        ResponseEntity<Results<List<PowerSupplyAopDTO>>> resultsResponseEntity = feign.powerSupplyAopList(powerSupplyAopQuery);
        List<PowerSupplyAopDTO> datas = resultsResponseEntity.getBody().getData();
        return datas;
    }
}
