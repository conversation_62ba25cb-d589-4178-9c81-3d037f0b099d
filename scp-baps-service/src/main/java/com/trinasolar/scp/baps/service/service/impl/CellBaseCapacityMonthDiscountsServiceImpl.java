package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityMonthDiscountsDTO;
import com.trinasolar.scp.baps.domain.convert.CellBaseCapacityMonthDiscountsDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellBaseCapacityDiscounts;
import com.trinasolar.scp.baps.domain.entity.CellBaseCapacityMonthDiscounts;
import com.trinasolar.scp.baps.domain.entity.QCellBaseCapacityMonthDiscounts;
import com.trinasolar.scp.baps.domain.excel.CellBaseCapacityMonthDiscountsExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityMonthDiscountsQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacityMonthDiscountsSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.EmailConstant;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellBaseCapacityDiscountsRepository;
import com.trinasolar.scp.baps.service.repository.CellBaseCapacityMonthDiscountsRepository;
import com.trinasolar.scp.baps.service.service.CellBaseCapacityMonthDiscountsService;
import com.trinasolar.scp.baps.service.service.mail.MailService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * IE产能打折月度（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-28 03:34:54
 */
@Slf4j
@Service("cellBaseCapacityMonthDiscountsService")
@RequiredArgsConstructor
public class CellBaseCapacityMonthDiscountsServiceImpl implements CellBaseCapacityMonthDiscountsService {
    private static final QCellBaseCapacityMonthDiscounts qCellBaseCapacityMonthDiscounts = QCellBaseCapacityMonthDiscounts.cellBaseCapacityMonthDiscounts;

    private final CellBaseCapacityMonthDiscountsDEConvert convert;

    private final CellBaseCapacityMonthDiscountsRepository repository;
    private final CellBaseCapacityDiscountsRepository cellBaseCapacityDiscountsRepository;
    private final MailService mailService;

    @Override
    public Page<CellBaseCapacityMonthDiscountsDTO> queryByPage(CellBaseCapacityMonthDiscountsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query = convert.toCellBaseCapacityMonthDiscountsQueryCNNameByName(query, MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.ASC, "isOversea", "basePlace", "workshop", "workunit", "month", "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellBaseCapacityMonthDiscounts> page = repository.findAll(booleanBuilder, pageable);
        List<CellBaseCapacityMonthDiscountsDTO> dtos = convert.toDto(page.getContent());
        dtos = convert.toCellBaseCapacityMonthDiscountsDTOSNameById(dtos);
        return new PageImpl(dtos, page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellBaseCapacityMonthDiscountsQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.isOversea.eq(query.getIsOversea()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.basePlace.eq(query.getBasePlace()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkunitId() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.workunitId.eq(query.getWorkunitId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.workunit.eq(query.getWorkunit()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.month.eq(query.getMonth()));
        }
        if (query.getD1() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d1.eq(query.getD1()));
        }
        if (query.getD2() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d2.eq(query.getD2()));
        }
        if (query.getD3() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d3.eq(query.getD3()));
        }
        if (query.getD4() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d4.eq(query.getD4()));
        }
        if (query.getD5() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d5.eq(query.getD5()));
        }
        if (query.getD6() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d6.eq(query.getD6()));
        }
        if (query.getD7() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d7.eq(query.getD7()));
        }
        if (query.getD8() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d8.eq(query.getD8()));
        }
        if (query.getD9() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d9.eq(query.getD9()));
        }
        if (query.getD10() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d10.eq(query.getD10()));
        }
        if (query.getD11() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d11.eq(query.getD11()));
        }
        if (query.getD12() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d12.eq(query.getD12()));
        }
        if (query.getD13() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d13.eq(query.getD13()));
        }
        if (query.getD14() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d14.eq(query.getD14()));
        }
        if (query.getD15() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d15.eq(query.getD15()));
        }
        if (query.getD16() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d16.eq(query.getD16()));
        }
        if (query.getD17() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d17.eq(query.getD17()));
        }
        if (query.getD18() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d18.eq(query.getD18()));
        }
        if (query.getD19() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d19.eq(query.getD19()));
        }
        if (query.getD20() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d20.eq(query.getD20()));
        }
        if (query.getD21() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d21.eq(query.getD21()));
        }
        if (query.getD22() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d22.eq(query.getD22()));
        }
        if (query.getD23() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d23.eq(query.getD23()));
        }
        if (query.getD24() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d24.eq(query.getD24()));
        }
        if (query.getD25() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d25.eq(query.getD25()));
        }
        if (query.getD26() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d26.eq(query.getD26()));
        }
        if (query.getD27() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d27.eq(query.getD27()));
        }
        if (query.getD28() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d28.eq(query.getD28()));
        }
        if (query.getD29() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d29.eq(query.getD29()));
        }
        if (query.getD30() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d30.eq(query.getD30()));
        }
        if (query.getD31() != null) {
            booleanBuilder.and(qCellBaseCapacityMonthDiscounts.d31.eq(query.getD31()));
        }
    }

    @Override
    public CellBaseCapacityMonthDiscountsDTO queryById(Long id) {
        CellBaseCapacityMonthDiscounts queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellBaseCapacityMonthDiscountsDTO save(CellBaseCapacityMonthDiscountsSaveDTO saveDTO) {
        CellBaseCapacityMonthDiscounts newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellBaseCapacityMonthDiscounts());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach((id) -> {
            repository.deleteById(id);
            cellBaseCapacityDiscountsRepository.deleteByFromId(id);

        });
        //发邮件提醒
        //1、准备收件人email(读取lov)
        List<String> emails;
        String emailName;
        LovLineDTO lovLineDTOEmail = LovUtils.get(LovHeaderCodeConstant.BAPS_EMAIL_REMINDER, LovHeaderCodeConstant.BAPS_CAPICITY_DISCOUNT_MAIL_REMINDER);
        if (Objects.isNull(lovLineDTOEmail)) {
            throw new BizException("baps_email_configure_error");
        } else {
            emailName = lovLineDTOEmail.getAttribute1();
            String email = lovLineDTOEmail.getAttribute2();
            if (StringUtils.isEmpty(email)) {
                throw new BizException("baps_email_configure_error");
            }
            String[] emailArray = email.split(";");
            emails = Arrays.asList(emailArray);
        }
        //2、准备发送的内容
        CellBaseCapacityMonthDiscountsQuery query = new CellBaseCapacityMonthDiscountsQuery();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        Page<CellBaseCapacityMonthDiscountsDTO> page = queryByPage(query);
        List<CellBaseCapacityMonthDiscountsDTO> dtos = new ArrayList<>();
        if (page != null) {
            if (page.getContent() != null) {
                dtos = page.getContent();
            }
        }
        Map<String, Object> content = MapUtil.of("contentList", dtos);
        content.put("emailName", emailName);
        content.put("updateTime", DateUtil.getFormatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        mailService.send(emails, EmailConstant.CellBaseCapacityDiscountsEmail, "IE产能打折（人力）更新提醒", content, null);
    }


    @Override
    @SneakyThrows
    public void export(CellBaseCapacityMonthDiscountsQuery query, HttpServletResponse response) {
        List<CellBaseCapacityMonthDiscountsDTO> dtos = queryByPage(query).getContent();

        // dto数据转为ExcelData数据
        List<CellBaseCapacityMonthDiscountsExcelDTO> datas = convert.toExcelDTO(dtos);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellBaseCapacityMonthDiscountsExcelDTO.class, datas, JSON.toJSONString(query), "IE产能打折(人力)", response);

    }

    @Override
    public List<CellBaseCapacityMonthDiscountsDTO> findByMonth(String month) {
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qCellBaseCapacityMonthDiscounts.month.eq(month));
        List<CellBaseCapacityMonthDiscounts> data = IterableUtils.toList(repository.findAll(builder));
        return convert.toDto(data);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile) {
        List<CellBaseCapacityMonthDiscountsExcelDTO> excelDtos = new LinkedList<>();
        // 这里为了简单 所以注册了 同样的head 和Listener 自己使用功能必须不同的Listener
        ExcelReaderBuilder readerBuilder = EasyExcel.read(multipartFile.getInputStream(), CellBaseCapacityMonthDiscountsExcelDTO.class, new ReadListener<CellBaseCapacityMonthDiscountsExcelDTO>() {
            @Override
            public void invoke(CellBaseCapacityMonthDiscountsExcelDTO data, AnalysisContext context) {
                excelDtos.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        });
        readerBuilder.registerConverter(new LocalDateConverter());
        readerBuilder.sheet(0).doRead();
        //验证数据
        checkInput(excelDtos);
        final QuerySheetExcelDTO[] querySheetExcelDTO = {null};
        EasyExcel.read(multipartFile.getInputStream(), QuerySheetExcelDTO.class, new ReadListener<QuerySheetExcelDTO>() {
            @Override
            public void invoke(QuerySheetExcelDTO data, AnalysisContext context) {
                querySheetExcelDTO[0] = data;
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet(1).doRead();
        QuerySheetExcelDTO queryParam = querySheetExcelDTO[0];

        if (Objects.isNull(queryParam)) {
            throw new BizException("baps_import_params_error");
        }

        // 查询原始行

        //  CellBaseCapacityMonthDiscountsQuery page = JSON.parseObject(queryParam.getQueryParam(), CellBaseCapacityMonthDiscountsQuery.class);
        CellBaseCapacityMonthDiscountsQuery page = JSON.parseObject("{}", CellBaseCapacityMonthDiscountsQuery.class);
        page.setPageSize(GlobalConstant.max_page_size);
        page.setPageNumber(1);
        List<CellBaseCapacityMonthDiscountsDTO> oriDTOS = this.queryByPage(page).getContent();


        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("baps_import_file_error");
        }

        List<CellBaseCapacityMonthDiscountsSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        saveDTOS = convert.toCellBaseCapacityMonthDiscountsSaveDTOCNNameById(saveDTOS);
        // 先删除之前的数据,再保存信息的数据
        oriDTOS.forEach((i) -> {
            repository.deleteById(i.getId());
            cellBaseCapacityDiscountsRepository.deleteByFromId(i.getId());

        });
        List<CellBaseCapacityMonthDiscountsDTO> dtos = new ArrayList<>();
        saveDTOS.stream().forEach(dto -> {
            //月份
            String month = dto.getMonth();
            //获取month月份的天数
            int days = DateUtil.getDaysForMonth(month);
            for (int i = days + 1; i <= 31; i++) {

                ReflectUtil.setFieldValue(dto, "d" + i, null);
            }

            CellBaseCapacityMonthDiscountsDTO cellBaseCapacityMonthDiscountsDTO = save(dto);
            List<CellBaseCapacityDiscounts> datas = Lists.newArrayList();


            for (int i = 1; i <= days; i++) {
                BigDecimal val = ReflectUtil.invoke(cellBaseCapacityMonthDiscountsDTO, "getD" + i);
                if (Objects.isNull(val)) {
                    continue;
                }
                CellBaseCapacityDiscounts cellBaseCapacityDiscounts = BeanUtil.copyProperties(cellBaseCapacityMonthDiscountsDTO, CellBaseCapacityDiscounts.class, "id");
                cellBaseCapacityDiscounts.setFromId(cellBaseCapacityMonthDiscountsDTO.getId());
                cellBaseCapacityDiscounts.setId(null);
                cellBaseCapacityDiscounts.setWorkshopid(cellBaseCapacityMonthDiscountsDTO.getWorkshopId());
                LocalDate startTime = DateUtil.getLocalDate(month, i);
                cellBaseCapacityDiscounts.setStartTime(startTime);
                cellBaseCapacityDiscounts.setEndTime(startTime);
                cellBaseCapacityDiscounts.setRatio(val);
                cellBaseCapacityDiscounts.setWorkunit(cellBaseCapacityMonthDiscountsDTO.getWorkunit());
                cellBaseCapacityDiscounts.setWorkunitid(cellBaseCapacityMonthDiscountsDTO.getWorkunitId());
                datas.add(cellBaseCapacityDiscounts);

            }
            cellBaseCapacityDiscountsRepository.saveAll(datas);
            dtos.add(cellBaseCapacityMonthDiscountsDTO);
        });
        //发邮件提醒
        //1、准备收件人email(读取lov)
        List<String> emails;
        String emailName;
        LovLineDTO lovLineDTOEmail = LovUtils.get(LovHeaderCodeConstant.BAPS_EMAIL_REMINDER, LovHeaderCodeConstant.BAPS_CAPICITY_DISCOUNT_MAIL_REMINDER);
        if (Objects.isNull(lovLineDTOEmail)) {
            throw new BizException("baps_import_params_error");
        } else {
            emailName = lovLineDTOEmail.getAttribute1();
            String email = lovLineDTOEmail.getAttribute2();
            if (StringUtils.isEmpty(email)) {
                throw new BizException("baps_import_params_error");
            }
            String[] emailArray = email.split(";");
            emails = Arrays.asList(emailArray);
        }


        //2、准备发送的内容
        Map<String, Object> content = MapUtil.of("contentList", dtos);
        content.put("emailName", emailName);
        content.put("updateTime", DateUtil.getFormatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        mailService.send(emails, EmailConstant.CellBaseCapacityDiscountsEmail, "IE产能打折（人力）更新提醒", content, null);

    }

    public void checkInput(List<CellBaseCapacityMonthDiscountsExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors = new ArrayList<>();
        Set<String> keys = Sets.newHashSet();
        Joiner joiner = Joiner.on("-").useForNull("null");
        excelDTOS.stream().forEach(excelDTO -> {
            String month = excelDTO.getMonth();

            if (StringUtils.isEmpty(month)) {
                String error = MessageHelper.getMessage("baps_row_month_not_null", new Object[]{i[0]}).getDesc();
                errors.add(error);
            }

            //验证国内海外
            LovLineDTO lovLineDTO = null;
            if (Objects.isNull(excelDTO.getIsOversea())) {
                String error = MessageHelper.getMessage("baps_row_isOversea_not_null", new Object[]{i[0]}).getDesc();
                errors.add(error);
            } else {
                lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOversea());
                if (lovLineDTO == null) {
                    String error = MessageHelper.getMessage("baps_row_isOversea_not_exist", new Object[]{i[0], excelDTO.getIsOversea()}).getDesc();
                    errors.add(error);
                }
            }
            //验证生产基地
            LovLineDTO lovLineDTO_BasePlace = null;
            if (Objects.isNull(excelDTO.getBasePlace())) {
                String error = MessageHelper.getMessage("baps_row_base_place_not_null", new Object[]{i[0]}).getDesc();
                errors.add(error);
            } else {
                lovLineDTO_BasePlace = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace());
                if (lovLineDTO_BasePlace == null) {
                    String error = MessageHelper.getMessage("baps_row_base_place_not_exist", new Object[]{i[0], excelDTO.getBasePlace()}).getDesc();
                    errors.add(error);
                }
            }
            //验证基地与国内海外的关系
            if (lovLineDTO != null && lovLineDTO_BasePlace != null) {
                if (!(lovLineDTO_BasePlace.getAttribute2() != null && lovLineDTO_BasePlace.getAttribute2().equals(lovLineDTO.getLovLineId().toString()))) {
                    String error = MessageHelper.getMessage("baps_row_base_place_not_correct", new Object[]{i[0], excelDTO.getBasePlace(), excelDTO.getIsOversea()}).getDesc();
                    errors.add(error);
                }
            }
            //验证生产车间
            LovLineDTO lovLineDTO_WorkShop = null;
            if (Objects.isNull(excelDTO.getWorkshop())) {
                String error = MessageHelper.getMessage("baps_row_workshop_not_null", new Object[]{i[0]}).getDesc();
                errors.add(error);
            } else {
                lovLineDTO_WorkShop = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop());
                if (lovLineDTO_WorkShop == null) {
                    String error = MessageHelper.getMessage("baps_row_workshop_not_exist", new Object[]{i[0], excelDTO.getWorkshop()}).getDesc();
                    errors.add(error);
                }
            }
            //验证车间与基地的关系
            if (lovLineDTO_WorkShop != null && lovLineDTO_BasePlace != null) {
                if (!(lovLineDTO_WorkShop.getAttribute1() != null && lovLineDTO_WorkShop.getAttribute1().equals(lovLineDTO_BasePlace.getLovLineId().toString()))) {
                    String error = MessageHelper.getMessage("baps_row_workshop_not_correct", new Object[]{i[0], excelDTO.getWorkshop(), excelDTO.getBasePlace()}).getDesc();
                    errors.add(error);
                }
            }
            //验证生产单元(可选)
            LovLineDTO lovLineDTO_Workunit = null;
            if (StringUtils.isNotEmpty(excelDTO.getWorkunit())) {

                lovLineDTO_Workunit = LovUtils.getByName(LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit());
                if (lovLineDTO_Workunit == null) {
                    String error = MessageHelper.getMessage("baps_row_workunit_not_exist", new Object[]{i[0], excelDTO.getWorkunit()}).getDesc();
                    errors.add(error);
                }
            }
            //验证单元与车间的关系
            if (lovLineDTO_WorkShop != null && lovLineDTO_Workunit != null) {

                if (!(lovLineDTO_Workunit.getAttribute2() != null && lovLineDTO_Workunit.getAttribute2().equals(lovLineDTO_WorkShop.getLovLineId().toString()))) {
                    String error = MessageHelper.getMessage("baps_row_workunit_not_correct", new Object[]{i[0], excelDTO.getWorkunit(), excelDTO.getWorkshop()}).getDesc();
                    errors.add(error);
                }
            }
            i[0]++;
            String key = joiner.join(month, excelDTO.getIsOversea(), excelDTO.getBasePlace(), excelDTO.getWorkshop(), excelDTO.getWorkunit());
            if (keys.contains(key)) {
                String error = MessageHelper.getMessage("baps_row_not_duplicate", new Object[]{i[0]}).getDesc();
                errors.add(error);
            } else {
                keys.add(key);
            }
            //判断excelDTO的getD1()到getD31()方法的返回值 不是空的时候，值必须是百分数
            for (int j = 1; j <= 31; j++) {
                String val = ReflectUtil.invoke(excelDTO, "getD" + j);
                if (StringUtils.isNotEmpty(val)) {
                    //判断val是不是百分数
                    if (!val.matches("^\\d+(\\.\\d+)?%$")) {
                        String error = MessageHelper.getMessage("baps_row_value_not_percent", new Object[]{i[0], j}).getDesc();
                        errors.add(error);
                    }
                }
            }
        });
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(","));
            throw new BizException(errorString);
        }

    }


}
