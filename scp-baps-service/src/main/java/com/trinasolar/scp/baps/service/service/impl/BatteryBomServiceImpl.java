package com.trinasolar.scp.baps.service.service.impl;

import com.google.common.collect.Maps;
import com.trinasolar.scp.baps.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.ItemsDTO;
import com.trinasolar.scp.baps.domain.query.bbom.ItemsNewQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.feign.BatteryBomFeign;
import com.trinasolar.scp.baps.service.feign.BbomFeign;
import com.trinasolar.scp.baps.service.service.BatteryBomService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("batteryBomService")
@AllArgsConstructor
public class BatteryBomServiceImpl implements BatteryBomService {

    private final BbomFeign bbomFeign;

    @Override
    public List<BatteryTypeMainDTO> queryBatteryCodeTypeAll() {
        List<BatteryTypeMainDTO> batteryTypeMainDTOS = new ArrayList<>();
        Set<Long> set = new HashSet<>();
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BATTERY_TYPE);
        for (LovLineDTO lvDto : allByHeaderCode.values()) {
            if (!set.contains(lvDto.getLovLineId())) {
                set.add(lvDto.getLovLineId());
            } else {
                continue;
            }
            BatteryTypeMainDTO batteryTypeMainDTO = new BatteryTypeMainDTO();
            batteryTypeMainDTO.setBatteryName(lvDto.getLovName());
            batteryTypeMainDTO.setId(lvDto.getLovLineId());
            batteryTypeMainDTOS.add(batteryTypeMainDTO);
        }
        return batteryTypeMainDTOS;
    }

    @Override
    public Map<String, ItemsDTO> findItems(List<String> itemCodes) {
        if (CollectionUtils.isNotEmpty(itemCodes)) {
            itemCodes = itemCodes.stream().distinct().collect(Collectors.toList());
            ItemsNewQuery query = new ItemsNewQuery();
            query.setItemCodes(itemCodes);
            Results<Map<String, ItemsDTO>> response = bbomFeign.queryByItemCodeAll(query).getBody();
            if(!response.isSuccess() || response.getData() == null){
                throw new BizException("baps_call_bom_interface_failed");
            }
            return response.getData();
        }
        return Maps.newHashMap();
    }

    @Override
    public Map<String, ItemsDTO> findItemsNew(List<String> itemCodes) {
        if (CollectionUtils.isNotEmpty(itemCodes)) {
            itemCodes = itemCodes.stream().distinct().collect(Collectors.toList());
            ItemsNewQuery query = new ItemsNewQuery();
            query.setItemCodes(itemCodes);
            Results<Map<String, ItemsDTO>> response = bbomFeign.queryByItemCodeAllNew(query).getBody();
            if(!response.isSuccess() || response.getData() == null){
                throw new BizException("baps_call_bom_interface_failed");
            }
            return response.getData();
        }
        return Maps.newHashMap();
    }


}
