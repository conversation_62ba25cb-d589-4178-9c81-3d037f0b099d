package com.trinasolar.scp.baps.service.repository;
import com.trinasolar.scp.baps.domain.entity.DeliveryHoliday;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 物流节假日表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
@Repository
public interface DeliveryHolidayRepository extends JpaRepository<DeliveryHoliday, Long>, QuerydslPredicateExecutor<DeliveryHoliday> {
    @Modifying
    @Query("delete  from  DeliveryHoliday d where d.month = :month")
    @Transactional(rollbackFor = Exception.class)
    void deleteByMonth(@Param("month") String month);
}
