package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellLocatorWorkshopRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 货位对应车间关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 02:57:01
 */
@Repository
public interface CellLocatorWorkshopRelationRepository extends JpaRepository<CellLocatorWorkshopRelation, Long>, QuerydslPredicateExecutor<CellLocatorWorkshopRelation> {
}
