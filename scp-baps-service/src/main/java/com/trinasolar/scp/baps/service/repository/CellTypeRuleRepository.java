package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellTypeRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 电池类型转化规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-11 01:42:00
 */
@Repository
public interface CellTypeRuleRepository extends JpaRepository<CellTypeRule, Long>, QuerydslPredicateExecutor<CellTypeRule> {
}
