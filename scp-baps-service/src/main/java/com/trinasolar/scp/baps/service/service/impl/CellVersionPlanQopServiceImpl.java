package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.convert.CellVersionPlanQopDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanQop;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanQop;
import com.trinasolar.scp.baps.domain.entity.CellVersionQopIe;
import com.trinasolar.scp.baps.domain.entity.QCellVersionPlanQop;
import com.trinasolar.scp.baps.domain.excel.CellVersionPlanQopExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellVersionQopIeExcelDTO;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.baps.domain.save.CellVersionPlanQopSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.repository.CellVersionPlanQopRepository;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 计划与qop
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Slf4j
@Service("cellVersionPlanQopService")
@RequiredArgsConstructor
public class CellVersionPlanQopServiceImpl implements CellVersionPlanQopService {
    private static final QCellVersionPlanQop qCellVersionPlanQop = QCellVersionPlanQop.cellVersionPlanQop;

    private final CellVersionPlanQopDEConvert convert;

    private final CellVersionPlanQopRepository repository;

    private final QopDetailsService sopDetailsService;

    private final CellInstockPlanTotalService instockPlanTotalService;
    private final CellConversionFactorService cellConversionFactorService;

    @Override
    public Page<CellVersionPlanQopDTO> queryByPage(CellVersionPlanQopQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellVersionPlanQop> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellVersionPlanQopQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellVersionPlanQop.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellVersionPlanQop.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOverseaName())) {
            booleanBuilder.and(qCellVersionPlanQop.isOverseaName.eq(query.getIsOverseaName()));
        }
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qCellVersionPlanQop.cellTypeId.eq(query.getCellTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellTypeName())) {
            booleanBuilder.and(qCellVersionPlanQop.cellTypeName.eq(query.getCellTypeName()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellVersionPlanQop.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshopName())) {
            booleanBuilder.and(qCellVersionPlanQop.workshopName.eq(query.getWorkshopName()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellVersionPlanQop.month.eq(query.getMonth()));
        }
        if (query.getRate() != null) {
            booleanBuilder.and(qCellVersionPlanQop.rate.eq(query.getRate()));
        }
        if (query.getTarget() != null) {
            booleanBuilder.and(qCellVersionPlanQop.target.eq(query.getTarget()));
        }
        if (query.getPlanCapacity() != null) {
            booleanBuilder.and(qCellVersionPlanQop.planCapacity.eq(query.getPlanCapacity()));
        }
        if (query.getGap() != null) {
            booleanBuilder.and(qCellVersionPlanQop.gap.eq(query.getGap()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qCellVersionPlanQop.remark.eq(query.getRemark()));
        }
    }

    @Override
    public CellVersionPlanQopDTO queryById(Long id) {
        CellVersionPlanQop queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellVersionPlanQopDTO save(CellVersionPlanQopSaveDTO saveDTO) {
        CellVersionPlanQop newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellVersionPlanQop());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellVersionPlanQopQuery query, HttpServletResponse response) {
        List<CellVersionPlanQopDTO> cellVersionPlanQopCollect=new ArrayList<>();
        Map<String, Object> mapData = makeReport(query);

        Object detailsObj=  mapData.get("details");
        if (detailsObj!=null){
            cellVersionPlanQopCollect.addAll((List<CellVersionPlanQopDTO>)detailsObj);
        }
        Object planVersionObj=   mapData.get("planVersion");
        String planVersion="";
        if (planVersionObj!=null){
            planVersion=planVersionObj.toString();
        }
        Object qopVersionObj=  mapData.get("qopVersion");
        String qopVersion="";
        if (qopVersionObj!=null){
            qopVersion=qopVersionObj.toString();
        }

        // dto数据转为ExcelData数据
        List<CellVersionPlanQopExcelDTO> datas = convert.toExcelDTO(cellVersionPlanQopCollect);
        ExcelPara excelPara=new ExcelPara();
        excelPara.addColumn("isOverseaName","国内/海外",0, 500.0);
        excelPara.addColumn("workshopName","生产车间",1,200.0);
        excelPara.addColumn("cellTypeName","电池类型",2,200.0);
        excelPara.addColumn("month","月份",3,100.0);
        excelPara.addColumn("ratePercent","产能利用率",4,200.0);
        excelPara.addColumn("target",StringUtils.isNotEmpty(qopVersion)?"QOP目标\n"+qopVersion:"QOP目标",5,100.0);
        excelPara.addColumn("planCapacity",StringUtils.isNotEmpty(planVersion)?query.getMonth()+"计划\n"+planVersion:query.getMonth()+"计划",6,100.0);
        excelPara.addColumn("gap","计划与QOP差异",7,100.0);
        List<List<Object>> excelData = ExcelUtils.getList(datas, excelPara);
        String fileName="版本对比计划与QOP";
        String sheetName = fileName;
        fileName = fileName + "_" + DateUtils.formatDate(new Date(), "yyyy-MM-dd+HH:mm:ss");
        ExcelUtils.exportEx(response, fileName, sheetName, excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public Map<String, Object> makeReport(CellVersionPlanQopQuery query) {
        String oldLang= MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        LovLineDTO overseaLov = null;
        if (query.getIsOverseaId() == null) {
            //默认读取国内数据
            overseaLov = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, OverseaConstant.INLAND);

        } else {
            overseaLov = LovUtils.get(query.getIsOverseaId());
        }
        query.setIsOverseaId(overseaLov.getLovLineId());
        query.setIsOverseaName(overseaLov.getLovName());

        Map<String, Object> mapData = new HashMap<>();
        String qopVersion = "V" + query.getMonth() + "01";//QOP数据版本
        //一、获取QOP数据
        QopDetailsQuery qopDetailsQuery = convert.toQopDetailsQuery(query);
        List<CellVersionPlanQop> cellVersionPlanQops = prepareQopData(qopDetailsQuery);
        //二、获取入库计划数据
        CellInstockPlanTotalQuery cellInstockPlanTotalQuery = convert.toCellInstockPlanTotalQuery(query);
        cellInstockPlanTotalQuery.setCellsTypeId(query.getCellTypeId());
        String productionVersion = instockPlanTotalService.queryMaxVersion(cellInstockPlanTotalQuery);
        List<CellVersionPlanQop> cellVersionPlanIeListByProduction = preparePlanData(query, cellInstockPlanTotalQuery);
        //三、对统计后的QOP数据和计划数据合并处理
        List<CellVersionPlanQop> cellVersionPlanIeCollect = mergeQopAndPlanData(cellVersionPlanQops, cellVersionPlanIeListByProduction);
        //去0处理
        cellVersionPlanIeCollect = filterZero(cellVersionPlanIeCollect);
        //四、生成报表信息基础数据
        makeMapResult(query, oldLang, mapData, qopVersion, productionVersion, cellVersionPlanIeCollect);
        return mapData;
    }
    private   List<CellVersionPlanQop> filterZero(List<CellVersionPlanQop> cellVersionPlanQopCollect) {
        return   Optional.ofNullable(cellVersionPlanQopCollect).orElse(Lists.newArrayList()).stream().filter(cellVersionPlanQop -> {
            return Optional.ofNullable(cellVersionPlanQop.getPlanCapacity()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO)>0 ||
                    Optional.ofNullable(cellVersionPlanQop.getTarget()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO)>0;
        }).collect(Collectors.toList());

    }
    /**
     * 生成报表信息基础数据
     * @param query
     * @param oldLang
     * @param mapData
     * @param qopVersion
     * @param productionVersion
     * @param cellVersionPlanIeCollect
     */
    private void makeMapResult(CellVersionPlanQopQuery query, String oldLang, Map<String, Object> mapData, String qopVersion, String productionVersion, List<CellVersionPlanQop> cellVersionPlanIeCollect) {
        AtomicReference<BigDecimal> capacityAll = new AtomicReference<>();
        AtomicReference<BigDecimal> planCapacityAll = new AtomicReference<>();
        cellVersionPlanIeCollect.stream().forEach(cellVersionPlanQop -> {
            if (cellVersionPlanQop.getTarget() != null) {
                if (capacityAll.get() == null) {
                    capacityAll.set(BigDecimal.ZERO);
                }
                capacityAll.set(capacityAll.get().add(cellVersionPlanQop.getTarget()));
            }
            if (cellVersionPlanQop.getPlanCapacity() != null) {
                if (planCapacityAll.get() == null) {
                    planCapacityAll.set(BigDecimal.ZERO);
                }
                planCapacityAll.set(planCapacityAll.get().add(cellVersionPlanQop.getPlanCapacity()));
            }
            if (cellVersionPlanQop.getPlanCapacity() != null && cellVersionPlanQop.getTarget() != null) {
                if (BigDecimal.ZERO.compareTo(cellVersionPlanQop.getTarget())!=0){
                    cellVersionPlanQop.setRate(cellVersionPlanQop.getPlanCapacity().divide(cellVersionPlanQop.getTarget(), 4, RoundingMode.HALF_UP));
                }
                cellVersionPlanQop.setGap(cellVersionPlanQop.getPlanCapacity().subtract(cellVersionPlanQop.getTarget()));
            }
        });
        CellVersionPlanQop cellVersionPlanIeAll = new CellVersionPlanQop();
        cellVersionPlanIeAll.setWorkshopName((query.getIsOverseaName() == null ? "" : query.getIsOverseaName()));
        cellVersionPlanIeAll.setTarget(capacityAll.get());
        cellVersionPlanIeAll.setPlanCapacity(planCapacityAll.get());
        if (cellVersionPlanIeAll.getPlanCapacity() != null && cellVersionPlanIeAll.getTarget() != null) {
            if (BigDecimal.ZERO.compareTo(cellVersionPlanIeAll.getTarget())!=0){
                cellVersionPlanIeAll.setRate(cellVersionPlanIeAll.getPlanCapacity().divide(cellVersionPlanIeAll.getTarget(), 4, RoundingMode.HALF_UP));
            }

            cellVersionPlanIeAll.setGap(cellVersionPlanIeAll.getPlanCapacity().subtract(cellVersionPlanIeAll.getTarget()));
        }
        cellVersionPlanIeCollect.stream().forEach(cellVersionPlanQop -> {
            cellVersionPlanQop.setMonth(query.getMonth());
            cellVersionPlanQop.setPlanCapacity(null==cellVersionPlanQop.getPlanCapacity()?BigDecimal.ZERO:cellVersionPlanQop.getPlanCapacity());
            cellVersionPlanQop.setGap(null==cellVersionPlanQop.getGap()?BigDecimal.ZERO:cellVersionPlanQop.getGap());
            cellVersionPlanQop.setTarget(null==cellVersionPlanQop.getTarget()?BigDecimal.ZERO:cellVersionPlanQop.getTarget());
        });
        List<CellVersionPlanQopDTO> details = convert.toDto(cellVersionPlanIeCollect);
        CellVersionPlanQopDTO firstRow = convert.toDto(cellVersionPlanIeAll);
        //对报表基础信息汇总
        //依据电池类型汇总
        Map<String, List<CellVersionPlanQop>> collectCellVersionPlanIe = cellVersionPlanIeCollect.stream().collect(Collectors.groupingBy(CellVersionPlanQop::getCellTypeName));
        List<CellVersionPlanQop> datas = new ArrayList<>();
        collectCellVersionPlanIe.entrySet().stream().forEach(cellsTypecellVersionPlanIe -> {
            String celltypeName = cellsTypecellVersionPlanIe.getKey();
            AtomicReference<BigDecimal> capcity = new AtomicReference<>();
            AtomicReference<BigDecimal> planCapcity = new AtomicReference<>();
            cellsTypecellVersionPlanIe.getValue().stream().forEach(cellVersionPlanQop -> {
                if (cellVersionPlanQop.getTarget() != null) {
                    if (capcity.get() == null) {
                        capcity.set(BigDecimal.ZERO);
                    }
                    capcity.set(capcity.get().add(cellVersionPlanQop.getTarget()));
                }
                if (cellVersionPlanQop.getPlanCapacity() != null) {
                    if (planCapcity.get() == null) {
                        planCapcity.set(BigDecimal.ZERO);
                    }
                    planCapcity.set(planCapcity.get().add(cellVersionPlanQop.getPlanCapacity()));

                }

            });
            CellVersionPlanQop cellVersionPlanIeByCelltype = new CellVersionPlanQop();
            cellVersionPlanIeByCelltype.setMonth(query.getMonth());
            cellVersionPlanIeByCelltype.setCellTypeName(celltypeName);
            cellVersionPlanIeByCelltype.setPlanCapacity(planCapcity.get());
            cellVersionPlanIeByCelltype.setTarget(capcity.get());
            if (planCapcity.get() != null && capcity.get() != null) {
                if (BigDecimal.ZERO.compareTo(capcity.get())!=0){
                    cellVersionPlanIeByCelltype.setRate(planCapcity.get().divide(capcity.get(), 4, RoundingMode.HALF_UP));
                }

                cellVersionPlanIeByCelltype.setGap(planCapcity.get().subtract(capcity.get()));
            }
            datas.add(cellVersionPlanIeByCelltype);
        });
        Collections.sort(details, new Comparator<CellVersionPlanQopDTO>() {
            @Override
            public int compare(CellVersionPlanQopDTO o1, CellVersionPlanQopDTO o2) {
                if (Objects.equals(o1.getWorkshopName(),o2.getWorkshopName())){
                    return o1.getCellTypeName().compareTo(o2.getCellTypeName());
                }else{
                    return o1.getWorkshopName().compareTo(o2.getWorkshopName());
                }
            }
        });
        List<CellVersionPlanQopDTO> datasByCellType = convert.toDto(datas);
        Collections.sort(datasByCellType, new Comparator<CellVersionPlanQopDTO>() {
            @Override
            public int compare(CellVersionPlanQopDTO o1, CellVersionPlanQopDTO o2) {
                return o1.getCellTypeName().compareTo(o2.getCellTypeName());
            }
        });
        MyThreadLocal.get().setLang(oldLang);
        //翻译第一行
        String workshopName = firstRow.getWorkshopName();
        workshopName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,workshopName);
        firstRow.setWorkshopName( workshopName);
        details.add(0,firstRow);
        details.addAll(datasByCellType);
        //略过第一行，翻译details
        details.stream().skip(1).forEach(cellVersionPlanQopDTO -> {
            String cellTypeName = cellVersionPlanQopDTO.getCellTypeName();
            if (StringUtils.isNotEmpty(cellTypeName)){
                cellTypeName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,cellTypeName);
                cellVersionPlanQopDTO.setCellTypeName(cellTypeName);
            }
            String myWorkshopName = cellVersionPlanQopDTO.getWorkshopName();
            if (StringUtils.isNotEmpty(myWorkshopName)){
                myWorkshopName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,myWorkshopName);
                cellVersionPlanQopDTO.setWorkshopName(myWorkshopName);
            }

        });
        mapData.put("details",details);
        mapData.put("qopVersion", qopVersion);
        mapData.put("planVersion", productionVersion);
    }

    /**
     * 对统计后的QOP数据和计划数据合并处理
     * @param cellVersionPlanQops
     * @param cellVersionPlanIeListByProduction
     * @return
     */
    private List<CellVersionPlanQop> mergeQopAndPlanData(List<CellVersionPlanQop> cellVersionPlanQops, List<CellVersionPlanQop> cellVersionPlanIeListByProduction) {
        cellVersionPlanQops.stream().forEach(cellVersionPlanQop -> {
            int index = cellVersionPlanIeListByProduction.indexOf(cellVersionPlanQop);
            if (index > -1) {
                cellVersionPlanQop.setPlanCapacity(cellVersionPlanIeListByProduction.get(index).getPlanCapacity());
            }
        });
        List<CellVersionPlanQop> collectByProduction = cellVersionPlanIeListByProduction.stream().filter(cellVersionPlanQop -> {
            return !cellVersionPlanQops.contains(cellVersionPlanQop);
        }).collect(Collectors.toList());
        List<CellVersionPlanQop> cellVersionPlanIeCollect = Stream.concat(cellVersionPlanQops.stream(), collectByProduction.stream()).collect(Collectors.toList());
        return cellVersionPlanIeCollect;
    }

    /**
     * 获取入库计划数据
     * @param query
     * @param cellInstockPlanTotalQuery
     * @return
     */
    private List<CellVersionPlanQop> preparePlanData(CellVersionPlanQopQuery query, CellInstockPlanTotalQuery cellInstockPlanTotalQuery) {
        List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTOList = instockPlanTotalService.queryByFirst(cellInstockPlanTotalQuery);
        if (cellInstockPlanTotalDTOList == null) {
            throw new BizException(query.getMonth() + "月还没有入库计划确认数据，无法版本对比");
        }
        //对查到的计划数据依据国内海外、车间、电池类型分组统计
        Map<String, Map<String, Map<String, List<CellInstockPlanTotalDTO>>>> cellInstockPlanTotalCollect =
                cellInstockPlanTotalDTOList.stream().collect(Collectors.groupingBy(CellInstockPlanTotalDTO::getIsOversea,
                        Collectors.groupingBy(CellInstockPlanTotalDTO::getWorkshop,
                                Collectors.groupingBy(CellInstockPlanTotalDTO::getCellsType))));

        List<CellVersionPlanQop> cellVersionPlanIeListByProduction = new ArrayList<>();
        cellInstockPlanTotalCollect.entrySet().stream().forEach(entryOversea -> {
            String oversea = entryOversea.getKey();
            entryOversea.getValue().entrySet().stream().forEach(entryWorkshop -> {
                String workshop = entryWorkshop.getKey();
                entryWorkshop.getValue().entrySet().stream().forEach(entryCelltype -> {
                    String celltype = entryCelltype.getKey();

                    CellVersionPlanQop cellVersionPlanQop = new CellVersionPlanQop();
                    cellVersionPlanQop.setIsOverseaName(oversea);
                    cellVersionPlanQop.setWorkshopName(workshop);
                    cellVersionPlanQop.setCellTypeName(celltype);
                    AtomicReference<BigDecimal> planCapacity = new AtomicReference<>();
                    entryCelltype.getValue().stream().forEach(cellProductionPlanTotalDTO -> {
                        BigDecimal qtyThousandPc = cellProductionPlanTotalDTO.getQtyThousandPc();
                        if (planCapacity.get() == null) {
                            planCapacity.set(BigDecimal.ZERO);
                        }
                        if (qtyThousandPc != null) {
                            planCapacity.set(planCapacity.get().add(qtyThousandPc));
                        }
                    });
                    cellVersionPlanQop.setTarget(BigDecimal.ZERO);
                    cellVersionPlanQop.setPlanCapacity(planCapacity.get());
                    cellVersionPlanIeListByProduction.add(cellVersionPlanQop);
                });
            });
        });
        // 版本数量/兆瓦系数
        cellVersionPlanIeListByProduction.stream().forEach(versionPlan -> {
            CellConversionFactorDTO cellConversionFactorDTO = cellConversionFactorService.queryByCellsType(versionPlan.getIsOverseaName(),versionPlan.getCellTypeName());
            if (null == cellConversionFactorDTO) {
                versionPlan.setPlanCapacity(null);
            } else if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) == 0) {
                versionPlan.setPlanCapacity(null);
            } else {
                if (versionPlan.getPlanCapacity() != null) {
                    versionPlan.setPlanCapacity(versionPlan.getPlanCapacity().divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP));
                }

            }
        });
        return cellVersionPlanIeListByProduction;
    }

    /**
     * 获取Qop数据
     * @param qopDetailsQuery
     * @return
     */
    private List<CellVersionPlanQop> prepareQopData(QopDetailsQuery qopDetailsQuery) {
        List<QopDetailsDTO> sopDetailsDTOS = sopDetailsService.queryByPage(qopDetailsQuery);
        if (CollectionUtils.isNotEmpty(sopDetailsDTOS)){
            if (Objects.nonNull( qopDetailsQuery.getCellTypeId())){
                sopDetailsDTOS = sopDetailsDTOS.stream().filter(sopDetailsDTO ->Objects.equals( sopDetailsDTO.getCellTypeId() ,qopDetailsQuery.getCellTypeId())).collect(Collectors.toList());
            }
        }

        sopDetailsDTOS.stream().forEach(item->{
            item.setSopQty(item.getSopQty().setScale(2,RoundingMode.HALF_UP));
        });


        //依据国内海外、电池类型、车间、月份（必须项）查询POP
        //对查到的POP数据依据国内海外、车间、电池类型分组统计
        Map<String, Map<String, Map<String, List<QopDetailsDTO>>>> collectCellBaseCapacityDTO = sopDetailsDTOS.stream().collect(
                Collectors.groupingBy(QopDetailsDTO::getIsOverseaName,
                        Collectors.groupingBy(QopDetailsDTO::getWorkshopName,
                                Collectors.groupingBy(QopDetailsDTO::getCellTypeName))));

        List<CellVersionPlanQop> cellVersionPlanQops = new ArrayList<>();
        collectCellBaseCapacityDTO.entrySet().stream().forEach(entryIsOversea -> {
            String oversea = entryIsOversea.getKey();
            entryIsOversea.getValue().entrySet().stream().forEach(entryWorkshop -> {
                String workshop = entryWorkshop.getKey();
                entryWorkshop.getValue().entrySet().stream().forEach(entryCellsType -> {
                    String cellsType = entryCellsType.getKey();
                    CellVersionPlanQop cellVersionPlanQop = new CellVersionPlanQop();
                    cellVersionPlanQop.setIsOverseaName(oversea);
                    cellVersionPlanQop.setWorkshopName(workshop);
                    cellVersionPlanQop.setCellTypeName(cellsType);

                    AtomicReference<BigDecimal> target = new AtomicReference<>();
                    entryCellsType.getValue().stream().forEach(qopDetailsDTO -> {
                        if (target.get() == null) {
                            target.set(BigDecimal.ZERO);
                        }
                        target.set(target.get().add(qopDetailsDTO.getSopQty()));

                    });
                    cellVersionPlanQop.setPlanCapacity(BigDecimal.ZERO);
                    cellVersionPlanQop.setTarget(target.get());
                    cellVersionPlanQops.add(cellVersionPlanQop);
                });
            });
        });
        return cellVersionPlanQops;
    }
}
