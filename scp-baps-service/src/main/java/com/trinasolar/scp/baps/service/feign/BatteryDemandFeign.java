package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.baps.domain.dto.bdm.DemandPlanLinesApsQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.baps.service.feign.fallback.BatteryDemandFeignFallbackFactory;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 电池需求计划
 */
@FeignClient(value = FeignConstant.SCP_BATTERY_DM_API, fallbackFactory = BatteryDemandFeignFallbackFactory.class, path = "/scp-battery-dm-api",configuration = LanguageHeaderInterceptor.class)
public interface BatteryDemandFeign {

    @PostMapping(path = "/demand-plan-header/listForAps")
    public  ResponseEntity<Results<List<BatteryDemandPlanLinesDTO>>>  list(@RequestBody DemandPlanLinesApsQuery query);

    @PostMapping("/demand-plan-lines-aps/list-baps")
    @ApiOperation(value = "需求计划明细（APS）列表给baps使用", notes = "获得需求计划明细（APS）列表给baps使用")
    public ResponseEntity<Results<List<BatteryDemandPlanLinesDTO>>> queryByConditionForBaps(@RequestBody DemandPlanLinesApsQuery query) ;
}
