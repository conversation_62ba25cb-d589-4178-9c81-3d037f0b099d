package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellVersionPlanHistoryDTO;
import com.trinasolar.scp.baps.domain.query.CellVersionPlanHistoryQuery;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanHistory;
import com.trinasolar.scp.baps.domain.query.CellVersionPlanHistoryQuery;
import com.trinasolar.scp.baps.domain.save.CellVersionPlanHistorySaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 计划与上一版本计划对比 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
public interface CellVersionPlanHistoryService {
    /**
     * 分页获取计划与上一版本计划对比
     *
     * @param query 查询对象
     * @return 计划与上一版本计划对比分页对象
     */
    Page<CellVersionPlanHistoryDTO> queryByPage(CellVersionPlanHistoryQuery query);

    /**
     * 根据主键获取计划与上一版本计划对比详情
     *
     * @param id 主键
     * @return 计划与上一版本计划对比详情
     */
        CellVersionPlanHistoryDTO queryById(Long id);

    /**
     * 保存或更新计划与上一版本计划对比
     *
     * @param saveDTO 计划与上一版本计划对比保存对象
     * @return 计划与上一版本计划对比对象
     */
    CellVersionPlanHistoryDTO save(CellVersionPlanHistorySaveDTO saveDTO);

    /**
     * 根据主键逻辑删除计划与上一版本计划对比
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellVersionPlanHistoryQuery query, HttpServletResponse response);
    Map<String,Object> makeReport(CellVersionPlanHistoryQuery query);
}

