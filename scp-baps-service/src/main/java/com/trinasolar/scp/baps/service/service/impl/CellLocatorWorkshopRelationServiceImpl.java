package com.trinasolar.scp.baps.service.service.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellLocatorWorkshopRelationDTO;
import com.trinasolar.scp.baps.domain.convert.CellLocatorWorkshopRelationDEConvert;
import com.trinasolar.scp.baps.domain.dto.aps.CellBooksDTO;
import com.trinasolar.scp.baps.domain.entity.CellLocatorWorkshopRelation;
import com.trinasolar.scp.baps.domain.entity.QCellLocatorWorkshopRelation;
import com.trinasolar.scp.baps.domain.excel.CellLocatorWorkshopRelationExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellLocatorWorkshopRelationQuery;
import com.trinasolar.scp.baps.domain.query.aps.CellBooksForBapsQuery;
import com.trinasolar.scp.baps.domain.save.CellLocatorWorkshopRelationSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.repository.CellLocatorWorkshopRelationRepository;
import com.trinasolar.scp.baps.service.service.CellLocatorWorkshopRelationService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * 货位对应车间关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 02:57:01
 */
@Slf4j
@Service("cellLocatorWorkshopRelationService")
@RequiredArgsConstructor
public class CellLocatorWorkshopRelationServiceImpl implements CellLocatorWorkshopRelationService {
    private static final QCellLocatorWorkshopRelation qCellLocatorWorkshopRelation = QCellLocatorWorkshopRelation.cellLocatorWorkshopRelation;

    private final CellLocatorWorkshopRelationDEConvert convert;

    private final CellLocatorWorkshopRelationRepository repository;
    private final ApsFeign apsFeign;

    @Override
    public Page<CellLocatorWorkshopRelationDTO> queryByPage(CellLocatorWorkshopRelationQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellLocatorWorkshopRelation> page = repository.findAll(booleanBuilder, pageable);
        List<CellLocatorWorkshopRelationDTO> dtos = convert.toDto(page.getContent());
        convetData(dtos);
        return new PageImpl(dtos, page.getPageable(), page.getTotalElements());
    }

    private void convetData(List<CellLocatorWorkshopRelationDTO> dtos) {
        Map<String, LovLineDTO> allByHeaderCode = Optional.ofNullable(LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP)).orElse(Maps.newHashMap());
        Map<String, String> workshopMap = allByHeaderCode.values().stream().collect(Collectors.toMap(LovLineDTO::getLovValue, LovLineDTO::getLovName, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(dtos)) {
            dtos.forEach(dto -> {
                dto.setWorkshopName(workshopMap.get(dto.getWorkshop()));
            });
        }
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellLocatorWorkshopRelationQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellLocatorWorkshopRelation.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getLocator())) {
            booleanBuilder.and(qCellLocatorWorkshopRelation.locator.eq(query.getLocator()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellLocatorWorkshopRelation.workshop.eq(query.getWorkshop()));
        }
    }

    @Override
    public CellLocatorWorkshopRelationDTO queryById(Long id) {
        CellLocatorWorkshopRelation queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellLocatorWorkshopRelationDTO save(CellLocatorWorkshopRelationSaveDTO saveDTO) {
        CellLocatorWorkshopRelation newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellLocatorWorkshopRelation());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellLocatorWorkshopRelationQuery query, HttpServletResponse response) {
        List<CellLocatorWorkshopRelationDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportExWithLocalDate(response, "货位对应车间关系表", "货位对应车间关系表", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellLocatorWorkshopRelationExcelDTO> excelDtos = ExcelUtils.readExcel(multipartFile.getInputStream(), null, CellLocatorWorkshopRelationExcelDTO.class, excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("baps_import_file_error");
        }
        //验证数据
        checkInput(excelDtos);
        List<CellLocatorWorkshopRelationSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        saveDTOS.stream().forEach(saveDTO -> {
            save(saveDTO);
        });

    }

    @Override
    public List<CellLocatorWorkshopRelation> getAll() {
        return getRelationList();
    }

    private List<CellLocatorWorkshopRelation> getRelationList() {
        CellBooksForBapsQuery query = new CellBooksForBapsQuery();
        query.setBooksTypeList(Arrays.asList(LovHeaderCodeConstant.BOOKS_TYPE_CELL, LovHeaderCodeConstant.BOOKS_TYPE_CELL_COMPONENT));
        ResponseEntity<Results<List<CellBooksDTO>>> response = apsFeign.getCellLocatorWorkshopRelation(query);
        if (!response.getBody().isSuccess()) {
            throw new BizException("baps_call_aps_cell_books_interface_failed");
        }
        List<CellBooksDTO> cellBooksDTOS = Optional.ofNullable(response).map(item -> response.getBody()).map(body -> body.getData()).orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(cellBooksDTOS)) {
            cellBooksDTOS = cellBooksDTOS.stream().filter(dto -> StringUtils.isNotEmpty(dto.getSubCellSlotting())).collect(Collectors.toList());
        }
        //构建货位到车间的关系
        List<CellLocatorWorkshopRelation> relationList = Lists.newArrayList();
        cellBooksDTOS.stream().forEach(dto -> {
            String subCellSlotting = dto.getSubCellSlotting();
            subCellSlotting = subCellSlotting.replace("，", ",");
            List<String> subCellSlottingList = Arrays.asList(subCellSlotting.split(","));
            subCellSlottingList.stream().forEach(subCellSlottingItem -> {
                CellLocatorWorkshopRelation relation = new CellLocatorWorkshopRelation();
                relation.setLocator(subCellSlottingItem.trim());
                relation.setWorkshop(dto.getWorkshop());
                relationList.add(relation);
            });
        });
        return relationList;
    }

    private void checkInput(List<CellLocatorWorkshopRelationExcelDTO> excelDtos) {
        Integer row = 1;

        Map<String, List<Integer>> map = Maps.newHashMap();
        Set<String> locatorSet = Sets.newHashSet();
        for (CellLocatorWorkshopRelationExcelDTO excelDto : excelDtos) {
            if (StringUtils.isBlank(excelDto.getLocator()) || StringUtils.isBlank(excelDto.getWorkshopName())) {
                throw new BizException("baps_row_location_or_workshop_not_null", row);
            }
            if (Objects.isNull(LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDto.getWorkshopName()))) {
                throw new BizException("baps_row_workshop_not_exist", row);
            }
            if (!locatorSet.add(excelDto.getLocator())) {
                throw new BizException("baps_row_goods_location_duplicate", excelDto.getLocator());
            }
            String key = Joiner.on(",").join(excelDto.getLocator(), excelDto.getWorkshopName());
            if (map.containsKey(key)) {
                List<Integer> integers = map.get(key);
                integers.add(row);
            } else {
                List<Integer> integers = Lists.newArrayList();
                integers.add(row);
                map.put(key, integers);
            }
            row++;
        }
        List<String> errors = Lists.newArrayList();
        map.forEach((k, v) -> {
            if (v.size() > 1) {
                errors.add(k + ":" + Joiner.on(",").join(v) + "行存在重复");
            }
        });
        if (CollectionUtils.isNotEmpty(errors)) {
            throw new BizException(Joiner.on("\n").join(errors));
        }
    }

    public static void main(String[] args) {
        List<String> subCellSlottingList = Arrays.asList("aaa,bbb,ccc".split(","));
        subCellSlottingList.stream().forEach(subCellSlottingItem -> {
            System.out.println(subCellSlottingItem);
        });
    }
}
