package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellProductionLeadTime;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 投产提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Repository
public interface CellProductionLeadTimeRepository extends JpaRepository<CellProductionLeadTime, Long>, QuerydslPredicateExecutor<CellProductionLeadTime> {
}
