package com.trinasolar.scp.baps.service.service.erp.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.trinasolar.scp.baps.domain.entity.ErpWipIssue;
import com.trinasolar.scp.baps.domain.query.erp.SyncWipIssueQuery;
import com.trinasolar.scp.baps.service.feign.Cux3WipErpFeign;
import com.trinasolar.scp.baps.service.repository.ErpWipIssueRepository;
import com.trinasolar.scp.baps.service.service.erp.DataSyncService;
import com.trinasolar.scp.baps.service.service.system.SystemService;
import com.trinasolar.scp.common.api.util.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@AllArgsConstructor
@Service("dataSyncService")
@Slf4j
public class DataSyncServiceImpl implements DataSyncService {
    private final static int PAGE_SIZE = 1000;
    private final Cux3WipErpFeign erpFeign;
    private final SystemService systemService;
    private final ErpWipIssueRepository erpWipIssueRepository;
    @Transactional(rollbackOn = Exception.class)
    @Override
    public void syncWipIssue(LocalDate fromDate, LocalDate toDate) {
        //获取所有生效的库存组织code
        List<Long> organizationIds = systemService.findOrganizationIds();
        LocalDate beginDate = LocalDate.of(2024, 5, 1);
        LocalDate endDate = LocalDate.now();
        if (Objects.nonNull(fromDate) ) {
            beginDate = fromDate;
            if (Objects.nonNull(toDate)) {
                if (toDate.isBefore(endDate)){
                    endDate = toDate;
                }
            }

        }
        if (beginDate.isAfter(endDate)){
            return;
        }
        LocalDate finalEndDate = endDate;
        LocalDate finalBeginDate = beginDate;
        //依据不同组织机构查询
        organizationIds.stream().forEach(id -> {
            // 获取最后更新时间
            ErpWipIssue lastUpdateObj = erpWipIssueRepository.findLastUpdateObj(id);
            if (lastUpdateObj != null) {
                LocalDateTime fromDateTime = LocalDateTimeUtil.of(lastUpdateObj.getCreationDate()).plusSeconds(1);
                LocalDateTime toDateTime = finalEndDate.atTime(9, 31, 59);
                syncWipIssueByOrganizationId(fromDateTime, toDateTime, id);
            } else {
                syncWipIssueByOrganizationId(finalBeginDate.atTime(0, 0, 0), finalEndDate.atTime(9, 31, 59), id);
            }
        });


    }

    private void syncWipIssueByOrganizationId(LocalDateTime fromDateTime, LocalDateTime toDateTime, Long organizationId) {
        log.info("开始同步库存组织ID：{}", organizationId);
        SyncWipIssueQuery query = new SyncWipIssueQuery();

        query.setOrganizationIds(Arrays.asList(organizationId));

        query.setPageNum(1);
        query.setPageSize(PAGE_SIZE);
        query.setTransactionDateFrom(fromDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        query.setTransactionDateTo(toDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        List<Long> transactionTypeIds = Arrays.asList(44L, 17L, 38L, 48L, 35L, 42L);
        query.setTransactionTypeIds(transactionTypeIds);
        JSONObject response;
        do {
            response = erpFeign.cux3WipPage(query);
            if (response == null  ) {
                log.error("ERP接口访问出错！");
                throw new BizException("baps_call_erp_interface_failed");

            }
            query.setPageNum(query.getPageNum() + 1);
            JSONArray dataArray = (JSONArray) response.get("records");
            if (dataArray != null) {
                List<ErpWipIssue> wipIssueList = Lists.newArrayListWithCapacity(dataArray.size());
                for (Object obj : dataArray) {
                    JSONObject data = (JSONObject) obj;
                    ErpWipIssue wipIssue = new ErpWipIssue();
                    wipIssue.setTransactionQuantity(data.getBigDecimal("transactionQuantity"));
                    wipIssue.setItemNumber(data.getString("itemNumber"));
                    wipIssue.setTransferOrganizationId(data.getLong("transferOrganizationId"));
                    wipIssue.setTransactionUomCode(data.getString("transactionUomCode"));
                    wipIssue.setOrganizationId(data.getLong("organizationId"));
                    wipIssue.setItemDescription(data.getString("itemDescription"));
                    wipIssue.setLocatorCode(data.getString("locatorCode"));
                    wipIssue.setTransactionSourceTypeName(data.getString("transactionSourceTypeName"));
                    wipIssue.setWorkOrder(data.getString("workOrder"));
                    wipIssue.setOrganizationName(data.getString("organizationName"));
                    wipIssue.setTransactionTypeId(data.getLong("transactionTypeId"));
                    wipIssue.setLotPrimaryQuantity(data.getBigDecimal("lotPrimaryQuantity"));
                    wipIssue.setSubinventoryCode(data.getString("subinventoryCode"));
                    wipIssue.setLotNumber(data.getString("lotNumber"));
                    wipIssue.setTransactionDate(LocalDateTimeUtil.of(data.getDate("transactionDate")));
                    wipIssue.setTransactionId(data.getLong("transactionId"));
                    wipIssue.setOrganizationId(data.getLong("organizationId"));
                    wipIssue.setTransactionSourceTypeId(data.getLong("transactionSourceTypeId"));
                    wipIssue.setInventoryItemId(data.getLong("inventoryItemId"));
                    wipIssue.setOrganizationCode(data.getString("organizationCode"));
                    wipIssue.setLocatorId(data.getLong("locatorId"));
                    wipIssue.setPrimaryQuantity(data.getBigDecimal("primaryQuantity"));
                    wipIssue.setLotTransactionQuantity(data.getBigDecimal("lotTransactionQuantity"));
                    wipIssue.setCreationDate(LocalDateTimeUtil.of(data.getDate("creationDate")));
                    wipIssueList.add(wipIssue);
                }
                if (CollectionUtils.isNotEmpty(wipIssueList)) {
                    erpWipIssueRepository.saveAll(wipIssueList);
                }
            }
        } while (response.get("records") != null && ((JSONArray) response.get("records")).size() == PAGE_SIZE);
        log.info("结束同步库存组织ID：{}", organizationId);
    }

}
