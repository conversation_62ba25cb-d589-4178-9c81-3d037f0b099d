package com.trinasolar.scp.baps.service.service.impl;
import com.trinasolar.scp.baps.domain.dto.ConfigCellGoodDTO;
import com.trinasolar.scp.baps.domain.query.ConfigCellGoodQuery;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.service.feign.ConfigCellGoodFeign;
import com.trinasolar.scp.baps.service.feign.PageFeign;
import com.trinasolar.scp.baps.service.service.ConfigCellGoodService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.util.Results;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
@Slf4j
@Service("configCellGoodService")
public class ConfigCellGoodServiceImpl implements ConfigCellGoodService {
    @Autowired
    private ConfigCellGoodFeign configCellGoodFeign;
    @Override
    public List<ConfigCellGoodDTO> getConfigCellGoods(List<Integer> years) {
        //获取那几年的数据（最近两年或三年（年中前三年，年后后两年））
        List<ConfigCellGoodDTO> datas=new ArrayList<>();
        years.stream().forEach(year->{
            ConfigCellGoodQuery query=new ConfigCellGoodQuery();
            query.setPageNumber(1);
            query.setPageSize(GlobalConstant.max_page_size);
            query.setYear(year+"");
            query.setParameterType("1572156321808125952");//满产
            datas.addAll(configCellGoodFeign.queryByPage(query).getBody().getData().getContent());
        });


        return datas;
    }
}
