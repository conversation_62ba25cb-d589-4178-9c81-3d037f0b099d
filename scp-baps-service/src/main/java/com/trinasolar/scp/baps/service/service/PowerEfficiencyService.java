package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.PowerEfficiencyDTO;
import com.trinasolar.scp.baps.domain.query.PowerEfficiencyQuery;
import com.trinasolar.scp.common.api.base.PageFeign;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface PowerEfficiencyService {
    List<PowerEfficiencyDTO> queryByPage();

    List<PowerEfficiencyDTO> queryByPageCN();

    List<PowerEfficiencyDTO> queryWithRelations(PowerEfficiencyQuery query);

    /**
     * 更新数据
     */
    void refreshData();
}
