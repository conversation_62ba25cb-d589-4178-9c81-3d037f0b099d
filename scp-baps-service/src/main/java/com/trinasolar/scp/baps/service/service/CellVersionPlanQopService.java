package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellVersionPlanQopDTO;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanQop;
import com.trinasolar.scp.baps.domain.query.CellVersionPlanQopQuery;
import com.trinasolar.scp.baps.domain.save.CellVersionPlanQopSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 计划与qop 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
public interface CellVersionPlanQopService {
    /**
     * 分页获取计划与qop
     *
     * @param query 查询对象
     * @return 计划与qop分页对象
     */
    Page<CellVersionPlanQopDTO> queryByPage(CellVersionPlanQopQuery query);

    /**
     * 版本对比计划与QOP
     * @param query 查询对象
     * @return 计划与qop分页对象
     */
    Map<String, Object> makeReport(CellVersionPlanQopQuery query);

    /**
     * 根据主键获取计划与qop详情
     *
     * @param id 主键
     * @return 计划与qop详情
     */
        CellVersionPlanQopDTO queryById(Long id);

    /**
     * 保存或更新计划与qop
     *
     * @param saveDTO 计划与qop保存对象
     * @return 计划与qop对象
     */
    CellVersionPlanQopDTO save(CellVersionPlanQopSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除计划与qop
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellVersionPlanQopQuery query, HttpServletResponse response);
}

