package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.PriorityLogisticsLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 物流线路优先级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:51:00
 */
@Repository
public interface PriorityLogisticsLineRepository extends JpaRepository<PriorityLogisticsLine, Long>, QuerydslPredicateExecutor<PriorityLogisticsLine> {
}
