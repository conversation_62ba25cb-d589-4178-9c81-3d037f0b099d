package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.system.OrganizationDefinitionsDTO;
import com.trinasolar.scp.baps.domain.dto.system.OrganizationDefinitionsQuery;
import com.trinasolar.scp.common.api.base.DataPrivilegeDTO;
import com.trinasolar.scp.common.api.base.DataPrivilegeQuery;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.LovLineQuery;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 获取系统LOV值集
 *
 * @author: darke
 * @create: 2022年6月17日09:14:45
 */

@FeignClient(path = "scp-system-api", value = "scp-system-api")
public interface SystemFeign {

    @PostMapping("/lovLine/queryByCode")
    ResponseEntity<Results<List<LovLineDTO>>> queryByCode(@RequestBody LovLineQuery lovLineQuery);

    /**
     * 获取用户数据查询权限
     */

    @PostMapping("/data-privilege/list")
    ResponseEntity<Results<List<DataPrivilegeDTO>>> queryDataPrivilege(@RequestBody DataPrivilegeQuery query);

    @PostMapping("/organization-definitions/listForAps")
    ResponseEntity<Results<List<OrganizationDefinitionsDTO>>> listForAps(@RequestBody OrganizationDefinitionsQuery query);

}
