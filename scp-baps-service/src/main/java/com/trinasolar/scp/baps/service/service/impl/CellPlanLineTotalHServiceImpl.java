package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalHDTO;
import com.trinasolar.scp.baps.domain.convert.CellPlanLineTotalHDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineTotalH;
import com.trinasolar.scp.baps.domain.entity.QCellPlanLineTotalH;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineTotalHExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalHQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineTotalHSaveDTO;
import com.trinasolar.scp.baps.service.repository.CellPlanLineTotalHRepository;
import com.trinasolar.scp.baps.service.service.CellPlanLineTotalHService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.LocalDateConverter;
import com.trinasolar.scp.common.api.util.LocalDateTimeConverter;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 投产计划H汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-31 06:53:01
 */
@Slf4j
@Service("cellPlanLineTotalHService")
@RequiredArgsConstructor
public class CellPlanLineTotalHServiceImpl implements CellPlanLineTotalHService {
    private static final QCellPlanLineTotalH qCellPlanLineTotalH = QCellPlanLineTotalH.cellPlanLineTotalH;

    private final CellPlanLineTotalHDEConvert convert;

    private final CellPlanLineTotalHRepository repository;

    @Override
    public Page<CellPlanLineTotalHDTO> queryByPage(CellPlanLineTotalHQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellPlanLineTotalH> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellPlanLineTotalHQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.isOverseaId.eq(query.getIsOverseaId()));
        }
        if(StringUtils.isNotEmpty(query.getIsOversea())){
            booleanBuilder.and(qCellPlanLineTotalH.isOversea.eq(query.getIsOversea()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlace())){
            booleanBuilder.and(qCellPlanLineTotalH.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.basePlaceId.eq(query.getBasePlaceId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qCellPlanLineTotalH.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.workshopId.eq(query.getWorkshopId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkunit())){
            booleanBuilder.and(qCellPlanLineTotalH.workunit.eq(query.getWorkunit()));
        }
        if (query.getWorkunitId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.workunitId.eq(query.getWorkunitId()));
        }
        if(StringUtils.isNotEmpty(query.getLineName())){
            booleanBuilder.and(qCellPlanLineTotalH.lineName.eq(query.getLineName()));
        }
        if(StringUtils.isNotEmpty(query.getCellsType())){
            booleanBuilder.and(qCellPlanLineTotalH.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if(StringUtils.isNotEmpty(query.getCellSource())){
            booleanBuilder.and(qCellPlanLineTotalH.cellSource.eq(query.getCellSource()));
        }
        if (query.getCellSourceId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.cellSourceId.eq(query.getCellSourceId()));
        }
        if(StringUtils.isNotEmpty(query.getIsSpecialRequirement())){
            booleanBuilder.and(qCellPlanLineTotalH.isSpecialRequirement.eq(query.getIsSpecialRequirement()));
        }
        if(StringUtils.isNotEmpty(query.getLowResistance())){
            booleanBuilder.and(qCellPlanLineTotalH.lowResistance.eq(query.getLowResistance()));
        }
        if(StringUtils.isNotEmpty(query.getCellMfrs())){
            booleanBuilder.and(qCellPlanLineTotalH.cellMfrs.eq(query.getCellMfrs()));
        }
        if (query.getCellMfrsId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.cellMfrsId.eq(query.getCellMfrsId()));
        }
        if(StringUtils.isNotEmpty(query.getSilverPulpMfrs())){
            booleanBuilder.and(qCellPlanLineTotalH.silverPulpMfrs.eq(query.getSilverPulpMfrs()));
        }
        if (query.getSilverPulpMfrsId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.silverPulpMfrsId.eq(query.getSilverPulpMfrsId()));
        }
        if(StringUtils.isNotEmpty(query.getSiMfrs())){
            booleanBuilder.and(qCellPlanLineTotalH.siMfrs.eq(query.getSiMfrs()));
        }
        if (query.getSiMfrsId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.siMfrsId.eq(query.getSiMfrsId()));
        }
        if(StringUtils.isNotEmpty(query.getScreenPlateMfrs())){
            booleanBuilder.and(qCellPlanLineTotalH.screenPlateMfrs.eq(query.getScreenPlateMfrs()));
        }
        if (query.getScreenPlateMfrsId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.screenPlateMfrsId.eq(query.getScreenPlateMfrsId()));
        }
        if(StringUtils.isNotEmpty(query.getSpecialOrderNo())){
            booleanBuilder.and(qCellPlanLineTotalH.specialOrderNo.eq(query.getSpecialOrderNo()));
        }
        if(StringUtils.isNotEmpty(query.getVerificationMark())){
            booleanBuilder.and(qCellPlanLineTotalH.verificationMark.eq(query.getVerificationMark()));
        }
        if (query.getWaferYieldRatio() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.waferYieldRatio.eq(query.getWaferYieldRatio()));
        }
        if (query.getWaferGradeRatio() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.waferGradeRatio.eq(query.getWaferGradeRatio()));
        }
        if(StringUtils.isNotEmpty(query.getVersion())){
            booleanBuilder.and(qCellPlanLineTotalH.version.eq(query.getVersion()));
        }
        if(StringUtils.isNotEmpty(query.getMonth())){
            booleanBuilder.and(qCellPlanLineTotalH.month.eq(query.getMonth()));
        }
        if(StringUtils.isNotEmpty(query.getHTrace())){
            booleanBuilder.and(qCellPlanLineTotalH.hTrace.eq(query.getHTrace()));
        }
        if (query.getHTraceId() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.hTraceId.eq(query.getHTraceId()));
        }
        if (query.getD1() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d1.eq(query.getD1()));
        }
        if (query.getD2() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d2.eq(query.getD2()));
        }
        if (query.getD3() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d3.eq(query.getD3()));
        }
        if (query.getD4() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d4.eq(query.getD4()));
        }
        if (query.getD5() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d5.eq(query.getD5()));
        }
        if (query.getD6() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d6.eq(query.getD6()));
        }
        if (query.getD7() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d7.eq(query.getD7()));
        }
        if (query.getD8() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d8.eq(query.getD8()));
        }
        if (query.getD9() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d9.eq(query.getD9()));
        }
        if (query.getD10() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d10.eq(query.getD10()));
        }
        if (query.getD11() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d11.eq(query.getD11()));
        }
        if (query.getD12() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d12.eq(query.getD12()));
        }
        if (query.getD13() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d13.eq(query.getD13()));
        }
        if (query.getD14() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d14.eq(query.getD14()));
        }
        if (query.getD15() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d15.eq(query.getD15()));
        }
        if (query.getD16() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d16.eq(query.getD16()));
        }
        if (query.getD17() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d17.eq(query.getD17()));
        }
        if (query.getD18() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d18.eq(query.getD18()));
        }
        if (query.getD19() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d19.eq(query.getD19()));
        }
        if (query.getD20() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d20.eq(query.getD20()));
        }
        if (query.getD21() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d21.eq(query.getD21()));
        }
        if (query.getD22() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d22.eq(query.getD22()));
        }
        if (query.getD23() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d23.eq(query.getD23()));
        }
        if (query.getD24() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d24.eq(query.getD24()));
        }
        if (query.getD25() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d25.eq(query.getD25()));
        }
        if (query.getD26() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d26.eq(query.getD26()));
        }
        if (query.getD27() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d27.eq(query.getD27()));
        }
        if (query.getD28() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d28.eq(query.getD28()));
        }
        if (query.getD29() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d29.eq(query.getD29()));
        }
        if (query.getD30() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d30.eq(query.getD30()));
        }
        if (query.getD31() != null) {
            booleanBuilder.and(qCellPlanLineTotalH.d31.eq(query.getD31()));
        }
    }

    @Override
    public CellPlanLineTotalHDTO queryById(Long id) {
        CellPlanLineTotalH queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellPlanLineTotalHDTO save(CellPlanLineTotalHSaveDTO saveDTO) {
        CellPlanLineTotalH newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new CellPlanLineTotalH());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellPlanLineTotalHQuery query, HttpServletResponse response) {
       List<CellPlanLineTotalHDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "投产计划H汇总表", "投产计划H汇总表", excelPara.getSimpleHeader(), excelData);
    }
}
