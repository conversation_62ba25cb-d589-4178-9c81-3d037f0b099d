package com.trinasolar.scp.baps.service.service.impl;

import com.trinasolar.scp.baps.domain.dto.MaxVersionSliceSupplyAndDmaterialDeliveryDTO;
import com.trinasolar.scp.baps.domain.dto.SiliconSliceSupplyLinesDTO;
import com.trinasolar.scp.baps.domain.query.MaxVersionSliceSupplyAndDmaterialDeliveryQuery;
import com.trinasolar.scp.baps.domain.query.SiliconSliceSupplyLinesQuery;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.feign.SiliconSliceSupplyLinesFeign;
import com.trinasolar.scp.baps.service.service.SiliconSliceSupplyLinesService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service("siliconSliceSupplyLinesService")
@RequiredArgsConstructor
public class SiliconSliceSupplyLinesServiceImpl implements SiliconSliceSupplyLinesService {
    private final SiliconSliceSupplyLinesFeign feign;

    @Override
    public List<SiliconSliceSupplyLinesDTO> getSupplyByMonth(String month) {
        SiliconSliceSupplyLinesQuery query = new SiliconSliceSupplyLinesQuery();
        query.setMonth(month);
        return feign.list(query).getBody().getData();
    }

    @Override
    public Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> getSupplyByMonths(List<String> months, LocalDate inventoryDate) {
        MaxVersionSliceSupplyAndDmaterialDeliveryQuery maxVersionSliceSupplyAndDmaterialDeliveryQuery = new MaxVersionSliceSupplyAndDmaterialDeliveryQuery();
        maxVersionSliceSupplyAndDmaterialDeliveryQuery.setMonths(months);
        maxVersionSliceSupplyAndDmaterialDeliveryQuery.setStartDate(inventoryDate);
        ResponseEntity<Results<List<MaxVersionSliceSupplyAndDmaterialDeliveryDTO>>> resultsResponseEntity =
                feign.queryMaxVersionSliceSupplyAndDmaterialDelivery(maxVersionSliceSupplyAndDmaterialDeliveryQuery);
        List<MaxVersionSliceSupplyAndDmaterialDeliveryDTO> data = resultsResponseEntity.getBody().getData();

        // processType 需要 转换
        Map<String, String> processTypeConverter = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.SILICON_PROCESS_CATEGORY).values()
                .stream().collect(Collectors.toMap(LovLineDTO::getLovValue, LovLineDTO::getAttribute1, (a, b) -> b));

        // 分组转换
        LocalDate firstDay = inventoryDate.plusDays(1);
        Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> result = data.stream()
                .peek(i -> {
                    if (i.getDemandDate().isBefore(firstDay)) {
                        i.setDemandDate(firstDay);
                    }
                })
                .collect(
                Collectors.groupingBy(MaxVersionSliceSupplyAndDmaterialDeliveryDTO::getItemCode,
                        Collectors.groupingBy(MaxVersionSliceSupplyAndDmaterialDeliveryDTO::getBasePlace,
                                Collectors.groupingBy(MaxVersionSliceSupplyAndDmaterialDeliveryDTO::getDemandDate,
                                        Collectors.toMap(i -> processTypeConverter.getOrDefault(i.getProcessType(), ""), MaxVersionSliceSupplyAndDmaterialDeliveryDTO::getQuantity,
                                                (a, b) -> Optional.ofNullable(a).orElse(BigDecimal.ZERO).add(
                                                        Optional.ofNullable(b).orElse(BigDecimal.ZERO)
                                                )
                                        )
                                ))));

        // 处理为负数的情况,从后往前扣减
        result.forEach((itemCode, basePlaceMap) -> {
            basePlaceMap.forEach((basePlace, dateQuantityMap) -> {
                List<LocalDate> dates = dateQuantityMap.keySet().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                Map<String, BigDecimal> leftQtys = new HashMap<>();
                dates.forEach(date -> {
                    Map<String, BigDecimal> processTypeAndQtyMap = dateQuantityMap.get(date);
                    processTypeAndQtyMap.forEach((processType, quantity) -> {
                        BigDecimal lastQuantity = leftQtys.getOrDefault(processType, BigDecimal.ZERO);
                        if (quantity.compareTo(BigDecimal.ZERO) > 0) {
                            // 进行扣减
                            if (lastQuantity.compareTo(BigDecimal.ZERO) < 0) {
                                if (quantity.add(lastQuantity).compareTo(BigDecimal.ZERO) > 0) {
                                    processTypeAndQtyMap.put(processType, quantity.add(lastQuantity));
                                    leftQtys.put(processType, BigDecimal.ZERO);
                                } else {
                                    processTypeAndQtyMap.put(processType, BigDecimal.ZERO);
                                    leftQtys.put(processType, quantity.add(lastQuantity));
                                }
                            }
                        } else {
                            leftQtys.put(processType, quantity.add(lastQuantity));
                        }
                    });
                });
            });
        });
        return result;
    }
}
