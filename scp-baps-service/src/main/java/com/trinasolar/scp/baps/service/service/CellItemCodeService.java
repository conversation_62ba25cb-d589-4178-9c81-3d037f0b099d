package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.entity.CellInstockPlan;
import com.trinasolar.scp.baps.domain.entity.CellPlanLine;

import java.util.List;
import java.util.Map;

public interface CellItemCodeService {
    public   Map<Long,List<String>>  getItemCodesByCellPlanLine(List<CellPlanLine> cellPlanLines);

    Map<Long,String> getItemCodesByCellInstockPlan(List<CellInstockPlan> cellInstockPlans);
}
