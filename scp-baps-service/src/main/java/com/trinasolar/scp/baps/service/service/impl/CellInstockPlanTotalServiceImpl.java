package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanTotalDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanVersionDEConvert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.dto.bbom.BatteryScreenPlateWorkshopDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.BatterySiliconWaferDTO;
import com.trinasolar.scp.baps.domain.dto.message.EmailAddress;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanTotal;
import com.trinasolar.scp.baps.domain.entity.QCellInstockPlan;
import com.trinasolar.scp.baps.domain.entity.QCellInstockPlanTotal;
import com.trinasolar.scp.baps.domain.enums.PlanChangeStatusEnum;
import com.trinasolar.scp.baps.domain.excel.CellInstockPlanTotalExcelDTO;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.baps.domain.query.bbom.BatteryScreenPlateWorkshopQuery;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanTotalSaveDTO;
import com.trinasolar.scp.baps.domain.utils.*;
import com.trinasolar.scp.baps.service.feign.BbomFeign;
import com.trinasolar.scp.baps.service.feign.PageFeign;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanRepository;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanTotalRepository;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanVersionRepository;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.baps.service.service.log.LogService;
import com.trinasolar.scp.baps.service.service.mail.MailService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.common.api.util.exStrategy.CellStyleModel;
import com.trinasolar.scp.common.api.util.exStrategy.CustomCellStyleHandler;
import jodd.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 入库计划汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-26 11:20:51
 */
@Slf4j
@Service("cellInstockPlanTotalService")
@RequiredArgsConstructor
public class CellInstockPlanTotalServiceImpl implements CellInstockPlanTotalService {
    private static final QCellInstockPlanTotal qCellInstockPlanTotal = QCellInstockPlanTotal.cellInstockPlanTotal;
    private final CellInstockPlanTotalDEConvert convert;
    private final CellInstockPlanTotalRepository repository;
    private final CellInstockPlanService cellInstockPlanService;
    private final CellInstockPlanRepository cellInstockPlanRepository;
    private final JPAQueryFactory jpaQueryFactory;
    private final CellInstockPlanDEConvert cellInstockPlanDEConvert;
    private final CellConversionFactorService cellConversionFactorService;
    private final MailService mailService;
    private final CellInstockPlanVersionDEConvert cellInstockPlanVersionDEConvert;
    private final CellInstockPlanVersionRepository cellInstockPlanVersionRepository;
    private final LogService logService;
    private final RedissonClient redissonClient;
    private final CellTypeRuleService cellTypeRuleService;
    private final CellInstockPlanVersionService cellInstockPlanVersionService;
    private final CellInStockPlanRemarkService cellInStockPlanRemarkService;
    private final CellInStockPlanTotalRemarkService cellInStockPlanTotalRemarkService;
    private final BbomFeign bbomFeign;
    private final static Joiner JOINER = Joiner.on("_").useForNull("null");
    @Override
    public Page<CellInstockPlanTotalDTO> queryByPage(CellInstockPlanTotalQuery query, Pair<String, String> versions) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        query.setVersionsPair(versions);

        //读取入库汇总表里的fromVersion最新版本号
        CellInstockPlanQuery planQuery = new CellInstockPlanQuery();
        BeanUtils.copyProperties(query,planQuery);
        String lastFromVersionOfCellPlanLineTotal = "";
        if(StringUtils.isBlank(query.getBusinessType())){
            lastFromVersionOfCellPlanLineTotal = getLastFromVersion(query.getMonth(), query.getIsOversea());

        } else if("1".equals(query.getBusinessType())) {
             lastFromVersionOfCellPlanLineTotal = StringUtils.isNotBlank(versions.getLeft()) ? versions.getLeft() : versions.getRight();
        }
        if (StringUtils.isNotBlank(lastFromVersionOfCellPlanLineTotal)  && (lastFromVersionOfCellPlanLineTotal.equals(versions.getLeft()) || lastFromVersionOfCellPlanLineTotal.equals(versions.getRight()))) {
            //直接查询入库计划汇总表里的数据
            return getCellInstockPlanTotalByQuery(query, oldLang);
        } else {
            //查询入库计划表里的数据进行汇总返回
            return calcCellInstockPlanTotalByQuery(convert.toCellInstockPlan(query), oldLang);

        }
    }

    @Override
    public Pair<String, String> getSendedEmailOrLastVersion(CellInstockPlanTotalQuery query, Boolean sendedEmailVersionFlag) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (!sendedEmailVersionFlag) {
            if (StringUtils.isNotEmpty(query.getIsOversea())) {
                return getPlanLastVersion(query);
            } else {
                String isOversea = OverseaConstant.INLAND;
                String InVersion = getPlanLastVersion(month, isOversea);
                isOversea = OverseaConstant.OVERSEA;
                String outVersion = getPlanLastVersion(month, isOversea);
                return new ImmutablePair<>(InVersion, outVersion);
            }
        } else {
            if (StringUtils.isNotEmpty(query.getIsOversea())) {
                Long isOverseaId = LovUtils.get(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()).getLovLineId();
                String version = cellInstockPlanVersionService.findMaxVersionSendedEmail(isOverseaId, month);
                if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                    return new ImmutablePair<>(version, null);
                } else {
                    return new ImmutablePair<>(null, version);
                }
            } else {
                String inVersion = cellInstockPlanVersionService.findMaxVersionSendedEmail(OverseaConstant.INLAND_ID, month);
                String outVersion = cellInstockPlanVersionService.findMaxVersionSendedEmail(OverseaConstant.OVERSEA_ID, month);
                return new ImmutablePair<>(inVersion, outVersion);
            }
        }
    }

    /**
     * 统计汇总最新版本数据(从汇总表直接读)
     *
     * @param query
     * @return
     */
    private Page<CellInstockPlanTotalDTO> getCellInstockPlanTotalByQuery(CellInstockPlanTotalQuery query, String oldLang) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Pair<String, String> versions = query.getVersionsPair();
        if (StringUtils.isNotEmpty(versions.getLeft()) && StringUtils.isNotEmpty(versions.getRight())) {
            booleanBuilder.and(qCellInstockPlanTotal.fromVersion.in(versions.getLeft(), versions.getRight()));
        } else if (StringUtils.isNotEmpty(versions.getLeft())) {
            booleanBuilder.and(qCellInstockPlanTotal.fromVersion.eq(versions.getLeft()));
        } else if (StringUtils.isNotEmpty(versions.getRight())) {
            booleanBuilder.and(qCellInstockPlanTotal.fromVersion.eq(versions.getRight()));
        }
        Sort sort = Sort.by(Sort.Direction.ASC, "isOversea", "basePlace", "workshop", "cellsType", "itemCode", "hTrace", "cellSource", "regionalCountry", "transparentDoubleGlass", "aesthetics", "productionGrade");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellInstockPlanTotal> page = repository.findAll(booleanBuilder, pageable);
        List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTOS = convert.toDto(page.getContent());
        //翻译转换
        MyThreadLocal.get().setLang(oldLang);
        cellInstockPlanTotalDTOS = convert.toCellInstockPlanTotalDTONameByCnName(cellInstockPlanTotalDTOS);
        //转换电池类型名（依据电池类型转化规则）
        changeDtoCellTypeName(cellInstockPlanTotalDTOS);
        this.convertDataStructures(query.getMonth(), cellInstockPlanTotalDTOS);
        return new PageImpl(cellInstockPlanTotalDTOS, page.getPageable(), page.getTotalElements());
    }

    private void convertDataStructures(String month, List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTOS) {
        int days = DateUtil.getDaysForMonth(month);
        YearMonth yearMonth = YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyyMM"));
        cellInstockPlanTotalDTOS.forEach(ele -> {
            TreeMap<String, String> dataStructures = Maps.newTreeMap();
            for (int i = 1; i <= days; i++) {
                LocalDate localDate = LocalDate.of(yearMonth.getYear(), yearMonth.getMonthValue(), i);
                Object fieldValue = ReflectUtil.getFieldValue(ele, String.format("d%s", i));
                BigDecimal bigDecimal = Optional.ofNullable((BigDecimal) fieldValue).orElse(null);
                dataStructures.put(localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        Objects.nonNull(bigDecimal) ? String.valueOf(bigDecimal) : "");
            }
            ele.setDataStructures(dataStructures);
        });
    }

    private Page<CellInstockPlanTotalDTO> getCellInstockPlanTotalByQuery(CellInstockPlanTotalQuery query, Pair<String, String> versions) {
        if (StringUtils.isEmpty(versions.getLeft()) && StringUtils.isEmpty(versions.getRight())) {
            return new PageImpl(Collections.emptyList());
        }
        query.setVersionsPair(versions);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);

        if (StringUtils.isNotEmpty(versions.getLeft()) && StringUtils.isNotEmpty(versions.getRight())) {
            booleanBuilder.and(qCellInstockPlanTotal.fromVersion.in(versions.getLeft(), versions.getRight()));
        } else if (StringUtils.isNotEmpty(versions.getLeft())) {
            booleanBuilder.and(qCellInstockPlanTotal.fromVersion.eq(versions.getLeft()));
        } else if (StringUtils.isNotEmpty(versions.getRight())) {
            booleanBuilder.and(qCellInstockPlanTotal.fromVersion.eq(versions.getRight()));
        }

        Sort sort = Sort.by(Sort.Direction.ASC, "isOversea", "basePlace", "workshop", "cellsType", "itemCode", "hTrace", "cellSource", "regionalCountry", "transparentDoubleGlass", "productionGrade");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellInstockPlanTotal> page = repository.findAll(booleanBuilder, pageable);
        List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTOS = convert.toDto(page.getContent());
        this.convertDataStructures(query.getMonth(), cellInstockPlanTotalDTOS);
        return new PageImpl(cellInstockPlanTotalDTOS, page.getPageable(), page.getTotalElements());
    }

    /**
     * 获取上一版本入库计划汇总信息
     *
     * @param query
     * @return
     */
    private Page<CellInstockPlanTotalDTO> getPreCellInstockPlanTotalByQuery(CellInstockPlanTotalQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        Pair<String, String> versions = getInstockPlanTotalPreConfirmVersion(query);

        buildWhere(booleanBuilder, query, versions);
        Sort sort = Sort.by(Sort.Direction.ASC, "isOversea");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellInstockPlanTotal> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    /**
     * 获取入库计划表确认的版本号（上一版本）
     *
     * @param query
     * @return
     */
    private Pair<String, String> getInstockPlanTotalPreConfirmVersion(CellInstockPlanTotalQuery query) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getInstockPlanTotalPreConfirmVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }
        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getInstockPlanTotalPreConfirmVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getInstockPlanTotalPreConfirmVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }

    }

    /**
     * 获取入库计划汇总表版本号（依据国内外），上一版本确认的版本号
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getInstockPlanTotalPreConfirmVersion(String month, String isOversea) {
        QCellInstockPlanTotal qCellInstockPlanTotal = QCellInstockPlanTotal.cellInstockPlanTotal;
        List<String> versions = jpaQueryFactory.selectDistinct(qCellInstockPlanTotal.version).from(qCellInstockPlanTotal).where(
                qCellInstockPlanTotal.month.eq(month)
        ).where(
                qCellInstockPlanTotal.isOversea.eq(isOversea)
        ).orderBy(qCellInstockPlanTotal.version.desc()).limit(2).fetch();
        if (CollectionUtils.isNotEmpty(versions)) {
            if (versions.size() > 1) {
                return versions.get(1);
            }
        }
        return null;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellInstockPlanTotalQuery query, Pair<String, String> versions) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellInstockPlanTotal.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellInstockPlanTotal.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellInstockPlanTotal.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellInstockPlanTotal.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qCellInstockPlanTotal.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qCellInstockPlanTotal.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellInstockPlanTotal.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qCellInstockPlanTotal.productionGrade.eq(query.getProductionGrade()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            booleanBuilder.and(qCellInstockPlanTotal.cellSource.eq(query.getCellSource()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellInstockPlanTotal.month.eq(query.getMonth()));
        }
        if (query.getQtyThousandPc() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.qtyThousandPc.eq(query.getQtyThousandPc()));
        }
        if (query.getCellMv() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.cellMv.eq(query.getCellMv()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qCellInstockPlanTotal.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getFromVersion())) {
            booleanBuilder.and(qCellInstockPlanTotal.fromVersion.eq(query.getFromVersion()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qCellInstockPlanTotal.itemCode.eq(query.getItemCode()));
        }
        if (query.getD1() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d1.eq(query.getD1()));
        }
        if (query.getD2() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d2.eq(query.getD2()));
        }
        if (query.getD3() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d3.eq(query.getD3()));
        }
        if (query.getD4() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d4.eq(query.getD4()));
        }
        if (query.getD5() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d5.eq(query.getD5()));
        }
        if (query.getD6() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d6.eq(query.getD6()));
        }
        if (query.getD7() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d7.eq(query.getD7()));
        }
        if (query.getD8() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d8.eq(query.getD8()));
        }
        if (query.getD9() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d9.eq(query.getD9()));
        }
        if (query.getD10() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d10.eq(query.getD10()));
        }
        if (query.getD11() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d11.eq(query.getD11()));
        }
        if (query.getD12() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d12.eq(query.getD12()));
        }
        if (query.getD13() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d13.eq(query.getD13()));
        }
        if (query.getD14() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d14.eq(query.getD14()));
        }
        if (query.getD15() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d15.eq(query.getD15()));
        }
        if (query.getD16() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d16.eq(query.getD16()));
        }
        if (query.getD17() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d17.eq(query.getD17()));
        }
        if (query.getD18() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d18.eq(query.getD18()));
        }
        if (query.getD19() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d19.eq(query.getD19()));
        }
        if (query.getD20() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d20.eq(query.getD20()));
        }
        if (query.getD21() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d21.eq(query.getD21()));
        }
        if (query.getD22() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d22.eq(query.getD22()));
        }
        if (query.getD23() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d23.eq(query.getD23()));
        }
        if (query.getD24() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d24.eq(query.getD24()));
        }
        if (query.getD25() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d25.eq(query.getD25()));
        }
        if (query.getD26() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d26.eq(query.getD26()));
        }
        if (query.getD27() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d27.eq(query.getD27()));
        }
        if (query.getD28() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d28.eq(query.getD28()));
        }
        if (query.getD29() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d29.eq(query.getD29()));
        }
        if (query.getD30() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d30.eq(query.getD30()));
        }
        if (query.getD31() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d31.eq(query.getD31()));
        }

        if (StringUtils.isNotEmpty(versions.getLeft()) && StringUtils.isNotEmpty(versions.getRight())) {
            booleanBuilder.and(qCellInstockPlanTotal.version.in(versions.getLeft(), versions.getRight()));
        } else if (StringUtils.isNotEmpty(versions.getLeft())) {
            booleanBuilder.and(qCellInstockPlanTotal.version.eq(versions.getLeft()));
        } else if (StringUtils.isNotEmpty(versions.getRight())) {
            booleanBuilder.and(qCellInstockPlanTotal.version.eq(versions.getRight()));
        } else {
            //不用查了，加个肯定不成立的条件
            booleanBuilder.and(qCellInstockPlanTotal.id.eq(-1L));
        }


    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellInstockPlanTotalQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellInstockPlanTotal.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellInstockPlanTotal.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellInstockPlanTotal.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellInstockPlanTotal.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qCellInstockPlanTotal.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qCellInstockPlanTotal.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellInstockPlanTotal.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qCellInstockPlanTotal.productionGrade.eq(query.getProductionGrade()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            booleanBuilder.and(qCellInstockPlanTotal.cellSource.eq(query.getCellSource()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellInstockPlanTotal.month.eq(query.getMonth()));
        }
        if (query.getQtyThousandPc() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.qtyThousandPc.eq(query.getQtyThousandPc()));
        }
        if (query.getCellMv() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.cellMv.eq(query.getCellMv()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qCellInstockPlanTotal.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getFromVersion())) {
            booleanBuilder.and(qCellInstockPlanTotal.fromVersion.eq(query.getFromVersion()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qCellInstockPlanTotal.itemCode.eq(query.getItemCode()));
        }
        if (query.getD1() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d1.eq(query.getD1()));
        }
        if (query.getD2() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d2.eq(query.getD2()));
        }
        if (query.getD3() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d3.eq(query.getD3()));
        }
        if (query.getD4() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d4.eq(query.getD4()));
        }
        if (query.getD5() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d5.eq(query.getD5()));
        }
        if (query.getD6() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d6.eq(query.getD6()));
        }
        if (query.getD7() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d7.eq(query.getD7()));
        }
        if (query.getD8() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d8.eq(query.getD8()));
        }
        if (query.getD9() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d9.eq(query.getD9()));
        }
        if (query.getD10() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d10.eq(query.getD10()));
        }
        if (query.getD11() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d11.eq(query.getD11()));
        }
        if (query.getD12() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d12.eq(query.getD12()));
        }
        if (query.getD13() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d13.eq(query.getD13()));
        }
        if (query.getD14() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d14.eq(query.getD14()));
        }
        if (query.getD15() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d15.eq(query.getD15()));
        }
        if (query.getD16() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d16.eq(query.getD16()));
        }
        if (query.getD17() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d17.eq(query.getD17()));
        }
        if (query.getD18() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d18.eq(query.getD18()));
        }
        if (query.getD19() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d19.eq(query.getD19()));
        }
        if (query.getD20() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d20.eq(query.getD20()));
        }
        if (query.getD21() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d21.eq(query.getD21()));
        }
        if (query.getD22() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d22.eq(query.getD22()));
        }
        if (query.getD23() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d23.eq(query.getD23()));
        }
        if (query.getD24() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d24.eq(query.getD24()));
        }
        if (query.getD25() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d25.eq(query.getD25()));
        }
        if (query.getD26() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d26.eq(query.getD26()));
        }
        if (query.getD27() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d27.eq(query.getD27()));
        }
        if (query.getD28() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d28.eq(query.getD28()));
        }
        if (query.getD29() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d29.eq(query.getD29()));
        }
        if (query.getD30() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d30.eq(query.getD30()));
        }
        if (query.getD31() != null) {
            booleanBuilder.and(qCellInstockPlanTotal.d31.eq(query.getD31()));
        }
    }

    @Override
    public CellInstockPlanTotalDTO queryById(Long id) {
        CellInstockPlanTotal queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellInstockPlanTotalDTO save(CellInstockPlanTotalSaveDTO saveDTO) {
        CellInstockPlanTotal newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellInstockPlanTotal());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellInstockPlanTotalQuery query, HttpServletResponse response) {
        //获取数据
        Pair<String, String> versions = this.getSendedEmailOrLastVersion(query, false);
        List<CellInstockPlanTotalDTO> dtos = queryByPage(query, versions).getContent();

        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("暂无数据");
        }

        Pair<String, String> sendedEmailVersions = this.getSendedEmailOrLastVersion(query, true);
        Boolean sameVersionFlag = (Objects.isNull(sendedEmailVersions.getLeft()) && Objects.isNull(sendedEmailVersions.getRight()))
                || ((Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getLeft()) && Objects.equals(versions.getLeft(), sendedEmailVersions.getLeft()))
                && (Objects.nonNull(versions.getRight()) && Objects.nonNull(versions.getRight()) && Objects.equals(versions.getRight(), sendedEmailVersions.getRight())));
        List<CellInstockPlanTotalDTO> sendedEmailDTOS = null;
        Map<String, CellInstockPlanTotalDTO> sendedEmailDTOMap;
        if (!sameVersionFlag) {
            query.setBusinessType("1");
            sendedEmailDTOS = this.queryByPage(query, sendedEmailVersions).getContent();
            sendedEmailDTOMap = sendedEmailDTOS.stream().collect(Collectors.toMap(CellInstockPlanTotalDTO::group, Function.identity(), (v1, v2) -> v1));
        } else {
            sendedEmailDTOMap = null;
        }

        dtos.forEach(ele -> {
            if (MapUtils.isNotEmpty(ele.getDataStructures())) {
                TreeMap<String, String> dataStructures = ele.getDataStructures();
                dataStructures.forEach((k, v) -> {
                    dataStructures.put(k, !StringUtils.isEmpty(v) ? String.valueOf(new BigDecimal(v).setScale(2, RoundingMode.HALF_UP)) : "");
                });
                ele.setDataStructures(dataStructures);
            }
            if (sameVersionFlag) {
                ele.setChangeStatusDesc(PlanChangeStatusEnum.UNCHANGE.getDesc());
            } else {
                ele.setChangeStatusDesc(PlanChangeStatusEnum.getDescForCompare(ele, sendedEmailDTOMap.get(ele.group())));
            }

        });

        List<String> dtosList = dtos.stream().map(CellInstockPlanTotalDTO::group).collect(toList());
        if (CollectionUtils.isNotEmpty(sendedEmailDTOS)) {
            sendedEmailDTOS.forEach(ele -> {
                if (MapUtils.isNotEmpty(ele.getDataStructures())) {
                    TreeMap<String, String> dataStructures = ele.getDataStructures();
                    dataStructures.forEach((k, v) -> {
                        dataStructures.put(k, !StringUtils.isEmpty(v) ? String.valueOf(new BigDecimal(v).setScale(2, RoundingMode.HALF_UP)) : "");
                    });
                    ele.setDataStructures(dataStructures);
                }
                ele.setChangeStatusDesc(dtosList.contains(ele.group()) ? "" : PlanChangeStatusEnum.DELETE.getDesc());
            });
        }

        //需要先刷新一下数据的变更状态,excel赋值后才能获取头部模板,不然会有问题
        differentialJudgment(Lists.newArrayList(),dtos,sendedEmailDTOS);

        // dto数据转为ExcelData数据
        List<CellInstockPlanTotalExcelDTO> datas = convert.toExcelDTO(dtos);
        if (CollectionUtils.isNotEmpty(datas)) {
            for (int i = 0; i < dtos.size(); i++) {
                TreeMap<String, BigDecimal> dataStructuresMap = Maps.newTreeMap();
                dtos.get(i).getDataStructures().forEach((k, v) -> {
                    dataStructuresMap.put(k, StringUtils.isEmpty(v) ? null : new BigDecimal(v));
                });
                datas.get(i).setDataStructuresMap(dataStructuresMap);
            }
        }

        List<CellInstockPlanTotalExcelDTO> sendedEmaildatas = sameVersionFlag ? null : convert.toExcelDTO(sendedEmailDTOS);
        if (!sameVersionFlag && CollectionUtils.isNotEmpty(sendedEmaildatas)) {
            for (int i = 0; i < sendedEmaildatas.size(); i++) {
                TreeMap<String, BigDecimal> dataStructuresMap = Maps.newTreeMap();
                sendedEmailDTOS.get(i).getDataStructures().forEach((k, v) -> {
                    dataStructuresMap.put(k, StringUtils.isEmpty(v) ? null : new BigDecimal(v));
                });
                sendedEmaildatas.get(i).setDataStructuresMap(dataStructuresMap);
            }
        }

        ExcelPara excelPara = CellInstockPlanTotalExcelDTO.buildExcelPara(CollectionUtils.isEmpty(datas) ? null : datas.get(0).getDataStructuresMap());
        //判断每日数据和上版本变动，并标记蓝色
        List<DataColumn> columns = excelPara.getColumns();
        List<CellStyleModel> models = differentialJudgment(columns,dtos,sendedEmailDTOS);

        // 导出调用excelUtils
//        ExcelUtils.excelExportByQueryFilter(CellInstockPlanTotalExcelDTO.class, datas, JSON.toJSONString(query), "入库计划汇总表", response);

        List<List<Object>> objList = ExcelUtils.getList(datas, excelPara);
        List<List<Object>> resultList = Lists.newArrayList();
        objList.forEach(ele -> {
            List<Object> itemResultList = Lists.newArrayList();
            ele.forEach(item -> {
                if (Objects.nonNull(item) && item.getClass() == TreeMap.class) {
                    ((TreeMap)item).values().forEach(itemResultList::add);
                } else {
                    itemResultList.add(item);
                }
            });
            resultList.add(itemResultList);
        });

        List<List<Object>> sheet2ResultList = Lists.newArrayList();
        if (!sameVersionFlag) {
            List<List<Object>> sheet2ObjList = ExcelUtils.getList(sendedEmaildatas, excelPara);
            sheet2ObjList.forEach(ele -> {
                List<Object> itemResultList = Lists.newArrayList();
                ele.forEach(item -> {
                    if (Objects.nonNull(item) && item.getClass() == TreeMap.class) {
                        ((TreeMap)item).values().forEach(itemResultList::add);
                    } else {
                        itemResultList.add(item);
                    }
                });
                sheet2ResultList.add(itemResultList);
            });
        }

        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        Iterator<List<String>> iterator = simpleHeader.iterator();
        while (iterator.hasNext()) {
            if (iterator.next().contains("dataStructuresMap")) {
                iterator.remove();
            }
        }

        try {
            String fileName = "入库计划汇总表" + "_" + DateUtils.formatDate(new Date(), new Object[]{"yyyy-MM-dd HH:mm:ss"});

            try {
                ExcelUtils.setExportResponseHeader(response, fileName);
                ExcelWriterBuilder excelWriterBuilder = (ExcelWriterBuilder)((ExcelWriterBuilder)((ExcelWriterBuilder)
                        EasyExcelFactory.write(response.getOutputStream())
                        .registerConverter(new LocalDateConverter()))
                        .registerConverter(new LocalDateTimeConverter()))
                        .registerConverter(new LongStringConverter())
                        .registerConverter(new MapConverter())
                        .registerWriteHandler(new CustomCellStyleHandler(models));
                ExcelWriter writer = excelWriterBuilder.build();

                WriteSheet sheet1 = new WriteSheet();
                sheet1.setSheetName("当前版本");
                sheet1.setSheetNo(0);
                WriteTable table = new WriteTable();
                table.setTableNo(1);
                table.setHead(simpleHeader);
                writer.write(resultList, sheet1, table);

                WriteSheet sheet2 = new WriteSheet();
                sheet2.setSheetName("上一版本");
                sheet2.setSheetNo(1);
                WriteTable table2 = new WriteTable();
                table2.setTableNo(1);
                table2.setHead(simpleHeader);
                writer.write(sameVersionFlag ? resultList : sheet2ResultList, sheet2, table2);

                WriteSheet sheet3 = new WriteSheet();
                sheet3.setSheetName("范围");
                sheet3.setSheetNo(2);
                WriteTable table3 = new WriteTable();
                table3.setTableNo(1);
                List<List<String>> list = new ArrayList();
                list.add(com.google.common.collect.Lists.newArrayList(new String[]{"查询条件"}));
                table3.setHead(list);
                writer.write(Collections.singletonList(JSON.toJSONString(query)), sheet3, table3);

                writer.finish();
            } finally {
                response.getOutputStream().close();
            }

        } catch (Throwable var17) {
            throw var17;
        }
    }

    @Override
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void confirm(CellInstockPlanTotalQuery query) {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(query.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, query.getIsOversea(), query.getMonth(), "instock", "confirm");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行入库计划确认
                doConfirm(query);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }


    }

    private void doConfirm(CellInstockPlanTotalQuery query) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_INSTOCK_PLAN_CONFIRM_PLAN);
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月入库计划数据", query.getMonth()));
            List<CellInstockPlanDTO> cellInstockPlanDTOs = cellInstockPlanService.query(convert.toCellInstockPlanQuery(query));
            //1、获取入库数据
            List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTOS = calcCellInstockPlanTotalByQuery(cellInstockPlanDTOs);
            if (CollectionUtils.isEmpty(cellInstockPlanTotalDTOS)) {
                //logService.addLog(task,ScheduleTaskStatusEnum.ERROR,"暂无数据");
                throw new BizException("baps_month_not_find_data", query.getMonth());
            }
            //2、版本号生成
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段2：生成版本号");
            Pair<String, String> lastVersion = getLastVersion(query);
            String inlandVersion = null;
            String overseaVersion = null;
            String version = DateUtils.getDate("yyyy-MM-dd");
            String month = query.getMonth();
            //2.1 国内版本号生成
            if (StringUtils.isEmpty(lastVersion.getLeft())) {
                //本月还没汇总
                version = version + "-INLAND-" + month + "-(v01)";
            } else {
                String oldVersion = lastVersion.getLeft();
                int startIndex = oldVersion.indexOf("v") + 1;
                int endIndex = oldVersion.indexOf(")");
                Integer version_val = Integer.parseInt(oldVersion.substring(startIndex, endIndex));
                version_val = version_val +1;
                if (version_val < 10) {
                    version += "-INLAND-" + month + "-(v0" + version_val + ")";
                } else {
                    version += "-INLAND-" + month + "-(v" + version_val + ")";
                }


            }
            inlandVersion = version;
            version = DateUtils.getDate("yyyy-MM-dd");
            //2.2 海外版本号生成
            if (StringUtils.isEmpty(lastVersion.getRight())) {
                //本月还没汇总
                version = version + "-OVERSEA-" + month + "-(v01)";
            } else {
                String oldVersion = lastVersion.getRight();
                int startIndex = oldVersion.indexOf("v") + 1;
                int endIndex = oldVersion.indexOf(")");
                Integer version_val = Integer.parseInt(oldVersion.substring(startIndex, endIndex));
                version_val = version_val + 1;
                if (version_val < 10) {
                    version += "-OVERSEA-" + month + "-(v0" + version_val + ")";
                } else {
                    version += "-OVERSEA-" + month + "-(v" + version_val + ")";
                }

            }
            overseaVersion = version;
            CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto = null;
            CellInstockPlanVersionDTO cellInstockPlanVersionOverseaDto = null;
            //3、汇总表设置版本号
            for (CellInstockPlanTotalDTO cellPlanLineTotal : cellInstockPlanTotalDTOS) {

                if (cellPlanLineTotal.getIsOversea().equals(OverseaConstant.INLAND)) {
                    cellPlanLineTotal.setVersion(inlandVersion);
                } else {
                    cellPlanLineTotal.setVersion(overseaVersion);
                }
                //入库版本记录开始
                CellInstockPlanTotalDTO item = cellPlanLineTotal;
                if (cellInstockPlanVersionInlandDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.INLAND)) {
                        cellInstockPlanVersionInlandDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getFromVersion()));
                        cellInstockPlanVersionInlandDto.setIsConfirmPlan(1);
                        cellInstockPlanVersionInlandDto.setVersion(inlandVersion);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionInlandDto));
                    }
                }
                if (cellInstockPlanVersionOverseaDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                        cellInstockPlanVersionOverseaDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getFromVersion()));
                        cellInstockPlanVersionOverseaDto.setIsConfirmPlan(1);
                        cellInstockPlanVersionOverseaDto.setVersion(overseaVersion);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionOverseaDto));
                    }
                }
                //入库版本记录结束
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段3：入库汇总表落表存储");
            //4、汇总表落表存储
            repository.saveAll(convert.toEntity(cellInstockPlanTotalDTOS));
            //5、入库计划表回填版本号

            for (CellInstockPlanDTO cellInstockPlanDTO : cellInstockPlanDTOs) {
                cellInstockPlanDTO.setConfirmPlan(1);
                if (cellInstockPlanDTO.getIsOversea().equals(OverseaConstant.INLAND)) {
                    cellInstockPlanDTO.setFinalVersion(inlandVersion);
                } else {
                    cellInstockPlanDTO.setFinalVersion(overseaVersion);
                }
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段4：入库计划表版本号回填存储");
            //6、入库计划存储保存
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(cellInstockPlanDTOs));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");


        } catch (Exception exception) {
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw exception;
        } finally {
            logService.saveTaskLog(task);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void email(CellInstockPlanTotalQuery query) {
        if (StringUtils.isEmpty(query.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, query.getIsOversea(), query.getMonth(), "instock", "email");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //邮件发送
                doEmail(query, oldLang);
            } catch (Exception e) {
                log.error(ExceptionUtil.exceptionChainToString(e));
                throw e;
            } finally {

                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }

    }

    /**
     * 发邮件
     *
     * @param query   查询条件
     * @param oldLang 语言
     */
    private void doEmail(CellInstockPlanTotalQuery query, String oldLang) {
        query.setPageSize(GlobalConstant.max_page_size);
        query.setPageNumber(1);
        //1、获取本次版本数据
        Pair<String, String> versions = this.getSendedEmailOrLastVersion(query, false);
        List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTOS = getCellInstockPlanTotalByQuery(query, versions).getContent();

        Pair<String, String> sendedEmailVersions = this.getSendedEmailOrLastVersion(query, true);
        List<CellInstockPlanTotalDTO> sendedEmailDTOS = getCellInstockPlanTotalByQuery(query, sendedEmailVersions).getContent();
        Map<String, CellInstockPlanTotalDTO> sendedEmailDTOMap = sendedEmailDTOS.stream().collect(Collectors.toMap(CellInstockPlanTotalDTO::group, Function.identity(), (v1, v2) -> v1));

        Boolean sameVersionFlag = (Objects.isNull(sendedEmailVersions.getLeft()) && Objects.isNull(sendedEmailVersions.getRight()))
                || ((Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getLeft()) && Objects.equals(versions.getLeft(), sendedEmailVersions.getLeft()))
                && (Objects.nonNull(versions.getRight()) && Objects.nonNull(versions.getRight()) && Objects.equals(versions.getRight(), sendedEmailVersions.getRight())));

        if (CollectionUtils.isEmpty(cellInstockPlanTotalDTOS) && CollectionUtils.isEmpty(sendedEmailDTOS)) {
            throw new BizException("暂无数据可发送");
        }
        //获取备注
        List<CellInStockPlanRemarkGroupDTO> remarkList = getRemarkList(cellInstockPlanTotalDTOS, query);
        cellInstockPlanTotalDTOS.forEach(ele -> {
            if (sameVersionFlag) {
                ele.setChangeStatusDesc(PlanChangeStatusEnum.UNCHANGE.getDesc());
            } else {
                ele.setChangeStatusDesc(PlanChangeStatusEnum.getDescForCompare(ele, sendedEmailDTOMap.get(ele.group())));
            }
        });
        List<String> dtosList = cellInstockPlanTotalDTOS.stream().map(CellInstockPlanTotalDTO::group).collect(toList());
        sendedEmailDTOS.forEach(ele -> {
            ele.setChangeStatusDesc(dtosList.contains(ele.group()) ? PlanChangeStatusEnum.DELETE.getDesc() : null);
        });


        //2、当前版本依据生产基地分组后的数据
        Map<String, List<CellInstockPlanTotalDTO>> collect = cellInstockPlanTotalDTOS.stream().collect(Collectors.groupingBy(CellInstockPlanTotalDTO::getBasePlace));
        Map<String, List<CellInstockPlanTotalDTO>> sendedEmailMap = sendedEmailDTOS.stream().collect(Collectors.groupingBy(CellInstockPlanTotalDTO::getBasePlace));

        //3、准备邮件列表
        //3.1 key->邮件地址，value->邮件地址对应的多个基地
        Map<EmailAddress, List<String>> mapEmailList = new HashMap<>();
        //3.2 接受所有基地数据的邮件地址
        List<EmailAddress> allEmailList = new ArrayList<>();
        //3.3 邮件列表获取
        prepareEmailMap(query.getIsOversea(), mapEmailList, allEmailList);
        CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto = null; //国内版本管理对象
        CellInstockPlanVersionDTO cellInstockPlanVersionOverseaDto = null; //海外版本管理对象
        List<CellTypeRuleDTO> ruleDTOList=getSortCellTypeRuleDTOS();//电池类型转换规则
        //4、依据不同的用户发邮件
        List<String> fileList= Lists.newArrayList();
        for (Map.Entry<EmailAddress, List<String>> entry : mapEmailList.entrySet()) {
            //4.1 邮件地址
            EmailAddress emailAddress = entry.getKey();
            //4.2 要发送的基地
            List<String> basePlaceList = entry.getValue();
            //4.3 准备邮件发送内容
            List<CellInstockPlanTotalDTO> curVersionDatas = new ArrayList<>();
            List<CellInstockPlanTotalDTO> lastVersionDatas = new ArrayList<>();
            for (String basePlace : basePlaceList) {
                List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTO1 = collect.get(basePlace);
                if (CollectionUtils.isNotEmpty(cellInstockPlanTotalDTO1)) {
                    curVersionDatas.addAll(cellInstockPlanTotalDTO1);
                }
                List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTO2 = sendedEmailMap.get(basePlace);
                if (CollectionUtils.isNotEmpty(cellInstockPlanTotalDTO2)) {
                    lastVersionDatas.addAll(cellInstockPlanTotalDTO2);
                }
            }
            //if (CollectionUtils.isEmpty(curVersionDatas) && CollectionUtils.isEmpty(lastVersionDatas)) {
            if (CollectionUtils.isEmpty(curVersionDatas)) {
                    continue;
            }
            String isOversea = curVersionDatas.get(0).getIsOversea();
            String month = curVersionDatas.get(0).getMonth();
            String version = curVersionDatas.get(0).getFromVersion();
            //4.4 邮件列表
            List<EmailAddress> emailList = new ArrayList<>();
            emailList.add(emailAddress);
            //4.5 数据排序
            curVersionDatas = curVersionDatas.stream()
                    .sorted(Comparator.comparing(CellInstockPlanTotalDTO::getIsOversea, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getBasePlace, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getProductionGrade, Comparator.nullsLast(Comparator.naturalOrder()))
                    ).collect(toList());
            lastVersionDatas = lastVersionDatas.stream()
                    .sorted(Comparator.comparing(CellInstockPlanTotalDTO::getIsOversea, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getBasePlace, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CellInstockPlanTotalDTO::getProductionGrade, Comparator.nullsLast(Comparator.naturalOrder()))
                    ).collect(toList());
            //4.6 翻译转化
            MyThreadLocal.get().setLang(oldLang);
            curVersionDatas = convert.toCellInstockPlanTotalDTONameByCnName(curVersionDatas);
            lastVersionDatas = convert.toCellInstockPlanTotalDTONameByCnName(lastVersionDatas);
            MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
            //4.7 发送邮件
            //电池类型名转化
            changeDtoCellTypeName(curVersionDatas, ruleDTOList);
            ExcelPara excelPara = CellInstockPlanTotalExcelDTO.buildExcelPara(CollectionUtils.isEmpty(mailService.convertExcelDTO(cellInstockPlanTotalDTOS)) ? null : mailService.convertExcelDTO(cellInstockPlanTotalDTOS).get(0).getDataStructuresMap());
            List<DataColumn> columns = excelPara.getColumns();
            List<CellStyleModel> models = differentialJudgment(columns,curVersionDatas,null);
            query.setModels(models);
            //根据基地过滤
            List<CellInStockPlanRemarkDTO> filteredRemarkList = remarkList.stream().flatMap(dto -> dto.getGroup().stream()).filter(dto -> basePlaceList.contains(dto.getBasePlace())).collect(toList());
            mailService.sendInstockPlanlineTotal(query, curVersionDatas, null, emailList,fileList,filteredRemarkList);
            //入库版本记录开始
            if (cellInstockPlanVersionInlandDto == null) {
                if (isOversea.equals(OverseaConstant.INLAND)) {
                    cellInstockPlanVersionInlandDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(isOversea, month, version));
                    cellInstockPlanVersionInlandDto.setIsSendEmail(1);
                    cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionInlandDto));
                }
            }
            if (cellInstockPlanVersionOverseaDto == null) {
                if (isOversea.equals(OverseaConstant.OVERSEA)) {
                    cellInstockPlanVersionOverseaDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(isOversea, month, version));
                    cellInstockPlanVersionOverseaDto.setIsSendEmail(1);
                    cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionOverseaDto));
                }
            }
            //入库版本记录结束
        }
        //5、发所有数据邮件给allEmailList
        //5.1 数据排序
        cellInstockPlanTotalDTOS = cellInstockPlanTotalDTOS.stream().sorted(
                Comparator.comparing(CellInstockPlanTotalDTO::getIsOversea, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getBasePlace, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getProductionGrade, Comparator.nullsLast(Comparator.naturalOrder()))
        ).collect(toList());
        sendedEmailDTOS = sendedEmailDTOS.stream().sorted(
                Comparator.comparing(CellInstockPlanTotalDTO::getIsOversea, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getBasePlace, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellInstockPlanTotalDTO::getProductionGrade, Comparator.nullsLast(Comparator.naturalOrder()))
        ).collect(toList());
        //5.2 翻译转化
        MyThreadLocal.get().setLang(oldLang);
        cellInstockPlanTotalDTOS = convert.toCellInstockPlanTotalDTONameByCnName(cellInstockPlanTotalDTOS);
        sendedEmailDTOS = convert.toCellInstockPlanTotalDTONameByCnName(sendedEmailDTOS);
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        //5.3 发送邮件
        //电池类型名转化
        changeDtoCellTypeName(cellInstockPlanTotalDTOS,ruleDTOList);

        //发送邮件-判断每日数据和上版本变动，并标记蓝色
        ExcelPara excelPara = CellInstockPlanTotalExcelDTO.buildExcelPara(CollectionUtils.isEmpty(mailService.convertExcelDTO(cellInstockPlanTotalDTOS)) ? null : mailService.convertExcelDTO(cellInstockPlanTotalDTOS).get(0).getDataStructuresMap());
        List<DataColumn> columns = excelPara.getColumns();
        List<CellStyleModel> models = differentialJudgment(columns,cellInstockPlanTotalDTOS,sendedEmailDTOS);

        mailService.sendInstockPlanlineTotalToAll(query, cellInstockPlanTotalDTOS, sendedEmailDTOS, allEmailList,fileList,models,remarkList);
        //5.4 删除邮件附件
        mailService.deleteEmailFile(fileList);
    }

    /**
     * @param mapEmailList 以基地Name构建为key，构建基地的邮件列表
     * @param allEmailList 发送给所有基地的邮件列表
     */
    private void prepareEmailMap(String isOversea, Map<EmailAddress, List<String>> mapEmailList, List<EmailAddress> allEmailList) {
        //准备邮箱数据
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BAPS_SCHEDULE_USER_EMAIL);
        //是否发送入库计划为是的lov
        List<LovLineDTO> lovLineDTOS = new ArrayList<>();
        Set<Long> set = new HashSet<>();
        for (LovLineDTO lovLineDTO : allByHeaderCode.values()
        ) {
            if (!set.contains(lovLineDTO.getLovLineId())) {
                set.add(lovLineDTO.getLovLineId());
                if (LovHeaderCodeConstant.BAPS_CELLPLANLINEINSTOCK_YES.equals(lovLineDTO.getAttribute1())) {
                    lovLineDTOS.add(lovLineDTO);
                }
            }
        }

        //以基地Name构建为key，构建基地的邮件列表
        //发送给所有基地的邮件列表
        for (LovLineDTO lov : lovLineDTOS) {
            String basePlaceIds = lov.getAttribute3();
            EmailAddress emailAddress = new EmailAddress(lov.getLovName(), lov.getLovValue());
            if (StringUtils.isEmpty(basePlaceIds)) {
                //判断是否发送国内所有
                if (OverseaConstant.INLAND.equals(isOversea)) {
                    if (LovHeaderCodeConstant.BAPS_CELLPLANLINEINSTOCK_YES.equals(lov.getAttribute4())) {
                        allEmailList.add(emailAddress);
                    }
                } else {
                    if (LovHeaderCodeConstant.BAPS_CELLPLANLINEINSTOCK_YES.equals(lov.getAttribute5())) {
                        allEmailList.add(emailAddress);
                    }
                }
                continue;
            }
            //basePlaceId依据逗号拆分
            String[] basePlaceIdsArray = basePlaceIds.split(",");
            mapEmailList.put(emailAddress, new ArrayList<>());
            for (String basePlaceId : basePlaceIdsArray
            ) {
                String basePlaceName = LovUtils.get(Long.parseLong(basePlaceId)).getLovName();
                mapEmailList.get(emailAddress).add(basePlaceName);

            }

        }

    }

    private List<CellInstockPlanTotalDTO> calcCellInstockPlanTotalByQuery(List<CellInstockPlanDTO> datas) {
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        List<CellInstockPlanTotalDTO> CellInstockPlanTotalDTOs = new ArrayList<>();
        Map<String, Map<String, List<CellInstockPlanDTO>>> collect = datas.stream().collect(
                Collectors.groupingBy(CellInstockPlanDTO::getCellsType, Collectors.groupingBy(item -> {
                            return StringTools.joinWith(",", item.getBasePlace(), item.getWorkshop(), item.getHTrace(),item.getHChangeFlag(), item.getTransparentDoubleGlass(), item.getCellSource(), item.getRegionalCountry(), item.getItemCode(), item.getProductionGrade(), item.getMainGridSpace(), item.getSourceType(), item.getAesthetics(),item.getSupplyMethod(),item.getCertCode(),item.getRatioCode());
                        })
                ));
        Set<Map.Entry<String, Map<String, List<CellInstockPlanDTO>>>> entriesCellTypeSet = collect.entrySet();
        for (Map.Entry<String, Map<String, List<CellInstockPlanDTO>>> entryCellsType : entriesCellTypeSet) {
            //电池类型
            String cellsType = entryCellsType.getKey();
            Set<Map.Entry<String, List<CellInstockPlanDTO>>> entries = entryCellsType.getValue().entrySet();
            //依据电池类型获取对象折算系数
            CellConversionFactorDTO cellConversionFactorDTO = null;
            for (Map.Entry<String, List<CellInstockPlanDTO>> entry : entries) {

                CellInstockPlanTotalDTO cellInstockPlanTotalDTO = null;
                List<CellInstockPlanDTO> dtos = entry.getValue();
                //统计数据
                BigDecimal qtyCount = BigDecimal.ZERO;//万片数
                for (CellInstockPlanDTO dto : dtos) {
                    if (cellConversionFactorDTO == null) {
                        cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", dto.getIsOversea(), dto.getCellsType()));
                    }
                    if (Objects.isNull(cellInstockPlanTotalDTO)) {
                        cellInstockPlanTotalDTO = convert.toCellInstockPlanTotalDTO(dto);
                        cellInstockPlanTotalDTO.setFromVersion(dto.getVersion());
                    }
                    int day = dto.getStartTime().getDayOfMonth();
                    Object val = ReflectUtil.invoke(cellInstockPlanTotalDTO, "getD" + day);
                    BigDecimal value = dto.getQtyPc();
                   // value = value.setScale(2, RoundingMode.HALF_UP);
                    if (Objects.isNull(val)) {
                        ReflectUtil.invoke(cellInstockPlanTotalDTO, "setD" + day, value);
                    } else {
                        BigDecimal addVal = ((BigDecimal) val).add(value);
                        ReflectUtil.invoke(cellInstockPlanTotalDTO, "setD" + day, addVal);
                    }
                    if (Objects.nonNull(dto.getQtyPc())) {
                        qtyCount = qtyCount.add(dto.getQtyPc());
                    }

                }


                //依据瓦片折算系数计算MV
                if (Objects.nonNull(cellConversionFactorDTO)) {
                    if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                        BigDecimal mv = qtyCount.divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                        cellInstockPlanTotalDTO.setCellMv(mv);
                    }

                } else {
                    log.warn("没有《" + cellInstockPlanTotalDTO.getCellsType() + "》电池类型折算系数数据，不能进行对应的mv计算");
                }
                //qtyCount = qtyCount.setScale(2, RoundingMode.HALF_UP);
                cellInstockPlanTotalDTO.setQtyThousandPc(qtyCount);
                CellInstockPlanTotalDTOs.add(cellInstockPlanTotalDTO);

            }

        }
        return CellInstockPlanTotalDTOs;
    }

    /**
     * 统计汇总最新版本数据(从入库计划表读，读后计算)
     *
     * @param query
     */
    private Page<CellInstockPlanTotalDTO> calcCellInstockPlanTotalByQuery(CellInstockPlanQuery query, String oldLang) {
        //1、读取兆瓦系数
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        //2、读取入库计划表里的数据
        List<CellInstockPlanTotalDTO> cellPlanLineTotals = new ArrayList<>();
        List<CellInstockPlanDTO> datas = cellInstockPlanService.query(query);
        //2.2 分组
        Map<String, List<CellInstockPlanDTO>> collect = datas.stream().collect(Collectors.groupingBy(CellInstockPlanDTO::group));
        //2.3 依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        Collections.sort(keys);
        //2.4 准备分页参数
        Integer total = keys.size();
        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer endPos = Math.min(startPos + pageSize, total);
        //3、分页计算
        for (int i = startPos; i < endPos; i++) {
            String key = keys.get(i);
            //依据电池类型获取对象折算系数
            CellConversionFactorDTO cellConversionFactorDTO = null;
            CellInstockPlanTotalDTO cellPlanLineTotal = null;
            List<CellInstockPlanDTO> dtos = collect.get(key);
            //统计数据
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            for (CellInstockPlanDTO dto : dtos) {
                if (cellConversionFactorDTO == null) {
                    cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", dto.getIsOversea(), dto.getCellsType()));
                }
                if (Objects.isNull(cellPlanLineTotal)) {
                    cellPlanLineTotal = convert.toCellInstockPlanTotalDTO(dto);
                    cellPlanLineTotal.setFromVersion(dto.getVersion());
                }
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellPlanLineTotal, "getD" + day);
                BigDecimal value = dto.getQtyPc();
                //不为空不为0
                if(!MathUtils.checkIsZero(value)){
                    //   value = value.setScale(2, RoundingMode.HALF_UP);
                    if (Objects.isNull(val)) {
                        ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, value);
                    } else {
                        BigDecimal addVal = ((BigDecimal) val).add(value);
                        ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, addVal);
                    }
                    if (Objects.nonNull(dto.getQtyPc())) {
                        qtyCount = qtyCount.add(dto.getQtyPc());
                    }
                }
            }
            //依据瓦片折算系数计算MV
            if (Objects.nonNull(cellConversionFactorDTO)) {
                if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                    BigDecimal mv = qtyCount.divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                    cellPlanLineTotal.setCellMv(mv);
                }

            } else {
                log.warn("没有《" + cellPlanLineTotal.getCellsType() + "》电池类型折算系数数据，不能进行对应的mv计算");
            }
            //qtyCount = qtyCount.setScale(2, RoundingMode.HALF_UP);
            cellPlanLineTotal.setQtyThousandPc(qtyCount);
            cellPlanLineTotals.add(cellPlanLineTotal);
        }
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //翻译转换
        MyThreadLocal.get().setLang(oldLang);
        cellPlanLineTotals = convert.toCellInstockPlanTotalDTONameByCnName(cellPlanLineTotals);
        //转换电池类型名称(依据电池类型规则)
        changeDtoCellTypeName(cellPlanLineTotals);
        this.convertDataStructures(query.getMonth(), cellPlanLineTotals);
        return new PageImpl(cellPlanLineTotals, pageable, total);
    }


    /**
     * 获取投产计划汇总表fromVersion版本号
     *
     * @param query
     * @return
     */
    private Pair<String, String> getLastFromVersion(CellInstockPlanTotalQuery query) {

        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getLastFromVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getLastFromVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getLastFromVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }

    }

    private Pair<String, String> getPlanLastVersion(CellInstockPlanTotalQuery query) {

        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getPlanLineLastVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getPlanLineLastVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getPlanLineLastVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }

    }

    /**
     * 获取入库汇总的version
     *
     * @param query
     * @return
     */
    private Pair<String, String> getLastVersion(CellInstockPlanTotalQuery query) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getLastVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getLastVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getLastVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);

        }
    }

    /**
     * 获取入库汇总的version
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getLastVersion(String month, String isOversea) {
        QCellInstockPlanTotal qCellInstockPlanTotal = QCellInstockPlanTotal.cellInstockPlanTotal;
        String version = jpaQueryFactory.select(qCellInstockPlanTotal.version.max()).from(qCellInstockPlanTotal).where(
                qCellInstockPlanTotal.month.eq(month)
        ).where(
                qCellInstockPlanTotal.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    private String getLastFromVersion(String month, String isOversea) {
        QCellInstockPlanTotal qCellInstockPlanTotal = QCellInstockPlanTotal.cellInstockPlanTotal;
        String version = jpaQueryFactory.select(qCellInstockPlanTotal.fromVersion.max()).from(qCellInstockPlanTotal).where(
                qCellInstockPlanTotal.month.eq(month)
        ).where(
                qCellInstockPlanTotal.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    private String getPlanLastVersion(String month, String isOversea) {
        QCellInstockPlan qCellInstockPlan = QCellInstockPlan.cellInstockPlan;
        String version = jpaQueryFactory.select(qCellInstockPlan.version.max()).from(qCellInstockPlan).where(
                qCellInstockPlan.month.eq(month)
        ).where(
                qCellInstockPlan.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }


    /**
     * 获取入库计划表版本号（依据国内外）
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getPlanLineLastVersion(String month, String isOversea) {

        if (OverseaConstant.OVERSEA_VALUE.equals(isOversea)) {
            isOversea = OverseaConstant.OVERSEA;
        } else if (OverseaConstant.INLAND_VALUE.equals(isOversea)) {
            isOversea = OverseaConstant.INLAND;
        }
        QCellInstockPlan qCellInstockPlan = QCellInstockPlan.cellInstockPlan;
        String version = jpaQueryFactory.select(qCellInstockPlan.version.max()).from(qCellInstockPlan).where(
                qCellInstockPlan.month.eq(month)
        ).where(
                qCellInstockPlan.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    @Override
    public String queryMaxVersion(CellInstockPlanTotalQuery query) {
        //求最大版本号
        JPAQuery<String> where = jpaQueryFactory.select(
                qCellInstockPlanTotal.version.max()
        ).from(qCellInstockPlanTotal).where(
                qCellInstockPlanTotal.month.eq(query.getMonth())
        );
        if (Objects.nonNull(query.getIsOverseaId())) {
            where.where(qCellInstockPlanTotal.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            where.where(qCellInstockPlanTotal.isOversea.eq(query.getIsOversea()));
        }
        String version = where.fetchFirst();
        return version;
    }

    /**
     * 获取最新版的投产数据
     *
     * @param query
     * @return
     */
    @Override
    public List<CellInstockPlanTotalDTO> queryByFirst(CellInstockPlanTotalQuery query) {
        String version = queryMaxVersion(query);
        log.info("投产版本：" + version);
        if (version == null) {
            return null;
        }
        //查询最大版本号对应的数据
        JPAQuery<CellInstockPlanTotal> whereData = jpaQueryFactory.select(
                qCellInstockPlanTotal
        ).from(qCellInstockPlanTotal).where(
                qCellInstockPlanTotal.month.eq(query.getMonth())
        );
        if (Objects.nonNull(query.getIsOverseaId())) {
            whereData.where(qCellInstockPlanTotal.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            whereData.where(qCellInstockPlanTotal.isOversea.eq(query.getIsOversea()));
        }
        if (Objects.nonNull(query.getWorkshopId())) {
            whereData.where(qCellInstockPlanTotal.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            whereData.where(qCellInstockPlanTotal.workshop.eq(query.getWorkshop()));
        }
        if (Objects.nonNull(query.getCellsTypeId())) {
            whereData.where(qCellInstockPlanTotal.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            whereData.where(qCellInstockPlanTotal.cellsType.eq(query.getCellsType()));
        }
        List<CellInstockPlanTotal> fetch = whereData.where(qCellInstockPlanTotal.version.eq(version)).fetch();

        return convert.toDto(fetch);
    }

    /**
     * 依据国内外以及月份获取三个最大版本号
     *
     * @param query
     * @return
     */
    @Override
    public List<String> queryThreeMaxVersion(CellInstockPlanTotalQuery query) {
        //求最大版本号
        JPAQuery<String> where = jpaQueryFactory.selectDistinct(
                qCellInstockPlanTotal.version
        ).from(qCellInstockPlanTotal).where(
                qCellInstockPlanTotal.month.eq(query.getMonth())
        ).where(
                qCellInstockPlanTotal.isOverseaId.eq(query.getIsOverseaId())
        );
        where.orderBy(qCellInstockPlanTotal.version.desc());
        List<String> versions = where.limit(3).fetch();
        return versions;
    }

    /**
     * 查询某月国内或海外某版本的数据(给版本对比使用)
     *
     * @param query
     * @return
     */
    @Override
    public List<CellInstockPlanTotalDTO> query(CellInstockPlanTotalQuery query, String month, Long isOverseaId, String version) {
        JPAQuery<CellInstockPlanTotal> where = jpaQueryFactory.select(qCellInstockPlanTotal).from(qCellInstockPlanTotal).where(
                qCellInstockPlanTotal.month.eq(query.getMonth())
        );
        where.where(
                qCellInstockPlanTotal.version.eq(version)
        );
        where.where(
                qCellInstockPlanTotal.isOverseaId.eq(isOverseaId)
        );
        if (query.getWorkshopId() != null) {
            where.where(
                    qCellInstockPlanTotal.workshopId.eq(query.getWorkshopId())
            );
        }
        if (query.getCellsTypeId() != null) {
            where.where(
                    qCellInstockPlanTotal.cellsTypeId.eq(query.getCellsTypeId())
            );
        }
        List<CellInstockPlanTotal> fetch = where.fetch();
        return convert.toDto(fetch);
    }

    /**
     * 更改电池类型名（依据电池类型转换规则）
     * @param dtos
     * @return
     */
    @Override
    public List<CellInstockPlanTotalDTO> changeDtoCellTypeName(List<CellInstockPlanTotalDTO> dtos) {
        List<CellTypeRuleDTO> ruleDTOList = getSortCellTypeRuleDTOS();
        dtos.stream().forEach(dto -> {
            if (StringUtils.isBlank(dto.getHChangeFlag())) {
                dto.setHChangeFlag("N");
            }
            changeDtoCellTypeName(dto, ruleDTOList);
        });
        return dtos;
    }
    /**
     * 更改电池类型名（依据电池类型转换规则）
     * @param dtos
     * @return
     */
    @Override
    public List<CellInstockPlanTotalDTO> changeDtoCellTypeName(List<CellInstockPlanTotalDTO> dtos, List<CellTypeRuleDTO> ruleDTOList) {
        dtos.stream().forEach(dto -> {
            changeDtoCellTypeName(dto, ruleDTOList);
        });
        return dtos;
    }
    /**
     * 获取电池类型转换规则
     * @return
     */
    private List<CellTypeRuleDTO> getSortCellTypeRuleDTOS() {
        //获取电池类型转换规则
        CellTypeRuleQuery query = new CellTypeRuleQuery();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        List<CellTypeRuleDTO> ruleDTOList = Optional.ofNullable(cellTypeRuleService.queryByPage(query).getContent()).orElse(new ArrayList<>());
       //把ruleDTOList这个不可变列表生成一个可变列表
        ruleDTOList = ruleDTOList.stream().collect(toList());
        //ruleDTOList依据顺序排序
        ruleDTOList.sort(Comparator.comparing(CellTypeRuleDTO::getSortNo));
        return ruleDTOList;
    }

    /**
     * 更改电池类型名（依据电池类型转换规则）
     * @param dto
     * @param ruleDTOList(排好顺序的)
     * @return
     */
    @Override
    public CellInstockPlanTotalDTO changeDtoCellTypeName(CellInstockPlanTotalDTO dto,List<CellTypeRuleDTO> ruleDTOList ) {
        //依据规则匹配
        StringBuilder cellTypeName = new StringBuilder(dto.getCellsType());
        if (CollectionUtils.isNotEmpty(ruleDTOList)) {

            ruleDTOList.forEach(ruleDTO -> {
                String field = ruleDTO.getField();
                String rule = ruleDTO.getRule();
                String result = ruleDTO.getResult();
                if (StringUtils.isNotEmpty(field) && StringUtils.isNotEmpty(rule) && StringUtils.isNotEmpty(result)) {
                    //通过反射获取dto的field对应的属性值与rule是否相等
                    if (ReflectUtil.hasField(dto.getClass(), field)) {
                        Object value = ReflectUtil.getFieldValue(dto, field);
                        if (value != null && value.toString().equals(rule)) {
                            cellTypeName.append("_" + result);
                        }
                    }
                }
            });

        }
        dto.setComponentCellsType(cellTypeName.toString());
        return dto;
    }

    @Override
    public List<String> queryTwoMonthAllItemCodes() {
        String curMonth = DateUtil.getMonth(LocalDate.now());
        String nextMonth = DateUtil.getMonth(LocalDate.now().plusMonths(1));
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.DOMESTIC_OVERSEA);
        List<Long> lovLineIds = allByHeaderCode.values().stream().map(LovLineDTO::getLovLineId).distinct().collect(toList());
        List<String> allMonths = ImmutableList.of(curMonth, nextMonth);
        List<String> allVersionList = Lists.newArrayList();
        lovLineIds.forEach(isOverseaId -> {
            allMonths.forEach(month -> {
                String maxVersion = cellInstockPlanVersionService.findMaxVersion(isOverseaId, month);
                if (!StringUtils.isEmpty(maxVersion)) {
                    allVersionList.add(maxVersion);
                }
            });
        });
        if (CollectionUtils.isEmpty(allVersionList)) return allVersionList;

        return jpaQueryFactory.selectDistinct(qCellInstockPlanTotal.itemCode)
                .from(qCellInstockPlanTotal)
                .where(qCellInstockPlanTotal.month.in(allMonths))
                .where(qCellInstockPlanTotal.version.in(allVersionList))
                .where(qCellInstockPlanTotal.itemCode.isNotEmpty())
                .fetch();
    }

    @Override
    public List<CellInStockPlanRemarkGroupDTO> queryRemarkList(CellInstockPlanTotalQuery query) {
        Pair<String, String> sendedEmailOrLastVersion = getSendedEmailOrLastVersion(query, false);
        return getRemarkList(queryByPage(query, sendedEmailOrLastVersion).getContent(), query);
    }

    @Transactional
    @Override
    public void saveOrUpdateRemark(List<CellInStockPlanRemarkGroupDTO> dtoList) {

        dtoList.forEach(dto -> {
            String totalRemark = dto.getTotalRemark();
            CellInStockPlanRemarkDTO cellInStockPlanRemarkDTO = dto.getGroup().get(0);
            CellInStockPlanTotalRemarkDTO cellInStockPlanTotalRemarkDTO = new CellInStockPlanTotalRemarkDTO();
            BeanUtils.copyProperties(cellInStockPlanRemarkDTO,cellInStockPlanTotalRemarkDTO,"remark");
            cellInStockPlanTotalRemarkDTO.setTotalRemark(totalRemark);
            CellInStockPlanTotalRemarkQuery cellInStockPlanTotalRemarkQuery = new CellInStockPlanTotalRemarkQuery();
            cellInStockPlanTotalRemarkQuery.setMonth(cellInStockPlanTotalRemarkDTO.getMonth());
            cellInStockPlanTotalRemarkQuery.setIsOverseaId(cellInStockPlanTotalRemarkDTO.getIsOverseaId());
            List<CellInStockPlanTotalRemarkDTO> cellInStockPlanTotalRemarkDTOList = cellInStockPlanTotalRemarkService.queryBy(cellInStockPlanTotalRemarkQuery);
            if (CollectionUtils.isNotEmpty(cellInStockPlanTotalRemarkDTOList)) {
                cellInStockPlanTotalRemarkDTO.setId(cellInStockPlanTotalRemarkDTOList.get(0).getId());
            }
            cellInStockPlanTotalRemarkService.save(cellInStockPlanTotalRemarkDTO);
        });

        dtoList.stream()
                .flatMap(dto -> dto.getGroup().stream())
                .forEach(dto -> {
                    CellInStockPlanRemarkQuery cellInStockPlanRemarkQuery = new CellInStockPlanRemarkQuery();
                    cellInStockPlanRemarkQuery.setMonth(dto.getMonth());
                    cellInStockPlanRemarkQuery.setIsOverseaId(dto.getIsOverseaId());
                    cellInStockPlanRemarkQuery.setBasePlaceId(dto.getBasePlaceId());
                    List<CellInStockPlanRemarkDTO> cellInStockPlanRemarkDTOList = cellInStockPlanRemarkService.queryBy(cellInStockPlanRemarkQuery);
                    if (CollectionUtils.isNotEmpty(cellInStockPlanRemarkDTOList)) {
                        dto.setId(cellInStockPlanRemarkDTOList.get(0).getId());
                    }
                    cellInStockPlanRemarkService.save(dto);
                });
    }

    private List<CellInStockPlanRemarkGroupDTO> getRemarkList(List<CellInstockPlanTotalDTO> cellInStockPlanTotalDTOList,CellInstockPlanTotalQuery query) {
        List<CellInStockPlanRemarkDTO> cellInStockPlanRemarkDTOList = Lists.newArrayList();
        cellInStockPlanTotalDTOList.stream()
                .collect(Collectors.groupingBy(dto -> JOINER.join(dto.getBasePlace(), dto.getIsOversea(), dto.getMonth())))
                .forEach((groupKey, group) -> {
                    CellInstockPlanTotalDTO cellInstockPlanTotalDTO = group.get(0);
                    CellInStockPlanRemarkDTO cellInStockPlanRemarkDTO = new CellInStockPlanRemarkDTO();
                    cellInStockPlanRemarkDTO.setIsOverseaId(cellInstockPlanTotalDTO.getIsOverseaId());
                    cellInStockPlanRemarkDTO.setIsOversea(cellInstockPlanTotalDTO.getIsOversea());
                    cellInStockPlanRemarkDTO.setBasePlaceId(cellInstockPlanTotalDTO.getBasePlaceId());
                    cellInStockPlanRemarkDTO.setBasePlace(cellInstockPlanTotalDTO.getBasePlace());
                    cellInStockPlanRemarkDTO.setMonth(cellInstockPlanTotalDTO.getMonth());
                    cellInStockPlanRemarkDTOList.add(cellInStockPlanRemarkDTO);
                });
        //备注列表
        CellInStockPlanRemarkQuery cellInStockPlanRemarkQuery = new CellInStockPlanRemarkQuery();
        cellInStockPlanRemarkQuery.setMonth(query.getMonth());
        //关联备注（基地+国内/海外+月份）
        Map<String, CellInStockPlanRemarkDTO> cellInStockPlanRemarkMap = cellInStockPlanRemarkService.queryBy(cellInStockPlanRemarkQuery)
                .stream()
                .collect(Collectors.toMap(dto -> JOINER.join(dto.getBasePlace(), dto.getIsOversea(), dto.getMonth()), Function.identity(),(o, n)->n));

        //汇总备注列表
        CellInStockPlanTotalRemarkQuery cellInStockPlanTotalRemarkQuery = new CellInStockPlanTotalRemarkQuery();
        cellInStockPlanTotalRemarkQuery.setMonth(query.getMonth());
        //关联汇总备注（国内/海外+月份）
        Map<String, CellInStockPlanTotalRemarkDTO> cellInStockPlanTotalRemarkMap = cellInStockPlanTotalRemarkService.queryBy(cellInStockPlanTotalRemarkQuery)
                .stream()
                .collect(Collectors.toMap(dto -> JOINER.join(dto.getIsOversea(), dto.getMonth()), Function.identity(),(o, n)->n));
        //设置备注
        cellInStockPlanRemarkDTOList.forEach(dto -> {
            CellInStockPlanRemarkDTO cellInStockPlanRemarkDTO = cellInStockPlanRemarkMap.get(JOINER.join(dto.getBasePlace(), dto.getIsOversea(), dto.getMonth()));
            if (cellInStockPlanRemarkDTO != null) {
                dto.setRemark(cellInStockPlanRemarkDTO.getRemark());
            }
        });
        List<CellInStockPlanRemarkGroupDTO> cellInStockPlanRemarkGroupDTOList = Lists.newArrayList();
        //设置汇总备注
        cellInStockPlanRemarkDTOList.stream()
                .collect(Collectors.groupingBy(dto -> JOINER.join(dto.getIsOversea(), dto.getMonth())))
                .forEach((groupKey, group) -> {
                    String groupId = UUID.fastUUID().toString();
                    CellInStockPlanRemarkGroupDTO cellInStockPlanRemarkGroupDTO = new CellInStockPlanRemarkGroupDTO();
                    group.forEach(dto -> {
                        dto.setGroupId(groupId);
                        dto.setSummaryId(UUID.fastUUID().toString());
                    });
                    cellInStockPlanRemarkGroupDTO.setGroup(group);
                    cellInStockPlanRemarkGroupDTO.setGroupId(groupId);
                    CellInStockPlanTotalRemarkDTO cellInStockPlanTotalRemarkDTO = cellInStockPlanTotalRemarkMap.get(groupKey);
                    if (cellInStockPlanTotalRemarkDTO != null) {
                        cellInStockPlanRemarkGroupDTO.setTotalRemark(cellInStockPlanTotalRemarkDTO.getTotalRemark());
                    }
                    cellInStockPlanRemarkGroupDTOList.add(cellInStockPlanRemarkGroupDTO);
                });
        return cellInStockPlanRemarkGroupDTOList;
    }

    /**
     * 对比前后的d1-d31的改动
     * @param source 上一次发送邮件的数据
     * @param target
     */
    public List<CellPlanLineContrastDTO> changeContrast(CellInstockPlanTotalDTO source,CellInstockPlanTotalDTO target) throws NoSuchFieldException, IllegalAccessException {
        List<CellPlanLineContrastDTO> contrasList = Lists.newArrayList();
        target.setChangeStatusDesc(PlanChangeStatusEnum.getDescForCompare(target, source));
        for(String key:target.getDataStructures().keySet()){
            String targetValue = target.getDataStructures().get(key);
            CellPlanLineContrastDTO dto = new CellPlanLineContrastDTO();
            dto.setName(key);
            if(StringUtils.isBlank(targetValue)){
                dto.setValue(BigDecimal.ZERO);
            }else{
                dto.setValue(new BigDecimal(targetValue));
            }
            dto.setChangeFlag(0);

            if(ObjectUtils.isNotEmpty(source)){
                //没有对比数据时不进入逻辑
                String sourceValue = source.getDataStructures().get(key);
                targetValue = StringUtils.isBlank(targetValue)?"0":new BigDecimal(targetValue).setScale(2,RoundingMode.HALF_UP).toString();
                sourceValue = StringUtils.isBlank(sourceValue)?"0":new BigDecimal(sourceValue).setScale(2,RoundingMode.HALF_UP).toString();
                if( (StringUtils.isNotEmpty(sourceValue) && StringUtils.isNotEmpty(targetValue) &&
                        new BigDecimal(targetValue).compareTo(new BigDecimal(sourceValue))!=0) ){
                    dto.setChangeFlag(1);
                    target.setChangeStatusDesc(PlanChangeStatusEnum.CHANGEED.getDesc());
                }
            }

            contrasList.add(dto);
        }
        target.setChangeContrastList(contrasList);
        return contrasList;
    }

    public List<CellStyleModel> differentialJudgment(List<DataColumn> columns
            , List<CellInstockPlanTotalDTO> dtos
            , List<CellInstockPlanTotalDTO> sendedEmailDTOS) {
        List<CellStyleModel> models = Lists.newArrayList();

        int lineCount = 0;

        //赋值上个邮件的变更状态，下面会赋值当前状态
        if(CollectionUtils.isNotEmpty(sendedEmailDTOS)){
            List<String> curVersionDatasGroup = dtos.stream().map(CellInstockPlanTotalDTO::group).collect(toList());
            sendedEmailDTOS.forEach(item->{
                item.setChangeStatusDesc(curVersionDatasGroup.contains(item.group()) ? "" : PlanChangeStatusEnum.DELETE.getDesc());
            });
        }

        if(CollectionUtils.isEmpty(sendedEmailDTOS)){
            return models;
        }
        Map<String, CellInstockPlanTotalDTO> sendedEmailDTOMap = sendedEmailDTOS.stream().collect(Collectors.toMap(CellInstockPlanTotalDTO::group, Function.identity(), (v1, v2) -> v1));

        //循环本次数据，并循环字段，用来获取字段下标
        for (CellInstockPlanTotalDTO dto : dtos) {

            CellInstockPlanTotalDTO email = sendedEmailDTOMap.get(dto.group());
            dto.setChangeStatusDesc(PlanChangeStatusEnum.getDescForCompare(dto, email));
            if(ObjectUtils.isEmpty(email)){
                continue;
            }
            //通用方法获取改变需要标记颜色的数据
            List<CellPlanLineContrastDTO> changeList = Lists.newArrayList();
            try{
                changeList.addAll(this.changeContrast(email,dto));
            }catch (Exception e){
                log.error(ExceptionUtil.exceptionChainToString(e));
                continue;
            }
            if(CollectionUtils.isEmpty(changeList)){
                continue;
            }
            for (DataColumn col : columns) {
                String name = col.getTitle();
                for(CellPlanLineContrastDTO changeItem : changeList){
                    if(name.equals(changeItem.getName()) && 1==changeItem.getChangeFlag()){
                        //进入此判断说明有改动
                        CellStyleModel model = CellStyleModel.createBackgroundColorCellStyleModel("当前版本", lineCount, col.getIndex()-1, 0,245,255);
                        models.add(model);
                    }
                }
            }
            lineCount++;
        }
        return models;
    }

}
