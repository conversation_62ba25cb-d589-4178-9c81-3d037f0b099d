package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CalendarDTO;
import com.trinasolar.scp.baps.domain.entity.Calendar;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturing;
import com.trinasolar.scp.baps.domain.query.CalendarQuery;
import com.trinasolar.scp.baps.domain.save.CalendarSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 生产日历 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
public interface CalendarService {
    /**
     * 分页获取生产日历
     *
     * @param query 查询对象
     * @return 生产日历分页对象
     */
    Page<CalendarDTO> queryByPage(CalendarQuery query);

    /**
     * 根据主键获取生产日历详情
     *
     * @param id 主键
     * @return 生产日历详情
     */
        CalendarDTO queryById(Long id);

    /**
     * 保存或更新生产日历
     *
     * @param saveDTO 生产日历保存对象
     * @return 生产日历对象
     */
    CalendarDTO save(CalendarSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除生产日历
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CalendarQuery query, HttpServletResponse response);

    /**
     * 去重（同一生产单元同一日期有多条重复数据。需在写入表前按生产单元、日期归集数据，归集时标准产线数和资源量取最大值。）
     * @param bom
     * @param calendars 设置默认值
     * @return生产日历
     */
    List<Calendar> deduplication(CellBomManufacturing bom, List<Calendar> calendars);
}

