package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellShippableLeadTimeDTO;
import com.trinasolar.scp.baps.domain.convert.CellShippableLeadTimeDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellShippableLeadTime;
import com.trinasolar.scp.baps.domain.entity.QCellShippableLeadTime;
import com.trinasolar.scp.baps.domain.excel.CellShippableLeadTimeExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.excel.SiliconSplitRuleExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellShippableLeadTimeQuery;
import com.trinasolar.scp.baps.domain.save.CellShippableLeadTimeSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellShippableLeadTimeRepository;
import com.trinasolar.scp.baps.service.service.CellShippableLeadTimeService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * 可发货计划提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-08 06:14:56
 */
@Slf4j
@Service("cellShippableLeadTimeService")
@RequiredArgsConstructor
public class CellShippableLeadTimeServiceImpl implements CellShippableLeadTimeService {
    private static final QCellShippableLeadTime qCellShippableLeadTime = QCellShippableLeadTime.cellShippableLeadTime;

    private final CellShippableLeadTimeDEConvert convert;

    private final CellShippableLeadTimeRepository repository;

    @Override
    public Page<CellShippableLeadTimeDTO> queryByPage(CellShippableLeadTimeQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query=   convert.toCellShippableLeadTimeQuery(query,MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "cellType","basePlace","workShop","workUnit","createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellShippableLeadTime> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellShippableLeadTimeQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellShippableLeadTime.id.eq(query.getId()));
        }
        /*if(StringUtils.isNotEmpty(query.getLeadType())){
            booleanBuilder.and(qCellShippableLeadTime.leadType.eq(query.getLeadType()));
        }*/
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qCellShippableLeadTime.cellTypeId.eq(query.getCellTypeId()));
        }
        if(StringUtils.isNotEmpty(query.getCellType())){
            booleanBuilder.and(qCellShippableLeadTime.cellType.eq(query.getCellType()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellShippableLeadTime.basePlaceId.eq(query.getBasePlaceId()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlace())){
            booleanBuilder.and(qCellShippableLeadTime.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBuffDays() != null) {
            booleanBuilder.and(qCellShippableLeadTime.buffDays.eq(query.getBuffDays()));
        }
        if (query.getWorkShopId() != null) {
            booleanBuilder.and(qCellShippableLeadTime.workShopId.eq(query.getWorkShopId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkShop())){
            booleanBuilder.and(qCellShippableLeadTime.workShop.eq(query.getWorkShop()));
        }
        if (query.getWorkUnitId() != null) {
            booleanBuilder.and(qCellShippableLeadTime.workUnitId.eq(query.getWorkUnitId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkUnit())){
            booleanBuilder.and(qCellShippableLeadTime.workUnit.eq(query.getWorkUnit()));
        }
        /*if (query.getValidateTime() != null) {
            booleanBuilder.and(qCellShippableLeadTime.validateTime.eq(query.getValidateTime()));
        }*/
    }

    @Override
    public CellShippableLeadTimeDTO queryById(Long id) {
        CellShippableLeadTime queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellShippableLeadTimeDTO save(CellShippableLeadTimeSaveDTO saveDTO) {
        CellShippableLeadTime newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellShippableLeadTime());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellShippableLeadTimeQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellShippableLeadTimeDTO> dtos = queryByPage(query).getContent();
        // dto数据转为ExcelData数据
        List<CellShippableLeadTimeExcelDTO> excelDtos = convert.toExcelDTO(dtos);
        ExcelPara excelPara =  query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(excelDtos, excelPara);
        // 导出调用excelUtils
        String fileName= BapsMessgeHelper.getMessage("export.cellshippableleadtime.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellShippableLeadTimeExcelDTO> excelDtos = Lists.newArrayList();
        excelDtos=  ExcelUtils.readExcel(multipartFile.getInputStream(),null, CellShippableLeadTimeExcelDTO.class,excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        //数据验证
        checkInput(excelDtos);
        List<CellShippableLeadTimeSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        saveDTOS= convert.toCellShippableLeadTimeSaveDTOCnName(saveDTOS);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        saveDTOS.stream().parallel().forEach(this::save);
    }

    public void checkInput(List<CellShippableLeadTimeExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors=new ArrayList<>();
        Map<String,List<Integer>> errorMap = Maps.newLinkedHashMap();
        excelDTOS.stream().forEach(excelDTO -> {
            if (StringUtils.isNotEmpty(excelDTO.getCellType()) && StringUtils.isNotEmpty(excelDTO.getBasePlace()) && StringUtils.isNotEmpty(excelDTO.getWorkShop())
            && StringUtils.isNotEmpty(excelDTO.getWorkUnit())){
                String key = Joiner.on(",").useForNull("").join(excelDTO.getCellType(),excelDTO.getBasePlace(),excelDTO.getWorkShop(),excelDTO.getWorkUnit());
                if (errorMap.containsKey(key)){
                    errorMap.get(key).add(i[0]);
                }else {
                    List<Integer> list = new ArrayList<>();
                    list.add(i[0]);
                    errorMap.put(key,list);
                }
            }
            //验证电池类型
            LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellType());
            if (lovLineDTO == null) {
                String message =  BapsMessgeHelper.getMessage("the.row.cellstype.not.exists",new Object[]{i[0],excelDTO.getCellType()});
                errors.add(message);
            }
            //验证生产基地
            LovLineDTO   lovLineDTO_BasePlace = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace());
            if (lovLineDTO_BasePlace == null) {
                String message =  BapsMessgeHelper.getMessage("the.row.baseplace.not.exists",new Object[]{i[0],excelDTO.getBasePlace()});
                errors.add(message);
            }
            //验证生产车间
            LovLineDTO   lovLineDTO_WorkShop = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkShop());
            if (lovLineDTO_WorkShop == null) {
                String message =  BapsMessgeHelper.getMessage("the.row.workshop.not.exists",new Object[]{i[0],excelDTO.getWorkShop()});
                errors.add(message);
            }
            //验证生产单元
            LovLineDTO   lovLineDTO_WorkUnit = LovUtils.getByName(LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkUnit());
            if (lovLineDTO_WorkUnit == null) {
                String message =  BapsMessgeHelper.getMessage("the.row.workunit.not.exists",new Object[]{i[0],excelDTO.getWorkUnit()});
                errors.add(message);
            }

            if(null == excelDTO.getBuffDays()){
                String message =  BapsMessgeHelper.getMessage("cellshippableleadtime.import.buffdays.not.null",new Object[]{i[0]});
                errors.add(message);
            }

            //判断excelDTO.getRatePercent()不是null并且是类似与0.01%这种格式
            if(null != excelDTO.getRatePercent()){
                String regex = "^[0-9]+(?:\\.[0-9]+)?%$";
                if(!excelDTO.getRatePercent().matches(regex)){
                    String message =  BapsMessgeHelper.getMessage("cellshippableleadtime.import.ratepercent.format.error",new Object[]{i[0]});
                    errors.add(message);
                }
            } else{
                String  message =  BapsMessgeHelper.getMessage("cellshippableleadtime.import.ratepercent.not.null",new Object[]{i[0]});
                errors.add(message);
            }

            i[0]++;
        });
        if (errorMap.size()>0){
            String row= BapsMessgeHelper.getMessage("row");
            String repeat= BapsMessgeHelper.getMessage("repeat");
            String errorString = errorMap.entrySet().stream().filter(entry -> entry.getValue().size()>1)
                    .map(entry -> entry.getValue().stream().map(String::valueOf).collect(Collectors.joining(","))+row+entry.getKey()+repeat)
                    .collect(Collectors.joining(","));
            if (StringUtils.isNotEmpty(errorString)){
                throw new BizException(errorString);
            }

        }
        if (errors.size()>0){
            String errorString = errors.stream()
                    .collect(Collectors.joining(","));
            throw new BizException(errorString);
        }

    }

}
