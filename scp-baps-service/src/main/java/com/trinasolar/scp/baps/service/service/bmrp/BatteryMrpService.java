package com.trinasolar.scp.baps.service.service.bmrp;

import com.trinasolar.scp.baps.domain.dto.SiliconSliceSupplyLinesDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.BmrpSafetyStockDaysDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.SiliconSlicePurchasePlanDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.TjOnHandDTO;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: BatteryMrpService
 * @date 2024/6/6 09:19
 */
public interface BatteryMrpService {
    List<TjOnHandDTO> findInventory(List<String> subInventoryCodeList, LocalDate inventoryDate);

    List<SiliconSliceSupplyLinesDTO> findSupplyLines(List<String> monthList);

    List<SiliconSlicePurchasePlanDTO> findSiliconSlicePurchasePlan(List<String> monthList);

    List<BmrpSafetyStockDaysDTO> safetyStockList(String itemCategory, List<String> basePlaceList);
}
