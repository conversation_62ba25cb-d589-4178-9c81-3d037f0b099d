package com.trinasolar.scp.baps.service.service.impl;
import com.trinasolar.scp.baps.domain.dto.ConfigCellAMinusPercentDTO;
import com.trinasolar.scp.baps.domain.query.ConfigCellAMinusQuery;
import com.trinasolar.scp.baps.service.feign.ConfigCellAMinusFeign;
import com.trinasolar.scp.baps.service.service.ConfigCellAMinusPercentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
@Slf4j
@Service("configCellAMinusPercentService")
public class ConfigCellAMinusPercentServiceImpl implements ConfigCellAMinusPercentService {
  @Autowired
   private ConfigCellAMinusFeign feign;
    @Override
    public List<ConfigCellAMinusPercentDTO> getAll(ConfigCellAMinusQuery query) {
        return feign.gueryIECelLAMinusByPage(query).getBody().getData().getContent();
    }
}
