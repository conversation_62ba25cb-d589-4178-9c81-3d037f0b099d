package com.trinasolar.scp.baps.service.service.impl;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellLossDTO;
import com.trinasolar.scp.baps.domain.convert.CellLossDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellLoss;
import com.trinasolar.scp.baps.domain.entity.QCellLoss;
import com.trinasolar.scp.baps.domain.excel.CellLossExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellLossQuery;
import com.trinasolar.scp.baps.domain.save.CellLossSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellLossRepository;
import com.trinasolar.scp.baps.service.service.CellLossService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;


/**
 * 产能切换损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Slf4j
@Service("cellLossService")
@RequiredArgsConstructor
public class CellLossServiceImpl implements CellLossService {
    private static final QCellLoss qCellLoss = QCellLoss.cellLoss;

    private final CellLossDEConvert convert;

    private final CellLossRepository repository;

    @Override
    public Page<CellLossDTO> queryByPage(CellLossQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query=convert.toCellLossCNNameQuery(query, MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "startMonth");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellLoss> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellLossQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellLoss.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qCellLoss.workshop.eq(query.getWorkshop()));
        }
        if(StringUtils.isNotEmpty(query.getStartMonth())){
            booleanBuilder.and(qCellLoss.startMonth.eq(query.getStartMonth()));
        }
        if(StringUtils.isNotEmpty(query.getEndMonth())){
            booleanBuilder.and(qCellLoss.endMonth.eq(query.getEndMonth()));
        }
        if(StringUtils.isNotEmpty(query.getOldProduct())){
            booleanBuilder.and(qCellLoss.oldProduct.eq(query.getOldProduct()));
        }
        if(StringUtils.isNotEmpty(query.getNewProduct())){
            booleanBuilder.and(qCellLoss.newProduct.eq(query.getNewProduct()));
        }
        if (query.getLossTime() != null) {
            booleanBuilder.and(qCellLoss.lossTime.eq(query.getLossTime()));
        }
        if(StringUtils.isNotEmpty(query.getUnit())){
            booleanBuilder.and(qCellLoss.unit.eq(query.getUnit()));
        }
        if (StringUtils.isNotEmpty(query.getOldIsSingleGlass())){
            booleanBuilder.and(qCellLoss.oldIsSingleGlass.eq(query.getOldIsSingleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getNewIsSingleGlass())){
            booleanBuilder.and(qCellLoss.newIsSingleGlass.eq(query.getNewIsSingleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getOldIsRegionalCountry())){
            booleanBuilder.and(qCellLoss.oldIsRegionalCountry.eq(query.getOldIsRegionalCountry()));
        }
        if (StringUtils.isNotEmpty(query.getNewIsRegionalCountry())){
            booleanBuilder.and(qCellLoss.newIsRegionalCountry.eq(query.getNewIsRegionalCountry()));
        }
    }

    @Override
    public CellLossDTO queryById(Long id) {
        CellLoss queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellLossDTO save(CellLossSaveDTO saveDTO) {
        CellLoss newObj = Optional.ofNullable(saveDTO.getId())
            .map(id -> repository.getOne(saveDTO.getId()))
            .orElse(new CellLoss());

        BeanUtils.copyProperties(saveDTO, newObj);
        handlerRule(newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    /**
     * 规格处理用于aps排产，只加到数据库，页面不用显示
     * @param newObj
     */
    private void handlerRule(CellLoss newObj) {
        //规格1处理
        newObj.setSpecOne(Optional.ofNullable(newObj.getOldProduct()).orElse(""));
        if (StringUtils.isNotBlank(newObj.getOldIsSingleGlass()) && newObj.getOldIsSingleGlass().equals("Y")){
            newObj.setSpecOne(newObj.getSpecOne() + "_单玻");
        }
        if (StringUtils.isNotBlank(newObj.getOldIsRegionalCountry()) && newObj.getOldIsRegionalCountry().equals("Y")){
            newObj.setSpecOne(newObj.getSpecOne() + "_小区域国家");
        }
        if ("Y".equals(newObj.getIs1081())){
            newObj.setSpecOne(newObj.getSpecOne() + "_10.8");
        }
        //规格2处理
        newObj.setSpecTwo(Optional.ofNullable(newObj.getNewProduct()).orElse(""));
        if (StringUtils.isNotBlank(newObj.getNewIsSingleGlass()) && newObj.getNewIsSingleGlass().equals("Y")){
            newObj.setSpecTwo(newObj.getSpecTwo() + "_单玻");
        }
        if (StringUtils.isNotBlank(newObj.getNewIsRegionalCountry()) && newObj.getNewIsRegionalCountry().equals("Y")){
            newObj.setSpecTwo(newObj.getSpecTwo() + "_小区域国家");
        }
        if ("Y".equals(newObj.getIs1082())){
            newObj.setSpecTwo(newObj.getSpecTwo() + "_10.8");
        }
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellLossQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellLossDTO> dtos = queryByPage(query).getContent();
        // dto数据转为ExcelData数据
        List<CellLossExcelDTO> excelDtos = convert.toExcelDTO(dtos);
        ExcelPara excelPara =  query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(excelDtos, excelPara);
        // 导出调用excelUtils
        String fileName= BapsMessgeHelper.getMessage("export.cellloss.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);
    }
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellLossExcelDTO> excelDtos = Lists.newArrayList();
        excelDtos=  ExcelUtils.readExcel(multipartFile.getInputStream(),null,CellLossExcelDTO.class,excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        checkInput(excelDtos);
        List<CellLossSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        saveDTOS=convert.toCellLossCNNameSaveDTOById(saveDTOS);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        saveDTOS.stream().forEach(this::save);
    }

    public void checkInput(List<CellLossExcelDTO> excelDTOS) {
        Map<String, LovLineDTO> allYesOrNoMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.YES_OR_NO);
        final int[] i = {1};
        List<String> errors=new ArrayList<>();
        excelDTOS.stream().forEach(excelDTO -> {
            checkNullField(allYesOrNoMap,excelDTO,i[0]);
            String startMonth=excelDTO.getStartMonth();
            String endMonth=excelDTO.getEndMonth();
            if (StringUtils.isEmpty(startMonth)){
                String message=BapsMessgeHelper.getMessage("cellloss.import.start.month.must.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            if (StringUtils.isEmpty(endMonth)){
                String message=BapsMessgeHelper.getMessage("cellloss.import.end.month.must.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            LocalDate startDate= DateUtil.month2BeginLocalDate(startMonth);
            LocalDate endDate= DateUtil.month2BeginLocalDate(endMonth);
            if (endDate.isBefore(startDate)){
                String message=BapsMessgeHelper.getMessage("cellloss.import.end.month.must.not.before.start.month",new Object[]{i[0]});
                errors.add(message);
            }
            //验证电池类型1
            if(StringUtils.isNotEmpty(excelDTO.getOldProduct())){
                LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getOldProduct());
                if (lovLineDTO == null) {
                    String message=BapsMessgeHelper.getMessage("cellloss.import.oldproduct.must.not.exists",new Object[]{i[0],excelDTO.getOldProduct()});
                    errors.add(message);
                }
            }else{
                String message=BapsMessgeHelper.getMessage("cellloss.import.oldproduct.must.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            //验证电池类型2
            if(StringUtils.isNotEmpty(excelDTO.getNewProduct())){
                LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getNewProduct());
                if (lovLineDTO == null) {
                    String message=BapsMessgeHelper.getMessage("cellloss.import.newproduct.must.not.exists",new Object[]{i[0],excelDTO.getNewProduct()});
                    errors.add(message);
                }
            }else{
                String message=BapsMessgeHelper.getMessage("cellloss.import.newproduct.must.not.null",new Object[]{i[0]});
                errors.add(message);
            }

            //验证生产车间
            if(StringUtils.isNotEmpty(excelDTO.getWorkshop())){
                LovLineDTO   lovLineDTO_WorkShop = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop());
                if (lovLineDTO_WorkShop == null) {
                    String message=BapsMessgeHelper.getMessage("the.row.workshop.not.exists",new Object[]{i[0],excelDTO.getWorkshop()});
                    errors.add(message);
                }
            }else{
                String message=BapsMessgeHelper.getMessage("the.row.workshop.not.null",new Object[]{i[0]});
                errors.add(message);
            }

            i[0]++;
        });
        if (errors.size()>0){
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }

    }
    private void checkNullField(Map<String, LovLineDTO> allYesOrNoMap, CellLossExcelDTO excelDTO, int i) {
        if (StringUtils.isBlank(excelDTO.getOldProduct()) || StringUtils.isBlank(excelDTO.getNewProduct()) ||
                StringUtils.isBlank(excelDTO.getWorkshop())    ) {
            String message=BapsMessgeHelper.getMessage("cellloss.import.row.not.null",new Object[]{i});
            throw new BizException(message);
        }
        if  ( !(StringUtils.equalsAny(  excelDTO.getOldIsSingleGlass(),"Y","N",null,"")
                && StringUtils.equalsAny(  excelDTO.getNewIsSingleGlass(),"Y","N",null,"")
                && StringUtils.equalsAny(  excelDTO.getOldIsRegionalCountry(),"Y","N",null,"")
                && StringUtils.equalsAny(  excelDTO.getNewIsRegionalCountry(),"Y","N",null,"")
                && StringUtils.equalsAny(  excelDTO.getIs1081(),"Y","N",null,"")
                && StringUtils.equalsAny(  excelDTO.getIs1082(),"Y","N",null,"")
        )
                )
        {
            String message=BapsMessgeHelper.getMessage("cellloss.import.row.must.y.or.n",new Object[]{i});
            throw new BizException(message);
        }

    }
}
