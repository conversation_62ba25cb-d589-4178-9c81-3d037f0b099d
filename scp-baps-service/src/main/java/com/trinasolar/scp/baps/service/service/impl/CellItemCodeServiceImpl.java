package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.trinasolar.scp.baps.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlan;
import com.trinasolar.scp.baps.domain.entity.CellPlanLine;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.baps.service.feign.MaterielMatchHeaderFeign;
import com.trinasolar.scp.baps.service.service.CellItemCodeService;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import org.apache.axis.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service("cellItemCodeService")
@RequiredArgsConstructor
public class CellItemCodeServiceImpl implements CellItemCodeService {
    private final MaterielMatchHeaderFeign feign;
    /*
    @Override
    public List<String> getItemCodesByCellPlanLine(CellPlanLine cellPlanLine) {
        MaterielMatchHeaderDTO headerDTO=new MaterielMatchHeaderDTO();
        List<String> itemCodesList = new ArrayList<>();
        headerDTO.setId(cellPlanLine.getId());
        headerDTO.setCreatedTime(cellPlanLine.getStartTime());
        headerDTO.setBasePlace(Optional.ofNullable(cellPlanLine.getBasePlace()).orElse("无"));
        headerDTO.setWorkshop(Optional.ofNullable(cellPlanLine.getWorkshop()).orElse("无"));
        headerDTO.setWorkunit(Optional.ofNullable(cellPlanLine.getWorkunit()).orElse("无"));
        headerDTO.setBatteryType(Optional.ofNullable(cellPlanLine.getCellsType()).orElse("无"));
        headerDTO.setHTrace(Optional.ofNullable(cellPlanLine.getHTrace()).orElse("无"));
        headerDTO.setAesthetics(Optional.ofNullable(cellPlanLine.getAesthetics()).orElse("无"));
        headerDTO.setTransparentDoubleGlass(Optional.ofNullable(cellPlanLine.getTransparentDoubleGlass()).orElse("无"));
         headerDTO.setSpecialArea(Optional.ofNullable(cellPlanLine.getRegionalCountry()).orElse("无"));
        headerDTO.setPcsSourceType(Optional.ofNullable(cellPlanLine.getCellSource()).orElse("无"));
        headerDTO.setPcsSourceLevel(Optional.ofNullable(cellPlanLine.getWaferGrade()).orElse("无"));
        headerDTO.setBatteryManufacturer(Optional.ofNullable(cellPlanLine.getCellMfrs()).orElse("无"));
        headerDTO.setIsSpecialRequirements(Optional.ofNullable(cellPlanLine.getIsSpecialRequirement()).orElse("无"));
        headerDTO.setSiliconMaterialManufacturer(Optional.ofNullable(cellPlanLine.getSiMfrs()).orElse("无"));
        headerDTO.setScreenManufacturer(Optional.ofNullable(cellPlanLine.getScreenPlateMfrs()).orElse("无"));
        headerDTO.setSilverSlurryManufacturer(Optional.ofNullable(cellPlanLine.getSilverPulpMfrs()).orElse("无"));
        headerDTO.setLowResistance(Optional.ofNullable(cellPlanLine.getLowResistance()).orElse("无"));
        headerDTO.setDemandPlace(Optional.ofNullable(cellPlanLine.getDemandBasePlace()).orElse("无"));
        headerDTO.setProcessCategory(Optional.ofNullable(cellPlanLine.getProcessCategory()).orElse("无"));
        ResponseEntity<Results<Map<String, Long>>> resultsResponseEntity = feign.query4AByMatchHeadDto(headerDTO);

        if (resultsResponseEntity!=null){
            if (resultsResponseEntity.getBody()!=null){
                if (resultsResponseEntity.getBody().getData()!=null){
                    itemCodesList=  resultsResponseEntity.getBody().getData().keySet().stream().collect(Collectors.toList());
                }
            }
        }
        return itemCodesList;
    }*/

    /**
     * @param cellPlanLines
     * @return <MaterielMatchHeaderDTO的Id, 对应的料号集合>
     */
    @Override
    public Map<Long, List<String>> getItemCodesByCellPlanLine(List<CellPlanLine> cellPlanLines) {
        List<MaterielMatchHeaderDTO> materielMatchHeaderDTOS = new ArrayList<>();
        cellPlanLines.stream().forEach(cellPlanLine -> {
            MaterielMatchHeaderDTO headerDTO = new MaterielMatchHeaderDTO();
            headerDTO.setId(cellPlanLine.getId());
            headerDTO.setRequestFlag("4A");
            LocalDateTime startTime = cellPlanLine.getStartTime();
            startTime = startTime.withHour(0).withMinute(0).withSecond(0);
            headerDTO.setCreatedTime(startTime);
            headerDTO.setBasePlace(Optional.ofNullable(cellPlanLine.getBasePlace()).orElse("无"));
            headerDTO.setWorkshop(Optional.ofNullable(cellPlanLine.getWorkshop()).orElse("无"));
            headerDTO.setWorkunit(Optional.ofNullable(cellPlanLine.getWorkunit()).orElse("无"));
            headerDTO.setBatteryType(Optional.ofNullable(cellPlanLine.getCellsType()).orElse("无"));
            headerDTO.setHTrace(Optional.ofNullable(cellPlanLine.getHTrace()).orElse("无"));
            headerDTO.setAesthetics(Optional.ofNullable(cellPlanLine.getAesthetics()).orElse("无"));
            headerDTO.setTransparentDoubleGlass(Optional.ofNullable(cellPlanLine.getTransparentDoubleGlass()).orElse("无"));
            headerDTO.setSpecialArea(Optional.ofNullable(cellPlanLine.getRegionalCountry()).orElse("无"));
            headerDTO.setPcsSourceType(Optional.ofNullable(cellPlanLine.getCellSource()).orElse("无"));
            headerDTO.setPcsSourceLevel(Optional.ofNullable(cellPlanLine.getWaferGrade()).orElse("无"));
            headerDTO.setBatteryManufacturer(Optional.ofNullable(cellPlanLine.getCellMfrs()).orElse("无"));
            headerDTO.setIsSpecialRequirements(Optional.ofNullable(cellPlanLine.getIsSpecialRequirement()).orElse("无"));
            headerDTO.setSiliconMaterialManufacturer(Optional.ofNullable(cellPlanLine.getSiMfrs()).orElse("无"));
            headerDTO.setScreenManufacturer(Optional.ofNullable(cellPlanLine.getScreenPlateMfrs()).orElse("无"));
            headerDTO.setSilverSlurryManufacturer(Optional.ofNullable(cellPlanLine.getSilverPulpMfrs()).orElse("无"));
            headerDTO.setLowResistance(Optional.ofNullable(cellPlanLine.getLowResistance()).orElse("无"));
            headerDTO.setDemandPlace(Optional.ofNullable(cellPlanLine.getDemandBasePlace()).orElse("无"));
            headerDTO.setCreatedTime(LocalDateTime.now());
            headerDTO.setDemandPlace(Optional.ofNullable(cellPlanLine.getDemandBasePlace()).orElse("无"));
            headerDTO.setMainGridSpace(Optional.ofNullable(cellPlanLine.getMainGridSpace()).orElse("无"));
            materielMatchHeaderDTOS.add(headerDTO);
        });
        Map<String, List<MaterielMatchHeaderDTO>> collect = materielMatchHeaderDTOS.stream().collect(Collectors.groupingBy(item -> {
            return StringTools.joinWith(",", item.getCreatedTime().toString(), item.getBasePlace(), item.getWorkshop(), item.getBatteryType(),
                    item.getHTrace(), item.getAesthetics(), item.getTransparentDoubleGlass(), item.getSpecialArea(),
                    item.getPcsSourceType(), item.getPcsSourceLevel(),
                    item.getBatteryManufacturer(), item.getSiliconMaterialManufacturer(), item.getScreenManufacturer(),
                    item.getProcessCategory(),item.getMainGridSpace());
        }));
        List<MaterielMatchHeaderDTO> headerDTOS = new ArrayList<>();

        Map<Long, Long> cellPlanlineToHeader = new HashMap<>();  //投产Id--》头行id
        Long headerDtoId = 1L;
        for (Map.Entry<String, List<MaterielMatchHeaderDTO>> stringListEntry : collect.entrySet()) {
            MaterielMatchHeaderDTO headerDTO = null;
            List<MaterielMatchHeaderDTO> datas = stringListEntry.getValue();
            for (MaterielMatchHeaderDTO dto : datas) {
                if (headerDTO == null) {
                    headerDTO = BeanUtil.copyProperties(dto, MaterielMatchHeaderDTO.class);
                    headerDTO.setId(headerDtoId);
                    headerDTOS.add(headerDTO);
                }
                cellPlanlineToHeader.put(dto.getId(), headerDtoId);// 投产id-》headid
            }
            headerDtoId++;

        }

        System.out.println("头行数：" + headerDTOS.size());
        ResponseEntity<Results<Map<Long, List<String>>>> resultsResponseEntity = feign.query4AByMatchHeadDto(headerDTOS);
        if (resultsResponseEntity == null || resultsResponseEntity.getBody() == null || !resultsResponseEntity.getBody().isSuccess()) {
            throw new BizException("baps_call_bom_match4A_interface_failed");
        }
        Map<Long, List<String>> allItemCodes = new HashMap<>(); //头行id-》料号集合
        if (resultsResponseEntity.getBody().getData() != null) {
            allItemCodes = resultsResponseEntity.getBody().getData();
        }
        //构建投产id--》料号集合
        Map<Long, List<String>> allItemCodesByCellPlanline = new HashMap<>();
        if (allItemCodes != null) {
            for (Map.Entry<Long, Long> entry : cellPlanlineToHeader.entrySet()) {
                Long headid = entry.getValue();
                List<String> itemcodes = allItemCodes.getOrDefault(headid, new ArrayList<>());
                allItemCodesByCellPlanline.put(entry.getKey(), itemcodes);
            }
        }

        return allItemCodesByCellPlanline;
    }


    @Override
    public Map<Long, String> getItemCodesByCellInstockPlan(List<CellInstockPlan> cellPlanLines) {
        List<MaterielMatchHeaderDTO> dtos = new ArrayList<>();
        cellPlanLines.stream().forEach(cellPlanLine -> {
            MaterielMatchHeaderDTO headerDTO = new MaterielMatchHeaderDTO();

            headerDTO.setId(cellPlanLine.getId());
            headerDTO.setBasePlace(Optional.ofNullable(cellPlanLine.getBasePlace()).orElse("无"));
            headerDTO.setWorkshop(Optional.ofNullable(cellPlanLine.getWorkshop()).orElse("无"));
            headerDTO.setWorkunit(Optional.ofNullable(cellPlanLine.getWorkunit()).orElse("无"));
            headerDTO.setBatteryType(Optional.ofNullable(cellPlanLine.getCellsType()).orElse("无"));
            headerDTO.setHTrace(Optional.ofNullable(cellPlanLine.getHTrace()).orElse("无"));
            headerDTO.setAesthetics(Optional.ofNullable(cellPlanLine.getAesthetics()).orElse("无"));
            headerDTO.setTransparentDoubleGlass(Optional.ofNullable(cellPlanLine.getTransparentDoubleGlass()).orElse("无"));
            headerDTO.setSpecialArea(Optional.ofNullable(cellPlanLine.getRegionalCountry()).orElse("无"));
            headerDTO.setPcsSourceType(Optional.ofNullable(cellPlanLine.getCellSource()).orElse("无"));
            headerDTO.setPcsSourceLevel(Optional.ofNullable(cellPlanLine.getWaferGrade()).orElse("无"));
            headerDTO.setBatteryManufacturer(Optional.ofNullable(cellPlanLine.getCellMfrs()).orElse("无"));
            headerDTO.setIsSpecialRequirements(Optional.ofNullable(cellPlanLine.getIsSpecialRequirement()).orElse("无"));
            headerDTO.setSiliconMaterialManufacturer(Optional.ofNullable(cellPlanLine.getSiMfrs()).orElse("无"));
            headerDTO.setScreenManufacturer(Optional.ofNullable(cellPlanLine.getScreenPlateMfrs()).orElse("无"));
            headerDTO.setSilverSlurryManufacturer(Optional.ofNullable(cellPlanLine.getSilverPulpMfrs()).orElse("无"));
            headerDTO.setLowResistance(Optional.ofNullable(cellPlanLine.getLowResistance()).orElse("无"));
            headerDTO.setDemandPlace(Optional.ofNullable(cellPlanLine.getDemandBasePlace()).orElse("无"));
            headerDTO.setProcessCategory(Optional.ofNullable(cellPlanLine.getProcessCategory()).orElse("无"));
            headerDTO.setProductionGrade(Optional.ofNullable(cellPlanLine.getProductionGrade()).orElse("无"));
            headerDTO.setCreatedTime(LocalDateTime.now());
            headerDTO.setMainGridSpace(Optional.ofNullable(cellPlanLine.getMainGridSpace()).orElse("无"));
            // 设置需求日期,作为评判网版切换的标准
            headerDTO.setDemandDate(cellPlanLine.getOldStartTime().toLocalDate());
            dtos.add(headerDTO);
        });
        Map<String, List<MaterielMatchHeaderDTO>> collect = dtos.stream().collect(Collectors.groupingBy(i -> {
            return Joiner.on(",").useForNull("无").join(i.getBasePlace(), i.getWorkshop(), i.getWorkunit(), i.getBatteryType(), i.getHTrace(), i.getAesthetics(), i.getTransparentDoubleGlass(), i.getSpecialArea(),
                    i.getPcsSourceType(), i.getPcsSourceLevel(), i.getBatteryManufacturer(), i.getIsSpecialRequirements(), i.getSiliconMaterialManufacturer(),
                    i.getScreenManufacturer(), i.getSilverSlurryManufacturer(), i.getLowResistance(), i.getDemandPlace(), i.getProcessCategory(), i.getProductionGrade(),i.getMainGridSpace());
        }));
        List<MaterielMatchHeaderDTO> headerDTOS = Lists.newArrayList();
        for (Map.Entry<String, List<MaterielMatchHeaderDTO>> entry : collect.entrySet()) {
            List<MaterielMatchHeaderDTO> datas = entry.getValue();
            if (CollectionUtils.isNotEmpty(datas)){
                MaterielMatchHeaderDTO headerDTO =datas.get(0);
                List<Long> ids = datas.stream().map(i -> i.getId()).collect(Collectors.toList());
                headerDTO.getIds().addAll(ids);
                headerDTOS.add(headerDTO);
            }
        }

        ResponseEntity<Results<Map<Long, String>>> resultsResponseEntity = feign.query5AByMatchHeadDto(headerDTOS);

        if (resultsResponseEntity == null || resultsResponseEntity.getBody() == null || !resultsResponseEntity.getBody().isSuccess()) {
            throw new BizException("baps_call_bom_match5A_interface_failed");
        }
        if(resultsResponseEntity.getBody().getData() != null){
            Map<Long, String> data = resultsResponseEntity.getBody().getData();
            return makeItemCode(data, headerDTOS);
        }
        return null;
    }
    private  Map<Long, String> makeItemCode( Map<Long, String> data,List<MaterielMatchHeaderDTO> headerDTOS){
        Map<Long, String> resultMap= Maps.newHashMap();
        for (MaterielMatchHeaderDTO dto:headerDTOS){
            Long id=dto.getId();
            String itemCode=data.getOrDefault(id,null);
            if (StringUtils.isEmpty(itemCode)){
                continue;
            }
            Map<Long, String> map = dto.getIds().stream().collect(Collectors.toMap(i -> i, i -> itemCode));
            resultMap.putAll(map);
        }
        return resultMap;
    }
}
