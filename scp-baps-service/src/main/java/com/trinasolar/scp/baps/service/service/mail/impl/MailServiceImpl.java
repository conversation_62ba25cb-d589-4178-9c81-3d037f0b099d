package com.trinasolar.scp.baps.service.service.mail.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanTotalDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellPlanLineTotalDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellInStockPlanRemarkDTO;
import com.trinasolar.scp.baps.domain.dto.CellInStockPlanRemarkGroupDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalDTO;
import com.trinasolar.scp.baps.domain.dto.message.EmailAddress;
import com.trinasolar.scp.baps.domain.dto.message.EmailDataResultDTO;
import com.trinasolar.scp.baps.domain.dto.message.MsgReceiverDto;
import com.trinasolar.scp.baps.domain.enums.PlanChangeStatusEnum;
import com.trinasolar.scp.baps.domain.excel.CellInstockPlanTotalExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineTotalExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalQuery;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.FileUtil;
import com.trinasolar.scp.baps.domain.utils.MapConverter;
import com.trinasolar.scp.baps.domain.utils.PlanEnum;
import com.trinasolar.scp.baps.service.feign.MessageFeign;
import com.trinasolar.scp.baps.service.service.CellInstockPlanTotalService;
import com.trinasolar.scp.baps.service.service.CellPlanLineTotalService;
import com.trinasolar.scp.baps.service.service.mail.MailService;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.common.api.util.exStrategy.CellStyleModel;
import com.trinasolar.scp.common.api.util.exStrategy.CustomCellStyleHandler;
import freemarker.template.Template;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Service("mailService")
public class MailServiceImpl implements MailService {
    @Autowired
    private MessageFeign messageFeign;
    @Autowired
    private SCPFileService fileService;
    @Autowired
    private CellPlanLineTotalDEConvert convert;
    @Autowired
    private CellInstockPlanTotalDEConvert instockPlanTotalDEConvert;

    /**
     *
     * @param toList  邮箱列表
     * @param mailTemplate  邮件模版
     * @param subject  标题
     * @param content  数据
     * @param fileJson 附件传参 jsonArray.toJSONString()
     *         JSONArray jsonArray = new JSONArray();
     *         JSONObject jsonObject = new JSONObject();
     *         jsonObject.put("fileName",scheduleEmailTaskLine.getFileName());
     *         jsonObject.put("fileUrl",scheduleEmailTaskLine.getFileUrl());
     *         jsonArray.add(jsonObject);
     * @return
     * @throws Exception
     */
    @Override
    @SneakyThrows
    public boolean send(List<String> toList, String mailTemplate, String subject, Object content,String fileJson)  {
        MsgReceiverDto msgReceiverDto = new MsgReceiverDto();
        Template template = getTemplate(mailTemplate);
        StringWriter writer = new StringWriter();
        template.process(content, writer);
        msgReceiverDto.setAppCode("20");
        msgReceiverDto.setTunnelCode("1001");
        msgReceiverDto.setSendMethod("EMAIL");
        msgReceiverDto.setTitle(subject);
        msgReceiverDto.setContent(writer.toString());
        msgReceiverDto.setRecipientNo(String.join(",", toList));
        msgReceiverDto.setFileJson(fileJson);
        Object sendMsg = messageFeign.sendMsg(msgReceiverDto);
        Gson gson = new Gson();
        String code = gson.fromJson(sendMsg.toString(), JsonObject.class).getAsJsonObject("RESPONSE").getAsJsonObject("RETURN_DATA").get("statusCodeValue").toString();
        if ("200".equals(code)) {
            return true;
        }
        return false;
    }

    /**
     * 依据基地发投产邮件
     * @param query
     * @param curVersionDatas
     * @param preVersionDatas
     * @param emailList
     */
    @Override
    public void sendCellPlanlineTotal(CellPlanLineTotalQuery query, List<CellPlanLineTotalDTO> curVersionDatas, List<CellPlanLineTotalDTO> preVersionDatas,   List<EmailAddress> emailList,List<String> fileList) {
        List<EmailDataResultDTO> emailDataResultDTOS=new ArrayList<>();
        //当前版本excel文件生成
        if (CollectionUtils.isNotEmpty(curVersionDatas)){
            EmailDataResultDTO emailDataResultDTO=new EmailDataResultDTO();
            exportDataToExcelFile(query,emailDataResultDTO, this.convertExcelDTOForPlanLine(curVersionDatas),this.convertExcelDTOForPlanLine(preVersionDatas));
            emailDataResultDTOS.add(emailDataResultDTO);
            fileList.add(emailDataResultDTO.getFilePath());
        }
        //上一版本excel文件生成
//        if (CollectionUtils.isNotEmpty(preVersionDatas)){
//            EmailDataResultDTO emailPreDataResultDTO=new EmailDataResultDTO();
//            exportDataToExcelFile(query,emailPreDataResultDTO,  this.convertExcelDTOForPlanLine(preVersionDatas),null);
//            emailDataResultDTOS.add(emailPreDataResultDTO);
//            fileList.add(emailPreDataResultDTO.getFilePath());
//        }
        //收件人
        List<String> email=emailList.stream().map(EmailAddress::getEmailAddress).collect(Collectors.toList());

        //邮件主题
        String version=curVersionDatas.get(0).getVersion();
        //curVersionDatas依据基地分组去重后按把基地用-连接
        String basePlace=curVersionDatas.stream().map(CellPlanLineTotalDTO::getBasePlace).distinct().collect(Collectors.joining("-"));
       // String basePlace=curVersionDatas.get(0).getBasePlace();
        String subject=makeEmailTitle(query.getMonth(),basePlace,version,PlanEnum.CELL_PLAN);
        //邮件内容
        Map<String,Object> content= new HashMap<>();
        content.put("month",DateUtil.getMonthFormat(query.getMonth()) );

        content.put("basePlace",basePlace);
        //邮件模板
        String mailTemplate="cellplanline_email.html";
        //附件
        String fileJson=buildFileJson(emailDataResultDTOS);
        send(email,mailTemplate,subject,content,fileJson);

    }

    @Override
    public void sendCellPlanlineTotalToAll(CellPlanLineTotalQuery query, List<CellPlanLineTotalDTO> curVersionDatas, List<CellPlanLineTotalDTO> preVersionDatas, List<EmailAddress> allEmailList,List<String> fileList,List<CellStyleModel> models) {
        List<EmailDataResultDTO> emailDataResultDTOS=new ArrayList<>();
        String type="ALL";

        //发送邮件-判断每日数据和上版本变动，并标记蓝色
        //当前版本excel文件生成
        if (CollectionUtils.isNotEmpty(curVersionDatas)){
            EmailDataResultDTO emailDataResultDTO=new EmailDataResultDTO();
            query.setModels(models);
            exportDataToExcelFile(query,emailDataResultDTO, this.convertExcelDTOForPlanLine(curVersionDatas),this.convertExcelDTOForPlanLine(preVersionDatas),type);
            emailDataResultDTOS.add(emailDataResultDTO);
            fileList.add(emailDataResultDTO.getFilePath());
        }
        //上一版本excel文件生成
//        if (CollectionUtils.isNotEmpty(preVersionDatas)){
//            EmailDataResultDTO emailPreDataResultDTO=new EmailDataResultDTO();
//            exportDataToExcelFile(query,emailPreDataResultDTO, this.convertExcelDTOForPlanLine(preVersionDatas),null,type);
//            emailDataResultDTOS.add(emailPreDataResultDTO);
//            fileList.add(emailPreDataResultDTO.getFilePath());
//        }
        //收件人
        List<String> email=allEmailList.stream().map(EmailAddress::getEmailAddress).collect(Collectors.toList());
        //邮件主题
        String version=curVersionDatas.get(0).getVersion();
        String subject=makeEmailTitle(query.getMonth(),"ALL",version,PlanEnum.CELL_PLAN);
        //邮件内容
        Map<String,Object> content= new HashMap<>();
        content.put("month",DateUtil.getMonthFormat(query.getMonth()) );
        content.put("basePlace","");
        //邮件模板
        String mailTemplate="cellplanline_all.html";
        //附件
        String fileJson=buildFileJson(emailDataResultDTOS);
        send(email,mailTemplate,subject,content,fileJson);
    }

    public List<CellInstockPlanTotalExcelDTO> convertExcelDTO(List<CellInstockPlanTotalDTO> dtos) {
        List<CellInstockPlanTotalExcelDTO> datas = instockPlanTotalDEConvert.toExcelDTO(dtos);
        if (CollectionUtils.isNotEmpty(datas)) {
            for (int i = 0; i < dtos.size(); i++) {
                TreeMap<String, BigDecimal> dataStructuresMap = Maps.newTreeMap();
                dtos.get(i).getDataStructures().forEach((k, v) -> {
                    dataStructuresMap.put(k, StringUtils.isEmpty(v) ? null : new BigDecimal(v));
                });
                datas.get(i).setDataStructuresMap(dataStructuresMap);
            }
        }
        return datas;
    }

    private void convertDataStructures(String month, List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTOS) {
        int days = DateUtil.getDaysForMonth(month);
        YearMonth yearMonth = YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyyMM"));
        cellInstockPlanTotalDTOS.forEach(ele -> {
            TreeMap<String, String> dataStructures = Maps.newTreeMap();
            for (int i = 1; i <= days; i++) {
                LocalDate localDate = LocalDate.of(yearMonth.getYear(), yearMonth.getMonthValue(), i);
                Object fieldValue = ReflectUtil.getFieldValue(ele, String.format("d%s", i));
                BigDecimal bigDecimal = Optional.ofNullable((BigDecimal) fieldValue).orElse(null);
                dataStructures.put(localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        Objects.nonNull(bigDecimal) ? String.valueOf(bigDecimal) : "");
            }
            ele.setDataStructures(dataStructures);
        });
    }


    public List<CellPlanLineTotalExcelDTO> convertExcelDTOForPlanLine(List<CellPlanLineTotalDTO> dtos) {
        List<CellPlanLineTotalExcelDTO> datas = convert.toExcelDTO(dtos);
        if (CollectionUtils.isNotEmpty(datas)) {
            for (int i = 0; i < dtos.size(); i++) {
                if (Objects.nonNull(dtos.get(i).getDataStructures())) {
                    TreeMap<String, BigDecimal> dataStructuresMap = Maps.newTreeMap();
                    dtos.get(i).getDataStructures().forEach((k, v) -> {
                        dataStructuresMap.put(k, StringUtils.isEmpty(v) ? null : new BigDecimal(v));
                    });
                    datas.get(i).setDataStructuresMap(dataStructuresMap);
                }
            }
        }
        return datas;
    }

    /**
     * 依据基地发送入库邮件
     * @param query
     * @param curVersionDatas
     * @param preVersionDatas
     * @param emailList
     */
    @Override
    public void sendInstockPlanlineTotal(CellInstockPlanTotalQuery query, List<CellInstockPlanTotalDTO> curVersionDatas, List<CellInstockPlanTotalDTO> preVersionDatas, List<EmailAddress> emailList,List<String> fileList, List<CellInStockPlanRemarkDTO> remarkDTOList) {
        List<EmailDataResultDTO> emailDataResultDTOS=new ArrayList<>();

        //当前版本excel文件生成
        if (CollectionUtils.isNotEmpty(curVersionDatas)){
            EmailDataResultDTO emailDataResultDTO=new EmailDataResultDTO();
            exportDataToExcelFile(query,emailDataResultDTO, this.convertExcelDTO(curVersionDatas),this.convertExcelDTO(preVersionDatas));
            fileList.add(emailDataResultDTO.getFilePath());
            emailDataResultDTOS.add(emailDataResultDTO);
        }
        //上一版本excel文件生成
//        if (CollectionUtils.isNotEmpty(preVersionDatas)){
//            EmailDataResultDTO emailPreDataResultDTO=new EmailDataResultDTO();
//            exportDataToExcelFile(query,emailPreDataResultDTO, this.convertExcelDTO(preVersionDatas),null);
//            fileList.add(emailPreDataResultDTO.getFilePath());
//            emailDataResultDTOS.add(emailPreDataResultDTO);
//        }
        //收件人
        List<String> email=emailList.stream().map(EmailAddress::getEmailAddress).collect(Collectors.toList());

        //邮件主题
        String version=curVersionDatas.get(0).getVersion();
        //对curVersionDatas按基地分组去重，把基地用-连接
        String basePlace=curVersionDatas.stream().map(CellInstockPlanTotalDTO::getBasePlace).distinct().collect(Collectors.joining("-"));

        String subject=makeEmailTitle(query.getMonth(),basePlace,version,PlanEnum.INSTOCK_PLAN);
        //邮件内容
        Map<String,Object> content= new HashMap<>();
        content.put("month",DateUtil.getMonthFormat(query.getMonth()) );

        content.put("basePlace",basePlace);
        content.put("remarkList",remarkDTOList);
        //邮件模板
        String mailTemplate="cellinstock_email.html";
        //附件
        String fileJson=buildFileJson(emailDataResultDTOS);
        send(email,mailTemplate,subject,content,fileJson);
    }



    @Override
    public void sendInstockPlanlineTotalToAll(CellInstockPlanTotalQuery query, List<CellInstockPlanTotalDTO> curVersionDatas, List<CellInstockPlanTotalDTO> preVersionDatas, List<EmailAddress> emailList,List<String> fileList, List<CellStyleModel> models,List<CellInStockPlanRemarkGroupDTO> remarkDTOList) {
        List<EmailDataResultDTO> emailDataResultDTOS=new ArrayList<>();
        String type="ALL";

        //当前版本excel文件生成
        if (CollectionUtils.isNotEmpty(curVersionDatas)){
            EmailDataResultDTO emailDataResultDTO=new EmailDataResultDTO();
            query.setModels(models);
            exportDataToExcelFile(query,emailDataResultDTO, this.convertExcelDTO(curVersionDatas),this.convertExcelDTO(preVersionDatas), type);
            emailDataResultDTOS.add(emailDataResultDTO);
            fileList.add(emailDataResultDTO.getFilePath());
        }
        //上一版本excel文件生成
//        if (CollectionUtils.isNotEmpty(preVersionDatas)){
//            EmailDataResultDTO emailPreDataResultDTO=new EmailDataResultDTO();
//            exportDataToExcelFile(query,emailPreDataResultDTO, this.convertExcelDTO(preVersionDatas),null, type);
//            emailDataResultDTOS.add(emailPreDataResultDTO);
//            fileList.add(emailPreDataResultDTO.getFilePath());
//        }
        //收件人
        List<String> email=emailList.stream().map(EmailAddress::getEmailAddress).collect(Collectors.toList());

        //邮件主题
        String version=curVersionDatas.get(0).getVersion();
      //  String basePlace=curVersionDatas.get(0).getBasePlace();
        String subject=makeEmailTitle(query.getMonth(),"ALL",version,PlanEnum.INSTOCK_PLAN);
        //邮件内容
        Map<String,Object> content= new HashMap<>();
        content.put("month",DateUtil.getMonthFormat(query.getMonth()) );
       content.put("basePlace","");
        content.put("remarkGroupList",remarkDTOList);
        //邮件模板
        String mailTemplate="cellinstock_all.html";
        //附件
        String fileJson=buildFileJson(emailDataResultDTOS);
        send(email,mailTemplate,subject,content,fileJson);
    }

    public void deleteEmailFile(List<String> fileList) {
        List<File> dirs=Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(fileList)){
            fileList.forEach(fileName -> {
                File file = new File(fileName);
                if (file.exists()) {
                    if (file.getParentFile() != null) {
                        dirs.add(file.getParentFile());
                    }
                    file.deleteOnExit();
                }
            });
            //删除空目录
            dirs.forEach(dir -> {
                if (dir.exists() && dir.isDirectory()) {
                    File[] files = dir.listFiles();
                    if (files == null || files.length == 0) {
                        dir.deleteOnExit();
                    }
                }
            });
        }

    }

    private String buildFileJson(List<EmailDataResultDTO> dtos) {
        JSONArray jsonArray = new JSONArray();
        dtos.stream().forEach(dto->{
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("fileName", dto.getFileName());
            jsonObject.put("fileUrl", dto.getFileUrl());
            jsonArray.add(jsonObject);
        });
        return jsonArray.toJSONString();
    }

    private void exportDataToExcelFile(CellPlanLineTotalQuery query,EmailDataResultDTO emailDataResultDTO, List<CellPlanLineTotalExcelDTO> excelData,List<CellPlanLineTotalExcelDTO> emailData,String ... type) {
        File file = buildExcelFile(query,excelData,emailData,type);
        String fileUrl = this.fileUpload(file);
        emailDataResultDTO.setFileName(file.getName());
        emailDataResultDTO.setFileUrl(fileUrl);
        emailDataResultDTO.setFilePath(file.getPath());
    }
    private void exportDataToExcelFile(CellInstockPlanTotalQuery query,EmailDataResultDTO emailDataResultDTO, List<CellInstockPlanTotalExcelDTO> excelData,List<CellInstockPlanTotalExcelDTO> emailData,String ... type) {
        File file = buildExcelFile(query,excelData,emailData,type);
        String fileUrl = this.fileUpload(file);
        emailDataResultDTO.setFileName(file.getName());
        emailDataResultDTO.setFileUrl(fileUrl);
        emailDataResultDTO.setFilePath(file.getPath());
    }
    private File buildExcelFile(CellInstockPlanTotalQuery query, List<CellInstockPlanTotalExcelDTO> exportDTOS,List<CellInstockPlanTotalExcelDTO> emailDTOS,String ... type) {
        //创建目录
        File file=null;
        if (type!=null && type.length>0){
            file = createLocalFile(query,exportDTOS.get(0).getVersion(),type[0]);
        }else {
            //对exportDTOS中的基地去重后，把基地用-连接
            String basePlace=exportDTOS.stream().map(CellInstockPlanTotalExcelDTO::getBasePlace).distinct().collect(Collectors.joining("-"));
              file = createLocalFile(query,exportDTOS.get(0).getVersion(),basePlace);
        }

        ExcelPara excelPara = CellInstockPlanTotalExcelDTO.buildExcelPara(CollectionUtils.isEmpty(exportDTOS) ? null : exportDTOS.get(0).getDataStructuresMap());
        String sheetName =exportDTOS.get(0).getVersion();
        query.getModels().forEach(item->{
            item.setSheetName(sheetName);
        });

        ExcelWriterBuilder excelWriterBuilder = (ExcelWriterBuilder)((ExcelWriterBuilder)((ExcelWriterBuilder)
                EasyExcelFactory.write(file)
                        .registerConverter(new LocalDateConverter()))
                .registerConverter(new LocalDateTimeConverter()))
                .registerConverter(new LongStringConverter())
                .registerConverter(new MapConverter())
                .registerWriteHandler(new CustomCellStyleHandler(query.getModels()));
        ExcelWriter writer = excelWriterBuilder.build();

        // 文件输出位置
//        ExcelWriter writer = EasyExcelFactory.write(file)
//                .registerConverter(new LocalDateConverter())
//                .registerConverter(new LocalDateTimeConverter())
//                .registerConverter(new LongStringConverter())
//                .registerConverter(new MapConverter())
//                .registerWriteHandler(new CustomCellStyleHandler(query.getModels()))
//                .build();



        List<List<Object>> objList = ExcelUtils.getList(exportDTOS, excelPara);
        List<List<Object>> resultList = Lists.newArrayList();
        objList.forEach(ele -> {
            List<Object> itemResultList = Lists.newArrayList();
            ele.forEach(item -> {
                if (Objects.nonNull(item) && item.getClass() == TreeMap.class) {
                    ((TreeMap)item).values().forEach(itemResultList::add);
                } else {
                    itemResultList.add(item);
                }
            });
            resultList.add(itemResultList);
        });

        List<List<Object>> sheet2ResultList = Lists.newArrayList();
        if (ObjectUtil.isNotEmpty(emailDTOS)) {
            List<List<Object>> sheet2ObjList = ExcelUtils.getList(emailDTOS, excelPara);
            sheet2ObjList.forEach(ele -> {
                List<Object> itemResultList = Lists.newArrayList();
                ele.forEach(item -> {
                    if (Objects.nonNull(item) && item.getClass() == TreeMap.class) {
                        ((TreeMap)item).values().forEach(itemResultList::add);
                    } else {
                        itemResultList.add(item);
                    }
                });
                sheet2ResultList.add(itemResultList);
            });
        }

        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        Iterator<List<String>> iterator = simpleHeader.iterator();
        while (iterator.hasNext()) {
            if (iterator.next().contains("dataStructuresMap")) {
                iterator.remove();
            }
        }

        WriteSheet sheet1 = EasyExcelFactory.writerSheet(0, sheetName).head(simpleHeader).build();
        // 写数据
        writer.write(resultList, sheet1);

        WriteSheet sheet2 = EasyExcelFactory.writerSheet(1, "上一版本").head(simpleHeader).build();
        writer.write(CollectionUtils.isEmpty(sheet2ResultList) ? resultList : sheet2ResultList, sheet2);


        writer.finish();
        return file;
    }
    private File buildExcelFile(CellPlanLineTotalQuery query, List<CellPlanLineTotalExcelDTO> exportDTOS,List<CellPlanLineTotalExcelDTO> emailDTOS,String ... type) {
        //把exportDTOS分组去重获得不同基地用-连接
        String basePlace=exportDTOS.stream().map(CellPlanLineTotalExcelDTO::getBasePlace).distinct().collect(Collectors.joining("-"));
        //创建目录
        File file=null;
        if (type!=null && type.length>0){
              file = createLocalFile(query,exportDTOS.get(0).getVersion(),type[0]);
        }else {
              file = createLocalFile(query,exportDTOS.get(0).getVersion(),basePlace);
        }
        String sheetName =exportDTOS.get(0).getVersion();
        query.getModels().forEach(item->{
            item.setSheetName(sheetName);
        });
        // 文件输出位置
        ExcelWriter writer = EasyExcelFactory.write(file)
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .registerConverter(new MapConverter())
                .registerWriteHandler(new CustomCellStyleHandler(query.getModels()))
                .build();


        ExcelPara excelPara = CellPlanLineTotalExcelDTO.buildExcelPara(CollectionUtils.isEmpty(exportDTOS) ? null : exportDTOS.get(0).getDataStructuresMap());
        List<List<Object>> objList = ExcelUtils.getList(exportDTOS, excelPara);
        List<List<Object>> resultList = Lists.newArrayList();
        objList.forEach(ele -> {
            List<Object> itemResultList = Lists.newArrayList();
            ele.forEach(item -> {
                if (Objects.nonNull(item) && item.getClass() == TreeMap.class) {
                    ((TreeMap)item).values().forEach(itemResultList::add);
                } else {
                    itemResultList.add(item);
                }
            });
            resultList.add(itemResultList);
        });

        List<List<Object>> sheet2ResultList = Lists.newArrayList();
        if (ObjectUtil.isNotEmpty(emailDTOS)) {
            List<List<Object>> sheet2ObjList = ExcelUtils.getList(emailDTOS, excelPara);
            sheet2ObjList.forEach(ele -> {
                List<Object> itemResultList = Lists.newArrayList();
                ele.forEach(item -> {
                    if (Objects.nonNull(item) && item.getClass() == TreeMap.class) {
                        ((TreeMap)item).values().forEach(itemResultList::add);
                    } else {
                        itemResultList.add(item);
                    }
                });
                sheet2ResultList.add(itemResultList);
            });
        }

        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        Iterator<List<String>> iterator = simpleHeader.iterator();
        while (iterator.hasNext()) {
            if (iterator.next().contains("dataStructuresMap")) {
                iterator.remove();
            }
        }

        WriteSheet sheet1 = EasyExcelFactory.writerSheet(0, sheetName).head(simpleHeader).build();
        // 写数据
        writer.write(resultList, sheet1);

        WriteSheet sheet2 = EasyExcelFactory.writerSheet(1, "上一版本").head(simpleHeader).build();
        writer.write(CollectionUtils.isEmpty(sheet2ResultList) ? resultList : sheet2ResultList, sheet2);

        writer.finish();
        return file;
    }

    private File createLocalFile(CellInstockPlanTotalQuery query,String version,String basePlace) {
        File dir = new File(this.getLocalDir(basePlace));
        if(!dir.exists()){
            boolean mkdirs = dir.mkdirs();
            if (!mkdirs) {
                throw new BizException(String.format("创建文件夹{%s}失败", dir.getPath()));
            }
        }
        String fileName =makeEmailTitle(query.getMonth(),basePlace,version,PlanEnum.INSTOCK_PLAN);
        File file = new File(dir.getPath() + "/" + fileName + ".xlsx");
        if(file.exists()){
            file.deleteOnExit();
        }
        try {
            file.createNewFile();
        } catch (IOException e) {
            throw new BizException(String.format("创建文件名{%s}文件失败",fileName));
        }
        return file;
    }

    private File createLocalFile(CellPlanLineTotalQuery query,String version,String basePlace) {
        File dir = new File(this.getLocalDir(basePlace));
        if(!dir.exists()){
            boolean mkdirs = dir.mkdirs();
            if (!mkdirs) {
                throw new BizException(String.format("创建文件夹{%s}失败", dir.getPath()));
            }
        }
        String fileName =  makeEmailTitle(query.getMonth(),basePlace,version,PlanEnum.CELL_PLAN);
        File file = new File(dir.getPath() + "/" + fileName + ".xlsx");
        if(file.exists()){
            file.deleteOnExit();
        }
        try {
            file.createNewFile();
        } catch (IOException e) {
            throw new BizException(String.format("创建文件名{%s}文件失败",fileName));
        }
        return file;
    }

    private String getLocalDir(String basePlace) {
        return MailService.EMAIL_FILE_LOCAL_DIR_NAME+"/"+basePlace+"/";
    }
    @SneakyThrows
    private String fileUpload(File file) {
        MultipartFile multipartFile = FileUtil.getMultipartFile(file);
        return fileService.upload(multipartFile);
    }

    private  String makeEmailTitle(String month, String basePlace, String version, PlanEnum planEnum) {

        LocalDate localDate = DateUtil.getLocalDate(month,1);
        int year=localDate.getYear();
        String quarter=DateUtil.getQuarter(localDate);
        String monthName= DateUtil.getMonthName(localDate);
        Joiner joiner = Joiner.on("_").skipNulls() ;
        String title = joiner.join(year,quarter,monthName,"Cell-Plan",version,basePlace,planEnum.getValue());
        return title;
    }
}
