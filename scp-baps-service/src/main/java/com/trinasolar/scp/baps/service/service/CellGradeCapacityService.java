package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellGradeCapacityDTO;
import com.trinasolar.scp.baps.domain.query.CellGradeCapacityQuery;
import com.trinasolar.scp.baps.domain.save.CellGradeCapacitySaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 爬坡产能表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
public interface CellGradeCapacityService  {
    /**
     * 分页获取爬坡产能表
     *
     * @param query 查询对象
     * @return 爬坡产能表分页对象
     */
    Page<CellGradeCapacityDTO> queryByPage(CellGradeCapacityQuery query);

    /**
     * 根据主键获取爬坡产能表详情
     *
     * @param id 主键
     * @return 爬坡产能表详情
     */
        CellGradeCapacityDTO queryById(Long id);

    /**
     * 保存或更新爬坡产能表
     *
     * @param saveDTO 爬坡产能表保存对象
     * @return 爬坡产能表对象
     */
    CellGradeCapacityDTO save(CellGradeCapacitySaveDTO saveDTO);

    /**
     * 根据主键逻辑删除爬坡产能表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellGradeCapacityQuery query, HttpServletResponse response);

    List<CellGradeCapacityDTO> queryForDm(CellGradeCapacityQuery query);

    BigDecimal findByCondition(Long isOverseaId, String workshop, LocalDate startDate, LocalDate endDate, String productCategory, String crystalType);
    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

