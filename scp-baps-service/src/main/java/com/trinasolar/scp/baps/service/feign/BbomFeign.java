package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.MainGridSpacingRuleDTO;
import com.trinasolar.scp.baps.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.baps.domain.dto.MaterielMatchLineDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.*;
import com.trinasolar.scp.baps.domain.query.MainGridSpacingRuleQuery;
import com.trinasolar.scp.baps.domain.query.MaterielMatchHeaderQuery;
import com.trinasolar.scp.baps.domain.query.bbom.*;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.baps.service.feign.fallback.BbomFeignFallbackFactory;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * DP服务feign调用
 *
 * @author: darke
 * @create: 2022年6月28日09:12:08
 */
@FeignClient(path = FeignConstant.SCP_BATTERY_BOM_API, value = "scp-battery-bom-api", fallbackFactory = BbomFeignFallbackFactory.class,configuration = LanguageHeaderInterceptor.class)
public interface BbomFeign {


    /**
     * 料号匹配使用
     * 查询是否存在
     * bbom_materiel_match_header
     *
     * @param
     * @return
     */
    @PostMapping("/materiel-match-header/queryList")
    @ApiOperation(value = "查询电池料号匹配头")
    ResponseEntity<Results<List<MaterielMatchHeaderDTO>>> queryList(@RequestBody MaterielMatchHeaderQuery query);

    /**
     * 查询是否存在
     * 是 删除
     * bbom_materiel_match_header
     * bbom_materiel_match_line
     * bbom_materiel_match_line_match_status
     *
     * @param
     * @return
     */
    @PostMapping("/materiel-match-line/deleteByHeadId")
    @ApiOperation(value = "电池物料信息删除")
    ResponseEntity<Results<Object>> deleteByHeadId(@RequestBody List<MaterielMatchHeaderDTO> headerDTOList);

    /**
     * 查询行明细数据
     * 根据头id
     * bbom_materiel_match_header
     * bbom_materiel_match_line
     * bbom_materiel_match_line_match_status
     *
     * @param
     * @return
     */
    @PostMapping("/materiel-match-line/queryMatchLineByHeaderId")
    @ApiOperation(value = "电池物料行信息查询")
    ResponseEntity<Results<List<MaterielMatchLineDTO>>> queryMatchLineByHeaderId(@RequestBody MaterielMatchHeaderDTO headerDTO);

    /**
     * 查询是否存在
     * 否 新增
     * bbom_materiel_match_header
     *
     * @param
     * @return
     */
    @PostMapping("/materiel-match-header/addMatchInfo")
    @ApiOperation(value = "电池物料头信息新增")
    ResponseEntity<Results<Object>> addMatchInfo(@RequestBody MaterielMatchHeaderDTO headerDTO);
    /**
     * 查询物料信息
     *
     * @param itemsDTO
     */
    @PostMapping("/items/findOneByItemCode")
    @ApiOperation(value = "查询物料信息")
    ResponseEntity<Results<ItemsDTO>> findOneByItemCode(@RequestBody ItemsDTO itemsDTO);
    @PostMapping("/main-grid-spacing-rule/getAllRules")
    @ApiOperation(value = "获取电池主栅间距所有的规则", notes = "获取电池主栅间距所有的规则")
    ResponseEntity<Results<List<MainGridSpacingRuleDTO>>> getAllRules(@RequestBody MainGridSpacingRuleQuery query);
    /**
     * 查询物料项详情依据料号和产品等级属性
     *
     * @param query
     */
    @PostMapping("/items/findItemsForBaps")
    @ApiOperation(value = "查询物料信息给baps")
    public ResponseEntity<Results<List<ItemsDTO>>> findReworkItemCode(@RequestBody ItemsQuery query);

    @PostMapping("/items/queryByItemCodeAll")
    ResponseEntity<Results<Map<String, ItemsDTO>>> queryByItemCodeAll(@RequestBody ItemsNewQuery query);


    @PostMapping("/low-efficiency-cell-percent/list")
    ResponseEntity<Results<List<LowEfficiencyCellPercentDTO>>> list(@RequestBody LowEfficiencyCellPercentQuery query);

    @PostMapping("/items/queryByItemCodeAllNew")
    ResponseEntity<Results<Map<String, ItemsDTO>>> queryByItemCodeAllNew(@RequestBody ItemsNewQuery query);

    @PostMapping("/materiel-match-header/allMatchItem")
    ResponseEntity<Results<Object>> allMatchItem(@RequestBody MaterielMatchHeaderQuery query);

    @PostMapping("/battery-screen-plate-workshop/page")
    @ApiOperation(value = "车间级网版切换维护-网版分页列表", notes = "车间级网版切换维护-网版分页列表")
    ResponseEntity<Results<PageFeign<BatteryScreenPlateWorkshopDTO>>> queryBatteryScreenPlateList(@RequestBody BatteryScreenPlateWorkshopQuery query);

    @PostMapping("/battery-silicon-wafer/list")
    @ApiOperation(value = "车间级网版切换维护-网版分页列表", notes = "车间级网版切换维护-网版分页列表")
    ResponseEntity<Results<List<BatterySiliconWaferDTO>>> queryBatterySiliconWaferList();
}
