package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellGradeCapacityDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellClimbCapacityLeadDTO;
import com.trinasolar.scp.baps.domain.convert.CellClimbCapacityLeadDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellGradeCapacityDTO;
import com.trinasolar.scp.baps.domain.entity.CellClimbCapacityLead;
import com.trinasolar.scp.baps.domain.entity.CellGradeCapacity;
import com.trinasolar.scp.baps.domain.entity.QCellClimbCapacityLead;
import com.trinasolar.scp.baps.domain.entity.QCellGradeCapacity;
import com.trinasolar.scp.baps.domain.excel.CellClimbCapacityLeadExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellClimbCapacityLeadQuery;
import com.trinasolar.scp.baps.domain.query.CellGradeCapacityQuery;
import com.trinasolar.scp.baps.domain.save.CellClimbCapacityLeadSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellClimbCapacityLeadRepository;
import com.trinasolar.scp.baps.service.service.CellClimbCapacityLeadService;
import com.trinasolar.scp.baps.service.service.CellGradeCapacityService;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 爬坡产能可靠性验证表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-05 08:10:43
 */
@Slf4j
@Service("cellClimbCapacityLeadService")
@RequiredArgsConstructor
public class CellClimbCapacityLeadServiceImpl implements CellClimbCapacityLeadService {
    private static final QCellClimbCapacityLead qCellClimbCapacityLead = QCellClimbCapacityLead.cellClimbCapacityLead;

    private final CellClimbCapacityLeadDEConvert convert;

    private final CellClimbCapacityLeadRepository repository;
    private static final QCellGradeCapacity qCellGradeCapacity = QCellGradeCapacity.cellGradeCapacity;

    private final JPAQueryFactory jpaQueryFactory;

    private final CellGradeCapacityDEConvert capacityDEConvert;

    @Override
    public Page<CellClimbCapacityLeadDTO> queryByPage(CellClimbCapacityLeadQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query=  convert.toCellClimbCapacityLeadQuery(query,MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellClimbCapacityLead> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellClimbCapacityLeadQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellClimbCapacityLead.id.eq(query.getId()));
        }
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qCellClimbCapacityLead.cellTypeId.eq(query.getCellTypeId()));
        }
        if(StringUtils.isNotEmpty(query.getCellType())){
            booleanBuilder.and(qCellClimbCapacityLead.cellType.eq(query.getCellType()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellClimbCapacityLead.basePlaceId.eq(query.getBasePlaceId()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlace())){
            booleanBuilder.and(qCellClimbCapacityLead.basePlace.eq(query.getBasePlace()));
        }
        if (query.getWorkShopId() != null) {
            booleanBuilder.and(qCellClimbCapacityLead.workShopId.eq(query.getWorkShopId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkShop())){
            booleanBuilder.and(qCellClimbCapacityLead.workShop.eq(query.getWorkShop()));
        }
        if (query.getWorkUnitId() != null) {
            booleanBuilder.and(qCellClimbCapacityLead.workUnitId.eq(query.getWorkUnitId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkUnit())){
            booleanBuilder.and(qCellClimbCapacityLead.workUnit.eq(query.getWorkUnit()));
        }
        if (query.getValidateTime() != null) {
            booleanBuilder.and(qCellClimbCapacityLead.validateTime.eq(query.getValidateTime()));
        }
    }

    @Override
    public CellClimbCapacityLeadDTO queryById(Long id) {
        CellClimbCapacityLead queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellClimbCapacityLeadDTO save(CellClimbCapacityLeadSaveDTO saveDTO) {
        CellClimbCapacityLead newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new CellClimbCapacityLead());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }
    @Override
    public List<CellClimbCapacityLeadDTO> batchUpdate(List<CellClimbCapacityLeadSaveDTO> saveDTOList){
        saveDTOList.stream().forEach(saveDTO->{
            CellClimbCapacityLead newObj = Optional.ofNullable(saveDTO.getId())
                    .flatMap(repository::findById)
                    .orElse(new CellClimbCapacityLead());

          //  newObj = convert.saveDTOtoEntity(saveDTO, newObj);
            newObj.setValidateTime(saveDTO.getValidateTime());
            repository.save(newObj);
        });
        return convert.saveToEntityDTO(saveDTOList);
    }
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellClimbCapacityLeadQuery query, HttpServletResponse response) {
       List<CellClimbCapacityLeadDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);
       String fileName = BapsMessgeHelper.getMessage("export.cellclimbcapacityLead.table.name");
       ExcelUtils.exportExWithLocalDate(response, fileName, fileName, excelPara.getSimpleHeader(), excelData);
    }
    /** 同步之前先删除所有数据
     * 同步爬坡产能数据
     * 获取全量，根据电池类型、生产基地、生产车间、生产单元、生产线体分组数据
     */
    @Override
    public void syncClimbCapacity(){
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        List<CellClimbCapacityLead>leaList=repository.findAll();
        if(CollectionUtils.isNotEmpty(leaList)){
            repository.deleteAll();
        }
        List<CellGradeCapacity>cellGradeCapacityList=jpaQueryFactory.select(qCellGradeCapacity).from(qCellGradeCapacity).where(qCellGradeCapacity.isDeleted.eq(0).and(qCellGradeCapacity.reliabilityCheck.eq("Y"))).fetch();
        List<CellGradeCapacityDTO>cellGradeCapacityDTOList=new ArrayList<>();
        cellGradeCapacityList.stream().forEach(x->{
            CellGradeCapacityDTO cellGradeCapacityDTO=new CellGradeCapacityDTO();
            cellGradeCapacityDTO.setCellsType(x.getCellsType());
            cellGradeCapacityDTO.setCellsTypeId(x.getCellsTypeId());
            cellGradeCapacityDTO.setBasePlaceId(x.getBasePlaceId());
            cellGradeCapacityDTO.setBasePlace(x.getBasePlace());
            cellGradeCapacityDTO.setWorkshopid(x.getWorkshopid());
            cellGradeCapacityDTO.setWorkshop(x.getWorkshop());
            cellGradeCapacityDTO.setWorkunitid(x.getWorkunitid());
            cellGradeCapacityDTO.setWorkunit(x.getWorkunit());
            //cellGradeCapacityDTO.setLineNumber(x.getLineNumber());
            cellGradeCapacityDTO.setLineName(x.getLineName());
            cellGradeCapacityDTOList.add(cellGradeCapacityDTO);
        });
        //基地、车间分组 key值存在重复 覆盖
        Map<String, CellGradeCapacityDTO> listMap = cellGradeCapacityDTOList.stream().collect(Collectors.toMap(CellGradeCapacityDTO::getGroupByFiled, Function.identity(), (user1, user2) -> user2));

        listMap.keySet().stream().forEach(key->{
            CellClimbCapacityLeadDTO capacityLeadDTO=new CellClimbCapacityLeadDTO();
            String[]splitKey=key.split("/");
            capacityLeadDTO.setCellType(splitKey[0]);
            capacityLeadDTO.setCellTypeId(Long.parseLong(splitKey[1]));
            capacityLeadDTO.setBasePlaceId(Long.parseLong(splitKey[2]));
            capacityLeadDTO.setBasePlace(splitKey[3]);
            capacityLeadDTO.setWorkShopId(Long.parseLong(splitKey[4]));
            capacityLeadDTO.setWorkShop(splitKey[5]);
            capacityLeadDTO.setWorkUnitId(Long.parseLong(splitKey[6]));
            capacityLeadDTO.setWorkUnit(splitKey[7]);
            capacityLeadDTO.setLineName(splitKey[8]);
            List<CellClimbCapacityLead> leadList=repository.queryByLeadDto(capacityLeadDTO.getCellTypeId(),capacityLeadDTO.getBasePlaceId(),capacityLeadDTO.getWorkShopId(),capacityLeadDTO.getWorkUnitId(),capacityLeadDTO.getLineName());
            if(CollectionUtils.isNotEmpty(leadList)){
                capacityLeadDTO.setValidateTime(leadList.get(0).getValidateTime());
            }
            repository.save(convert.toEntity(capacityLeadDTO));
        });
    }
}
