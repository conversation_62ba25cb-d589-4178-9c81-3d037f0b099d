package com.trinasolar.scp.baps.service.service.log;
import com.trinasolar.scp.baps.domain.dto.ScheduleTaskStatusEnum;
import com.trinasolar.scp.baps.domain.dto.ScheduledTaskLinesDTO;
import com.trinasolar.scp.common.api.util.BizException;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * 日志記錄
 */
public interface LogService {
    /**
     * 获取日志任务对象
     * @return
     */
    public ScheduledTaskLinesDTO createLogTask(String taskName);

    /**
     * 向任务中添加日志
     * @param task
     * @param status
     * @param log
     */
    public void addLog(ScheduledTaskLinesDTO task, ScheduleTaskStatusEnum status, String log);

    /**
     * 保存任务日志
     * @param task
     */
    public void saveTaskLog(ScheduledTaskLinesDTO task);

    /**
     * 获取异常详细信息
     * @param t
     * @return
     */
    default   String getStackTraceAsString(Throwable t) {
        if (t instanceof BizException){
            return t.getMessage();
        }
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        t.printStackTrace(printWriter);
        String message= stringWriter.toString();
        //The maximum length of cell contents (text) is 32767 characters excel单元格存储最大长度,导出报错
        if (message.length()<30000){
            return message;
        }else{
          message=  message.substring(0,30000);
        }
        return message;
    }
}
