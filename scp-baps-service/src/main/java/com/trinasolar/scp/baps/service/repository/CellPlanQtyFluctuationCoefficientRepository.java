package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellPlanQtyFluctuationCoefficient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 投产计划浮动系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-30 01:49:53
 */
@Repository
public interface CellPlanQtyFluctuationCoefficientRepository extends JpaRepository<CellPlanQtyFluctuationCoefficient, Long>, QuerydslPredicateExecutor<CellPlanQtyFluctuationCoefficient> {
}
