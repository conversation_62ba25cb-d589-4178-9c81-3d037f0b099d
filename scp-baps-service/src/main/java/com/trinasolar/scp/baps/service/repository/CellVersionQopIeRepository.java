package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellVersionQopIe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * qop与ie对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:08
 */
@Repository
public interface CellVersionQopIeRepository extends JpaRepository<CellVersionQopIe, Long>, QuerydslPredicateExecutor<CellVersionQopIe> {
}
