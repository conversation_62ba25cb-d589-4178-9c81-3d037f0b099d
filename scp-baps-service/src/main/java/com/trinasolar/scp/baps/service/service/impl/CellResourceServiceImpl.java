package com.trinasolar.scp.baps.service.service.impl;
import com.google.common.base.Joiner;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellBaseCapacityDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellGradeCapacityDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDTO;
import com.trinasolar.scp.baps.domain.dto.CellGradeCapacityDTO;
import com.trinasolar.scp.baps.domain.dto.CellResourceDTO;
import com.trinasolar.scp.baps.domain.convert.CellResourceDEConvert;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.entity.Calendar;
import com.trinasolar.scp.baps.domain.query.CellResourceQuery;
import com.trinasolar.scp.baps.domain.save.CellResourceSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CalendarRepository;
import com.trinasolar.scp.baps.service.repository.CellResourceRepository;
import com.trinasolar.scp.baps.service.service.CellResourceService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
/**
 * 电池资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-08 02:52:37
 */
@Slf4j
@Service("cellResourceService")
@RequiredArgsConstructor
public class CellResourceServiceImpl implements CellResourceService {
    private static final QCellResource qCellResource = QCellResource.cellResource;
    private final CellResourceDEConvert convert;
    private final CellResourceRepository repository;
    private final JPAQueryFactory jpaQueryFactory;
    private final CellBaseCapacityDEConvert cellBaseCapacityDeconvert;
    private final CalendarRepository calendarRepository;
    private final CellGradeCapacityDEConvert cellGradeCapacityDeconvert;
    @Override
    public Page<CellResourceDTO> queryByPage(CellResourceQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellResource> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellResourceQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellResource.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellResource.isOverseaId.eq(query.getIsOverseaId()));
        }
        if(StringUtils.isNotEmpty(query.getIsOversea())){
            booleanBuilder.and(qCellResource.isOversea.eq(query.getIsOversea()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellResource.basePlaceId.eq(query.getBasePlaceId()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlace())){
            booleanBuilder.and(qCellResource.basePlace.eq(query.getBasePlace()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellResource.workshopId.eq(query.getWorkshopId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qCellResource.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkunitId() != null) {
            booleanBuilder.and(qCellResource.workunitId.eq(query.getWorkunitId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkunit())){
            booleanBuilder.and(qCellResource.workunit.eq(query.getWorkunit()));
        }
        if (query.getIsSplited() != null) {
            booleanBuilder.and(qCellResource.isSplited.eq(query.getIsSplited()));
        }
        if(StringUtils.isNotEmpty(query.getLineName())){
            booleanBuilder.and(qCellResource.lineName.eq(query.getLineName()));
        }
        if(StringUtils.isNotEmpty(query.getResourceGroup())){
            booleanBuilder.and(qCellResource.resourceGroup.eq(query.getResourceGroup()));
        }
        if (query.getNumberLine() != null) {
            booleanBuilder.and(qCellResource.numberLine.eq(query.getNumberLine()));
        }
    }

    @Override
    public CellResourceDTO queryById(Long id) {
        CellResource queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellResourceDTO save(CellResourceSaveDTO saveDTO) {
        CellResource newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new CellResource());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellResourceQuery query, HttpServletResponse response) {
       List<CellResourceDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "电池资源表", "电池资源表", excelPara.getSimpleHeader(), excelData);
    }
    @Override
    public  void makeData(Integer ieorgrade){
        //爬坡数据
        if (ieorgrade == 1){
            //先删除原有资源表里的数据
            repository.deleteByIeGrade(1);
            //爬坡产能数据
            //  1、取IE产能数据（国内海外、生产基地、生产车间、生产单元、可用线体数量）。筛选 结束时间大于等于本月
            QCellGradeCapacity qCellGradeCapacity = QCellGradeCapacity.cellGradeCapacity;
            //获取当前月类似202403形式
            String month = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
            JPAQuery<CellGradeCapacity> where = jpaQueryFactory.select(qCellGradeCapacity).from(qCellGradeCapacity);
            List<CellGradeCapacity> datas = where.fetch();
            //筛选datas中month大于等于本月的数据
            datas = datas.stream().filter(item -> item.getMonth().compareTo(month) >= 0).collect(Collectors.toList());
            List<CellGradeCapacityDTO> dtos=  cellGradeCapacityDeconvert.copyEntityToValueDto(datas);


            List<CellGradeCapacityDTO> filterData =dtos;
            //按国内海外、生产基地、生产单元、产线分组，获取每组中可用线体数最大的对象记录
            Joiner joiner = Joiner.on("-").useForNull("null");
            Map<String, Optional<CellGradeCapacityDTO>> collect = filterData.stream().collect(Collectors.groupingBy(
                    item -> {return joiner.join(item.getIsOversea(), item.getBasePlace(), item.getWorkunit(),item.getLineName());}
                    ,
                    Collectors.maxBy(Comparator.comparing(CellGradeCapacityDTO::getLineNumber))
            ));
            //获取collect这个map的value，并转换为list
            List<CellGradeCapacityDTO> preData = collect.values().stream().map(item -> item.get()).collect(Collectors.toList());
//2、展开拆分出生产线体。根据第一步获取的数据，按可用产线数量展开成多行数据，线体名称在生产单元后面加中滑线和序列号（如THAO_C1-1-1、THAO_C1-1-2、...、THAO_C1-1-8），线体数量改为
            preData=  cellGradeCapacityDeconvert.copyDtoNameFromDto(preData);
            List<CellResource> allCellResourceList =  new ArrayList<>();

            for (CellGradeCapacityDTO dto :preData) {
                CellResource cellResourceParent=     cellGradeCapacityDeconvert.toCellResourceEntity(dto);
                cellResourceParent.setId(null);
                cellResourceParent.setIeorgrade(1);
                cellResourceParent.setWorkshopId(dto.getWorkshopid());
                cellResourceParent.setWorkunitId(dto.getWorkunitid());
                cellResourceParent.setIsSplited(null);
                cellResourceParent.setResourceGroup(null);
                cellResourceParent.setNumberLine(dto.getLineNumber());
                cellResourceParent.setFromId(dto.getId());
                allCellResourceList.add(cellResourceParent);
                cellResourceParent.setLineName(dto.getLineName());
            }
            repository.saveAll(allCellResourceList);
        }else {
            //先删除电池资源的数据
            repository.deleteByIeGrade(0);
            //Ie产能数据
          //  1、取IE产能数据（国内海外、生产基地、生产车间、生产单元、可用线体数量）。筛选 结束时间大于等于本月 且 可用线体数量大于 1 ，排除掉LOV值的生产单元，
            QCellBaseCapacity qCellBaseCapacity = QCellBaseCapacity.cellBaseCapacity;
            //获取本月第一天
            LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
            JPAQuery<CellBaseCapacity> where = jpaQueryFactory.select(qCellBaseCapacity).from(qCellBaseCapacity).where(qCellBaseCapacity.endTime.goe(firstDayOfMonth));
            where.where(qCellBaseCapacity.usageLine.gt(1));
            List<CellBaseCapacity> datas = where.fetch();
            List<CellBaseCapacityDTO> dtos=  cellBaseCapacityDeconvert.copyEntityToValueDto(datas);

            //读取lov
            Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BAPS_WORKUNIT_TO_LINE);
            Set<String> allWorkUints = new HashSet<String>();
            for ( LovLineDTO dto : allByHeaderCode.values()) {
                allWorkUints.add(dto.getLovValue());
            }
            List<CellBaseCapacityDTO> filterData = dtos.stream().filter(item -> {
                return !allWorkUints.contains(item.getWorkunit());
            }).collect(Collectors.toList());
            List<CellBaseCapacityDTO> filterDataEq = dtos.stream().filter(item -> {
                return allWorkUints.contains(item.getWorkunit());
            }).collect(Collectors.toList());
            //依据开始时间取最小，结束时间取最大
            // 按国内海外、生产基地、生产单元分组，获取每组中可用线体数最大的对象记录

            //获取collect这个map的value，并转换为list
            List<CellBaseCapacityDTO> preData =groupData(filterData);
            List<CellBaseCapacityDTO> preDataEq = groupData(filterDataEq);
//2、展开拆分出生产线体。根据第一步获取的数据，按可用产线数量展开成多行数据，线体名称在生产单元后面加中滑线和序列号（如THAO_C1-1-1、THAO_C1-1-2、...、THAO_C1-1-8），线体数量改为
            preData=  cellBaseCapacityDeconvert.copyDtoNameFromDto(preData);
            preDataEq=  cellBaseCapacityDeconvert.copyDtoNameFromDto(preDataEq);
            List<CellResource> allCellResourceList =  new ArrayList<>();
            List<CellResource> splitCellResourceList =  new ArrayList<>();
            for (CellBaseCapacityDTO dto :preData) {
                //3、增加字段资源组（Resource_Group）, 字段取值等于生产单元。
                //4、将展开后数据存入BAPS_CELL_RESOURCE表中，拆分标识为

               BigDecimal userageLine= dto.getUsageLine();
               //判断userageLine是不是整数
                int userageLineInt=userageLine.intValue();
                if (userageLine.compareTo(new BigDecimal(userageLine.intValue()))!=0){
                    userageLineInt+=1;
                }
                CellResource cellResourceParent=     cellBaseCapacityDeconvert.toCellResourceEntity(dto);
                cellResourceParent.setIeorgrade(0);
                cellResourceParent.setId(null);
                cellResourceParent.setWorkshopId(dto.getWorkshopid());
                cellResourceParent.setWorkunitId(dto.getWorkunitid());
                cellResourceParent.setIsSplited(null);
                cellResourceParent.setLineName(null);
                cellResourceParent.setResourceGroup(null);
                cellResourceParent.setNumberLine(dto.getNumberLine());
                cellResourceParent.setFromId(dto.getId());
                allCellResourceList.add(cellResourceParent);

                for (int j = 1; j <=userageLineInt;j++){
                    CellResource cellResource=     cellBaseCapacityDeconvert.toCellResourceEntity(dto);
                    cellResource.setId(null);
                    cellResource.setIeorgrade(0);
                    cellResource.setWorkshopId(dto.getWorkshopid());
                    cellResource.setWorkunitId(dto.getWorkunitid());
                    cellResource.setIsSplited(1);
                    cellResource.setLineName(dto.getWorkunit()+"-"+j);
                    cellResource.setFromId(dto.getId());
                    //判断userageLine-1大于等于0，则线体数量为1，否则为直接赋值userageLine
                    if (userageLine.subtract(new BigDecimal(1)).compareTo(BigDecimal.ZERO)>=0){
                        cellResource.setNumberLine(BigDecimal.ONE);
                        userageLine=userageLine.subtract(new BigDecimal(1));
                    }else {
                        cellResource.setNumberLine(userageLine);
                    }
                    cellResource.setResourceGroup(dto.getWorkunit());
                    allCellResourceList.add(cellResource);
                    splitCellResourceList.add(cellResource);
                }
            }
            for (CellBaseCapacityDTO dto :preDataEq) {
                //3、增加字段资源组（Resource_Group）, 字段取值等于生产单元。
                //4、将展开后数据存入BAPS_CELL_RESOURCE表中，拆分标识为

                BigDecimal userageLine= dto.getUsageLine();

                CellResource cellResourceParent=     cellBaseCapacityDeconvert.toCellResourceEntity(dto);
                cellResourceParent.setIeorgrade(0);
                cellResourceParent.setId(null);
                cellResourceParent.setWorkshopId(dto.getWorkshopid());
                cellResourceParent.setWorkunitId(dto.getWorkunitid());
                cellResourceParent.setIsSplited(null);
                cellResourceParent.setLineName(null);
                cellResourceParent.setResourceGroup(null);
                cellResourceParent.setNumberLine(dto.getNumberLine());
                cellResourceParent.setFromId(dto.getId());
                allCellResourceList.add(cellResourceParent);


            }
             repository.saveAll(allCellResourceList);
            //构建生成日历，循环 splitCellResourceList
            List<Calendar> calendars=new ArrayList<>();
            for (CellResource cellResource : splitCellResourceList) {
                LocalDate startDate=cellResource.getStartTime();
                LocalDate endDate=cellResource.getEndTime().plusDays(1);
                do{
                    Calendar calendar = new Calendar();
                    calendar.setDate(startDate);
                    BeanUtils.copyProperties(cellResource,calendar);
                    CalendarRepository.setDefault(calendar);
                    calendar.setId(null);
                    calendar.setIeorgrade(0);
                    calendar.setSortorder(15);
                    calendar.setDefaultqty(cellResource.getNumberLine());
                    calendar.setNumLines(cellResource.getNumberLine());
                    startDate= startDate.plusDays(1);
                    calendar.setFromid(cellResource.getFromId());
                    calendars.add(calendar);

                }while (!startDate.equals(endDate));


            }
            calendarRepository.saveAll(calendars);





        }

    }
    private List<CellBaseCapacityDTO> groupData(List<CellBaseCapacityDTO> datas){
        List<CellBaseCapacityDTO> finalDatas=new ArrayList<>();
        Joiner joiner = Joiner.on("-").useForNull("null");
        Map<String, List<CellBaseCapacityDTO>> collect = datas.stream().collect(Collectors.groupingBy(
                item -> joiner.join(item.getIsOversea(), item.getBasePlace(), item.getWorkunit())
        ));
        //对分组后数据处理
        if (CollectionUtils.isNotEmpty(datas)){
            for (Map.Entry<String, List<CellBaseCapacityDTO>> entry : collect.entrySet()) {
                List<CellBaseCapacityDTO> preDatas = entry.getValue();
                if (CollectionUtils.isEmpty(preDatas)){
                    continue;
                }
                CellBaseCapacityDTO dto=new CellBaseCapacityDTO();
                BeanUtils.copyProperties(preDatas.get(0),dto);
                //获取preDatas中最小开始时间startTime值
                Optional<LocalDate> min = preDatas.stream().map(item -> item.getStartTime()).min(Comparator.naturalOrder());
                //获取preDatas中最大结束时间endTime值
                Optional<LocalDate> max = preDatas.stream().map(item -> item.getEndTime()).max(Comparator.naturalOrder());
                //获取preDatas中最大可用线体数
                Optional<BigDecimal> maxLine = preDatas.stream().map(item -> item.getUsageLine()).max(Comparator.naturalOrder());
                dto.setStartTime(min.get());
                dto.setEndTime(max.get());
                dto.setUsageLine(maxLine.get());
                finalDatas.add(dto);
            }
       }
        return finalDatas;

    }

}
