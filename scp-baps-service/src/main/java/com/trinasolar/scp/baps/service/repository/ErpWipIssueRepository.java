package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.ErpWipIssue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Erp实际入库来源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:23:58
 */
@Repository
public interface ErpWipIssueRepository extends JpaRepository<ErpWipIssue, Long>, QuerydslPredicateExecutor<ErpWipIssue> {
    //获取某个库存组织的数据最新更新时间
    @Query(value = "select * from  baps_erp_wip_issue AS tb WHERE tb.organization_id = (:organizationId) AND tb.is_deleted=0 and tb.creation_date IS NOT NULL  ORDER BY  tb.creation_date DESC LIMIT 1", nativeQuery = true)
    ErpWipIssue findLastUpdateObj(@Param("organizationId") Long organizationId);
}
