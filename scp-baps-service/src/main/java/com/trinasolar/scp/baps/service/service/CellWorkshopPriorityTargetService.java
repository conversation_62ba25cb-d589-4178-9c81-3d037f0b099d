package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellWorkshopPriorityTargetDTO;
import com.trinasolar.scp.baps.domain.query.CellWorkshopPriorityTargetQuery;
import com.trinasolar.scp.baps.domain.save.CellWorkshopPriorityTargetSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 车间优先度效率目标值 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
public interface CellWorkshopPriorityTargetService {
    /**
     * 分页获取车间优先度效率目标值
     *
     * @param query 查询对象
     * @return 车间优先度效率目标值分页对象
     */
    Page<CellWorkshopPriorityTargetDTO> queryByPage(CellWorkshopPriorityTargetQuery query);

    /**
     * 根据主键获取车间优先度效率目标值详情
     *
     * @param id 主键
     * @return 车间优先度效率目标值详情
     */
    CellWorkshopPriorityTargetDTO queryById(Long id);

    /**
     * 保存或更新车间优先度效率目标值
     *
     * @param saveDTO 车间优先度效率目标值保存对象
     * @return 车间优先度效率目标值对象
     */
    CellWorkshopPriorityTargetDTO save(CellWorkshopPriorityTargetSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除车间优先度效率目标值
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(CellWorkshopPriorityTargetQuery query, HttpServletResponse response);

    /**
     * 读取第第三方接口scp-aps效率分布的接口后进行数据整合
     *
     * @return
     */
    void refreshData();
}

