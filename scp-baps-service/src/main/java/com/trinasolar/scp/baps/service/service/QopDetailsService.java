package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.QopDetailsDTO;
import com.trinasolar.scp.baps.domain.query.QopDetailsQuery;
import com.trinasolar.scp.baps.domain.save.QopDetailsSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * SOP数据 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-30 03:38:41
 */
public interface QopDetailsService {
    List<QopDetailsDTO> query(QopDetailsQuery query);

    /**
     * 分页获取SOP数据
     *
     * @param query 查询对象
     * @return SOP数据分页对象
     */
    List<QopDetailsDTO> queryByPage(QopDetailsQuery query);

    /**
     * 根据主键获取SOP数据详情
     *
     * @param id 主键
     * @return SOP数据详情
     */
        QopDetailsDTO queryById(Long id);

    /**
     * 保存或更新SOP数据
     *
     * @param saveDTO SOP数据保存对象
     * @return SOP数据对象
     */
    QopDetailsDTO save(QopDetailsSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除SOP数据
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(QopDetailsQuery query, HttpServletResponse response);
}

