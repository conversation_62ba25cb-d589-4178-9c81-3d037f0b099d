package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellConversionFactorDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellConversionFactorDTO;
import com.trinasolar.scp.baps.domain.dto.MwCoefficientDTO;
import com.trinasolar.scp.baps.domain.entity.CellConversionFactor;
import com.trinasolar.scp.baps.domain.entity.QCellConversionFactor;
import com.trinasolar.scp.baps.domain.excel.CellConversionFactorExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellConversionFactorQuery;
import com.trinasolar.scp.baps.domain.query.MwCoefficientQuery;
import com.trinasolar.scp.baps.domain.save.CellConversionFactorSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.repository.CellConversionFactorRepository;
import com.trinasolar.scp.baps.service.service.CellConversionFactorService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static com.trinasolar.scp.baps.domain.entity.QCellConversionFactor.cellConversionFactor;

/**
 * 万片与兆瓦折算系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-31 10:33:29
 */
@Slf4j
@Service("cellConversionFactorService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class CellConversionFactorServiceImpl implements CellConversionFactorService {
    private static final QCellConversionFactor qCellConversionFactor = cellConversionFactor;

    private final CellConversionFactorDEConvert convert;

    private final CellConversionFactorRepository repository;

    private final ApsFeign apsFeign;

    private final JPAQueryFactory jpaQueryFactory;

    @Override
    public Page<CellConversionFactorDTO> queryByPage(CellConversionFactorQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellConversionFactor> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellConversionFactorQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellConversionFactor.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellConversionFactor.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellConversionFactor.isOversea.eq(query.getIsOversea()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellConversionFactor.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellConversionFactor.cellsType.eq(query.getCellsType()));
        }
        if (query.getConversionFactor() != null) {
            booleanBuilder.and(qCellConversionFactor.conversionFactor.eq(query.getConversionFactor()));
        }
    }

    @Override
    public CellConversionFactorDTO queryById(Long id) {
        CellConversionFactor queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    @Cacheable(cacheNames = "CellConversionFactorService_queryByCellsType", key = "#isOversea+'_'+#cellsType", unless = "#result == null")
    public CellConversionFactorDTO queryByCellsType(String isOversea, String cellsType) {
        CellConversionFactor firstFactor = jpaQueryFactory.selectFrom(qCellConversionFactor)
                .where(qCellConversionFactor.cellsType.eq(cellsType))
                .where(qCellConversionFactor.isOversea.eq(isOversea))
                .limit(1).fetchOne();
        return convert.toDto(firstFactor);
    }

    @Override
    public CellConversionFactorDTO save(CellConversionFactorSaveDTO saveDTO) {
        CellConversionFactor newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellConversionFactor());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellConversionFactorQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellConversionFactorDTO> dtos = queryByPage(query).getContent();
        // dto数据转为ExcelData数据
        List<CellConversionFactorExcelDTO> excelDtos = convert.toExcelDTO(dtos);
        ExcelPara excelPara =  query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(excelDtos, excelPara);
        // 导出调用excelUtils
        String fileName= BapsMessgeHelper.getMessage("export.cellconversionfactor.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile) {

        List<CellConversionFactorExcelDTO> excelDtos = new LinkedList<>();
        // 这里为了简单 所以注册了 同样的head 和Listener 自己使用功能必须不同的Listener
        ExcelReaderBuilder readerBuilder = EasyExcel.read(multipartFile.getInputStream(), CellConversionFactorExcelDTO.class, new ReadListener<CellConversionFactorExcelDTO>() {
            @Override
            public void invoke(CellConversionFactorExcelDTO data, AnalysisContext context) {
                excelDtos.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        });
        readerBuilder.registerConverter(new LocalDateConverter());
        readerBuilder.sheet(0).doRead();
        //验证数据
        checkInput(excelDtos);
        final QuerySheetExcelDTO[] querySheetExcelDTO = {null};
        EasyExcel.read(multipartFile.getInputStream(), QuerySheetExcelDTO.class, new ReadListener<QuerySheetExcelDTO>() {
            @Override
            public void invoke(QuerySheetExcelDTO data, AnalysisContext context) {
                querySheetExcelDTO[0] = data;
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet(1).doRead();
        QuerySheetExcelDTO queryParam = querySheetExcelDTO[0];

        if (Objects.isNull(queryParam)) {
            throw new BizException("baps_import_params_error");
        }

        // 查询原始行

        CellConversionFactorQuery page = JSON.parseObject(queryParam.getQueryParam(), CellConversionFactorQuery.class);

        List<CellConversionFactorDTO> oriDTOS = this.queryByPage(page).getContent();

        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("baps_import_file_error");
        }

        List<CellConversionFactorSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);

        // 先删除之前的数据,再保存信息的数据
        oriDTOS.forEach(i -> {
            repository.deleteById(i.getId());
        });


        saveDTOS.stream().forEach(this::save);


    }

    public void checkInput(List<CellConversionFactorExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors = new ArrayList<>();
        excelDTOS.stream().forEach(excelDTO -> {
            //验证电池类型
            LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType());
            if (lovLineDTO == null) {
                String error = MessageHelper.getMessage("baps_row_cell_type_not_exist", new Object[]{i[0], excelDTO.getCellsType()}).getDesc();
                errors.add(error);
            }
            i[0]++;
        });
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(","));
            throw new BizException(errorString);
        }
    }

    /**
     * 更新万片与兆瓦折算系数
     */
    @Override
    public void updateConversionFactor() {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        //每同步一次 删除原本数据 isDelete=1
        repository.deleteAll();
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BATTERY_TYPE);
        MwCoefficientQuery query = new MwCoefficientQuery();
        Set<String> set = new HashSet<>();
        allByHeaderCode.values().stream().forEach(value -> {
            set.add(value.getLovName());
        });
        query.setCellTypeList(set.stream().collect(Collectors.toList()));
        List<MwCoefficientDTO> mwCoefficientDTOList = apsFeign.findMwCoefficient(query).getBody().getData();
        List<CellConversionFactorSaveDTO> saveDTOList = new ArrayList<>();
        for (MwCoefficientDTO mwCoefficientDTO : mwCoefficientDTOList) {
            CellConversionFactorSaveDTO saveDTO = new CellConversionFactorSaveDTO();
            if (OverseaConstant.INLAND_VALUE.equals(mwCoefficientDTO.getIsOversea())) {
                saveDTO.setIsOversea(OverseaConstant.INLAND);
                saveDTO.setIsOverseaId(OverseaConstant.INLAND_ID);
            } else {
                saveDTO.setIsOversea(OverseaConstant.OVERSEA);
                saveDTO.setIsOverseaId(OverseaConstant.OVERSEA_ID);
            }
            saveDTO.setCellsType(mwCoefficientDTO.getCellType());
            saveDTO.setConversionFactor(mwCoefficientDTO.getCoefficient());
            LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, mwCoefficientDTO.getCellType());
            saveDTO.setCellsTypeId(lovLineDTO.getLovLineId());
            saveDTOList.add(saveDTO);
        }
        saveDTOList.stream().forEach(this::save);
    }

    /**
     * 构建电池类型+国内海外--》兆瓦转换对象的map
     *
     * @return
     */
    @Override
    public Map<String, CellConversionFactorDTO> createMapByCelltypeAndIsOversea() {
        List<CellConversionFactor> all = repository.findAll();
        List<CellConversionFactorDTO> cellConversionFactorDTOS = convert.toDto(all);
        Map<String, List<CellConversionFactorDTO>> collect = cellConversionFactorDTOS.stream().collect(Collectors.groupingBy(item -> {
            return StringTools.joinWith(",", item.getIsOversea(), item.getCellsType());
        }));
        Map<String, CellConversionFactorDTO> map = new HashMap<>();
        collect.keySet().stream().forEach(key -> {
            map.put(key, collect.get(key).get(0));
        });
        return map;
    }
}
