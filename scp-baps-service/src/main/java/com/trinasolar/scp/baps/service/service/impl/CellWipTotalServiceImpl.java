package com.trinasolar.scp.baps.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.convert.CellWipTotalDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellWipTotalDTO;
import com.trinasolar.scp.baps.domain.entity.CellWipTotal;
import com.trinasolar.scp.baps.domain.entity.QCellWipTotal;
import com.trinasolar.scp.baps.domain.query.CellWipTotalQuery;
import com.trinasolar.scp.baps.domain.save.CellWipTotalSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellWipTotalRepository;
import com.trinasolar.scp.baps.service.service.CellWipTotalService;
import com.trinasolar.scp.baps.service.service.PermissionService;
import com.trinasolar.scp.common.api.base.DataPrivilegeDTO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 开立工单明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Slf4j
@Service("cellWipTotalService")
@RequiredArgsConstructor
public class CellWipTotalServiceImpl implements CellWipTotalService {
    private static final QCellWipTotal qCellWipTotal = QCellWipTotal.cellWipTotal;

    private final CellWipTotalDEConvert convert;

    private final CellWipTotalRepository repository;
    private final PermissionService permissionService;

    @Override
    public Page<CellWipTotalDTO> queryByPage(CellWipTotalQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        List<DataPrivilegeDTO> dataPrivilegeDTOS = permissionService.getBasePlaceDataPrivilegeDTOS();
        if (CollectionUtils.isNotEmpty(dataPrivilegeDTOS)) {
            List<Long> basePlaceListIds = dataPrivilegeDTOS.stream().map(DataPrivilegeDTO::getDataId).collect(Collectors.toList());
            //如果basePlaceList不包含-1，进行筛选
            if (!basePlaceListIds.contains(-1L)) {
                //依据基地Id获取lov的name
                List<String> basePlaceNameList = basePlaceListIds.stream().map(basePlaceId -> {
                    LovLineDTO lovLineDTO = LovUtils.getByLang(LovHeaderCodeConstant.BASE_PLACE, basePlaceId, LovHeaderCodeConstant.LANGUAGE_CN);
                    return lovLineDTO.getLovName();
                }).collect(Collectors.toList());
                booleanBuilder.and(qCellWipTotal.basePlace.in(basePlaceNameList));
            }

        }else{
            booleanBuilder.and(qCellWipTotal.basePlace.eq("-1"));
        }
        Page<CellWipTotal> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellWipTotalQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellWipTotal.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getOrderCode())) {
            booleanBuilder.and(qCellWipTotal.orderCode.eq(query.getOrderCode()));
        }
        if (StringUtils.isNotEmpty(query.getBillingAccount())) {
            booleanBuilder.and(qCellWipTotal.billingAccount.eq(query.getBillingAccount()));
        }
        if (query.getBillingDate() != null) {
            booleanBuilder.and(qCellWipTotal.billingDate.eq(query.getBillingDate()));
        }
        if (StringUtils.isNotEmpty(query.getErpOrderCode())) {
            booleanBuilder.and(qCellWipTotal.erpOrderCode.eq(query.getErpOrderCode()));
        }
        if (query.getMonth() != null) {
            booleanBuilder.and(qCellWipTotal.month.eq(query.getMonth()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellWipTotal.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellWipTotal.workshop.eq(query.getWorkshop()));
        }
        if (query.getQty() != null) {
            booleanBuilder.and(qCellWipTotal.qty.eq(query.getQty()));
        }
        if (query.getQtyBilling() != null) {
            booleanBuilder.and(qCellWipTotal.qtyBilling.eq(query.getQtyBilling()));
        }
        if (query.getQtyBillingTotal() != null) {
            booleanBuilder.and(qCellWipTotal.qtyBillingTotal.eq(query.getQtyBillingTotal()));
        }
        if (StringUtils.isNotEmpty(query.getCellType())) {
            booleanBuilder.and(qCellWipTotal.cellType.eq(query.getCellType()));
        }
        if (StringUtils.isNotEmpty(query.getItemFivea())) {
            booleanBuilder.and(qCellWipTotal.itemFivea.eq(query.getItemFivea()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellWipTotal.isOversea.eq(query.getIsOversea()));
        }
        if (query.getStartTime() != null) {
            booleanBuilder.and(qCellWipTotal.startTime.eq(query.getStartTime()));
        }
        if (query.getEndTime() != null) {
            booleanBuilder.and(qCellWipTotal.endTime.eq(query.getEndTime()));
        }
    }

    @Override
    public CellWipTotalDTO queryById(Long id) {
        CellWipTotal queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellWipTotalDTO save(CellWipTotalSaveDTO saveDTO) {
        CellWipTotal newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellWipTotal());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellWipTotalQuery query, HttpServletResponse response) {
        List<CellWipTotalDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "开立工单明细表", "开立工单明细表", excelPara.getSimpleHeader(), excelData);
    }
}
