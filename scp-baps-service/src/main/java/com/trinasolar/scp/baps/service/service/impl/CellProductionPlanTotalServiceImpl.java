package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellProductionPlanDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellConversionFactorDTO;
import com.trinasolar.scp.baps.domain.dto.CellProductionPlanTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellProductionPlanDTO;
import com.trinasolar.scp.baps.domain.convert.CellProductionPlanTotalDEConvert;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.entity.CellProductionPlanTotal;
import com.trinasolar.scp.baps.domain.excel.CellProductionPlanTotalExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanQuery;
import com.trinasolar.scp.baps.domain.save.CellProductionPlanTotalSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.baps.service.repository.CellProductionPlanRepository;
import com.trinasolar.scp.baps.service.repository.CellProductionPlanTotalRepository;
import com.trinasolar.scp.baps.service.service.CellConversionFactorService;
import com.trinasolar.scp.baps.service.service.CellProductionPlanService;
import com.trinasolar.scp.baps.service.service.CellProductionPlanTotalService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.util.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

/**
 * 投产计划汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Slf4j
@Service("cellProductionPlanTotalService")
@RequiredArgsConstructor
public class CellProductionPlanTotalServiceImpl implements CellProductionPlanTotalService {
    private static final QCellProductionPlanTotal qCellProductionPlanTotal = QCellProductionPlanTotal.cellProductionPlanTotal;
    private final CellProductionPlanTotalDEConvert convert;
    private final CellConversionFactorService cellConversionFactorService;
    private final CellProductionPlanTotalRepository repository;
    private final CellProductionPlanService cellProductionPlanService;
    private final CellProductionPlanRepository cellProductionPlanRepository;
    private final CellProductionPlanDEConvert cellProductionPlanDEConvert;
    @Autowired
    private JPAQueryFactory jpaQueryFactory;
    @Override
    public Page<CellProductionPlanTotalDTO> queryByPage(CellProductionPlanTotalQuery query) {
        //当前月或以后月份到投产表里去查，否则到汇总表里查
        //当前月
        LocalDate curMonth = LocalDate.now().withDayOfMonth(1);
        //要查的月份
        LocalDate month = DateUtil.getLocalDate(query.getMonth(), 1);
        if (month.isEqual(curMonth) || month.isAfter(curMonth)) {
            //查询投产计划里的数据
            return calcCellProductionPlanTotalByQuery(convert.toCellProductionPlanQuery(query));
        }else
        {
            //查询投产汇总表里的数据
            return getCellProductionPlanTotalByQuery(query);
        }
    }
    /**
     * 统计汇总最新版本数据(从投产计划表读，读后计算)
     *
     * @param query
     */
    private Page<CellProductionPlanTotalDTO> calcCellProductionPlanTotalByQuery(CellProductionPlanQuery query) {
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        //获取投产计划表的version
        Pair<String, String> versions = getLastVersion(query);
        //对没有进行汇总的进行汇总
        List<CellProductionPlanTotalDTO> cellProductionPlanTotals = new ArrayList<>();
        List<CellProductionPlanDTO> datas = cellProductionPlanService.query(query, versions);
        Map<String, Map<String, List<CellProductionPlanDTO>>> collect = datas.stream().collect(Collectors.groupingBy(CellProductionPlanDTO::getCellsType, Collectors.groupingBy(
                i -> {
                    return StringTools.joinWith(",",i.getBasePlace(), i.getWorkshop(), i.getHTrace(), i.getAesthetics(), i.getTransparentDoubleGlass(), i.getCellSource());
                }
        )));

        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer index = 0;
        Integer endPos = startPos + pageSize;
        AtomicReference<Integer> total = new AtomicReference<>(0);
        collect.entrySet().stream().forEach(cellTypeEntry -> {
            total.set(total.get() + cellTypeEntry.getValue().size());
        });
        Set<Map.Entry<String, Map<String, List<CellProductionPlanDTO>>>> entriesCellTypeSet = collect.entrySet();
        boolean isOver = false;
        for (Map.Entry<String, Map<String, List<CellProductionPlanDTO>>> entryCellsType : entriesCellTypeSet) {
            //电池类型
            String cellsType = entryCellsType.getKey();
            Set<Map.Entry<String, List<CellProductionPlanDTO>>> entries = entryCellsType.getValue().entrySet();
            if (index + entries.size() < startPos) {
                index = index + entries.size();
                continue;
            }
            //依据电池类型获取对象折算系数
            CellConversionFactorDTO cellConversionFactorDTO = null;

            for (Map.Entry<String, List<CellProductionPlanDTO>> entry : entries) {
                if (index < startPos) {
                    index++;
                    continue;
                }
                CellProductionPlanTotalDTO cellProductionPlanTotal = null;
                List<CellProductionPlanDTO> dtos = entry.getValue();
                //统计数据
                BigDecimal qtyCount = BigDecimal.ZERO;//万片数
                for (CellProductionPlanDTO dto : dtos) {
                    if (cellConversionFactorDTO==null){
                        cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", dto.getIsOversea(), dto.getCellsType()));
                    }
                    if (Objects.isNull(cellProductionPlanTotal)) {
                        cellProductionPlanTotal =convert.toCellProductionPlanTotal(dto);
                        cellProductionPlanTotal.setFromVersion(dto.getVersion());
                    }
                    int day = dto.getStartTime().getDayOfMonth();
                    BigDecimal val = ReflectUtil.invoke(cellProductionPlanTotal, "getD" + day);
                    if (Objects.isNull(val)) {
                        ReflectUtil.invoke(cellProductionPlanTotal, "setD" + day, dto.getQtyPc());
                    } else {
                        BigDecimal addVal = val.add(dto.getQtyPc());
                        ReflectUtil.invoke(cellProductionPlanTotal, "setD" + day, addVal);
                    }
                    if (Objects.nonNull(dto.getQtyPc())) {
                        qtyCount = qtyCount.add(dto.getQtyPc());
                    }
                }

                //依据瓦片折算系数计算MV
                if (Objects.nonNull(cellConversionFactorDTO) && Objects.nonNull(cellConversionFactorDTO.getConversionFactor())) {
                    BigDecimal mv = qtyCount.multiply(cellConversionFactorDTO.getConversionFactor());
                   mv= mv.setScale(0, RoundingMode.HALF_UP);
                    cellProductionPlanTotal.setCellMv(mv);
                } else {
                    log.warn("没有《" + cellProductionPlanTotal.getCellsType() + "》电池类型折算系数数据，不能进行对应的mv计算");
                }
                qtyCount=qtyCount.setScale(2,RoundingMode.HALF_UP);
                cellProductionPlanTotal.setQtyThousandPc(qtyCount);
                cellProductionPlanTotals.add(cellProductionPlanTotal);
                index++;
                if (index >= endPos) {
                    isOver = true;
                    break;
                }
            }
            if (isOver) {
                break;
            }
        }
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        return new PageImpl(cellProductionPlanTotals, pageable, total.get());

    }

    /**
     * 统计汇总最新版本数据(从汇总表直接读)
     *
     * @param query
     * @return
     */
    private Page<CellProductionPlanTotalDTO> getCellProductionPlanTotalByQuery(CellProductionPlanTotalQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.ASC, "isOversea");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellProductionPlanTotal> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellProductionPlanTotalQuery query) {
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellProductionPlanTotal.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellProductionPlanTotal.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellProductionPlanTotal.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellProductionPlanTotal.month.eq(query.getMonth()));
        }

        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellProductionPlanTotal.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getProductionTotalLastVersion(query.getMonth(), query.getIsOversea());
            if (StringUtils.isNotEmpty(version)) {
                booleanBuilder.and(qCellProductionPlanTotal.version.eq(version));
            }

        } else {
            Pair<String, String> versions = getProductionTotalLastVersion(query);
            if (StringUtils.isNotEmpty(versions.getLeft()) && StringUtils.isNotEmpty(versions.getRight())) {
                booleanBuilder.and(qCellProductionPlanTotal.version.in(versions.getLeft(), versions.getRight()));
            } else if (StringUtils.isNotEmpty(versions.getLeft())) {
                booleanBuilder.and(qCellProductionPlanTotal.version.eq(versions.getLeft()));
            } else if (StringUtils.isNotEmpty(versions.getRight())) {
                booleanBuilder.and(qCellProductionPlanTotal.version.eq(versions.getRight()));
            }

        }
    }

    @Override
    public CellProductionPlanTotalDTO queryById(Long id) {
        CellProductionPlanTotal queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellProductionPlanTotalDTO save(CellProductionPlanTotalSaveDTO saveDTO) {
        CellProductionPlanTotal newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellProductionPlanTotal());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(CellProductionPlanTotalQuery query, HttpServletResponse response) {
        query.setPageSize(GlobalConstant.max_page_size);
        query.setPageNumber(1);
        query.setBasePlace(null);
        query.setWorkshop(null);
        query.setCellsType(null);
        //获取数据
        List<CellProductionPlanTotalDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("暂无数据");
        }
        // dto数据转为ExcelData数据
        List<CellProductionPlanTotalExcelDTO> datas = convert.toExcelDTO(dtos);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellProductionPlanTotalExcelDTO.class, datas, JSON.toJSONString(query), "投产计划汇总表", response);

    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void email(CellProductionPlanTotalQuery query) {
        query.setPageSize(GlobalConstant.max_page_size);
        query.setPageNumber(1);
        query.setBasePlace(null);
        query.setWorkshop(null);
        query.setCellsType(null);
        //本次版本数据
        List<CellProductionPlanTotalDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("暂无数据可发送");
        }
        List<CellProductionPlanTotal> preCellProductionPlanTotals = new ArrayList<>();    //存储上一版本汇总数据
        //上一版本的汇总数据map key本次数据id，值为上一个版本的对象（用于计算差异）
        Map<Long, CellProductionPlanTotal> preVersionMap = new HashMap<>();
        List<CellProductionPlan> allDatas=new ArrayList<>(); //该数据准备同步给bbom
        for (CellProductionPlanTotalDTO cellProductionPlanTotal:dtos) {
            //获取上一版本数据
            CellProductionPlanTotal preCellProductionPlanTotal = calcPreVersion(cellProductionPlanTotal);
            String version = DateUtils.getDate("yyyy-MM-dd");
            if (preCellProductionPlanTotal == null) {
                //本月还没汇总
                version = version + "(v1)";
            } else {
                preCellProductionPlanTotals.add(preCellProductionPlanTotal);
                String oldVersion = preCellProductionPlanTotal.getVersion();
                int startIndex = oldVersion.indexOf("v") + 1;
                int endIndex = oldVersion.indexOf(")");
                Integer version_val = Integer.parseInt(oldVersion.substring(startIndex, endIndex));
                version_val++;
                version += "(v" + version_val + ")";

            }
            preVersionMap.put(cellProductionPlanTotal.getId(), preCellProductionPlanTotal);
            cellProductionPlanTotal.setVersion(version);
            //落表版本号确定
            repository.save(convert.toEntity(cellProductionPlanTotal));
            //投产计划表回填版本号
            //获取要回填的记录
            QCellProductionPlan qCellProductionPlan = QCellProductionPlan.cellProductionPlan;
            JPAQuery<CellProductionPlan> cellProductionPlanWhere = jpaQueryFactory.select(qCellProductionPlan).from(qCellProductionPlan).where(qCellProductionPlan.month.eq(cellProductionPlanTotal.getMonth())).where(
                    qCellProductionPlan.basePlace.eq(cellProductionPlanTotal.getBasePlace())
            ).where(
                    qCellProductionPlan.version.eq(cellProductionPlanTotal.getFromVersion())
            ).where(
                    qCellProductionPlan.workshop.eq(cellProductionPlanTotal.getWorkshop())
            ).where(
                    qCellProductionPlan.cellsType.eq(cellProductionPlanTotal.getCellsType())
            );
            if (StringUtils.isEmpty(cellProductionPlanTotal.getHTrace())) {
                cellProductionPlanWhere.where(
                        qCellProductionPlan.hTrace.isNull()
                );
            } else {
                cellProductionPlanWhere.where(
                        qCellProductionPlan.hTrace.eq(cellProductionPlanTotal.getHTrace())
                );
            }
            if (StringUtils.isEmpty(cellProductionPlanTotal.getAesthetics())) {
                cellProductionPlanWhere.where(
                        qCellProductionPlan.aesthetics.isNull()
                );
            } else {
                cellProductionPlanWhere.where(
                        qCellProductionPlan.aesthetics.eq(cellProductionPlanTotal.getAesthetics())
                );
            }
            if (StringUtils.isEmpty(cellProductionPlanTotal.getTransparentDoubleGlass())) {
                cellProductionPlanWhere.where(
                        qCellProductionPlan.transparentDoubleGlass.isNull()
                );
            } else {
                cellProductionPlanWhere.where(
                        qCellProductionPlan.transparentDoubleGlass.eq(cellProductionPlanTotal.getTransparentDoubleGlass())
                );
            }
            if (StringUtils.isEmpty(cellProductionPlanTotal.getCellSource())) {
                cellProductionPlanWhere.where(
                        qCellProductionPlan.cellSource.isNull()
                );
            } else {
                cellProductionPlanWhere.where(
                        qCellProductionPlan.cellSource.eq(cellProductionPlanTotal.getCellSource())
                );
            }
            List<CellProductionPlan> fetch = cellProductionPlanWhere.fetch();
            String finalVersion = version;
            fetch.stream().forEach(cellProductionPlan -> {
                cellProductionPlan.setFinalVersion(finalVersion);
            });
            cellProductionPlanRepository.saveAll(fetch);
            if (CollectionUtils.isNotEmpty(fetch)){
                allDatas.addAll(fetch);//该数据准备同步给bbom
            }
        };

        //上一版本依据生产基地分组后的数组
        Map<String, List<CellProductionPlanTotal>> preCollect = preCellProductionPlanTotals.stream().collect(Collectors.groupingBy(CellProductionPlanTotal::getBasePlace));
        //当前版本依据生产基地分组后的数据
        Map<String, List<CellProductionPlanTotalDTO>> collect = dtos.stream().collect(Collectors.groupingBy(CellProductionPlanTotalDTO::getBasePlace));
        //依据不同的生产基地发邮件
        collect.entrySet().stream().forEach(entrySet -> {
            //基地名称
            String basePlace = entrySet.getKey();
            //依据基地名称获取邮件发送地址
            //准备邮件发送内容
            //1、本次版本数据，用于生成excel文档附件
            List<CellProductionPlanTotalDTO> curVersionDatas = entrySet.getValue();

            //2、上次版本计划数据（计划数据，如果邮件也要发送上次版本数据就需要该集合，目前需要不用）
            List<CellProductionPlanTotal> preVersionDatas = preCollect.get(basePlace);
            //3、准备本次和上次差异计算数据
            curVersionDatas.stream().forEach(data -> {
                log.info("本次数据：" + data);
                log.info("上次数据：" + preVersionMap.get(data.getId()));
            });

        });
        //数据同步给bbom
        cellProductionPlanService.summaryGroupByCellProductionPlan(cellProductionPlanDEConvert.toDto(allDatas));
    }
    /**
     * 查询上一个版本的CellProductionPlanTotal
     *
     * @param cellProductionPlanTotalDTO
     */
    private CellProductionPlanTotal calcPreVersion(CellProductionPlanTotalDTO cellProductionPlanTotalDTO) {
        //先查出上个版本
        QCellProductionPlanTotal qCellProductionPlanTotal = QCellProductionPlanTotal.cellProductionPlanTotal;
        JPAQuery<CellProductionPlanTotal> where = jpaQueryFactory.select(qCellProductionPlanTotal).from(qCellProductionPlanTotal).
                where(
                        qCellProductionPlanTotal.basePlace.eq(cellProductionPlanTotalDTO.getBasePlace())
                ).
                where(

                        qCellProductionPlanTotal.workshop.eq(cellProductionPlanTotalDTO.getWorkshop())
                ).where(
                        qCellProductionPlanTotal.month.eq(cellProductionPlanTotalDTO.getMonth())
                )
                .where(
                        qCellProductionPlanTotal.cellsType.eq(cellProductionPlanTotalDTO.getCellsType())
                );
        if (StringUtils.isNotEmpty(cellProductionPlanTotalDTO.getHTrace())) {
            where.where(
                    qCellProductionPlanTotal.hTrace.eq(cellProductionPlanTotalDTO.getHTrace())
            );
        } else {
            where.where(
                    qCellProductionPlanTotal.hTrace.isEmpty());
        }
        if (StringUtils.isNotEmpty(cellProductionPlanTotalDTO.getAesthetics())) {
            where.where(
                    qCellProductionPlanTotal.aesthetics.eq(cellProductionPlanTotalDTO.getAesthetics())
            );
        } else {
            where.where(
                    qCellProductionPlanTotal.aesthetics.isEmpty()
            );

        }
        if (StringUtils.isNotEmpty(cellProductionPlanTotalDTO.getTransparentDoubleGlass())) {
            where.where(
                    qCellProductionPlanTotal.transparentDoubleGlass.eq(cellProductionPlanTotalDTO.getTransparentDoubleGlass())
            );

        } else {
            where.where(
                    qCellProductionPlanTotal.transparentDoubleGlass.isEmpty()
            );

        }
        if (StringUtils.isNotEmpty(cellProductionPlanTotalDTO.getCellSource())) {
            where.where(
                    qCellProductionPlanTotal.cellSource.eq(cellProductionPlanTotalDTO.getCellSource())
            );
        } else {
            qCellProductionPlanTotal.cellSource.isEmpty();
        }
        if (StringUtils.isNotEmpty(cellProductionPlanTotalDTO.getVersion())) {
            where.where(
                    qCellProductionPlanTotal.version.ne(cellProductionPlanTotalDTO.getVersion())
            );
        }
        where.where(
                qCellProductionPlanTotal.version.isNotNull()
        );
        where.orderBy(qCellProductionPlanTotal.updatedTime.desc());
        return where.fetchFirst();


    }

    /**
     * 获取投产计划最新版本号（国内海外）
     *
     * @param query
     * @return
     */

    private Pair<String, String> getLastVersion(CellProductionPlanQuery query) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getLastVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getLastVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getLastVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }

    }

    /**
     * 获取投产计划最新版本
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getLastVersion(String month, String isOversea) {
        QCellProductionPlan qCellProductionPlan = QCellProductionPlan.cellProductionPlan;
        String version = jpaQueryFactory.select(qCellProductionPlan.version.max()).from(qCellProductionPlan).where(
                qCellProductionPlan.month.eq(month)
        ).where(
                qCellProductionPlan.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    /**
     * 获取投产计划汇总最新版本号（国内海外）
     *
     * @param query
     * @return
     */

    private Pair<String, String> getProductionTotalLastVersion(CellProductionPlanTotalQuery query) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getProductionTotalLastVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getProductionTotalLastVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getProductionTotalLastVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }

    }

    /**
     * 获取投产计划汇总最新版本
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getProductionTotalLastVersion(String month, String isOversea) {
        QCellProductionPlanTotal qCellProductionPlanTotal = QCellProductionPlanTotal.cellProductionPlanTotal;
        String version = jpaQueryFactory.select(qCellProductionPlanTotal.version.max()).from(qCellProductionPlanTotal).where(
                qCellProductionPlanTotal.month.eq(month)
        ).where(
                qCellProductionPlanTotal.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    /**
     * 获取最新版的投产数据
     *
     * @param query
     * @return
     */
    @Override
    public List<CellProductionPlanTotalDTO> queryByFirst(CellProductionPlanTotalQuery query) {
        QCellProductionPlanTotal cellProductionPlanTotal = QCellProductionPlanTotal.cellProductionPlanTotal;
        String version = queryMaxVersion(query);
        log.info("投产版本：" + version);
        if (version == null) {
            return null;
        }
        //查询最大版本号对应的数据
        JPAQuery<CellProductionPlanTotal> whereData = jpaQueryFactory.select(
                cellProductionPlanTotal
        ).from(cellProductionPlanTotal).where(
                cellProductionPlanTotal.month.eq(query.getMonth())
        );
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            whereData.where(cellProductionPlanTotal.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            whereData.where(cellProductionPlanTotal.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            whereData.where(cellProductionPlanTotal.cellsType.eq(query.getCellsType()));
        }
        List<CellProductionPlanTotal> fetch = whereData.where(cellProductionPlanTotal.version.eq(version)).fetch();

        return convert.toDto(fetch);
    }

    @Override
    public String queryMaxVersion(CellProductionPlanTotalQuery query) {
        QCellProductionPlanTotal cellProductionPlanTotal = QCellProductionPlanTotal.cellProductionPlanTotal;
        //求最大版本号
        JPAQuery<String> where = jpaQueryFactory.select(
                cellProductionPlanTotal.version
        ).from(cellProductionPlanTotal).where(
                cellProductionPlanTotal.month.eq(query.getMonth())
        ).where(
                cellProductionPlanTotal.version.isNotNull()
        );
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            where.where(cellProductionPlanTotal.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            where.where(cellProductionPlanTotal.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            where.where(cellProductionPlanTotal.cellsType.eq(query.getCellsType()));
        }
        where.orderBy(cellProductionPlanTotal.updatedTime.desc());
        String version = where.fetchFirst();
        return version;
    }

    @Override
    public List<String> queryThreeMaxVersion(CellProductionPlanTotalQuery query) {
        QCellProductionPlanTotal cellProductionPlanTotal = QCellProductionPlanTotal.cellProductionPlanTotal;
        //求最大版本号
        JPAQuery<String> where = jpaQueryFactory.selectDistinct(
                cellProductionPlanTotal.version
        ).from(cellProductionPlanTotal).where(
                cellProductionPlanTotal.month.eq(query.getMonth())
        ).where(
                cellProductionPlanTotal.version.isNotNull()
        );
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            where.where(cellProductionPlanTotal.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            where.where(cellProductionPlanTotal.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            where.where(cellProductionPlanTotal.cellsType.eq(query.getCellsType()));
        }
        where.orderBy(cellProductionPlanTotal.version.desc());
        List<String> versions = where.limit(3).fetch();
        return versions;
    }

    @Override
    public List<CellProductionPlanTotal> queryByVersion(String version) {
        return repository.selectByVersion(version);

    }

}
