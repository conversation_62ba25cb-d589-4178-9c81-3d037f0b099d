package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellInstockPlanTotal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 入库计划汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-26 11:20:51
 */
@Repository
public interface CellInstockPlanTotalRepository extends JpaRepository<CellInstockPlanTotal, Long>, QuerydslPredicateExecutor<CellInstockPlanTotal> {
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query("delete from CellInstockPlanTotal c where c.isOversea = :isOversea and c.month = :month and c.fromVersion = :version")
    void deleteByVersion(@Param("isOversea") String isOversea, @Param("month") String month, @Param("version") String version);
}
