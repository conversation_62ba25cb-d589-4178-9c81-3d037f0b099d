package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.ConfigCellGoodDTO;
import com.trinasolar.scp.baps.domain.query.ConfigCellGoodQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = FeignConstant.SCP_AOP_API, path = "/scp-aop-api",configuration =LanguageHeaderInterceptor.class)
public interface ConfigCellGoodFeign {

    /**
     * 获取报价系统的电池良率数据
     *
     * @param query
     * @return 不能使用Page类（它的实现类没有无参构造器，要使用自定义的PageFeign）
     */
    @PostMapping("/config-cell-good/page")
    @ApiOperation(value = "电池良率行表分页列表", notes = "获得电池良率行表分页列表")
    public ResponseEntity<Results<PageFeign<ConfigCellGoodDTO>>> queryByPage(@RequestBody ConfigCellGoodQuery query);
}
