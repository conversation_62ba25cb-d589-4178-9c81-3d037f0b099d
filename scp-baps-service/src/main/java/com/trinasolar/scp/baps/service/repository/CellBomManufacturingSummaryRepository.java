package com.trinasolar.scp.baps.service.repository;

import com.google.protobuf.FieldOrBuilder;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturing;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturingSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 制造BOM表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Repository
public interface CellBomManufacturingSummaryRepository extends JpaRepository<CellBomManufacturingSummary, Long>, QuerydslPredicateExecutor<CellBomManufacturingSummary> {
   @Modifying
   @Transactional(rollbackFor = Exception.class)
   @Query("delete from CellBomManufacturingSummary c where c.ieorgrade = :ieorgrade")
    public void deleteByIeOrGrade(@Param("ieorgrade") Integer ieorgrade);
    /**
     * 设置默认值
     * @param cellBomManufacturing
     */
    public static void setDefault(CellBomManufacturingSummary cellBomManufacturing) {
        if (cellBomManufacturing.getProcessCode() == null) {
            cellBomManufacturing.setProcessCode("10");
        }
        if (cellBomManufacturing.getProcessId() == null) {
            cellBomManufacturing.setProcessId(10);
        }
        if (cellBomManufacturing.getInstructionType() == null) {
            cellBomManufacturing.setInstructionType("U");
        }
        if (cellBomManufacturing.getInstructionCode() == null) {
            cellBomManufacturing.setInstructionCode("M");
        }
        if (cellBomManufacturing.getCapacityQuantity() != null) {
            cellBomManufacturing.setManufacturing(cellBomManufacturing.getCapacityQuantity() + "PH");
        }
    }
}
