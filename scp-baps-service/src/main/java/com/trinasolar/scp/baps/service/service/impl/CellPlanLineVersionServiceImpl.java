package com.trinasolar.scp.baps.service.service.impl;

import com.google.common.collect.Maps;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionDTO;
import com.trinasolar.scp.baps.domain.convert.CellPlanLineVersionDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionQueryDto;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineVersion;
import com.trinasolar.scp.baps.domain.entity.QCellPlanLineVersion;
import com.trinasolar.scp.baps.domain.query.CellPlanLineVersionQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineVersionSaveDTO;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.repository.CellPlanLineVersionRepository;
import com.trinasolar.scp.baps.service.service.CellPlanLineVersionService;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

/**
 * 投产计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Slf4j
@Service("cellPlanLineVersionService")
@RequiredArgsConstructor
public class CellPlanLineVersionServiceImpl implements CellPlanLineVersionService {
    private static final QCellPlanLineVersion qCellPlanLineVersion = QCellPlanLineVersion.cellPlanLineVersion;

    private final CellPlanLineVersionDEConvert convert;

    private final CellPlanLineVersionRepository repository;
    private final JPAQueryFactory jpaQueryFactory;
    private final CellPlanLineVersionDEConvert cellPlanLineVersionDEConvert;

    @Override
    public Page<CellPlanLineVersionDTO> queryByPage(CellPlanLineVersionQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellPlanLineVersion> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public CellPlanLineVersionDTO query(CellPlanLineVersionQueryDto query) {
        if (StringUtils.isEmpty(query.getIsOversea())) {
            String inlandVersion = getLastVersion(OverseaConstant.INLAND, query.getMonth());
            String overseaVersion = getLastVersion(OverseaConstant.OVERSEA, query.getMonth());
            CellPlanLineVersionDTO cellPlanLineVersionInland = null;
            if (StringUtils.isNotEmpty(inlandVersion)) {
                cellPlanLineVersionInland = cellPlanLineVersionDEConvert.toDto(repository.selectByVersion(OverseaConstant.INLAND, query.getMonth(), inlandVersion));
            }
            CellPlanLineVersionDTO cellPlanLineVersionOversea = null;
            if (StringUtils.isNotEmpty(overseaVersion)) {
                cellPlanLineVersionOversea = cellPlanLineVersionDEConvert.toDto(repository.selectByVersion(OverseaConstant.OVERSEA, query.getMonth(), overseaVersion));
            }
            if (cellPlanLineVersionInland != null && cellPlanLineVersionOversea != null) {
                //合并
                cellPlanLineVersionInland.setVersion(null);
                cellPlanLineVersionInland.setIsOversea(null);
                if (cellPlanLineVersionInland.getIsProcessCategory() + cellPlanLineVersionOversea.getIsProcessCategory() != 2) {
                    cellPlanLineVersionInland.setIsProcessCategory(0);
                }
                if (!Objects.equals(cellPlanLineVersionInland.getIsSiMfrs() + cellPlanLineVersionOversea.getIsSiMfrs(), 2)) {
                    cellPlanLineVersionInland.setIsSiMfrs(0);
                }
                if (!Objects.equals(cellPlanLineVersionInland.getIsWaferGrade() + cellPlanLineVersionOversea.getIsWaferGrade(), 2)) {
                    cellPlanLineVersionInland.setIsWaferGrade(0);
                }
                if (!Objects.equals(cellPlanLineVersionInland.getIsConfirmPlan() + cellPlanLineVersionOversea.getIsConfirmPlan(), 2)) {
                    cellPlanLineVersionInland.setIsConfirmPlan(0);
                }
                if (!Objects.equals(cellPlanLineVersionInland.getIsCreateInstockPlan() + cellPlanLineVersionOversea.getIsCreateInstockPlan(), 2)) {
                    cellPlanLineVersionInland.setIsCreateInstockPlan(0);
                }
                if (!Objects.equals(cellPlanLineVersionInland.getIsSendEmail() + cellPlanLineVersionOversea.getIsSendEmail(), 2)) {
                    cellPlanLineVersionInland.setIsSendEmail(0);
                }
                return cellPlanLineVersionInland;
            } else {
                //返回某个
                if (cellPlanLineVersionInland != null) {
                    return cellPlanLineVersionInland;
                } else if (cellPlanLineVersionOversea != null) {
                    return cellPlanLineVersionOversea;
                }
            }
        } else {
            String version = getLastVersion(query.getIsOversea(), query.getMonth());
            if (StringUtils.isNotEmpty(version)) {
                return convert.toDto(repository.selectByVersion(query.getIsOversea(), query.getMonth(), version));
            }
        }
        CellPlanLineVersionDTO dto = new CellPlanLineVersionDTO();
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            dto.setIsOversea(query.getIsOversea());
        }
        dto.setMonth(query.getMonth());
        dto.setIsProcessCategory(0);
        dto.setIsSiMfrs(0);
        dto.setIsWaferGrade(0);
        dto.setIsConfirmPlan(0);
        dto.setIsSendEmail(0);
        dto.setIsTransform(0);
        dto.setIsCreateInstockPlan(0);
        return dto;
    }

    @Override
    public Map<String, String> findMaxVersion(Long isOverseaId, List<String> monthList) {
        monthList = monthList.stream().distinct().collect(Collectors.toList());
        //根据月份，国内海外获取最大版本号
        JPAQuery<Tuple> jpaQuery = jpaQueryFactory.select(qCellPlanLineVersion.month, qCellPlanLineVersion.version.max())
                .from(qCellPlanLineVersion)
                .where(qCellPlanLineVersion.isOverseaId.eq(isOverseaId))
                .where(qCellPlanLineVersion.month.in(monthList))
                .groupBy(qCellPlanLineVersion.month);
        List<Tuple> fetch = jpaQuery.fetch();
        Map<String, String> maxVersionMap = Maps.newHashMap();
        for (Tuple tuple : fetch) {
            maxVersionMap.put(tuple.get(qCellPlanLineVersion.month), tuple.get(qCellPlanLineVersion.version.max()));
        }
        return maxVersionMap;
    }

    @Override
    public String findMaxVersion(Long isOverseaId, String month) {
        QCellPlanLineVersion qCellPlanLineVersion = QCellPlanLineVersion.cellPlanLineVersion;
        String version = jpaQueryFactory.select(qCellPlanLineVersion.version.max()).from(qCellPlanLineVersion).where(qCellPlanLineVersion.month.eq(month)).where(
                qCellPlanLineVersion.isOverseaId.eq(isOverseaId)
        ).fetchFirst();
        return version;
    }

    @Override
    public String findMaxVersionSendedEmail(Long isOverseaId, String month) {
        QCellPlanLineVersion qCellPlanLineVersion = QCellPlanLineVersion.cellPlanLineVersion;
        String version = jpaQueryFactory.select(qCellPlanLineVersion.version.max()).from(qCellPlanLineVersion).where(qCellPlanLineVersion.month.eq(month))
                .where(qCellPlanLineVersion.isOverseaId.eq(isOverseaId)).where(qCellPlanLineVersion.finalVersion.isNotNull()).where(qCellPlanLineVersion.isSendEmail.eq(1)).fetchFirst();
        return version;
    }


    private String getLastVersion(String isOversea, String month) {
        QCellPlanLineVersion qCellPlanLineVersion = QCellPlanLineVersion.cellPlanLineVersion;
        String version = jpaQueryFactory.select(qCellPlanLineVersion.version.max()).from(qCellPlanLineVersion).where(qCellPlanLineVersion.month.eq(month)).where(
                qCellPlanLineVersion.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellPlanLineVersionQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellPlanLineVersion.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellPlanLineVersion.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellPlanLineVersion.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellPlanLineVersion.month.eq(query.getMonth()));
        }
        if (StringUtils.isNotEmpty(query.getFinalVersion())) {
            booleanBuilder.and(qCellPlanLineVersion.finalVersion.eq(query.getFinalVersion()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qCellPlanLineVersion.version.eq(query.getVersion()));
        }
        if (query.getIsTransform() != null) {
            booleanBuilder.and(qCellPlanLineVersion.isTransform.eq(query.getIsTransform()));
        }

        if (query.getIsSiMfrs() != null) {
            booleanBuilder.and(qCellPlanLineVersion.isSiMfrs.eq(query.getIsSiMfrs()));
        }
        if (query.getIsWaferGrade() != null) {
            booleanBuilder.and(qCellPlanLineVersion.isWaferGrade.eq(query.getIsWaferGrade()));
        }
        if (query.getIsProcessCategory() != null) {
            booleanBuilder.and(qCellPlanLineVersion.isProcessCategory.eq(query.getIsProcessCategory()));
        }
        if (query.getIsConfirmPlan() != null) {
            booleanBuilder.and(qCellPlanLineVersion.isConfirmPlan.eq(query.getIsConfirmPlan()));
        }
        if (query.getIsCreateInstockPlan() != null) {
            booleanBuilder.and(qCellPlanLineVersion.isCreateInstockPlan.eq(query.getIsCreateInstockPlan()));
        }
        if (query.getIsSendEmail() != null) {
            booleanBuilder.and(qCellPlanLineVersion.isSendEmail.eq(query.getIsSendEmail()));
        }
        if (StringUtils.isNotEmpty(query.getScheduleMonth())) {
            booleanBuilder.and(qCellPlanLineVersion.scheduleMonth.eq(query.getScheduleMonth()));
        }
    }

    @Override
    public CellPlanLineVersionDTO queryById(Long id) {
        CellPlanLineVersion queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellPlanLineVersionDTO save(CellPlanLineVersionSaveDTO saveDTO) {
        CellPlanLineVersion newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellPlanLineVersion());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellPlanLineVersionQuery query, HttpServletResponse response) {
        List<CellPlanLineVersionDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "投产计划版本管理表", "投产计划版本管理表", excelPara.getSimpleHeader(), excelData);
    }


}
