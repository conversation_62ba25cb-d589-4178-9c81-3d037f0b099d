package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellFineMonth;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 电池良率月拆分表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-05 05:51:04
 */
@Repository
public interface CellFineMonthRepository extends JpaRepository<CellFineMonth, Long>, QuerydslPredicateExecutor<CellFineMonth> {
    @Modifying
    @Query(value = "delete from  CellFineMonth  cellFineMonth where cellFineMonth.year = :year")
    void deleteByYear(@Param("year") Integer year);
}
