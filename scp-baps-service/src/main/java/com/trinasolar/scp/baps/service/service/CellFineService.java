package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellFineDTO;
import com.trinasolar.scp.baps.domain.entity.CellFine;
import com.trinasolar.scp.baps.domain.query.CellFineQuery;
import com.trinasolar.scp.baps.domain.save.CellFineSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池良率表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
public interface CellFineService {
    /**
     * 分页获取电池良率表
     *
     * @param query 查询对象
     * @return 电池良率表分页对象
     */
    Page<CellFineDTO> queryByPage(CellFineQuery query);

    List<CellFine> queryList(CellFineQuery query);

    /**
     * 根据主键获取电池良率表详情
     *
     * @param id 主键
     * @return 电池良率表详情
     */
    CellFineDTO queryById(Long id);

    /**
     * 保存或更新电池良率表
     *
     * @param saveDTO 电池良率表保存对象
     * @return 电池良率表对象
     */
    CellFineDTO save(CellFineSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池良率表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(CellFineQuery query, HttpServletResponse response);

    /**
     * 读取第第三方接口电池类型和电池良率的接口后进行数据整合
     *
     * @return
     */
    void importData();

    List<CellFineDTO> list(CellFineQuery query);
}

