package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.ActualInstockPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 入库数据（ERP实际入库、入库计划）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 02:47:50
 */
@Repository
public interface ActualInstockPlanRepository extends JpaRepository<ActualInstockPlan, Long>, QuerydslPredicateExecutor<ActualInstockPlan> {
    void deleteByBatchNo(String batchNo);
}
