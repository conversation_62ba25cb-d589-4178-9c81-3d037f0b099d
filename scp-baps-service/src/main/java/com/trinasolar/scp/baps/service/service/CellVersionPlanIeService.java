package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellVersionPlanIeDTO;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanIe;
import com.trinasolar.scp.baps.domain.query.CellVersionPlanIeQuery;
import com.trinasolar.scp.baps.domain.save.CellVersionPlanIeSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 计划与ie产能对比 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
public interface CellVersionPlanIeService {
    /**
     * 分页获取计划与ie产能对比
     *
     * @param query 查询对象
     * @return 计划与ie产能对比分页对象
     */
    Page<CellVersionPlanIeDTO> queryByPage(CellVersionPlanIeQuery query);

    /**
     * 根据主键获取计划与ie产能对比详情
     *
     * @param id 主键
     * @return 计划与ie产能对比详情
     */
        CellVersionPlanIeDTO queryById(Long id);

    /**
     * 保存或更新计划与ie产能对比
     *
     * @param saveDTO 计划与ie产能对比保存对象
     * @return 计划与ie产能对比对象
     */
    CellVersionPlanIeDTO save(CellVersionPlanIeSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除计划与ie产能对比
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellVersionPlanIeQuery query, HttpServletResponse response);
    Map<String,Object> makeReport(CellVersionPlanIeQuery query);
}

