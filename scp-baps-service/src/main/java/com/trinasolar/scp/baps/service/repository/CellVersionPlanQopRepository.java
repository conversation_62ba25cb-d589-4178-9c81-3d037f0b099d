package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellVersionPlanQop;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 计划与qop
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Repository
public interface CellVersionPlanQopRepository extends JpaRepository<CellVersionPlanQop, Long>, QuerydslPredicateExecutor<CellVersionPlanQop> {
}
