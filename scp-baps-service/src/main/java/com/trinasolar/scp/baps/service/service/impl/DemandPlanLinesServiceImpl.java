package com.trinasolar.scp.baps.service.service.impl;

import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.baps.domain.dto.bdm.DemandPlanLinesApsQuery;
import com.trinasolar.scp.baps.domain.query.CellDailyBalanceQuery;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.service.feign.BatteryDemandFeign;
import com.trinasolar.scp.baps.service.service.DemandPlanLinesService;
import com.trinasolar.scp.common.api.util.Results;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
@Service("cellDemandPlanService")
@CacheConfig(cacheManager = "caffeineCacheManager")
public class DemandPlanLinesServiceImpl implements DemandPlanLinesService {
    @Autowired
    private BatteryDemandFeign feign;

    @Autowired
    @Lazy
    private DemandPlanLinesService demandPlanLinesService;
    @Override
    public List<BatteryDemandPlanLinesDTO> getCellDemandPlanByMonth(CellDailyBalanceQuery query1) {
        return demandPlanLinesService.getCellDemandPlanByMonthAndIsOversea(query1.getMonth(), query1.getIsOversea());
    }

    @Override
    @Cacheable(cacheNames = "DemandPlanLinesService_getCellDemandPlanByMonthAndIsOversea", key = "#p0+'_'+#p1", unless = "#result == null", condition = "#p0!=null")
    public List<BatteryDemandPlanLinesDTO> getCellDemandPlanByMonthAndIsOversea(String month, String isOversea) {
        DemandPlanLinesApsQuery query = new DemandPlanLinesApsQuery();
        //查询整月数据
        if (StringUtils.isNotEmpty(month)) {
            Pair<LocalDate, LocalDate> firstDayAndLastDay = DateUtil.getFirstDayAndLastDay(month);
            LocalDate startDate = firstDayAndLastDay.getLeft();
            LocalDate endDate = firstDayAndLastDay.getRight();
            query.setStartDate(startDate);
            query.setEndDate(endDate);
        }
        //国内/海外筛选
        query.setDomesticOverseaName(isOversea);

        ResponseEntity<Results<List<BatteryDemandPlanLinesDTO>>> resultsResponseEntity = feign.list(query);
        if (resultsResponseEntity != null && resultsResponseEntity.getBody() != null && resultsResponseEntity.getBody().getData() != null) {
            return resultsResponseEntity.getBody().getData();
        } else {
            return new ArrayList<BatteryDemandPlanLinesDTO>();
        }
    }
}
