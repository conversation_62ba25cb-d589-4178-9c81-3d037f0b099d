package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellInstockPlanALowDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanSummaryDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanSaveDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

/**
 * 入库计划表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:13:25
 */
public interface CellInstockPlanService {
    /**
     * 分页获取入库计划表
     *
     * @param query 查询对象
     * @return 入库计划表分页对象
     */
    Page<CellInstockPlanDTO> queryByPage(CellInstockPlanQuery query);

    /**
     * 根据主键获取入库计划表详情
     *
     * @param id 主键
     * @return 入库计划表详情
     */
        CellInstockPlanDTO queryById(Long id);

    /**
     * 保存或更新入库计划表
     *
     * @param saveDTO 入库计划表保存对象
     * @return 入库计划表对象
     */
    CellInstockPlanDTO save(CellInstockPlanSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除入库计划表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellInstockPlanQuery query, HttpServletResponse response);

    List<CellInstockPlanDTO> queryByFinalVersion(CellInstockPlanQuery query);

    List<CellInstockPlanDTO> query(CellInstockPlanQuery query);

    Pair<String, String> getLastVersion(CellInstockPlanQuery query);


    void clearCacheByVersion(String version);

    Pair<String, String> getFinalVersion(CellInstockPlanQuery query);
    Page<CellInstockPlanALowDTO> queryAByPage(CellInstockPlanQuery query);

    List<CellInstockPlanDTO> queryCacheByVersion(String version);

    List<CellInstockPlanSummaryDTO> findSummaryByMonth(CellInstockPlanQuery query);

    List<CellInstockPlanDTO> findByCondtition(Long isOverseaId, String planVersion, LocalDate startDate, LocalDate endDate);

    void getItemCodesByCellInstockPlan(IdsDTO idsDTO);
}

