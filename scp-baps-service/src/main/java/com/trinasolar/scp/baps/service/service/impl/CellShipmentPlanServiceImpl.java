package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellShipmentPlanDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellShipmentPlanDTO;
import com.trinasolar.scp.baps.domain.dto.ScheduleTaskStatusEnum;
import com.trinasolar.scp.baps.domain.dto.ScheduledTaskLinesDTO;
import com.trinasolar.scp.baps.domain.dto.aps.CellPlanShippableSaveListDTO;
import com.trinasolar.scp.baps.domain.dto.aps.CellRelationDTO;
import com.trinasolar.scp.baps.domain.entity.CellShipmentPlan;
import com.trinasolar.scp.baps.domain.entity.QCellShipmentPlan;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.enums.PlanChangeStatusEnum;
import com.trinasolar.scp.baps.domain.excel.CellShipmentPlanMainExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellShipmentPlanQuery;
import com.trinasolar.scp.baps.domain.save.CellShipmentPlanSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.feign.BmrpFeign;
import com.trinasolar.scp.baps.service.repository.CellShipmentPlanRepository;
import com.trinasolar.scp.baps.service.service.CellShipmentPlanService;
import com.trinasolar.scp.baps.service.service.log.LogService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 可发货计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-28 11:52:39
 */
@Slf4j
@Service("cellShipmentPlanService")
@RequiredArgsConstructor
public class CellShipmentPlanServiceImpl implements CellShipmentPlanService {
    private static final QCellShipmentPlan qCellShipmentPlan = QCellShipmentPlan.cellShipmentPlan;

    private final CellShipmentPlanDEConvert convert;

    private final CellShipmentPlanRepository repository;

    private final JPAQueryFactory jpaQueryFactory;

    private final ApsFeign apsFeign;

    private final static DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtil.format_DateTime);
    private final BmrpFeign bmrpFeign;
    private final LogService logService;

    /**
     * 查询月份  month月份第一天以及最后一天
     *
     * @param query 查询对象
     * @return
     */
    @Override
    public Page<CellShipmentPlanDTO> queryByPage(CellShipmentPlanQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Iterable<CellShipmentPlan> page = repository.findAll(booleanBuilder);
        List<CellShipmentPlanDTO> shipmentPlanDTOList = convert.toDto(IterableUtils.toList(page));
        //分组汇总计算 基地、车间、单元、电池类型、月份、国内/海外、版本、需求来源,证书编号，配比
        Map<String, List<CellShipmentPlanDTO>> shipmentPlanMap = shipmentPlanDTOList.stream().collect(Collectors.groupingBy(shipmentPlanDTO -> StringUtils.join(
                        shipmentPlanDTO.getIsOversea(), "/",
                        shipmentPlanDTO.getBasePlace(), "/",
                        shipmentPlanDTO.getWorkshop(), "/",
                        shipmentPlanDTO.getWorkunit(), "/",
                        shipmentPlanDTO.getCellsType(), "/",
                        shipmentPlanDTO.getIsOversea(), "/",
                        shipmentPlanDTO.getFinalVersion(), "/",
                        shipmentPlanDTO.getItemCode(), "/",
                        query.getMonth(), "/",
                        shipmentPlanDTO.getMainGridSpace(), "/",
                        shipmentPlanDTO.getCertCode(), "/",
                        shipmentPlanDTO.getRatioCode(), "/",
                        shipmentPlanDTO.getSourceType()

                )
        ));
        long total = shipmentPlanMap.size();
        shipmentPlanDTOList = dataConvert(shipmentPlanMap, query.getMonth(), query);
        //排序国内海外>生产基地>生产车间>生产单元>电池类型
        List<CellShipmentPlanDTO> shipmentPlanDTOSortList = shipmentPlanDTOList.stream()
                .sorted(Comparator.comparing(CellShipmentPlanDTO::getIsOversea)
                        .thenComparing(CellShipmentPlanDTO::getBasePlace)
                        .thenComparing(CellShipmentPlanDTO::getWorkshop)
                        .thenComparing(CellShipmentPlanDTO::getWorkunit)
                        .thenComparing(CellShipmentPlanDTO::getCellsType))
                .collect(Collectors.toList());
        //翻译
        MyThreadLocal.get().setLang(oldLang);
        shipmentPlanDTOSortList = convert.toCellShipmentPlanDTONameByCnName(shipmentPlanDTOSortList);
        PageImpl pageImpl = new PageImpl(shipmentPlanDTOSortList, pageable, total);
        return pageImpl;
    }

    //数据转换
    public List<CellShipmentPlanDTO> dataConvert(Map<String, List<CellShipmentPlanDTO>> shipmentPlanMap, String month, CellShipmentPlanQuery query) {
        List<CellShipmentPlanDTO> cellShipmentPlanDTOList = Lists.newArrayList();
        //获取shipmentPlanMap所有key后进行自然排序
        List<String> keys = shipmentPlanMap.keySet().stream().sorted().collect(Collectors.toList());
        //获取要进行分页统计的key
        List<String> keysPage = keys.subList((query.getPageNumber() - 1) * query.getPageSize(), Math.min(query.getPageNumber() * query.getPageSize(), keys.size()));
        //筛选出shipmentPlanMap的key在keysPage中的map
        Map<String, List<CellShipmentPlanDTO>> shipmentPlanMapPage = shipmentPlanMap.entrySet().stream().filter(entry -> keysPage.contains(entry.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        //统计汇总
        cellShipmentPlanDTOList = groupByCellShipmentPlan(shipmentPlanMapPage, month);
        return cellShipmentPlanDTOList;
    }

    public List<CellShipmentPlanDTO> groupByCellShipmentPlan(Map<String, List<CellShipmentPlanDTO>> shipmentPlanMap, String month) {
        List<CellShipmentPlanDTO> dtos = Lists.newArrayList();
        //汇总统计
        for (Map.Entry<String, List<CellShipmentPlanDTO>> entry : shipmentPlanMap.entrySet()) {
            //待汇总数据
            List<CellShipmentPlanDTO> cellShipmentPlanDTOList = entry.getValue();
            if (CollectionUtils.isEmpty(cellShipmentPlanDTOList)) {
                continue;
            }
            //创建汇总对象
            CellShipmentPlanDTO dto = new CellShipmentPlanDTO();
            CellShipmentPlanDTO cellShipmentPlanDTO = cellShipmentPlanDTOList.get(0);
            dto.setBasePlace(cellShipmentPlanDTO.getBasePlace());
            dto.setWorkshop(cellShipmentPlanDTO.getWorkshop());
            dto.setWorkunit(cellShipmentPlanDTO.getWorkunit());
            dto.setCellsType(cellShipmentPlanDTO.getCellsType());
            dto.setIsOversea(cellShipmentPlanDTO.getIsOversea());
            dto.setFinalVersion(cellShipmentPlanDTO.getFinalVersion());
            dto.setMonth(month);
            dto.setItemCode(cellShipmentPlanDTO.getItemCode());
            dto.setMainGridSpace(cellShipmentPlanDTO.getMainGridSpace());
            dto.setSourceType(cellShipmentPlanDTO.getSourceType());
            dto.setFrontFineGrid(cellShipmentPlanDTO.getFrontFineGrid());
            dto.setBackFineGrid(cellShipmentPlanDTO.getBackFineGrid());
            dto.setSiliconWaferThickness(cellShipmentPlanDTO.getSiliconWaferThickness());
            dto.setSiliconWaferSize(cellShipmentPlanDTO.getSiliconWaferSize());
            dto.setCertCode(cellShipmentPlanDTO.getCertCode());
            dto.setRatioCode(cellShipmentPlanDTO.getRatioCode());
            //依据日期分组
            Map<String, List<CellShipmentPlanDTO>> collect = cellShipmentPlanDTOList.stream().collect(Collectors.groupingBy(item -> {
                return DateUtil.getMonthDay(item.getOutBoundDate());
            }));
            //日期集合
            List<String> subList = Optional.ofNullable(collect.keySet().stream().collect(Collectors.toList())).orElse(Lists.newArrayList());
            subList = subList.stream().sorted().collect(Collectors.toList());
            dto.setSubList(subList);
            //统计每天的值
            Map<String, BigDecimal> subMap = Maps.newLinkedHashMap();
            for (String k : subList) {
                BigDecimal sum = collect.getOrDefault(k, Lists.newArrayList()).stream().map(item -> {
                    return Optional.ofNullable(item.getQtyPc()).orElse(BigDecimal.ZERO);
                }).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
                subMap.put(k, sum);
            }
            dto.setSubMap(subMap);
            if (Objects.nonNull(dto)) {
                dtos.add(dto);
            }
        }
        return dtos;
    }


    private void buildWhere(BooleanBuilder booleanBuilder, CellShipmentPlanQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellShipmentPlan.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getOrderCode())) {
            booleanBuilder.and(qCellShipmentPlan.orderCode.eq(query.getOrderCode()));
        }
        if (StringUtils.isNotEmpty(query.getSourceType())) {
            booleanBuilder.and(qCellShipmentPlan.sourceType.eq(query.getSourceType()));
        }
        if (StringUtils.isNotEmpty(query.getDemandVersion())) {
            booleanBuilder.and(qCellShipmentPlan.demandVersion.eq(query.getDemandVersion()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellShipmentPlan.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellShipmentPlan.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellShipmentPlan.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellShipmentPlan.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellShipmentPlan.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellShipmentPlan.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qCellShipmentPlan.workunit.eq(query.getWorkunit()));
        }
        if (query.getWorkunitId() != null) {
            booleanBuilder.and(qCellShipmentPlan.workunitId.eq(query.getWorkunitId()));
        }
        if (StringUtils.isNotEmpty(query.getLineName())) {
            booleanBuilder.and(qCellShipmentPlan.lineName.eq(query.getLineName()));
        }
        if (query.getNumberLine() != null) {
            booleanBuilder.and(qCellShipmentPlan.numberLine.eq(query.getNumberLine()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellShipmentPlan.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellShipmentPlan.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qCellShipmentPlan.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qCellShipmentPlan.aesthetics.eq(query.getAesthetics()));
        }
        if (query.getIsTransparentDoubleGlass() != null) {
            booleanBuilder.and(qCellShipmentPlan.isTransparentDoubleGlass.eq(query.getIsTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellShipmentPlan.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            booleanBuilder.and(qCellShipmentPlan.cellSource.eq(query.getCellSource()));
        }
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qCellShipmentPlan.productionGrade.eq(query.getProductionGrade()));
        }
        if (StringUtils.isNotEmpty(query.getRegionalCountry())) {
            booleanBuilder.and(qCellShipmentPlan.regionalCountry.eq(query.getRegionalCountry()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qCellShipmentPlan.itemCode.eq(query.getItemCode()));
        }
        if (StringUtils.isNotEmpty(query.getDemandBasePlace())) {
            booleanBuilder.and(qCellShipmentPlan.demandBasePlace.eq(query.getDemandBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getIsSpecialRequirement())) {
            booleanBuilder.and(qCellShipmentPlan.isSpecialRequirement.eq(query.getIsSpecialRequirement()));
        }
        if (StringUtils.isNotEmpty(query.getLowResistance())) {
            booleanBuilder.and(qCellShipmentPlan.lowResistance.eq(query.getLowResistance()));
        }
        if (StringUtils.isNotEmpty(query.getCellMfrs())) {
            booleanBuilder.and(qCellShipmentPlan.cellMfrs.eq(query.getCellMfrs()));
        }
        if (StringUtils.isNotEmpty(query.getSilverPulpMfrs())) {
            booleanBuilder.and(qCellShipmentPlan.silverPulpMfrs.eq(query.getSilverPulpMfrs()));
        }
        if (query.getDemandQty() != null) {
            booleanBuilder.and(qCellShipmentPlan.demandQty.eq(query.getDemandQty()));
        }
        if (query.getEndTime() != null) {
            booleanBuilder.and(qCellShipmentPlan.endTime.eq(query.getEndTime()));
        }
        if (query.getStartTime() != null) {
            booleanBuilder.and(qCellShipmentPlan.startTime.eq(query.getStartTime()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            //出库日期 获取月份第一天和最后一天
            log.info("传入月份的第一天:{},最后一天{}", DateUtil.getMonthFirstDay(query.getMonth()), DateUtil.getMonthLastDay(query.getMonth()));
            LocalDateTime localDateTimeFirst = LocalDateTime.parse(DateUtil.getMonthFirstDay(query.getMonth()) + " 00:00:00", df);
            LocalDateTime localDateTimeLast = LocalDateTime.parse(DateUtil.getMonthLastDay(query.getMonth()) + " 23:59:59", df);
            booleanBuilder.and(qCellShipmentPlan.outBoundDate.goe(localDateTimeFirst));
            booleanBuilder.and(qCellShipmentPlan.outBoundDate.loe(localDateTimeLast));
        }
        if (query.getCellMv() != null) {
            booleanBuilder.and(qCellShipmentPlan.cellMv.eq(query.getCellMv()));
        }
        if (StringUtils.isNotEmpty(query.getFinalVersion())) {
            booleanBuilder.and(qCellShipmentPlan.finalVersion.eq(query.getFinalVersion()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qCellShipmentPlan.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qCellShipmentPlan.remark.eq(query.getRemark()));
        }
        if (query.getOldQtyPc() != null) {
            booleanBuilder.and(qCellShipmentPlan.oldQtyPc.eq(query.getOldQtyPc()));
        }
        if (query.getQtyPc() != null) {
            booleanBuilder.and(qCellShipmentPlan.qtyPc.eq(query.getQtyPc()));
        }
        if (query.getDemandSummaryLinesId() != null) {
            booleanBuilder.and(qCellShipmentPlan.demandSummaryLinesId.eq(query.getDemandSummaryLinesId()));
        }
        if (StringUtils.isNotEmpty(query.getSiMfrs())) {
            booleanBuilder.and(qCellShipmentPlan.siMfrs.eq(query.getSiMfrs()));
        }
        if (query.getIsSiMfrs() != null) {
            booleanBuilder.and(qCellShipmentPlan.isSiMfrs.eq(query.getIsSiMfrs()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconMaterialManufacturer())) {
            booleanBuilder.and(qCellShipmentPlan.siliconMaterialManufacturer.eq(query.getSiliconMaterialManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getScreenPlateMfrs())) {
            booleanBuilder.and(qCellShipmentPlan.screenPlateMfrs.eq(query.getScreenPlateMfrs()));
        }
        if (query.getStartEfficiency() != null) {
            booleanBuilder.and(qCellShipmentPlan.startEfficiency.eq(query.getStartEfficiency()));
        }
        if (query.getMaxEfficiency() != null) {
            booleanBuilder.and(qCellShipmentPlan.maxEfficiency.eq(query.getMaxEfficiency()));
        }
        if (StringUtils.isNotEmpty(query.getSpecialOrderNo())) {
            booleanBuilder.and(qCellShipmentPlan.specialOrderNo.eq(query.getSpecialOrderNo()));
        }
        if (query.getDemandDate() != null) {
            booleanBuilder.and(qCellShipmentPlan.demandDate.eq(query.getDemandDate()));
        }
        if (query.getIsWaferGrade() != null) {
            booleanBuilder.and(qCellShipmentPlan.isWaferGrade.eq(query.getIsWaferGrade()));
        }
        if (StringUtils.isNotEmpty(query.getWaferGrade())) {
            booleanBuilder.and(qCellShipmentPlan.waferGrade.eq(query.getWaferGrade()));
        }
        if (query.getIsASplit() != null) {
            booleanBuilder.and(qCellShipmentPlan.isASplit.eq(query.getIsASplit()));
        }
        if (query.getProcessCategoryPriority() != null) {
            booleanBuilder.and(qCellShipmentPlan.processCategoryPriority.eq(query.getProcessCategoryPriority()));
        }
        if (StringUtils.isNotEmpty(query.getProcessCategory())) {
            booleanBuilder.and(qCellShipmentPlan.processCategory.eq(query.getProcessCategory()));
        }
        if (query.getIsProcessCategory() != null) {
            booleanBuilder.and(qCellShipmentPlan.isProcessCategory.eq(query.getIsProcessCategory()));
        }
        if (query.getIsHandProcessCategory() != null) {
            booleanBuilder.and(qCellShipmentPlan.isHandProcessCategory.eq(query.getIsHandProcessCategory()));
        }
        if (query.getGap() != null) {
            booleanBuilder.and(qCellShipmentPlan.gap.eq(query.getGap()));
        }
        if (StringUtils.isNotEmpty(query.getDemandRemark())) {
            booleanBuilder.and(qCellShipmentPlan.demandRemark.eq(query.getDemandRemark()));
        }
        if (StringUtils.isNotEmpty(query.getGradeRule())) {
            booleanBuilder.and(qCellShipmentPlan.gradeRule.eq(query.getGradeRule()));
        }
        if (StringUtils.isNotEmpty(query.getVerificationMark())) {
            booleanBuilder.and(qCellShipmentPlan.verificationMark.eq(query.getVerificationMark()));
        }
        if (StringUtils.isNotEmpty(query.getDemandSource())) {
            booleanBuilder.and(qCellShipmentPlan.demandSource.eq(query.getDemandSource()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryMaterialCode())) {
            booleanBuilder.and(qCellShipmentPlan.batteryMaterialCode.eq(query.getBatteryMaterialCode()));
        }
        if (query.getOldStartTime() != null) {
            booleanBuilder.and(qCellShipmentPlan.oldStartTime.eq(query.getOldStartTime()));
        }
        if (query.getOldEndTime() != null) {
            booleanBuilder.and(qCellShipmentPlan.oldEndTime.eq(query.getOldEndTime()));
        }
        if (query.getSchedulingFromId() != null) {
            booleanBuilder.and(qCellShipmentPlan.schedulingFromId.eq(query.getSchedulingFromId()));
        }
        if (query.getPlanLineFromId() != null) {
            booleanBuilder.and(qCellShipmentPlan.planLineFromId.eq(query.getPlanLineFromId()));
        }
        if (query.getBbomId() != null) {
            booleanBuilder.and(qCellShipmentPlan.bbomId.eq(query.getBbomId()));
        }
        if (query.getParentId() != null) {
            booleanBuilder.and(qCellShipmentPlan.parentId.eq(query.getParentId()));
        }
        if (query.getConfirmPlan() != null) {
            booleanBuilder.and(qCellShipmentPlan.confirmPlan.eq(query.getConfirmPlan()));
        }
        if (StringUtils.isNotEmpty(query.getOldMonth())) {
            booleanBuilder.and(qCellShipmentPlan.oldMonth.eq(query.getOldMonth()));
        }
        if (query.getOutBoundDate() != null) {
            booleanBuilder.and(qCellShipmentPlan.outBoundDate.eq(query.getOutBoundDate()));
        }
    }

    @Override
    public CellShipmentPlanDTO queryById(Long id) {
        CellShipmentPlan queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellShipmentPlanDTO save(CellShipmentPlanSaveDTO saveDTO) {
        CellShipmentPlan newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellShipmentPlan());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellShipmentPlanQuery query, HttpServletResponse response) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<CellShipmentPlanMainExcelDTO> datas = new ArrayList<>();
        List<CellShipmentPlanDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isNotEmpty(dtos)) {
            dtos.stream().filter(x -> null != x.getSubMap()).forEach(y -> {
                CellShipmentPlanMainExcelDTO shippableDTO = new CellShipmentPlanMainExcelDTO();
                shippableDTO.setIsOversea(y.getIsOversea());
                shippableDTO.setBasePlace(y.getBasePlace());
                shippableDTO.setWorkShop(y.getWorkshop());
                shippableDTO.setWorkUnit(y.getWorkunit());
                shippableDTO.setCellsType(y.getCellsType());
                shippableDTO.setMonth(y.getMonth());
                shippableDTO.setItemCode(y.getItemCode());
                shippableDTO.setMainGridSpace(y.getMainGridSpace());
                shippableDTO.setCertCode(y.getCertCode());
                shippableDTO.setRatioCode(y.getRatioCode());
                shippableDTO.setSourceType(y.getSourceType());
                y.getSubMap().entrySet().stream().forEach(subMap -> {
                    LocalDateTime localDate = LocalDate.parse(subMap.getKey(), fmt).atStartOfDay();
                    int day = localDate.getDayOfMonth();
                    ReflectUtil.invoke(shippableDTO, "getD" + day);
                    ReflectUtil.invoke(shippableDTO, "setD" + day, subMap.getValue());
                });
                shippableDTO.setBackFineGrid(y.getBackFineGrid());
                shippableDTO.setFrontFineGrid(y.getFrontFineGrid());
                shippableDTO.setSiliconWaferThickness(y.getSiliconWaferThickness());
                shippableDTO.setSiliconWaferSize(y.getSiliconWaferSize());
                datas.add(shippableDTO);
            });

        }

        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellShipmentPlanMainExcelDTO.class, datas, JSON.toJSONString(query), "可发货计划表", response);
    }

    public void saveAll(List<CellShipmentPlanDTO> shipmentPlanDTOList) {
        convert.toShipmentPlanSaveDTO(shipmentPlanDTOList).stream().forEach(this::save);
    }

    /**
     * 电池产出计划
     * 国内、国外版本分别获取当月的 之后add.all
     * 以202402月份为例
     * 数据同步入库计划 通过可靠性验证和预留天数计算转换 获取出库日期
     * 落表
     * 当前2月份 1-29天 出库日期集合且添加入库时间<2424-02-01且出库日期>2024-02-01的日期集合
     * 如果存在多个相同出库日期  list添加  然后map分组推送
     */
    public void queryList(CellShipmentPlanQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        String monthDay = query.getMonth();

        // 获取当前月份
        /**********************************获取当月数据START*************************************************/
        //左国内 右国外
        Pair<String, String> stringPair = getFinalVersion(query);
        List<CellShipmentPlanDTO> domesticCellShipmentPlan = getDomesticCellShipmentPlan(stringPair.getLeft(), "", query, "");
        List<CellShipmentPlanDTO> abroadCellShipmentPlan = getAbroadCellShipmentPlan(stringPair.getRight(), "", query, "");
        //国内数据加上国外数据
        domesticCellShipmentPlan.addAll(abroadCellShipmentPlan);
        /**********************************获取当月数据END*************************************************/
        /**********************************获取当月入库时间<xxxx-xx-01且出库日期>xxxx-xx-01  START*************************************************/
        //左国内 右国外 查找当月前一个月的最大版本数据

        query.setMonth(DateUtil.getNewMonth(query.getMonth(), -1));
        Pair<String, String> stringPairByLastMonth = getFinalVersion(query);
        List<CellShipmentPlanDTO> domesticCellShipmentPlanByFlag = getDomesticCellShipmentPlan(stringPairByLastMonth.getLeft(), "Y", query, monthDay);
        List<CellShipmentPlanDTO> abroadCellShipmentPlanByFlag = getAbroadCellShipmentPlan(stringPairByLastMonth.getRight(), "Y", query, monthDay);
        domesticCellShipmentPlan.addAll(domesticCellShipmentPlanByFlag);
        //获取出库计划和入库计划日期对应的数据
        domesticCellShipmentPlan.addAll(abroadCellShipmentPlanByFlag);
        ScheduledTaskLinesDTO task = logService.createLogTask(LovHeaderCodeConstant.TASK_LOG_01);
        if (CollectionUtils.isEmpty(domesticCellShipmentPlan)) {
            logService.addLog(task, ScheduleTaskStatusEnum.WARN, String.format("%s月未查到可发货计划数据", query.getMonth()));
            logService.saveTaskLog(task);
            return;
        }
        Pair<String, String> versions = handlerVersion(stringPair, stringPairByLastMonth);
        domesticCellShipmentPlan.stream().forEach(x -> {
            if (StringUtils.equals(x.getIsOversea(), OverseaConstant.INLAND)) {
                x.setFinalVersion(versions.getLeft());
            }
            if (StringUtils.equals(x.getIsOversea(), OverseaConstant.OVERSEA)) {
                x.setFinalVersion(versions.getRight());
            }
        });
        List<CellPlanShippableSaveListDTO> shippableSaveListDTOS = getCellPlanShippableSaveListDTOS(query, domesticCellShipmentPlan, monthDay, task);
        log.info("电池产出计划集合:{}", JSON.toJSONString(shippableSaveListDTOS));
        if (CollectionUtils.isEmpty(shippableSaveListDTOS)) {
            throw new BizException("电池产出计划推送数据没有物料信息,请检查!");
        }
        boolean check = apsFeign.saveList(shippableSaveListDTOS.stream().filter(p -> StringUtils.isNotEmpty(p.getCellType())).collect(Collectors.toList())).getBody().isSuccess();
        if (!check) {
            throw new BizException("电池产出计划推送失败,请联系管理员!");
        }
        logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, String.format("阶段5：推送电池产出计划成功,%s", LocalDateTime.now()));
        logService.saveTaskLog(task);
        MyThreadLocal.get().setLang(oldLang);
    }
    private Pair<String, String> handlerVersion(Pair<String, String> stringPair, Pair<String, String> stringPairByLastMonth) {
        String leftVersion=stringPair.getLeft();
        String rightVersion=stringPair.getRight();
        if (StringUtils.isBlank(leftVersion)){
            leftVersion=stringPairByLastMonth.getLeft();
        }
        if (StringUtils.isBlank(rightVersion)){
            rightVersion=stringPairByLastMonth.getRight();
        }
        return Pair.of(leftVersion,rightVersion);
    }
    //数据提取 汇总返回集合
    private List<CellPlanShippableSaveListDTO> getCellPlanShippableSaveListDTOS(CellShipmentPlanQuery query, List<CellShipmentPlanDTO> domesticCellShipmentPlan, String monthDay, ScheduledTaskLinesDTO task) {
        //过滤出出库日期为当月的数据
        logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：过滤出出库日期为%s的数据,%s", monthDay, LocalDateTime.now()));
        List<CellShipmentPlanDTO> pushList = domesticCellShipmentPlan.stream().filter(plan -> DateUtil.isSameMonth(plan.getOutBoundDate().toLocalDate(), monthDay)).collect(Collectors.toList());
        List<CellPlanShippableSaveListDTO> saveListDTOList = new ArrayList<>();
        logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段2：获取可发货计划中料号数据集合,%s", LocalDateTime.now()));
        List<String> materialNos = pushList.stream().filter(p -> StringUtils.isNotEmpty(p.getItemCode())).map(CellShipmentPlanDTO::getItemCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(materialNos)) {
            List<CellRelationDTO> relationDTOList = queryByMaterialNoList(materialNos);
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段3：根据料号获取(aop)电池系列与料号关系列表,%s", LocalDateTime.now()));
            Map<String, String> map = relationDTOList.stream()
                    .collect(Collectors.toMap(relationDTO -> relationDTO.getMaterialNo(), relationDTO -> relationDTO.getCellType()));
            //天数相同的数量汇总
            List<CellShipmentPlanDTO> summaryPushListByDay = pushList.stream().filter(p -> StringUtils.isNotEmpty(p.getItemCode())).collect(Collectors.toList());
            summaryPushListByDay.forEach(plan -> {
                CellPlanShippableSaveListDTO saveListDTO = new CellPlanShippableSaveListDTO();
                LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, plan.getIsOversea());
                saveListDTO.setIsOversea(null != lovLineDTO ? lovLineDTO.getLovValue() : "");
                //saveListDTO.setIsOversea(plan.getIsOversea());
                saveListDTO.setBasePlace(plan.getBasePlace());
                saveListDTO.setWorkshop(plan.getWorkshop());
                saveListDTO.setCellType(map.get(plan.getItemCode()));
                saveListDTO.setCellNo(plan.getItemCode());
                saveListDTO.setRule(plan.getGradeRule());
                saveListDTO.setMonth(monthDay);
                saveListDTO.setQuantity(plan.getQtyPc());
                saveListDTO.setLimitQuantity(plan.getMaxQtyPc());
                saveListDTO.setDay(plan.getOutBoundDate().getDayOfMonth());
                saveListDTO.setFinalVersion(plan.getFinalVersion());
                saveListDTOList.add(saveListDTO);
            });
        } else {
            logService.addLog(task, ScheduleTaskStatusEnum.WARN, String.format("可发货计划中不存在料号数据,缺少数据,%s", LocalDateTime.now()));
            logService.saveTaskLog(task);
        }
        Map<String, List<CellPlanShippableSaveListDTO>> listMap = saveListDTOList.stream().collect(Collectors.groupingBy(planLine ->
                String.join("/", planLine.getIsOversea(),
                        planLine.getBasePlace(),
                        planLine.getWorkshop(),
                        planLine.getCellType(),
                        planLine.getCellNo(),
                        planLine.getRule(),
                        planLine.getMonth(),
                        String.valueOf(planLine.getDay()),
                        planLine.getFinalVersion())
                ));
        List<CellPlanShippableSaveListDTO> shippableSaveListDTOS = new ArrayList<>();
        listMap.keySet().stream().forEach(key -> {
            String[] keySplit = key.split("/");
            CellPlanShippableSaveListDTO saveListDTO = new CellPlanShippableSaveListDTO();
            saveListDTO.setIsOversea(keySplit[0]);
            saveListDTO.setBasePlace(keySplit[1]);
            saveListDTO.setWorkshop(keySplit[2]);
            saveListDTO.setCellType(keySplit[3]);
            saveListDTO.setCellNo(keySplit[4]);
            saveListDTO.setRule(keySplit[5]);
            saveListDTO.setMonth(keySplit[6]);
            saveListDTO.setDay(Integer.valueOf(keySplit[7]));
            saveListDTO.setFinalVersion(keySplit[8]);
            saveListDTO.setQuantity(listMap.get(key).stream().map(planDto -> Optional.ofNullable(planDto.getQuantity()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add));
            saveListDTO.setLimitQuantity(listMap.get(key).stream().map(planDto -> Optional.ofNullable(planDto.getLimitQuantity()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add));
            shippableSaveListDTOS.add(saveListDTO);
        });
        logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段4：组装数据集合根据天数汇总数量,%s", LocalDateTime.now()));

        return shippableSaveListDTOS;
    }

    //(aop)电池系列与料号关系列表
    @Override
    public List<CellRelationDTO> queryByMaterialNoList(List<String> materialNoList) {
        List<CellRelationDTO> relationDTOList = apsFeign.queryByMaterialNoList(materialNoList).getBody().getData();
        return relationDTOList;
    }

    //获取国内数据 flag标识不为空 获取当月入库时间<xxxx-xx-01且出库日期>xxxx-xx-01
    public List<CellShipmentPlanDTO> getDomesticCellShipmentPlan(String finalVersion, String flag, CellShipmentPlanQuery query, String monthDay) {
        if (StringUtils.isNotEmpty(finalVersion)) {
            JPAQuery<CellShipmentPlan> where = jpaQueryFactory.select(qCellShipmentPlan).from(qCellShipmentPlan)
                    .where(qCellShipmentPlan.finalVersion.eq(finalVersion)
                            .and(qCellShipmentPlan.isOversea.eq("国内")));
            if (StringUtils.isNotEmpty(query.getMonth())) {
                where.where(qCellShipmentPlan.month.eq(query.getMonth()));
            }
            if (StringUtils.isNotEmpty(flag)) {
                LocalDateTime localDateTimeFirst = LocalDateTime.parse(DateUtil.getMonthFirstDay(monthDay) + " 00:00:00", df);
                //LocalDateTime localDateTimeLast = LocalDateTime.parse(DateUtil.getMonthFirstDay(query.getMonth())+" 23:59:59", df);
                //where.where(qCellShipmentPlan.startTime.loe(localDateTimeLast));
                where.where(qCellShipmentPlan.outBoundDate.goe(localDateTimeFirst));
            }
            return convert.toDto(where.fetch());
        }
        return new ArrayList<>();
    }

    //获取国外数据 flag标识不为空 获取当月入库时间<xxxx-xx-01且出库日期>xxxx-xx-01
    public List<CellShipmentPlanDTO> getAbroadCellShipmentPlan(String finalVersion, String flag, CellShipmentPlanQuery query, String monthDay) {
        if (StringUtils.isNotEmpty(finalVersion)) {

            JPAQuery<CellShipmentPlan> where = jpaQueryFactory.select(qCellShipmentPlan).from(qCellShipmentPlan)
                    .where(qCellShipmentPlan.finalVersion.eq(finalVersion)
                            .and(qCellShipmentPlan.isOversea.eq("海外")));
            if (StringUtils.isNotEmpty(query.getMonth())) {
                where.where(qCellShipmentPlan.month.eq(query.getMonth()));
            }
            if (StringUtils.isNotEmpty(flag)) {
                LocalDateTime localDateTimeFirst = LocalDateTime.parse(DateUtil.getMonthFirstDay(monthDay) + " 00:00:00", df);
                //LocalDateTime localDateTimeLast = LocalDateTime.parse(DateUtil.getMonthFirstDay(query.getMonth())+" 23:59:59", df);
                //where.where(qCellShipmentPlan.startTime.loe(localDateTimeLast));
                where.where(qCellShipmentPlan.outBoundDate.goe(localDateTimeFirst));

            }
            return convert.toDto(where.fetch());
        }
        return new ArrayList<>();
    }

    /**
     * 获取版本号
     *
     * @param query
     * @return
     */
    public Pair<String, String> getFinalVersion(CellShipmentPlanQuery query) {

        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        String isOversea = OverseaConstant.INLAND;
        String InVersion = getFinalVersion(month, isOversea);
        isOversea = OverseaConstant.OVERSEA;
        String outVersion = getFinalVersion(month, isOversea);
        return new ImmutablePair<>(InVersion, outVersion);
    }

    private String getFinalVersion(String month, String isOversea) {

        String version = jpaQueryFactory.select(qCellShipmentPlan.finalVersion.max()).from(qCellShipmentPlan).where(
                qCellShipmentPlan.month.eq(month)
        ).where(
                qCellShipmentPlan.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    @Override
    public void deleteByMonth(String month) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        //String month= DateUtil.getMonth(LocalDate.now());
        /*log.info("传入月份的第一天:{},最后一天{}",DateUtil.getMonthFirstDay(month),DateUtil.getMonthLastDay(month));
        LocalDateTime localDateTimeFirst = LocalDateTime.parse(DateUtil.getMonthFirstDay(month)+" 00:00:00", df);
        LocalDateTime localDateTimeLast = LocalDateTime.parse(DateUtil.getMonthLastDay(month)+" 23:59:59", df);*/
        booleanBuilder.and(qCellShipmentPlan.month.eq(month));
        /*booleanBuilder.and(qCellShipmentPlan.outBoundDate.goe(localDateTimeFirst));
        booleanBuilder.and(qCellShipmentPlan.outBoundDate.loe(localDateTimeLast));*/
        Iterable<CellShipmentPlan> cellShipmentPlans = repository.findAll(booleanBuilder);
        repository.deleteAll(cellShipmentPlans);
    }
}
