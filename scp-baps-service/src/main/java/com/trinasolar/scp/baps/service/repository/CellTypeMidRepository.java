package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellTypeMid;
import org.hibernate.annotations.SQLDelete;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 电池类型转换表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Repository
public interface CellTypeMidRepository extends JpaRepository<CellTypeMid, Long>, QuerydslPredicateExecutor<CellTypeMid> {
}
