package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.dto.bbom.LowEfficiencyCellPercentDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.SiliconSliceSupplyLinesQuery;
import com.trinasolar.scp.baps.domain.query.bbom.LowEfficiencyCellPercentQuery;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 硅片拆分
 */
public interface SiliconSplitService {
    /**
     * 列出投产计划的统计结果
     */
    public Page<CellPlanLineSiliconTotalDTO> query(CellPlanLineQuery query);

    List<CellInstockPlanDTO> query(CellInstockPlanQuery query, Pair<String, String> versions);
    /**
     * 列出入库计划的统计结果
     */
    public Page<CellInstockPlanSiliconTotalDTO> query(CellInstockPlanQuery query);


    /**
     * 硅片拆分
     * @param query
     * @param type 硅片拆分类型
     */
    @Transactional(rollbackFor = Exception.class)
    void siliconSplit(CellPlanLineQuery query, String type,boolean isCover);

    void handSetWaferGrade(HandSetWaferGradeListDto listDto);

    void handSetSiMfrs(HandSetSiMfrsListDto listDto);

    void siliconASplit(CellInstockPlanQuery query);
    @Transactional(rollbackFor = Exception.class)
    void siliconTransparentDoubleGlassSplit(TransparentDoubleGlassSplitDto dto);

    List<ConfigCellAMinusPercentDTO> siliconASplitRules(CellAMinusDto dto);

    List<CellPlanLineDTO> processCategorySplit(ProcessCategorySplitDto dto, String... isHand);


    Page<CellPlanLineProcessCategoryTotalDTO> processCategoryQuery(CellPlanLineQuery query);

    List<CellPlanLineDTO> handSetProcessCategory(HandSetProcessCategoryListDto listDto);

    List<SiliconSliceSupplyLinesDTO> supplyLinesList(SiliconSliceSupplyLinesQuery query);

    List<CellInstockPlanDTO> cellGradeRuleMate(CellGradeRuleMateDto dto, String cellsType);

    Page<CellGradeRuleMateTotalDTO> gradeRuleTotalQuery(CellInstockPlanQuery query);

    void siliconAestheticsSplit(AestheticsSplitDto dto);

    void siliconLowEfficiencySplit(CellInstockPlanQuery query);

    List<LowEfficiencyCellPercentDTO> siliconLESplitRules(LowEfficiencyCellPercentQuery query);

    void itemCodeMatching(CellInstockPlanQuery query);

    void matchItemCallBack(CellInstockPlanQuery query);

    Pair<String, String> getLastVersion(CellInstockPlanQuery query);

    List<CellInstockPlanDTO> queryByVersion(Pair<String, String> versions);

    List<CellInstockPlanDTO> queryByVersion(String version);

    List<CellInstockPlanSplitDTO> cellInstockPlanSplit(CellInstockPlanQuery query);

    void aSplitSubmit(CellInstockPlanSplitSubmitDTO splitSubmitDTO);

    void lowEfficiencySplitSubmit(CellInstockPlanSplitSubmitDTO splitSubmitDTO);

    void transparentDoubleGlassSplitSubmit(CellInstockPlanSplitSubmitDTO splitSubmitDTO);

    void aestheticsSplitSubmit(CellInstockPlanSplitSubmitDTO splitSubmitDTO);

    List<CellPlanLineDTO> processCategorySplitByCellPlanLine(ProcessCategorySplitSubmitDTO query);
}
