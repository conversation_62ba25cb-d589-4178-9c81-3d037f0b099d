package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellPlanLineVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 投产计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Repository
public interface CellPlanLineVersionRepository extends JpaRepository<CellPlanLineVersion, Long>, QuerydslPredicateExecutor<CellPlanLineVersion> {
    @Query("select  c from  CellPlanLineVersion c where c.isOversea = ?1 and  c.month = ?2 and c.version = ?3 and c.isDeleted = 0")
     CellPlanLineVersion selectByVersion( String isOversea,  String month, String version);
}
