package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.Cell5AItemCodeListDto;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineDTO;
import com.trinasolar.scp.baps.domain.dto.CellProductionPlanDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanQuery;
import com.trinasolar.scp.baps.domain.save.CellProductionPlanSaveDTO;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 投产计划 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
public interface CellProductionPlanService {
    /**
     * 分页获取投产计划
     *
     * @param query 查询对象
     * @return 投产计划分页对象
     */
    Page<CellProductionPlanDTO> queryByPage(CellProductionPlanQuery query);

    /**
     * 根据主键获取投产计划详情
     *
     * @param id 主键
     * @return 投产计划详情
     */
    CellProductionPlanDTO queryById(Long id);

    /**
     * 保存或更新投产计划
     *
     * @param saveDTO 投产计划保存对象
     * @return 投产计划对象
     */
    CellProductionPlanDTO save(CellProductionPlanSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除投产计划
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    List<CellProductionPlanDTO> query(CellProductionPlanQuery query, Pair<String, String> versions);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(CellProductionPlanQuery query, HttpServletResponse response);

    List<CellProductionPlanDTO> listForMatchItem(CellProductionPlanQuery query);

    /**
     * 工单开立分页列表
     *
     * @param query 查询对象
     * @return 投产计划分页对象
     */
    Page<CellProductionPlanDTO> queryTicketOpening(CellProductionPlanQuery query);


    /**
     * 投产数据分组汇总返回
     * @return
     */
    public void summaryGroupByCellProductionPlan(List<CellProductionPlanDTO> cellProductionPlanDTOS);

    /**
     * 投产数据分组汇总返回
     * @return
     */
    public void summaryGroupByCellProductionPlan(CellProductionPlanQuery query);

    void changeDataBy5AMatch(Cell5AItemCodeListDto dto) throws InterruptedException;
}

