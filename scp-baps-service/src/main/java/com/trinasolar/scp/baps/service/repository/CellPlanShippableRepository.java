package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellPlanShippable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 可发货计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-09 10:05:37
 */
@Repository
public interface CellPlanShippableRepository extends JpaRepository<CellPlanShippable, Long>, QuerydslPredicateExecutor<CellPlanShippable> {
}
