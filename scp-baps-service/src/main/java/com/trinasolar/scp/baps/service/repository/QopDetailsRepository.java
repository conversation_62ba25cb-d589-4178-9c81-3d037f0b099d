package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.QopDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * SOP数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-30 03:38:41
 */
@Repository
public interface QopDetailsRepository extends JpaRepository<QopDetails, Long>, QuerydslPredicateExecutor<QopDetails> {
}
