package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellBaseCapacityMonthDiscounts;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * IE产能打折月度（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-28 03:34:54
 */
@Repository
public interface CellBaseCapacityMonthDiscountsRepository extends JpaRepository<CellBaseCapacityMonthDiscounts, Long>, QuerydslPredicateExecutor<CellBaseCapacityMonthDiscounts> {
}
