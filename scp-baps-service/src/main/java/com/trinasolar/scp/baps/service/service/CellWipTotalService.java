package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellWipTotalDTO;
import com.trinasolar.scp.baps.domain.query.CellWipTotalQuery;
import com.trinasolar.scp.baps.domain.save.CellWipTotalSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 开立工单明细表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
public interface CellWipTotalService {
    /**
     * 分页获取开立工单明细表
     *
     * @param query 查询对象
     * @return 开立工单明细表分页对象
     */
    Page<CellWipTotalDTO> queryByPage(CellWipTotalQuery query);

    /**
     * 根据主键获取开立工单明细表详情
     *
     * @param id 主键
     * @return 开立工单明细表详情
     */
        CellWipTotalDTO queryById(Long id);

    /**
     * 保存或更新开立工单明细表
     *
     * @param saveDTO 开立工单明细表保存对象
     * @return 开立工单明细表对象
     */
    CellWipTotalDTO save(CellWipTotalSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除开立工单明细表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellWipTotalQuery query, HttpServletResponse response);
}

