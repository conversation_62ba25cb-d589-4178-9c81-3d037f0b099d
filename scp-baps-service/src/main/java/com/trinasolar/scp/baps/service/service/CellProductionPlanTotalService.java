package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellProductionPlanTotalDTO;
import com.trinasolar.scp.baps.domain.entity.CellProductionPlanTotal;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanTotalQuery;
import com.trinasolar.scp.baps.domain.save.CellProductionPlanTotalSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 投产计划汇总表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
public interface CellProductionPlanTotalService {
    /**
     * 分页获取投产计划汇总表
     *
     * @param query 查询对象
     * @return 投产计划汇总表分页对象
     */
    Page<CellProductionPlanTotalDTO> queryByPage(CellProductionPlanTotalQuery query);

    /**
     * 获取最新版本的数据
     * @param query
     * @return
     */
    List<CellProductionPlanTotalDTO> queryByFirst(CellProductionPlanTotalQuery query);

    /**
     * 获取投产计划最新版本
     * @param query
     * @return
     */
    String queryMaxVersion(CellProductionPlanTotalQuery query);
    /**
     * 获取投产计划最新3个版本
     * @param query
     * @return
     */
    List<String> queryThreeMaxVersion(CellProductionPlanTotalQuery query);
    List<CellProductionPlanTotal> queryByVersion(String version);
    /**
     * 根据主键获取投产计划汇总表详情
     *
     * @param id 主键
     * @return 投产计划汇总表详情
     */
        CellProductionPlanTotalDTO queryById(Long id);

    /**
     * 保存或更新投产计划汇总表
     *
     * @param saveDTO 投产计划汇总表保存对象
     * @return 投产计划汇总表对象
     */
    CellProductionPlanTotalDTO save(CellProductionPlanTotalSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除投产计划汇总表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellProductionPlanTotalQuery query, HttpServletResponse response);

    void email(CellProductionPlanTotalQuery query);

}

