package com.trinasolar.scp.baps.service.service.impl;
import com.google.common.collect.Lists;
import com.trinasolar.scp.baps.domain.dto.aps.CellBooksDTO;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanFinish;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanNoFinish;
import com.trinasolar.scp.baps.domain.entity.CellLocatorWorkshopRelation;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanFinishRepository;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanNoFinishRepository;
import com.trinasolar.scp.baps.service.service.CellInstockPlanNoFinishService;
import com.trinasolar.scp.baps.service.service.CellLocatorWorkshopRelationService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.BeanUtils;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("cellInstockPlanNoFinishService")
@RequiredArgsConstructor
public class CellInstockPlanNoFinishServiceImpl implements CellInstockPlanNoFinishService {
    private final CellInstockPlanNoFinishRepository cellInstockPlanNoFinishRepository;
    private final CellInstockPlanFinishRepository cellInstockPlanFinishRepository;
    private final CellLocatorWorkshopRelationService cellLocatorWorkshopRelationService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void makeInstockPlanFinish() {
        List<CellLocatorWorkshopRelation> relationList = cellLocatorWorkshopRelationService.getAll();
        if (CollectionUtils.isEmpty(relationList)) {
            throw new BizException("baps_cell_locator_workshop_relation_not_exist");
        }
        Map<String, String> locatorWorkshopMap = relationList.stream().collect(Collectors.toMap(CellLocatorWorkshopRelation::getLocator, CellLocatorWorkshopRelation::getWorkshop, (s, s2) -> s2));
        List<CellInstockPlanNoFinish> noFinishList = Optional.ofNullable( cellInstockPlanNoFinishRepository.findAll()).orElse(Lists.newArrayList());
        Map<String, ErpWorkShop> workShopMap = prepareWorkhops();

        if (CollectionUtils.isNotEmpty(noFinishList)){
            List<CellInstockPlanFinish> finishList=Lists.newArrayList();
            noFinishList.forEach(noFinish -> {
                CellInstockPlanFinish finish=new CellInstockPlanFinish();
                  BeanUtils.copyProperties(noFinish,finish,"id");
                  finish.setId(null);
                //货位与车间转化
                String workshop = locatorWorkshopMap.get(noFinish.getLocatorCode());
                if (StringUtils.isNotBlank(workshop)){
                    ErpWorkShop erpWorkShop = workShopMap.get(workshop);
                    finish.setWorkshop(erpWorkShop.getWorkshopName());
                    finish.setWorkshopId(erpWorkShop.getWorkshopId());
                    finish.setBasePlace(erpWorkShop.getBaeplaceName());
                    finish.setBasePlaceId(erpWorkShop.getBaseplaceId());
                    finish.setIsOversea(erpWorkShop.getOverseaName());
                    finish.setIsOverseaId(erpWorkShop.getOverseaId());
                    finishList.add(finish);
                }
            });
            if (CollectionUtils.isNotEmpty(finishList)){
                cellInstockPlanFinishRepository.saveAll(finishList);
            }

        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void makeInstockPlanFinish(List<CellBooksDTO> cellBooksDTOS) {
        //1、获取货位与车间关系
        List<CellLocatorWorkshopRelation> relationList = prepareRelationList(cellBooksDTOS);
        if (CollectionUtils.isEmpty(relationList)) {
            throw new BizException("baps_cell_locator_workshop_relation_not_exist");
        }
        //货位->车间关系
        Map<String, String> locatorWorkshopMap = relationList.stream().collect(Collectors.toMap(CellLocatorWorkshopRelation::getLocator, CellLocatorWorkshopRelation::getWorkshop, (s, s2) -> s2));
        //2、获取待转化数据
        List<CellInstockPlanNoFinish> noFinishList = Optional.ofNullable( cellInstockPlanNoFinishRepository.findAll()).orElse(Lists.newArrayList());
       //准备车间-》基地-国内海外关系
        Map<String, ErpWorkShop> workShopMap = prepareWorkhops();

        if (CollectionUtils.isNotEmpty(noFinishList)){
            List<CellInstockPlanFinish> finishList=Lists.newArrayList();
            List<CellInstockPlanNoFinish> noFinishDelList=Lists.newArrayList();
            //3、待转化数据转到实际库存
            noFinishList.forEach(noFinish -> {
                CellInstockPlanFinish finish=new CellInstockPlanFinish();
                BeanUtils.copyProperties(noFinish,finish,"id");
                finish.setId(null);
                //货位与车间转化
                String workshop = locatorWorkshopMap.get(noFinish.getLocatorCode());
                if (StringUtils.isNotBlank(workshop)){
                    ErpWorkShop erpWorkShop = workShopMap.get(workshop);
                    finish.setWorkshop(erpWorkShop.getWorkshopName());
                    finish.setWorkshopId(erpWorkShop.getWorkshopId());
                    finish.setBasePlace(erpWorkShop.getBaeplaceName());
                    finish.setBasePlaceId(erpWorkShop.getBaseplaceId());
                    finish.setIsOversea(erpWorkShop.getOverseaName());
                    finish.setIsOverseaId(erpWorkShop.getOverseaId());
                    finishList.add(finish);
                    noFinishDelList.add(noFinish);
                }
            });
            if (CollectionUtils.isNotEmpty(finishList)){
                //4、数据存储
                cellInstockPlanFinishRepository.saveAll(finishList);
            }
            if (CollectionUtils.isNotEmpty(noFinishDelList)){
                //5、删除已经转移的数据
                cellInstockPlanNoFinishRepository.deleteAll(noFinishDelList);
            }


        }
    }

    private  List<CellLocatorWorkshopRelation> prepareRelationList(List<CellBooksDTO> cellBooksDTOS) {
        if (CollectionUtils.isNotEmpty(cellBooksDTOS)) {
            cellBooksDTOS = cellBooksDTOS.stream().filter(dto ->
                    StringUtils.equalsAny(dto.getBooksType(), LovHeaderCodeConstant.BOOKS_TYPE_CELL,LovHeaderCodeConstant.BOOKS_TYPE_CELL_COMPONENT) && StringUtils.isNotEmpty(dto.getSubCellSlotting() )).collect(Collectors.toList());
        }else {
            cellBooksDTOS=Lists.newArrayList();
        }
        //构建货位到车间的关系
        List<CellLocatorWorkshopRelation> relationList = Lists.newArrayList();
        cellBooksDTOS.stream().forEach(dto -> {
            String subCellSlotting = dto.getSubCellSlotting();
            subCellSlotting= subCellSlotting.replace("，", ",");
            List<String> subCellSlottingList = Arrays.asList(subCellSlotting.split(","));
            subCellSlottingList.stream().forEach(subCellSlottingItem -> {
                CellLocatorWorkshopRelation relation = new CellLocatorWorkshopRelation();
                relation.setLocator(subCellSlottingItem.trim());
                relation.setWorkshop(dto.getWorkshop());
                relationList.add(relation);
            });
        });
        return relationList;
    }

    private Map<String, ErpWorkShop> prepareWorkhops() {
        Map<String, LovLineDTO> allOverseas = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.DOMESTIC_OVERSEA);
        Map<String, LovLineDTO> allOverseasById = allOverseas.values().stream().collect(Collectors.toMap(item -> {
            return item.getLovLineId() + "";
        }, Function.identity(), (v1, v2) -> v1));
        Map<String, LovLineDTO> allBaseplaces = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BASE_PLACE);
        Map<String, LovLineDTO> allBaseplacesById = allBaseplaces.values().stream().collect(Collectors.toMap(item -> {
            return item.getLovLineId() + "";
        }, Function.identity(), (v1, v2) -> v1));
        Map<String, LovLineDTO> allWorkshops = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP);
        Map<String, LovLineDTO> allWorkshopsByValue = allWorkshops.values().stream().collect(Collectors.toMap(item -> {
            return item.getLovValue();
        }, Function.identity(), (v1, v2) -> v1));
        Map<String, ErpWorkShop> map = new HashMap<>();
        allWorkshopsByValue.forEach((k, v) -> {
            ErpWorkShop erpWorkShop = new ErpWorkShop();
            erpWorkShop.setWorkshopId(v.getLovLineId());
            erpWorkShop.setWorkshopName(v.getLovName());
            String basePlaceId = v.getAttribute1();
            LovLineDTO basePlaceLov = Optional.ofNullable(allBaseplacesById.get(basePlaceId)).orElse(new LovLineDTO());
            erpWorkShop.setBaseplaceId(basePlaceLov.getLovLineId());
            erpWorkShop.setBaeplaceName(basePlaceLov.getLovName());
            String isOvereaId = basePlaceLov.getAttribute2();
            LovLineDTO overseaLov = Optional.ofNullable(allOverseasById.get(isOvereaId)).orElse(new LovLineDTO());
            erpWorkShop.setOverseaId(overseaLov.getLovLineId());
            erpWorkShop.setOverseaName(overseaLov.getLovName());
            map.put(k, erpWorkShop);


        });
        return map;
    }
}
