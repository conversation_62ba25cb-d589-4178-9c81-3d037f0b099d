package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellShippableLeadTime;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 可发货计划提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-08 06:14:56
 */
@Repository
public interface CellShippableLeadTimeRepository extends JpaRepository<CellShippableLeadTime, Long>, QuerydslPredicateExecutor<CellShippableLeadTime> {
}
