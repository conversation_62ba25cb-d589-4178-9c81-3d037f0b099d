package com.trinasolar.scp.baps.service.service.impl;
import cn.hutool.core.util.ReflectUtil;
import com.google.common.collect.Lists;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.dto.CellConversionFactorDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellVersionPlanHistoryDTO;
import com.trinasolar.scp.baps.domain.convert.CellVersionPlanHistoryDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellVersionPlanHistory;
import com.trinasolar.scp.baps.domain.entity.QCellVersionPlanHistory;
import com.trinasolar.scp.baps.domain.excel.CellVersionPlanHistoryExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellVersionPlanHistoryQuery;
import com.trinasolar.scp.baps.domain.save.CellVersionPlanHistorySaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.baps.service.repository.CellVersionPlanHistoryRepository;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计划与上一版本计划对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Slf4j
@Service("cellVersionPlanHistoryService")
@RequiredArgsConstructor
public class CellVersionPlanHistoryServiceImpl implements CellVersionPlanHistoryService {
    private static final QCellVersionPlanHistory qCellVersionPlanHistory = QCellVersionPlanHistory.cellVersionPlanHistory;
    private final CellVersionPlanHistoryDEConvert convert;
    private final CellVersionPlanHistoryRepository repository;
    private final CellConversionFactorService cellConversionFactorService;
    private final CellInstockPlanTotalService instockPlanTotalService;
    private final JPAQueryFactory jpaQueryFactory;
    @Override
    public Page<CellVersionPlanHistoryDTO> queryByPage(CellVersionPlanHistoryQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellVersionPlanHistory> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }
    private void buildWhere(BooleanBuilder booleanBuilder, CellVersionPlanHistoryQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellVersionPlanHistory.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellVersionPlanHistory.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOverseaName())) {
            booleanBuilder.and(qCellVersionPlanHistory.isOverseaName.eq(query.getIsOverseaName()));
        }
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qCellVersionPlanHistory.cellTypeId.eq(query.getCellTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellTypeName())) {
            booleanBuilder.and(qCellVersionPlanHistory.cellTypeName.eq(query.getCellTypeName()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellVersionPlanHistory.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshopName())) {
            booleanBuilder.and(qCellVersionPlanHistory.workshopName.eq(query.getWorkshopName()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellVersionPlanHistory.month.eq(query.getMonth()));
        }
        if (query.getMonthV0() != null) {
            booleanBuilder.and(qCellVersionPlanHistory.monthV0.eq(query.getMonthV0()));
        }
        if (query.getMonthV1() != null) {
            booleanBuilder.and(qCellVersionPlanHistory.monthV1.eq(query.getMonthV1()));
        }
        if (query.getMonthV2() != null) {
            booleanBuilder.and(qCellVersionPlanHistory.monthV2.eq(query.getMonthV2()));
        }
        if (query.getGap() != null) {
            booleanBuilder.and(qCellVersionPlanHistory.gap.eq(query.getGap()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qCellVersionPlanHistory.remark.eq(query.getRemark()));
        }
    }

    @Override
    public CellVersionPlanHistoryDTO queryById(Long id) {
        CellVersionPlanHistory queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellVersionPlanHistoryDTO save(CellVersionPlanHistorySaveDTO saveDTO) {
        CellVersionPlanHistory newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellVersionPlanHistory());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellVersionPlanHistoryQuery query, HttpServletResponse response) {
        List<CellVersionPlanHistoryDTO> cellVersionPlanHistoryCollect=new ArrayList<>();
        Map<String, Object> mapData = makeReport(query);

        Object detailsObj=  mapData.get("details");
        if (detailsObj!=null){
            cellVersionPlanHistoryCollect.addAll((List<CellVersionPlanHistoryDTO>)detailsObj);
        }
        Object versionsObj=   mapData.get("versions");
        String[] versions=new String[]{"","",""};
        if (versionsObj!=null){
            versions= (String[]) versionsObj;
        }

        List<CellVersionPlanHistoryExcelDTO> datas = convert.toExcelDTO(cellVersionPlanHistoryCollect);
        ExcelPara excelPara=new ExcelPara();
        excelPara.addColumn("isOverseaName","国内/海外",0, 500.0);
        excelPara.addColumn("workshopName","生产车间",1,200.0);
        excelPara.addColumn("cellTypeName","电池类型",2,200.0);
        excelPara.addColumn("month","月份",3,100.0);
        excelPara.addColumn("HTrace","H追溯",4,100.0);
        excelPara.addColumn("cellSource","片源种类",5,100.0);
        excelPara.addColumn("aesthetics","美学",6,100.0);
        excelPara.addColumn("transparentDoubleGlass","透明双玻",7,100.0);
        excelPara.addColumn("regionalCountry","小区域国家",8,100.0);
        excelPara.addColumn("monthV0",StringUtils.isNotEmpty(versions[0])?query.getMonth()+"月份计划(上上版)\n"+versions[0]:query.getMonth()+"月份计划(上上版)",9,200.0);
        excelPara.addColumn("monthV1",StringUtils.isNotEmpty(versions[1])?query.getMonth()+"月份计划(上版)\n"+versions[1]:query.getMonth()+"月份计划(上版)",10,200.0);
        excelPara.addColumn("monthV2",StringUtils.isNotEmpty(versions[2])?query.getMonth()+"月份计划(最新版)\n"+versions[2]:query.getMonth()+"月份计划(最新版)",11,200.0);
        excelPara.addColumn("gap","差异",12,200.0);
        List<List<Object>> excelData = ExcelUtils.getList(datas, excelPara);
        String fileName="版本对比计划与上一版";
        String sheetName = fileName;
        fileName = fileName + "_" + DateUtils.formatDate(new Date(), "yyyy-MM-dd+HH:mm:ss");
        ExcelUtils.exportEx(response, fileName, sheetName, excelPara.getSimpleHeader(), excelData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> makeReport(CellVersionPlanHistoryQuery query) {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        LovLineDTO overseaLov = null;
        if (query.getIsOverseaId() == null) {
            //默认读取国内数据
            overseaLov = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, OverseaConstant.INLAND);

        } else {
            overseaLov = LovUtils.get(query.getIsOverseaId());
        }
        query.setIsOverseaId(overseaLov.getLovLineId());
        query.setIsOverseaName(overseaLov.getLovName());
        CellInstockPlanTotalQuery cellInstockPlanTotalQuery = convert.toCellInstockPlanTotalQuery(query);
        cellInstockPlanTotalQuery.setCellsTypeId(query.getCellTypeId());
        //一、获取入库计划汇总表最新的3个版本号
        List<String> versions = instockPlanTotalService.queryThreeMaxVersion(cellInstockPlanTotalQuery);
        String[] versions_array = new String[]{"", "", ""};
        final int[] index = {2};
        versions.stream().forEach(version -> {
            versions_array[index[0]] = version;
            index[0]--;
        });
        //二、获取每个版本号对应的计划汇总信息
        List<CellVersionPlanHistoryDTO> histories = new ArrayList<>();
        AtomicReference<Integer> version_order = new AtomicReference<>(2);
        versions.stream().forEach(version -> {
            //依据version版本获取数据
            List<CellInstockPlanTotalDTO> datas = instockPlanTotalService.query(cellInstockPlanTotalQuery, cellInstockPlanTotalQuery.getMonth(), cellInstockPlanTotalQuery.getIsOverseaId(), version);
            for (CellInstockPlanTotalDTO instockPlanTotalDTO : datas) {
                CellVersionPlanHistoryDTO history = convert.toCellVersionPlanHistoryFromCellInstockPlanTotalDTO(instockPlanTotalDTO);
                ReflectUtil.invoke(history, "setMonthV" + version_order.get(), instockPlanTotalDTO.getQtyThousandPc());
                histories.add(history);
            }
            version_order.set(version_order.get() - 1);
        });
        //三、兆瓦转换计算
        histories.stream().forEach(historyDTO -> {
            CellConversionFactorDTO cellConversionFactorDTO = cellConversionFactorService.queryByCellsType(historyDTO.getIsOverseaName(),historyDTO.getCellTypeName());
            if (null == cellConversionFactorDTO) {
                historyDTO.setMonthV0(null);
                historyDTO.setMonthV1(null);
                historyDTO.setMonthV2(null);
            } else if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) == 0) {
                historyDTO.setMonthV0(null);
                historyDTO.setMonthV1(null);
                historyDTO.setMonthV2(null);
            } else {
                if (null != historyDTO.getMonthV0()) {
                    historyDTO.setMonthV0(historyDTO.getMonthV0().divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP));
                }
                if (null != historyDTO.getMonthV1()) {
                    historyDTO.setMonthV1(historyDTO.getMonthV1().divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP));
                }
                if (null != historyDTO.getMonthV2()) {
                    historyDTO.setMonthV2(historyDTO.getMonthV2().divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP));
                }
            }
        });
        //四、继续汇总相同车间+相同电池类型合并一行，每行v0 v1 v2赋值
        List<CellVersionPlanHistoryDTO> detailLines = summaryQuantity(histories, CellVersionPlanHistoryDTO::groupDetail);
        List<CellVersionPlanHistoryDTO> cellTypeLines = summaryQuantity(histories, CellVersionPlanHistoryDTO::groupCellType);
        List<CellVersionPlanHistoryDTO> isOverseaLines = summaryQuantity(histories, CellVersionPlanHistoryDTO::groupIsOversea);
        detailLines.addAll(cellTypeLines);
        detailLines.forEach(this::transalte);
        isOverseaLines.forEach(item -> item.setWorkshopName(MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOverseaName())));
        List<CellVersionPlanHistoryDTO> details = Lists.newArrayList();
        details.addAll(isOverseaLines);
        details.addAll(detailLines);
        //排序
        details.sort(Comparator.comparing(CellVersionPlanHistoryDTO::getCellTypeId, Comparator.nullsFirst(Long::compareTo)).thenComparing(CellVersionPlanHistoryDTO::getWorkshopId, Comparator.nullsLast(Long::compareTo)));
        Map<String, Object> mapData = new HashMap<>();
        //九、数据组装map
        mapData.put("details", details);
        mapData.put("versions", versions_array);
        mapData.put("month", query.getMonth());
        return mapData;
    }

    /**
     * 根据条件汇总
     *
     * @param histories
     * @param groupMapper
     * @return
     */
    private List<CellVersionPlanHistoryDTO> summaryQuantity(List<CellVersionPlanHistoryDTO> histories, Function<CellVersionPlanHistoryDTO, CellVersionPlanHistoryDTO> groupMapper){
        Map<CellVersionPlanHistoryDTO, List<CellVersionPlanHistoryDTO>> groupMap = histories.stream().collect(Collectors.groupingBy(groupMapper));
        groupMap.forEach((item, list) ->{
            BigDecimal monthV0 = list.stream().map(CellVersionPlanHistoryDTO::getMonthV0).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal monthV1 = list.stream().map(CellVersionPlanHistoryDTO::getMonthV1).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal monthV2 = list.stream().map(CellVersionPlanHistoryDTO::getMonthV2).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setMonthV0(monthV0);
            item.setMonthV1(monthV1);
            item.setMonthV2(monthV2);
            item.setGap(monthV2.subtract(monthV1));
        });
        return IterableUtils.toList(groupMap.keySet());
    }

    private void transalte(CellVersionPlanHistoryDTO cellVersionPlanHistoryDTO) {
        String cellTypeName = cellVersionPlanHistoryDTO.getCellTypeName();
        if (StringUtils.isNotEmpty(cellTypeName)){
            cellTypeName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.BATTERY_TYPE,cellTypeName);
            cellVersionPlanHistoryDTO.setCellTypeName(cellTypeName);
        }
        String myWorkshopName = cellVersionPlanHistoryDTO.getWorkshopName();
        if (StringUtils.isNotEmpty(myWorkshopName)){
            myWorkshopName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,myWorkshopName);
            cellVersionPlanHistoryDTO.setWorkshopName(myWorkshopName);
        }
        String hTrace = cellVersionPlanHistoryDTO.getHTrace();
        if (StringUtils.isNotEmpty(hTrace)){
            hTrace= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.H_TRACE,hTrace);
            cellVersionPlanHistoryDTO.setHTrace(hTrace);
        }
        String cellSource = cellVersionPlanHistoryDTO.getCellSource();
        if (StringUtils.isNotEmpty(cellSource)){
            cellVersionPlanHistoryDTO.setCellSource(cellSource);
        }
        String transparentDoubleGlass = cellVersionPlanHistoryDTO.getTransparentDoubleGlass();
        if (StringUtils.isNotEmpty(transparentDoubleGlass)){
            transparentDoubleGlass= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS,transparentDoubleGlass);
            cellVersionPlanHistoryDTO.setTransparentDoubleGlass(transparentDoubleGlass);
        }
        String aesthetics = cellVersionPlanHistoryDTO.getAesthetics();
        if (StringUtils.isNotEmpty(aesthetics)){
            aesthetics= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AESTHETICS,aesthetics);
            cellVersionPlanHistoryDTO.setAesthetics(aesthetics);
        }
        String regionalCountry = cellVersionPlanHistoryDTO.getRegionalCountry();
        if (StringUtils.isNotEmpty(regionalCountry)){
            regionalCountry= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.REGIONAL_COUNTRY,regionalCountry);
            cellVersionPlanHistoryDTO.setRegionalCountry(regionalCountry);
        }
    }
}
