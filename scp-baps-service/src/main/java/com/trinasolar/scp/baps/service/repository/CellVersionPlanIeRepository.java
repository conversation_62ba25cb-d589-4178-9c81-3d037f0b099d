package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellVersionPlanIe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 计划与ie产能对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Repository
public interface CellVersionPlanIeRepository extends JpaRepository<CellVersionPlanIe, Long>, QuerydslPredicateExecutor<CellVersionPlanIe> {
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("delete from  CellVersionPlanIe c where c.month = :month and c.isOverseaId = :isOverseaId ")
    void deleteByMonth(@Param("month") String month, @Param("isOverseaId") Long isOverseaId);
}
