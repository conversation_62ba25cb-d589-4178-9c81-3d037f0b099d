package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.dto.SiliconSplitRuleDTO;
import com.trinasolar.scp.baps.domain.convert.SiliconSplitRuleDEConvert;
import com.trinasolar.scp.baps.domain.entity.SiliconSplitRule;
import com.trinasolar.scp.baps.domain.entity.QSiliconSplitRule;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.excel.SiliconSplitRuleExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.SiliconSplitRuleQuery;
import com.trinasolar.scp.baps.domain.save.SiliconSplitRuleSaveDTO;
import com.trinasolar.scp.baps.domain.utils.*;
import com.trinasolar.scp.baps.service.repository.SiliconSplitRuleRepository;
import com.trinasolar.scp.baps.service.service.SiliconSplitRuleService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * 硅片拆分规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 07:44:54
 */
@Slf4j
@Service("siliconSplitRuleService")
@RequiredArgsConstructor
public class SiliconSplitRuleServiceImpl implements SiliconSplitRuleService {
    private static final QSiliconSplitRule qSiliconSplitRule = QSiliconSplitRule.siliconSplitRule;

    private final SiliconSplitRuleDEConvert convert;

    private final SiliconSplitRuleRepository repository;
    private final JPAQueryFactory jpaQueryFactory;

    @Override
    public Page<SiliconSplitRuleDTO> queryByPage(SiliconSplitRuleQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query=  convert.toSiliconSplitRuleQueryCNName(query,MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<SiliconSplitRule> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, SiliconSplitRuleQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qSiliconSplitRule.id.eq(query.getId()));
        }
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qSiliconSplitRule.cellTypeId.eq(query.getCellTypeId()));
        }
        if(StringUtils.isNotEmpty(query.getCellType())){
            booleanBuilder.and(qSiliconSplitRule.cellType.eq(query.getCellType()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qSiliconSplitRule.workshopId.eq(query.getWorkshopId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qSiliconSplitRule.workshop.eq(query.getWorkshop()));
        }
       if (query.getWaferGradeId()!=null){
           booleanBuilder.and(qSiliconSplitRule.waferGradeId.eq(query.getWaferGradeId()));
       }
        if(StringUtils.isNotEmpty(query.getWaferGrade())){
            booleanBuilder.and(qSiliconSplitRule.waferGrade.eq(query.getWaferGrade()));
        }
        if (query.getSiMfrsId() != null) {
            booleanBuilder.and(qSiliconSplitRule.siMfrsId.eq(query.getSiMfrsId()));
        }
        if(StringUtils.isNotEmpty(query.getSiMfrs())){
            booleanBuilder.and(qSiliconSplitRule.siMfrs.eq(query.getSiMfrs()));
        }
        if (query.getCellFine() != null) {
            booleanBuilder.and(qSiliconSplitRule.cellFine.eq(query.getCellFine()));
        }
        if (query.getStartDate() != null) {
            booleanBuilder.and(qSiliconSplitRule.startDate.eq(query.getStartDate()));
        }
        if (query.getEndDate() != null) {
            booleanBuilder.and(qSiliconSplitRule.endDate.eq(query.getEndDate()));
        }
        if (query.getEndDate() != null) {
            booleanBuilder.and(qSiliconSplitRule.endDate.eq(query.getEndDate()));
        }
        if (query.getRuleType() != null) {
            booleanBuilder.and(qSiliconSplitRule.ruleType.eq(query.getRuleType()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())){
            Pair<LocalDate, LocalDate> firstDayAndLastDay = DateUtil.getFirstDayAndLastDay(query.getMonth());
            LocalDate firstDay = firstDayAndLastDay.getLeft();
            LocalDate lastDay = firstDayAndLastDay.getRight();
            booleanBuilder.and( qSiliconSplitRule.startDate.loe(firstDay).and(qSiliconSplitRule.endDate.goe(firstDay)).
                    or(qSiliconSplitRule.startDate.loe(lastDay).and(qSiliconSplitRule.endDate.goe(lastDay))));

        }
    }

    @Override
    public SiliconSplitRuleDTO queryById(Long id) {
        SiliconSplitRule queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public SiliconSplitRuleDTO save(SiliconSplitRuleSaveDTO saveDTO) {
        SiliconSplitRule newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new SiliconSplitRule());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(SiliconSplitRuleQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<SiliconSplitRuleDTO> dtos = queryByPage(query).getContent();
        // dto数据转为ExcelData数据
        List<SiliconSplitRuleExcelDTO> excelDtos = convert.toExcelDTO(dtos);
        ExcelPara excelPara =  query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(excelDtos, excelPara);
        // 导出调用excelUtils
        String fileName= BapsMessgeHelper.getMessage("export.siliconsplitrule.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);
    }
    /**
     * 获取某月份指定类型的硅片分类规则
     * @param query
     * @param type
     * @return
     */
    @Override
    public List<SiliconSplitRule> getRulesByMonthType(CellPlanLineQuery query, String type) {
        QSiliconSplitRule qSiliconSplitRule = QSiliconSplitRule.siliconSplitRule;
        Pair<LocalDate, LocalDate> firstDayAndLastDay = DateUtil.getFirstDayAndLastDay(query.getMonth());
        LocalDate firstDay = firstDayAndLastDay.getLeft();
        LocalDate lastDay = firstDayAndLastDay.getRight();
        JPAQuery<SiliconSplitRule> where = jpaQueryFactory.select(qSiliconSplitRule).from(qSiliconSplitRule).where(
                qSiliconSplitRule.ruleType.eq(type)
        ).where(
                qSiliconSplitRule.startDate.loe(firstDay).and(qSiliconSplitRule.endDate.goe(firstDay)).
                        or(qSiliconSplitRule.startDate.loe(lastDay).and(qSiliconSplitRule.endDate.goe(lastDay)))
        );
        if (StringUtils.isNotEmpty(query.getWorkshop())){
            where.where(
                    qSiliconSplitRule.workshop.eq(query.getWorkshop())
            );
        }
        if (StringUtils.isNotEmpty(query.getCellsType())){
            where.where(
                    qSiliconSplitRule.cellType.eq(query.getCellsType())
            );
        }
        return where.fetch();
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<SiliconSplitRuleExcelDTO> excelDtos = Lists.newArrayList();
        excelDtos=  ExcelUtils.readExcel(multipartFile.getInputStream(),null, SiliconSplitRuleExcelDTO.class,excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        //验证数据
        checkInput(excelDtos);
        List<SiliconSplitRuleSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        saveDTOS=  convert.toSiliconSplitRuleSaveDTOCNName(saveDTOS);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        saveDTOS.stream().forEach(this::save);
    }

    public void checkInput(List<SiliconSplitRuleExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors = new ArrayList<>();
        excelDTOS.stream().forEach(excelDTO -> {
            //验证分片规则
            String ruleType=excelDTO.getRuleType();
            if (StringUtils.isNotEmpty(ruleType)){
                if ( !ruleType.equals(SiliconSplitTypeConstant.SIMFRS) && !ruleType.equals(SiliconSplitTypeConstant.WAFERGRADE)){
                    String message = BapsMessgeHelper.getMessage("siliconsplitrule.import.ruletype.not.exists",new Object[]{i[0],ruleType});
                    errors.add(message);
                }
            }else{
                String message = BapsMessgeHelper.getMessage("siliconsplitrule.import.ruletype.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            if (ruleType.equals(SiliconSplitTypeConstant.WAFERGRADE)){
                if (StringUtils.isEmpty(excelDTO.getWaferGrade())){
                    String message = BapsMessgeHelper.getMessage("siliconsplitrule.import.wafergrade.not.null",new Object[]{i[0]});
                    errors.add(message);
                }
                //硅片等级lov验证
                LovLineDTO lov = LovUtils.getByName(LovHeaderCodeConstant.WAFER_GRADE, excelDTO.getWaferGrade());
                if (lov==null){
                    String message = BapsMessgeHelper.getMessage("siliconsplitrule.import.wafergrade.not.exists",new Object[]{i[0],excelDTO.getWaferGrade()});
                    errors.add(message);
                }
                String cellFine=excelDTO.getCellFine();
                if (StringUtils.isNotEmpty( cellFine)){
                    String regex= "\\d+(\\.\\d+)?%";
                    if (!cellFine.matches(regex)) {
                        String message = BapsMessgeHelper.getMessage("siliconsplitrule.import.cellfine.format.error",new Object[]{i[0],excelDTO.getCellFine()});
                        errors.add(message);
                    }
                }else{
                    String message = BapsMessgeHelper.getMessage("siliconsplitrule.import.cellfine.not.null",new Object[]{i[0]});
                    errors.add(message);
                }
            }
            if (ruleType.equals(SiliconSplitTypeConstant.SIMFRS)){
                if (StringUtils.isEmpty(excelDTO.getSiMfrs())){
                    String message = BapsMessgeHelper.getMessage("siliconsplitrule.import.simfrs.not.null",new Object[]{i[0]});
                    errors.add(message);
                }
                //硅片厂家lov验证
                LovLineDTO lov = LovUtils.getByName(LovHeaderCodeConstant.SI_MFRS, excelDTO.getSiMfrs());
                if (lov==null){
                    String message = BapsMessgeHelper.getMessage("siliconsplitrule.import.simfrs.not.exists",new Object[]{i[0],excelDTO.getSiMfrs()});
                    errors.add(message);
                }
            }
            LocalDate startDate= excelDTO.getStartDate();
            LocalDate endDate= excelDTO.getEndDate();
            if (startDate==null){
                String message=BapsMessgeHelper.getMessage("the.row.starttime.must.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            if (endDate==null){
                String message=BapsMessgeHelper.getMessage("the.row.endtime.must.not.null",new Object[]{i[0]});
                errors.add(message);
            }
            if(startDate!=null&&endDate!=null&&startDate.isAfter(endDate)){
                String message=BapsMessgeHelper.getMessage("the.row.endtime.must.not.before.starttime",new Object[]{i[0]});
                errors.add(message);
            }
            //验证电池类型
            LovLineDTO  lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellType());
            if (lovLineDTO == null) {
                String message = BapsMessgeHelper.getMessage("the.row.cellstype.not.exists",new Object[]{i[0],excelDTO.getCellType()});
                errors.add(message);
            }
            //验证生产车间
            LovLineDTO lovLineDTO_WorkShop = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop());
            if (lovLineDTO_WorkShop == null) {
                String message = BapsMessgeHelper.getMessage("the.row.workshop.not.exists",new Object[]{i[0], excelDTO.getWorkshop()});
                errors.add(message);
            }
            i[0]++;
        });
        //检测时间段重合
        Map<String, List<SiliconSplitRuleExcelDTO>> collect = excelDTOS.stream().collect(Collectors.groupingBy(
                item -> StringUtils.joinWith(" ", item.getRuleType(),item.getWorkshop(), item.getCellType())));
        collect.entrySet().forEach(item->{
            String key=item.getKey();
            List<SiliconSplitRuleExcelDTO> values = item.getValue();
            List<TimeRange> ranges = values.stream().map(v -> {
                return new TimeRange(v.getStartDate(), v.getEndDate());
            }).collect(Collectors.toList());
            Boolean  isOverlap= hasOverlap(ranges);
            if (isOverlap) {
                String message=BapsMessgeHelper.getMessage("the.row.time.repeat",new Object[]{key});
                errors.add(message);
            }
        });
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(","));
            throw new BizException(errorString);
        }

    }

    public   boolean hasOverlap(List<TimeRange> timeRanges) {
        for (int i = 0; i < timeRanges.size(); i++) {
            TimeRange range1 = timeRanges.get(i);
            for (int j = i + 1; j < timeRanges.size(); j++) {
                TimeRange range2 = timeRanges.get(j);
                if (range1.overlaps(range2)) {
                    return true; // 有重叠部分，返回true
                }
            }
        }
        return false; // 没有重叠部分，返回false
    }
}
class TimeRange {
    private LocalDate start;
    private LocalDate end;

    public TimeRange(LocalDate start, LocalDate end) {
        this.start = start;
        this.end = end;
    }

    public boolean overlaps(TimeRange other) {
        return !end.isBefore(other.start) && !start.isAfter(other.end);
    }
}