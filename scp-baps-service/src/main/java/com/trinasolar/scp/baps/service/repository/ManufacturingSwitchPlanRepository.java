package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.ManufacturingSwitchPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 制造工艺切换计划维护
 */
@Repository
public interface ManufacturingSwitchPlanRepository extends JpaRepository<ManufacturingSwitchPlan, Long>, QuerydslPredicateExecutor<ManufacturingSwitchPlan> {
}
