package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellResource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 电池资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-08 02:52:37
 */
@Repository
public interface CellResourceRepository extends JpaRepository<CellResource, Long>, QuerydslPredicateExecutor<CellResource> {
   @Query("delete  from CellResource c where c.ieorgrade= :ieorgrade")
   @Modifying
   @Transactional(rollbackFor = Exception.class)
    void deleteByIeGrade(@Param("ieorgrade") int ieorgrade);
}
