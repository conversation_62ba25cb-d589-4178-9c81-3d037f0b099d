package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.LovUtils;

import java.util.Map;

public class LvTest {
    public static void main(String[] args) {
         //获得所有得电磁类型
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BATTERY_TYPE);
        System.out.println(allByHeaderCode);
    }
}
