package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.MaxVersionSliceSupplyAndDmaterialDeliveryDTO;
import com.trinasolar.scp.baps.domain.dto.SiliconSliceSupplyLinesDTO;
import com.trinasolar.scp.baps.domain.query.MaxVersionSliceSupplyAndDmaterialDeliveryQuery;
import com.trinasolar.scp.baps.domain.query.SiliconSliceSupplyLinesQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = FeignConstant.SCP_BATTERY_MRP_API, path = "/scp-battery-mrp-api",configuration = LanguageHeaderInterceptor.class)
public interface SiliconSliceSupplyLinesFeign {

    @PostMapping("/silicon-slice-supply-lines/list")
    public ResponseEntity<Results<List<SiliconSliceSupplyLinesDTO>>> list(@RequestBody SiliconSliceSupplyLinesQuery query);


    @ApiOperation(value = "最新版本硅片供应能力+物料到货计划信息(包含基地信息)", notes = "最新版本硅片供应能力+物料到货计划信息")
    @PostMapping("/silicon-slice-supply-lines/maxVersionSliceSupplyAndDmaterialDeliveryWithBasePlace")
    ResponseEntity<Results<List<MaxVersionSliceSupplyAndDmaterialDeliveryDTO>>> queryMaxVersionSliceSupplyAndDmaterialDelivery(
            @RequestBody MaxVersionSliceSupplyAndDmaterialDeliveryQuery query);
}
