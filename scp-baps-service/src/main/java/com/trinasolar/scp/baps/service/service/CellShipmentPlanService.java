package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellShipmentPlanDTO;
import com.trinasolar.scp.baps.domain.dto.aps.CellPlanShippableSaveListDTO;
import com.trinasolar.scp.baps.domain.dto.aps.CellRelationDTO;
import com.trinasolar.scp.baps.domain.entity.CellShipmentPlan;
import com.trinasolar.scp.baps.domain.query.CellShipmentPlanQuery;
import com.trinasolar.scp.baps.domain.save.CellShipmentPlanSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 可发货计划表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-28 11:52:39
 */
public interface CellShipmentPlanService {
    /**
     * 分页获取可发货计划表
     *
     * @param query 查询对象
     * @return 可发货计划表分页对象
     */
    Page<CellShipmentPlanDTO> queryByPage(CellShipmentPlanQuery query);

    /**
     * 根据主键获取可发货计划表详情
     *
     * @param id 主键
     * @return 可发货计划表详情
     */
        CellShipmentPlanDTO queryById(Long id);

    /**
     * 保存或更新可发货计划表
     *
     * @param saveDTO 可发货计划表保存对象
     * @return 可发货计划表对象
     */
    CellShipmentPlanDTO save(CellShipmentPlanSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除可发货计划表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellShipmentPlanQuery query, HttpServletResponse response);

    void saveAll(List<CellShipmentPlanDTO> shipmentPlanDTOList);

    void queryList(CellShipmentPlanQuery query);

    //(aop)电池系列与料号关系列表
    List<CellRelationDTO> queryByMaterialNoList(List<String> materialNoList);

    void deleteByMonth(String month);
}

