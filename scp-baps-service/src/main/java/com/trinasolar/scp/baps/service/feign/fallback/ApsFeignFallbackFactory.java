package com.trinasolar.scp.baps.service.feign.fallback;

import com.trinasolar.scp.baps.domain.dto.ClassCodeDTO;
import com.trinasolar.scp.baps.domain.dto.MwCoefficientDTO;
import com.trinasolar.scp.baps.domain.dto.aps.*;
import com.trinasolar.scp.baps.domain.dto.erp.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.baps.domain.query.ClassCodeQuery;
import com.trinasolar.scp.baps.domain.query.MwCoefficientQuery;
import com.trinasolar.scp.baps.domain.query.aps.CellBooksForBapsQuery;
import com.trinasolar.scp.baps.domain.query.aps.ModuleBasePlaceQuery;
import com.trinasolar.scp.baps.domain.query.aps.RecordTransitionQuery;
import com.trinasolar.scp.baps.domain.query.erp.ErpAlternateDesignatorQuery;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.feign.PageFeign;
import com.trinasolar.scp.common.api.util.Results;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ApsFeignFallbackFactory implements FallbackFactory<ApsFeign> {
    @Override
    public ApsFeign create(Throwable cause) {
        return new ApsFeign() {
            @Override
            public ResponseEntity<Results<List<ClassCodeDTO>>> queryClassCode(ClassCodeQuery query) {
                log.warn("【Apseign-dp-schedule-lines/erp/classCode】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ErpAlternateDesignatorDTO>>> getAlternateDesignatorCode(ErpAlternateDesignatorQuery query) {
                log.warn("【Apseign-erp_alternate_designator/alternate_designator_code】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<MwCoefficientDTO>>> findMwCoefficient(MwCoefficientQuery query) {
                log.warn("【Apseign-mw-coefficient/list】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<String>> saveList(List<CellPlanShippableSaveListDTO> saveList) {
                log.warn("【Apseign-cell-production-plan/save-list】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<CellShippingDaysDTO>>> list(CellShippingDaysQuery query) {
                log.warn("【Apseign-cell-shipping-days/list】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<PageFeign<RecordTransitionDTO>>> queryByPage(RecordTransitionQuery query) {
                log.warn("【Apseign-record-transition/page】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<CellRelationDTO>>> queryByMaterialNoList(List<String> materialNoList) {
                log.warn("【cell-relation/queryByMaterialNoList】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<String>> saveReturnDataList(List<ReturnDataSaveDTO> saveList) {
                log.warn("【Apseign-return-data/save-list】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<PowerSupplyAopDTO>>> powerSupplyAopList(PowerSupplyAopQuery query) {
                log.warn("【Apseign-power-supply-aop/list】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Map<String, String>>> getEntityNameToClassCodeMap(List<String> names) {
                log.warn("【Apseign-erp-wip-discrete/getclasscodeforbaps】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<CellBooksDTO>>> getCellLocatorWorkshopRelation(CellBooksForBapsQuery query) {
                log.warn("【Apseign-cell-books/query-for-baps】发生异常：{}", cause);
                    return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<PageFeign<ModuleBasePlaceDTO>>> findModuleBasePlaceList(ModuleBasePlaceQuery query) {
                log.warn("【Apseign-cell-books/module-base-place】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ModuleBasePlaceDTO>>> allDataList() {
                log.warn("【/module-base-place/allDataList】发生异常：{}", cause);
                return Results.createFailRes();
            }
        };
    }
}
