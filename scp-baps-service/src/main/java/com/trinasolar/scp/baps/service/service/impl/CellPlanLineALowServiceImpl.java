package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellPlanLineALowDEConvert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineALow;
import com.trinasolar.scp.baps.domain.entity.QCellPlanLineALow;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineALowExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineALowQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineALowSaveDTO;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.baps.service.repository.CellPlanLineALowRepository;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.util.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import static com.trinasolar.scp.baps.domain.entity.QCellPlanLineALow.cellPlanLineALow;

/**
 * 入库计划表A-
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Slf4j
@Service("cellPlanLineALowService")
@RequiredArgsConstructor
public class CellPlanLineALowServiceImpl implements CellPlanLineALowService {
    private static final QCellPlanLineALow qCellPlanLineALow = cellPlanLineALow;
    private final CellPlanLineALowDEConvert convert;
    private final CellPlanLineALowRepository repository;
    private final CellInstockPlanService cellInstockPlanService;
    private final CellConversionFactorService cellConversionFactorService;
    @Override
    public Page<CellPlanLineALowDTO> queryByPage(CellPlanLineALowQuery query) {
        List<CellPlanLineALowDTO> result = calc(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "version").and(Sort.by(Sort.Direction.ASC, "id"));
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        int startIndex=(query.getPageNumber()-1)*query.getPageSize();
        int toIndex=startIndex+ pageable.getPageSize();
        toIndex=toIndex>result.size()?result.size():toIndex;
        return new PageImpl(result.subList(startIndex,toIndex), pageable, result.size());
    }
    private void buildWhere(BooleanBuilder booleanBuilder, CellPlanLineALowQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellPlanLineALow.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellPlanLineALow.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellPlanLineALow.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellPlanLineALow.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellPlanLineALow.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellPlanLineALow.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellPlanLineALow.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellPlanLineALow.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellPlanLineALow.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qCellPlanLineALow.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qCellPlanLineALow.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellPlanLineALow.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qCellPlanLineALow.productionGrade.eq(query.getProductionGrade()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellPlanLineALow.month.eq(query.getMonth()));
        }
        if (query.getCellMv() != null) {
            booleanBuilder.and(qCellPlanLineALow.cellMv.eq(query.getCellMv()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qCellPlanLineALow.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getFromVersion())) {
            booleanBuilder.and(qCellPlanLineALow.fromVersion.eq(query.getFromVersion()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qCellPlanLineALow.itemCode.eq(query.getItemCode()));
        }
        if (query.getD1() != null) {
            booleanBuilder.and(qCellPlanLineALow.d1.eq(query.getD1()));
        }
        if (query.getD2() != null) {
            booleanBuilder.and(qCellPlanLineALow.d2.eq(query.getD2()));
        }
        if (query.getD3() != null) {
            booleanBuilder.and(qCellPlanLineALow.d3.eq(query.getD3()));
        }
        if (query.getD4() != null) {
            booleanBuilder.and(qCellPlanLineALow.d4.eq(query.getD4()));
        }
        if (query.getD5() != null) {
            booleanBuilder.and(qCellPlanLineALow.d5.eq(query.getD5()));
        }
        if (query.getD6() != null) {
            booleanBuilder.and(qCellPlanLineALow.d6.eq(query.getD6()));
        }
        if (query.getD7() != null) {
            booleanBuilder.and(qCellPlanLineALow.d7.eq(query.getD7()));
        }
        if (query.getD8() != null) {
            booleanBuilder.and(qCellPlanLineALow.d8.eq(query.getD8()));
        }
        if (query.getD9() != null) {
            booleanBuilder.and(qCellPlanLineALow.d9.eq(query.getD9()));
        }
        if (query.getD10() != null) {
            booleanBuilder.and(qCellPlanLineALow.d10.eq(query.getD10()));
        }
        if (query.getD11() != null) {
            booleanBuilder.and(qCellPlanLineALow.d11.eq(query.getD11()));
        }
        if (query.getD12() != null) {
            booleanBuilder.and(qCellPlanLineALow.d12.eq(query.getD12()));
        }
        if (query.getD13() != null) {
            booleanBuilder.and(qCellPlanLineALow.d13.eq(query.getD13()));
        }
        if (query.getD14() != null) {
            booleanBuilder.and(qCellPlanLineALow.d14.eq(query.getD14()));
        }
        if (query.getD15() != null) {
            booleanBuilder.and(qCellPlanLineALow.d15.eq(query.getD15()));
        }
        if (query.getD16() != null) {
            booleanBuilder.and(qCellPlanLineALow.d16.eq(query.getD16()));
        }
        if (query.getD17() != null) {
            booleanBuilder.and(qCellPlanLineALow.d17.eq(query.getD17()));
        }
        if (query.getD18() != null) {
            booleanBuilder.and(qCellPlanLineALow.d18.eq(query.getD18()));
        }
        if (query.getD19() != null) {
            booleanBuilder.and(qCellPlanLineALow.d19.eq(query.getD19()));
        }
        if (query.getD20() != null) {
            booleanBuilder.and(qCellPlanLineALow.d20.eq(query.getD20()));
        }
        if (query.getD21() != null) {
            booleanBuilder.and(qCellPlanLineALow.d21.eq(query.getD21()));
        }
        if (query.getD22() != null) {
            booleanBuilder.and(qCellPlanLineALow.d22.eq(query.getD22()));
        }
        if (query.getD23() != null) {
            booleanBuilder.and(qCellPlanLineALow.d23.eq(query.getD23()));
        }
        if (query.getD24() != null) {
            booleanBuilder.and(qCellPlanLineALow.d24.eq(query.getD24()));
        }
        if (query.getD25() != null) {
            booleanBuilder.and(qCellPlanLineALow.d25.eq(query.getD25()));
        }
        if (query.getD26() != null) {
            booleanBuilder.and(qCellPlanLineALow.d26.eq(query.getD26()));
        }
        if (query.getD27() != null) {
            booleanBuilder.and(qCellPlanLineALow.d27.eq(query.getD27()));
        }
        if (query.getD28() != null) {
            booleanBuilder.and(qCellPlanLineALow.d28.eq(query.getD28()));
        }
        if (query.getD29() != null) {
            booleanBuilder.and(qCellPlanLineALow.d29.eq(query.getD29()));
        }
        if (query.getD30() != null) {
            booleanBuilder.and(qCellPlanLineALow.d30.eq(query.getD30()));
        }
        if (query.getD31() != null) {
            booleanBuilder.and(qCellPlanLineALow.d31.eq(query.getD31()));
        }
    }
    @Override
    public CellPlanLineALowDTO queryById(Long id) {
        CellPlanLineALow queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }
    @Override
    public CellPlanLineALowDTO save(CellPlanLineALowSaveDTO saveDTO) {
        CellPlanLineALow newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellPlanLineALow());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(CellPlanLineALowQuery query, HttpServletResponse response) {
        //获取数据库中数据
        query.setPageSize(GlobalConstant.max_page_size);
        query.setPageNumber(1);
        query.setBasePlace(null);
        query.setWorkshop(null);
        query.setCellsType(null);
        List<CellPlanLineALowDTO> dtos = calc(query);
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("baps_data_not_exist");
        }
        // dto数据转为ExcelData数据
        List<CellPlanLineALowExcelDTO> datas = convert.toExcelDTO(dtos);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellPlanLineALowExcelDTO.class, datas, JSON.toJSONString(query), "入库计划表A-", response);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public List<CellPlanLineALowDTO> calc(CellPlanLineALowQuery query) {
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        //1、获取入库计划数据
        CellInstockPlanQuery cellInstockPlanQuery = convert.toCellInstockPlanQuery(query);
        List<CellInstockPlanDTO> cellPlanLines = cellInstockPlanService.query(cellInstockPlanQuery);
        //2、分组
        Map<String, Map<String, List<CellInstockPlanDTO>>> collect = cellPlanLines.stream().
                collect(Collectors.groupingBy(CellInstockPlanDTO::getCellsType, Collectors.groupingBy(
                        item -> {
                            return StringTools.joinWith(",", item.getBasePlace(), item.getWorkshop(), item.getCellsType(), item.getHTrace(),item.getTransparentDoubleGlass(), item.getProductionGrade(),item.getCellSource());
                        })));

        //3、汇总统计
        List<CellPlanLineALowDTO> cellPlanLineALowDTOS = new ArrayList<>();
        Set<Map.Entry<String, Map<String, List<CellInstockPlanDTO>>>> collectEntries = collect.entrySet();
        for (Map.Entry<String, Map<String, List<CellInstockPlanDTO>>> cellTypeEntry : collectEntries) {
            Set<Map.Entry<String, List<CellInstockPlanDTO>>> entries = cellTypeEntry.getValue().entrySet();
            String cellType = cellTypeEntry.getKey();
            //依据电池类型获取对象mw折算系数
            CellConversionFactorDTO cellConversionFactorDTO = null;
            for (Map.Entry<String, List<CellInstockPlanDTO>> entry : entries) {
                CellPlanLineALowDTO cellPlanLineALow = null;
                //统计数据
                List<CellInstockPlanDTO> dtos = entry.getValue();
                BigDecimal qtyCount = BigDecimal.ZERO;//万片数
                for (CellInstockPlanDTO dto : dtos) {
                    if (cellConversionFactorDTO==null){
                        cellConversionFactorDTO=mapCellConversionFactorDTO.get(StringTools.joinWith(",",dto.getIsOversea(),dto.getCellsType()));
                    }
                    if (Objects.isNull(cellPlanLineALow)) {
                        cellPlanLineALow = new CellPlanLineALowDTO();
                        cellPlanLineALow.setMonth(dto.getMonth());
                        cellPlanLineALow.setIsOversea(dto.getIsOversea());
                        cellPlanLineALow.setBasePlace(dto.getBasePlace());
                        cellPlanLineALow.setWorkshop(dto.getWorkshop());
                        cellPlanLineALow.setCellsType(dto.getCellsType());
                        cellPlanLineALow.setHTrace(dto.getHTrace());
                        cellPlanLineALow.setTransparentDoubleGlass(dto.getTransparentDoubleGlass());
                        cellPlanLineALow.setProductionGrade(dto.getProductionGrade());
                        cellPlanLineALow.setCellSource(dto.getCellSource());
                    }
                    int day = dto.getStartTime().getDayOfMonth();

                    BigDecimal val = ReflectUtil.invoke(cellPlanLineALow, "getD" + day);
                    if (val!=null){
                        val=val.setScale(2,RoundingMode.HALF_UP);
                    }
                    BigDecimal value=dto.getQtyPc();
                    value=value.setScale(2,RoundingMode.HALF_UP);
                    if (Objects.isNull(val)) {
                        ReflectUtil.invoke(cellPlanLineALow, "setD" + day,value);

                    } else {
                        BigDecimal addVal = ((BigDecimal) val).add(value);
                        ReflectUtil.invoke(cellPlanLineALow, "setD" + day, addVal);
                    }

                    if (Objects.nonNull(qtyCount)) {
                        qtyCount = qtyCount.add(value);
                    }
                }
                cellPlanLineALow.setQtyThousandPc(qtyCount);
                //依据瓦片折算系数计算MV
                if (Objects.nonNull(cellConversionFactorDTO)) {
                    if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor())!=0){
                        BigDecimal mv = qtyCount.divide(cellConversionFactorDTO.getConversionFactor(),0,RoundingMode.HALF_UP);
                        cellPlanLineALow.setCellMv(mv);
                    }

                } else {
                    log.warn("没有《" + cellPlanLineALow.getCellsType() + "》电池类型折算系数数据，不能进行对应的mv计算");
                }
                cellPlanLineALowDTOS.add(cellPlanLineALow);
            }
        }
        //3、排序
        List<CellPlanLineALowDTO> sortedList = cellPlanLineALowDTOS.stream().sorted(
                Comparator.comparing(CellPlanLineALowDTO::getIsOversea).thenComparing(
                        CellPlanLineALowDTO::getBasePlace
                ).thenComparing(
                        CellPlanLineALowDTO::getWorkshop
                ).thenComparing(
                        CellPlanLineALowDTO::getCellsType
                ).thenComparing(
                        CellPlanLineALowDTO::getHTrace,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineALowDTO::getTransparentDoubleGlass,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                CellPlanLineALowDTO::getProductionGrade,
                Comparator.nullsLast(Comparator.naturalOrder())
        ).thenComparing(
                        CellPlanLineALowDTO::getCellSource,
                        Comparator.nullsFirst(Comparator.naturalOrder())
                )
        ).collect(Collectors.toList());
        return sortedList;
    }
}




