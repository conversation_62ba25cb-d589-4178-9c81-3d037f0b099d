package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.ConfigCellAMinusPercentDTO;
import com.trinasolar.scp.baps.domain.query.ConfigCellAMinusQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = FeignConstant.SCP_AOP_API, path = "/scp-aop-api",configuration = LanguageHeaderInterceptor.class)
public interface ConfigCellAMinusFeign {

    @PostMapping(path = "/config-cell-a-minus-percent/ie/Forecast/page")
    public ResponseEntity<Results<PageFeign<ConfigCellAMinusPercentDTO>>> gueryIECelLAMinusByPage(@RequestBody ConfigCellAMinusQuery query);
}
