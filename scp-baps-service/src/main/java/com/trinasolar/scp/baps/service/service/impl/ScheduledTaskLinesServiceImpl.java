package com.trinasolar.scp.baps.service.service.impl;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.convert.ScheduledTaskLinesDEConverter;
import com.trinasolar.scp.baps.domain.dto.ScheduledTaskLinesDTO;
import com.trinasolar.scp.baps.domain.entity.ScheduledTaskLines;
import com.trinasolar.scp.baps.domain.excel.ScheduledTaskLineExcelExportDTO;
import com.trinasolar.scp.baps.domain.query.ScheduledTaskLinesQuery;
import com.trinasolar.scp.baps.service.repository.ScheduledTaskLinesRepository;
import com.trinasolar.scp.baps.service.service.ScheduledTaskLinesService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import static com.trinasolar.scp.baps.domain.entity.QScheduledTaskLines.scheduledTaskLines;
@Service("scheduledTaskLinesService")
@Slf4j
public class ScheduledTaskLinesServiceImpl implements ScheduledTaskLinesService {
    @Resource
    private ScheduledTaskLinesRepository repository;
    @Resource
    private ScheduledTaskLinesDEConverter deConverter;
    @Override
    public Page<ScheduledTaskLinesDTO> queryByPage(ScheduledTaskLinesQuery query) {
        query = deConverter.toCNNameScheduledTaskLinesQuery(query);
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "taskNumber");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<ScheduledTaskLines> page = repository.findAll(booleanBuilder, pageable);
        List<ScheduledTaskLinesDTO> scheduledTaskLinesDTOList = deConverter.toDtoNameByCName(page.getContent() );
        return new PageImpl<>(scheduledTaskLinesDTOList, page.getPageable(), page.getTotalElements());
    }

    @Override
    public void exportToExcel(ScheduledTaskLinesQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        //最多10万
        query.setPageSize(GlobalConstant.max_page_size);
        List<ScheduledTaskLinesDTO> content = queryByPage(query).getContent();
        String fileName = "计划任务控制台";
        String sheetName = fileName;
         fileName = fileName + "_" + DateUtils.formatDate(new Date(), "yyyy-MM-dd+HH:mm:ss");
         if (CollectionUtils.isNotEmpty(content)){
             content.stream().forEach(item->{
                  //如果item的log长度大于32767只截取30000(excel单元格对长度有限制)
                 if (StringUtils.isNotEmpty(item.getLog())){
                     if (item.getLog().length() > 32767) {
                         item.setLog(item.getLog().substring(0, 30000));
                     }
                 }

             });
         }
        ExcelUtils.export(response, fileName, sheetName, content, ScheduledTaskLineExcelExportDTO.class);
    }
    private BooleanBuilder buildWhere(ScheduledTaskLinesQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (StringUtils.isNotBlank(query.getTaskName())) {
            booleanBuilder.and(scheduledTaskLines.taskName.eq(query.getTaskName()));
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            booleanBuilder.and(scheduledTaskLines.status.eq(query.getStatus()));
        }
        if (StringUtils.isNotBlank(query.getRequestBy())) {
            booleanBuilder.and(scheduledTaskLines.requestBy.like(StringUtils.join("%", query.getRequestBy(), "%")));
        }
        if (query.getCompleteDateFrom() != null) {
            booleanBuilder.and(scheduledTaskLines.completeDate.goe(query.getCompleteDateFrom()));
        }
        if (query.getCompleteDateTo() != null) {
            booleanBuilder.and(scheduledTaskLines.completeDate.loe(query.getCompleteDateTo()));
        }
        if (query.getRequestDateFrom() != null) {
            booleanBuilder.and(scheduledTaskLines.requestDate.goe(query.getRequestDateFrom()));
        }
        if (query.getRequestDateTo() != null) {
            booleanBuilder.and(scheduledTaskLines.requestDate.loe(query.getRequestDateTo()));
        }
        return booleanBuilder;
    }

}
