package com.trinasolar.scp.baps.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.baps.domain.query.erp.SyncWipIssueQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.baps.service.feign.fallback.Cux3WipErpFeignFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
/**
 * ERP对接相关接口
 *
 * @author: darke
 * @create: 2022年8月31日09:10:27
 */

@FeignClient(name = FeignConstant.FEIGN_NAME_CUX3WIP_ERP, fallbackFactory = Cux3WipErpFeignFallbackFactory.class,  url = "${erp.cux3Wip.ipass.url}")
public interface Cux3WipErpFeign {
    /**
     * CUX3_WIP_物料事务处理报表
     *
     * @param cux3WipDto
     * @return
     */
    @PostMapping(value = "${erp.cux3Wip.url}", headers = {"Content-Type=application/json;charset=UTF-8", "tsl-clientid=${erp.cux3Wip.clientid}", "tsl-clientsecret=${erp.cux3Wip.clientsecret}"})
    JSONObject cux3WipPage(@RequestBody SyncWipIssueQuery cux3WipDto);
}
