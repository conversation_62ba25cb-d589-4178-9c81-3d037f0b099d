package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.*;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.dto.aps.RecordTransitionDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.LowEfficiencyCellPercentDTO;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.baps.domain.query.bbom.LowEfficiencyCellPercentQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineSaveDTO;
import com.trinasolar.scp.baps.domain.utils.*;
import com.trinasolar.scp.baps.service.feign.BbomFeign;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanRepository;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanVersionRepository;
import com.trinasolar.scp.baps.service.repository.CellPlanLineRepository;
import com.trinasolar.scp.baps.service.repository.CellPlanLineVersionRepository;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.baps.service.service.aps.ApsService;
import com.trinasolar.scp.baps.service.service.log.LogService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.trinasolar.scp.baps.domain.enums.SelectedEnum;
import com.trinasolar.scp.baps.domain.enums.SplitTypeEnum;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.ConfigCellAMinusQuery;
import com.trinasolar.scp.baps.domain.query.SiliconSliceSupplyLinesQuery;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import java.time.LocalDateTime;

@Slf4j
@Service("siliconSplitService")
@RequiredArgsConstructor
public class SiliconSplitServiceImpl implements SiliconSplitService {
    private final JPAQueryFactory jpaQueryFactory;

    private final CellPlanLineService cellPlanLineService;

    private final CellPlanLineRepository cellPlanLineRepository;

    private final CellPlanLineDEConvert cellPlanLineDEConvert;

    private final SiliconSplitRuleService siliconSplitRuleService;

    private final CellConversionFactorService cellConversionFactorService;

    private final ConfigCellAMinusPercentService configCellAMinusPercentService;

    private final SiliconSliceSupplyLinesService siliconSliceSupplyLinesService;

    private final CellItemCodeService itemCodeService;

    private final ApsService apsService;

    private final static String NOFINDMATCHRULE = "未匹配到电池分档规则";

    private final static Integer loppLimit = 3000;

    private final CellInstockPlanService cellInstockPlanService;

    private final CellInstockPlanRepository cellInstockPlanRepository;

    private final CellInstockPlanDEConvert cellInstockPlanDEConvert;

    private final CellItemCodeService cellItemCodeService;

    private final CellPlanLineVersionRepository cellPlanLineVersionRepository;

    private final CellInstockPlanVersionDEConvert cellInstockPlanVersionDEConvert;

    private final CellInstockPlanVersionRepository cellInstockPlanVersionRepository;

    private final LogService logService;

    private final CellPlanLineTotalService cellPlanLineTotalService;


    @Autowired
    @Qualifier("dataSyncThreadPool")
    ExecutorService threadPoolExecutor;

    private final RedissonClient redissonClient;
    private final CellPlanLineTotalDEConvert cellPlanLineTotalDEConvert;
    private final SiliconSplitRuleDEConvert siliconSplitRuleDeconvert;
    private final CellInstockPlanTotalDEConvert cellInstockPlanTotalDEConvert;
    private final BbomFeign bbomFeign;
    @Override
    public Page<CellPlanLineSiliconTotalDTO> query(CellPlanLineQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        //1、获取兆瓦系数
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        Sort sort = Sort.by(Sort.Direction.DESC, "workshop");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //2、获取投产计划数据
        List<CellPlanLineDTO> dtos = cellPlanLineService.query(query);
        Map<String, List<CellPlanLineDTO>> collect = dtos.stream().collect(Collectors.groupingBy(
               // dto -> Joiner.on(",").useForNull("无").join(
                dto ->  StringTools.joinWith(",",
                        dto.getIsOversea(),
                        dto.getBasePlace(),
                        dto.getWorkshop(),
                        dto.getCellsType(),
                        dto.getHTrace(),
                        dto.getProductionGrade(),
                        dto.getProcessCategory(),
                        dto.getWaferGrade(),
                        dto.getSiMfrs(),
                        dto.getTransparentDoubleGlass(),
                        dto.getMonth(),
                        dto.getBackFineGrid(),
                        dto.getFrontFineGrid(),
                        dto.getSiliconWaferThickness(),
                        dto.getSiliconWaferSize(),
                        dto.getSupplyMethod(),
                        dto.getCellSource(),
                        dto.getCertCode()
                )
        ));
        //3、获取collect所有key自然排序
        List<String> keys = collect.keySet().stream().sorted().collect(Collectors.toList());
        //4、准备分页参数
        Integer total = keys.size();
        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer endPos = Math.min(startPos + pageSize, total);
        //5、分页计算
        List<CellPlanLineSiliconTotalDTO> datas = new ArrayList<>();
        for (int i = startPos; i < endPos; i++) {
            String key = keys.get(i);
            CellPlanLineSiliconTotalDTO cellPlanLineSiliconTotalDTO = null;
            AtomicReference<BigDecimal> allQtyPc = new AtomicReference<>(BigDecimal.ZERO);
            List<CellPlanLineDTO> cellPlanLineDTOS = collect.getOrDefault(key, Lists.newArrayList());
            CellConversionFactorDTO cellConversionFactorDTO = null;//兆瓦系数
            for (CellPlanLineDTO cellPlanLineDTO : cellPlanLineDTOS) {
                if (Objects.isNull(cellConversionFactorDTO)) {
                    cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", cellPlanLineDTO.getIsOversea(), cellPlanLineDTO.getCellsType()));
                }
                if (Objects.isNull(cellPlanLineSiliconTotalDTO)) {
                    cellPlanLineSiliconTotalDTO = cellPlanLineTotalDEConvert.cellPlanLineDTOToCellPlanLineSiliconTotalDTO(cellPlanLineDTO);
                    cellPlanLineSiliconTotalDTO.setCellType(cellPlanLineDTO.getCellsType());
                }
                int day = cellPlanLineDTO.getStartTime().getDayOfMonth();
                BigDecimal val = ReflectUtil.invoke(cellPlanLineSiliconTotalDTO, "getD" + day);
                val = Optional.ofNullable(val).orElse(BigDecimal.ZERO);
                BigDecimal qtyPc = Optional.ofNullable(cellPlanLineDTO.getQtyPc()).orElse(BigDecimal.ZERO);

                BigDecimal sum = val.add(qtyPc);
                ReflectUtil.invoke(cellPlanLineSiliconTotalDTO, "setD" + day, sum);
                allQtyPc.set(allQtyPc.get().add(qtyPc));
            }
            cellPlanLineSiliconTotalDTO.setQtyThousandPc(allQtyPc.get());
            if (cellConversionFactorDTO != null) {
                if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                    BigDecimal mv = allQtyPc.get().divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                    cellPlanLineSiliconTotalDTO.setCellMv(mv);
                }

            } else {
                cellPlanLineSiliconTotalDTO.setCellMv(null);
            }
            BigDecimal allQtyPcValue = allQtyPc.get().setScale(2, RoundingMode.HALF_UP);
            cellPlanLineSiliconTotalDTO.setQtyThousandPc(allQtyPcValue);
            setScale(cellPlanLineSiliconTotalDTO);
            datas.add(cellPlanLineSiliconTotalDTO);
        }
        //翻译转换
        MyThreadLocal.get().setLang(oldLang);
        datas = cellPlanLineTotalDEConvert.toCellPlanLineSiliconTotalDTONameByCnName(datas);
        return new PageImpl(datas, pageable, total);
    }

    private void setScale(CellPlanLineSiliconTotalDTO cellPlanLineTotalDTO) {
        for (int i = 1; i <= 31; i++) {
            BigDecimal val = ReflectUtil.invoke(cellPlanLineTotalDTO, "getD" + i);
            if (Objects.nonNull(val)) {
                ReflectUtil.invoke(cellPlanLineTotalDTO, "setD" + i, val.setScale(2, RoundingMode.HALF_UP));
            }
        }
    }

    private static List<LocalDate> getDaysFromMonths(List<String> months) {
        List<LocalDate> days = months.stream().map(i -> {
            // 获取月份的第一天和最后一天
            LocalDate firstDay = LocalDate.parse(i + "-01", DateTimeFormatter.ofPattern("yyyyMM-dd"));
            LocalDate lastDay = firstDay.with(TemporalAdjusters.lastDayOfMonth());
            return DateUtil.getDayList(firstDay, lastDay);
        }).flatMap(Collection::stream).collect(Collectors.toList()).stream().sorted().collect(Collectors.toList());
        return days;
    }


    public Page<CellInstockPlanSiliconTotalDTO> queryBak(CellInstockPlanQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        List<CellInstockPlanSiliconTotalDTO> datas = new ArrayList<>();
        Sort sort = Sort.by(Sort.Direction.DESC, "workshop");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //获取入库计划数据
        List<CellInstockPlanDTO> dtos = cellInstockPlanService.query(query);
        Map<String, Map<String, List<CellInstockPlanDTO>>> collect = dtos.stream().collect(Collectors.groupingBy(
                CellInstockPlanDTO::getCellsType, Collectors.groupingBy(
                        dto -> StringTools.joinWith(",",
                                dto.getBasePlace(),
                                dto.getWorkshop(),
                                dto.getHTrace(),
                                dto.getProductionGrade(),
                                dto.getProcessCategory(),
                                dto.getWaferGrade(),
                                dto.getSiMfrs(),
                                dto.getTransparentDoubleGlass()
                        )
                )));
        collect.entrySet().forEach(cellTypeEntry -> {
            String celltype = cellTypeEntry.getKey();
            //MW转换系数对象
            final CellConversionFactorDTO[] cellConversionFactorDTO = {null};
            cellTypeEntry.getValue().entrySet().stream().forEach(dto -> {
                CellInstockPlanSiliconTotalDTO cellPlanLineSiliconTotalDTO = null;
                AtomicReference<BigDecimal> allQtyPc = new AtomicReference<>(BigDecimal.ZERO);
                for (CellInstockPlanDTO cellPlanLineDTO : dto.getValue()) {
                    if (cellConversionFactorDTO[0] == null) {
                        cellConversionFactorDTO[0] = mapCellConversionFactorDTO.get(StringTools.joinWith(",", cellPlanLineDTO.getIsOversea(), cellPlanLineDTO.getCellsType()));
                    }
                    if (Objects.isNull(cellPlanLineSiliconTotalDTO)) {
                        cellPlanLineSiliconTotalDTO = BeanUtil.copyProperties(cellPlanLineDTO, CellInstockPlanSiliconTotalDTO.class);
                        cellPlanLineSiliconTotalDTO.setCellType(celltype);
                        //  cellPlanLineSiliconTotalDTO.setMonth(query.getMonth());

                    }
                    int day = cellPlanLineDTO.getStartTime().getDayOfMonth();
                    BigDecimal val = ReflectUtil.invoke(cellPlanLineSiliconTotalDTO, "getD" + day);
                    val = Optional.ofNullable(val).orElse(BigDecimal.ZERO);
                    val = val.setScale(2, RoundingMode.HALF_UP);
                    BigDecimal qtyPc = Optional.ofNullable(cellPlanLineDTO.getQtyPc()).orElse(BigDecimal.ZERO);
                    qtyPc = qtyPc.setScale(2, RoundingMode.HALF_UP);
                    BigDecimal sum = val.add(qtyPc);
                    ReflectUtil.invoke(cellPlanLineSiliconTotalDTO, "setD" + day, sum);
                    allQtyPc.set(allQtyPc.get().add(qtyPc));
                }
                cellPlanLineSiliconTotalDTO.setQtyThousandPc(allQtyPc.get());
                if (cellConversionFactorDTO[0] != null) {
                    if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO[0].getConversionFactor()) != 0) {
                        BigDecimal mv = allQtyPc.get().divide(cellConversionFactorDTO[0].getConversionFactor(), 0, RoundingMode.HALF_UP);
                        cellPlanLineSiliconTotalDTO.setCellMv(mv);
                    }

                }
                BigDecimal allQtyPcValue = allQtyPc.get().setScale(2, RoundingMode.HALF_UP);
                cellPlanLineSiliconTotalDTO.setQtyThousandPc(allQtyPcValue);
                datas.add(cellPlanLineSiliconTotalDTO);
            });
        });
        List<CellInstockPlanSiliconTotalDTO> sortList = datas.stream().sorted(
                        Comparator.comparing(CellInstockPlanSiliconTotalDTO::getIsOversea).thenComparing(
                                        CellInstockPlanSiliconTotalDTO::getBasePlace
                                ).thenComparing(
                                        CellInstockPlanSiliconTotalDTO::getWorkshop
                                ).thenComparing(
                                        CellInstockPlanSiliconTotalDTO::getCellType
                                ).thenComparing(
                                        CellInstockPlanSiliconTotalDTO::getHTrace,
                                        Comparator.nullsLast(Comparator.naturalOrder())
                                ).thenComparing(
                                        CellInstockPlanSiliconTotalDTO::getProductionGrade,
                                        Comparator.nullsLast(Comparator.naturalOrder())
                                ).thenComparing(
                                        CellInstockPlanSiliconTotalDTO::getProcessCategory,
                                        Comparator.nullsLast(Comparator.naturalOrder())
                                ).
                                thenComparing(
                                        CellInstockPlanSiliconTotalDTO::getWaferGrade,
                                        Comparator.nullsLast(Comparator.naturalOrder())
                                ).thenComparing(
                                        CellInstockPlanSiliconTotalDTO::getSiMfrs,
                                        Comparator.nullsLast(Comparator.naturalOrder())
                                ).thenComparing(
                                        CellInstockPlanSiliconTotalDTO::getTransparentDoubleGlass,
                                        Comparator.nullsLast(Comparator.naturalOrder())
                                )
                )
                .skip((pageable.getPageNumber()) * pageable.getPageSize()).limit(pageable.getPageSize()).
                collect(Collectors.toList());
        //翻译
        MyThreadLocal.get().setLang(oldLang);
        sortList = cellInstockPlanTotalDEConvert.toCellInstockPlanSiliconTotalDTONameByCNName(sortList);
        return new PageImpl(sortList, pageable, datas.size());
    }

    @Override
    public Page<CellInstockPlanSiliconTotalDTO> query(CellInstockPlanQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        //1、获取兆瓦系数
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        Sort sort = Sort.by(Sort.Direction.DESC, "workshop");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //2、获取入库计划数据
        List<CellInstockPlanDTO> dtos = cellInstockPlanService.query(query);
        Map<String, List<CellInstockPlanDTO>> collect = dtos.stream().collect(Collectors.groupingBy(
                dto -> Joiner.on(",").useForNull("无").join(
                        dto.getIsOversea(),
                        dto.getBasePlace(),
                        dto.getWorkshop(),
                        dto.getCellsType(),
                        dto.getHTrace(),
                        dto.getProductionGrade(),
                        dto.getProcessCategory(),
                        dto.getWaferGrade(),
                        dto.getSiMfrs(),
                        dto.getTransparentDoubleGlass(),
                        dto.getAesthetics(),
                        dto.getMonth()
                )
        ));
        //3、获取collect的所有key后自然排序
        List<String> keys = collect.keySet().stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
        //4、准备分页参数
        Integer total = keys.size();
        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer endPos = Math.min(startPos + pageSize, total);
        //5、分页计算
        List<CellInstockPlanSiliconTotalDTO> datas = new ArrayList<>();
        for (int i = startPos; i < endPos; i++) {
            String key = keys.get(i);
            CellConversionFactorDTO cellConversionFactorDTO = null;
            List<CellInstockPlanDTO> cellInstockPlanDTOS = collect.getOrDefault(key, Lists.newArrayList());
            CellInstockPlanSiliconTotalDTO cellPlanLineSiliconTotalDTO = null;
            AtomicReference<BigDecimal> allQtyPc = new AtomicReference<>(BigDecimal.ZERO);
            for (CellInstockPlanDTO cellPlanLineDTO : cellInstockPlanDTOS) {
                if (cellConversionFactorDTO == null) {
                    cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", cellPlanLineDTO.getIsOversea(), cellPlanLineDTO.getCellsType()));
                }
                if (Objects.isNull(cellPlanLineSiliconTotalDTO)) {
                    cellPlanLineSiliconTotalDTO = BeanUtil.copyProperties(cellPlanLineDTO, CellInstockPlanSiliconTotalDTO.class);
                    cellPlanLineSiliconTotalDTO.setCellType(cellPlanLineDTO.getCellsType());
                }
                int day = cellPlanLineDTO.getStartTime().getDayOfMonth();
                BigDecimal val = ReflectUtil.invoke(cellPlanLineSiliconTotalDTO, "getD" + day);
                val = Optional.ofNullable(val).orElse(BigDecimal.ZERO);
                val = val.setScale(2, RoundingMode.HALF_UP);
                BigDecimal qtyPc = Optional.ofNullable(cellPlanLineDTO.getQtyPc()).orElse(BigDecimal.ZERO);
                qtyPc = qtyPc.setScale(2, RoundingMode.HALF_UP);
                BigDecimal sum = val.add(qtyPc);
                ReflectUtil.invoke(cellPlanLineSiliconTotalDTO, "setD" + day, sum);
                allQtyPc.set(allQtyPc.get().add(qtyPc));
            }
            cellPlanLineSiliconTotalDTO.setQtyThousandPc(allQtyPc.get());
            if (cellConversionFactorDTO != null) {
                if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                    BigDecimal mv = allQtyPc.get().divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                    cellPlanLineSiliconTotalDTO.setCellMv(mv);
                }
            }
            BigDecimal allQtyPcValue = allQtyPc.get().setScale(2, RoundingMode.HALF_UP);
            cellPlanLineSiliconTotalDTO.setQtyThousandPc(allQtyPcValue);
            datas.add(cellPlanLineSiliconTotalDTO);
        }
        //6、翻译转化
        MyThreadLocal.get().setLang(oldLang);
        datas = cellInstockPlanTotalDEConvert.toCellInstockPlanSiliconTotalDTONameByCNName(datas);
        return new PageImpl(datas, pageable, total);
    }

    @Override
    @CacheEvict(cacheNames = "CellPlanLineService_queryByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)

    public void siliconSplit(CellPlanLineQuery query, String type, boolean isCover) {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        ScheduledTaskLinesDTO task = null;
        if (StringUtils.isEmpty(query.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, query.getIsOversea(), query.getMonth(), "cellplan", "splite", type);
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行硅料厂家指定和硅片等级指定
                doSiliconsplit(query, type, isCover, task);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }


    }

    private void doSiliconsplit(CellPlanLineQuery query, String type, boolean isCover, ScheduledTaskLinesDTO task) {
        try {
            if (Objects.equals(type, SiliconSplitTypeConstant.SIMFRS)) {
                task = logService.createLogTask(LovHeaderCodeConstant.BAPS_SIMFRS_SPLIT);
            } else {
                task = logService.createLogTask(LovHeaderCodeConstant.BAPS_WAFER_GRADE_SPLIT);
            }
            //国内硅料厂家拆分
            final CellPlanLineVersion[] cellPlanLineVersionSaveDTOOfSimfrsInland = {null};
            //国内硅片等级拆分
            CellPlanLineVersion[] cellPlanLineVersionSaveDTOOfWaferGradeInland = {null};
            //海外硅料厂家拆分
            CellPlanLineVersion[] cellPlanLineVersionSaveDTOOfSimfrsOversea = {null};
            //海外硅片等级拆分
            CellPlanLineVersion[] cellPlanLineVersionSaveDTOOfWaferGradeOversea = {null};
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行处理的数据", query.getMonth()));
            //1、读取要进行拆分的数据
            List<CellPlanLineDTO> cellPlanLineList = cellPlanLineDEConvert.toDto(getSplitDatas(query, type, isCover));
            //获取拆分规则
            //2、取出当月的拆分规则
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段2：获取硅片拆分的规则"));
            List<SiliconSplitRule> rules = siliconSplitRuleService.getRulesByMonthType(query, type);
            rules = siliconSplitRuleDeconvert.toSiliconSplitRuleCNName(rules);

            //3、构建拆分规则map
            Map<String, List<SiliconSplitRule>> ruleMap = rules.stream().collect(Collectors.groupingBy(item -> StringTools.joinWith(",", item.getWorkshop(), item.getCellType())));
            //4、构建投产计划的map
            Map<String, List<CellPlanLineDTO>> collect = cellPlanLineList.stream().collect(Collectors.groupingBy(item -> StringTools.joinWith(",", item.getWorkshop(), item.getCellsType())));
            //5、进行拆分
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段3：进行拆分处理"));
            List<CellPlanLineDTO> datas = Lists.newArrayList();
            collect.entrySet().stream().forEach(entry -> {
                String key = entry.getKey();
                //5.1获得对应拆分规则集合
                List<SiliconSplitRule> siliconSplitRules = ruleMap.get(key);
                if (siliconSplitRules != null) {
                    //5.2 同车间同电池类型的投产计划依次处理
                    entry.getValue().stream().forEach(cellPlanLine -> {
                        //5.3 获取投产计划对象对应的具体规则对象
                        List<SiliconSplitRule> ruleList = siliconSplitRules.stream().filter(item -> {
                            return cellPlanLine.getStartTime().toLocalDate().isAfter(item.getStartDate())
                                    && cellPlanLine.getStartTime().toLocalDate().isBefore(item.getEndDate())
                                    || cellPlanLine.getStartTime().toLocalDate().equals(item.getStartDate())
                                    || cellPlanLine.getStartTime().toLocalDate().equals(item.getEndDate());
                        }).collect(Collectors.toList());
                        //找到了具体的分片规则
                        if (CollectionUtils.isNotEmpty(ruleList)) {
                            SiliconSplitRule rule = ruleList.get(0);
                            //5.4 硅料厂家拆分
                            if (SiliconSplitTypeConstant.SIMFRS.equals(type)) {
                                //硅料厂家类型拆分
                                Long id = MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.SI_MFRS, rule.getSiMfrs());
                                cellPlanLine.setSiMfrsId(id);
                                cellPlanLine.setSiMfrs(rule.getSiMfrs());
                                cellPlanLine.setIsSiMfrs(1);
                                //版本管理记录开始
                                if (cellPlanLineVersionSaveDTOOfSimfrsInland[0] == null) {
                                    if (cellPlanLine.getIsOversea().equals(OverseaConstant.INLAND)) {
                                        cellPlanLineVersionSaveDTOOfSimfrsInland[0] = cellPlanLineVersionRepository.selectByVersion(cellPlanLine.getIsOversea(), query.getMonth(), cellPlanLine.getVersion());
                                        if (cellPlanLineVersionSaveDTOOfSimfrsInland[0] == null) {
                                            throw new BizException(String.format("（%s）未点击“查询”创建%s月的投产计划，请点击查询后再进行硅料厂家指定操作", cellPlanLine.getIsOversea(), query.getMonth()));
                                        }
                                        cellPlanLineVersionSaveDTOOfSimfrsInland[0].setIsSiMfrs(1);
                                        cellPlanLineVersionRepository.save(cellPlanLineVersionSaveDTOOfSimfrsInland[0]);
                                    }
                                }
                                if (cellPlanLineVersionSaveDTOOfSimfrsOversea[0] == null) {
                                    if (cellPlanLine.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                                        cellPlanLineVersionSaveDTOOfSimfrsOversea[0] = cellPlanLineVersionRepository.selectByVersion(cellPlanLine.getIsOversea(), query.getMonth(), cellPlanLine.getVersion());
                                        if (cellPlanLineVersionSaveDTOOfSimfrsOversea[0] == null) {
                                            throw new BizException(String.format("（%s）未点击“查询”创建%s月的投产计划，请点击查询后再进行硅料厂家指定操作", cellPlanLine.getIsOversea(), query.getMonth()));
                                        }
                                        cellPlanLineVersionSaveDTOOfSimfrsOversea[0].setIsSiMfrs(1);
                                        cellPlanLineVersionRepository.save(cellPlanLineVersionSaveDTOOfSimfrsOversea[0]);
                                    }
                                }
                                //版本管理记录结束
                                datas.add(cellPlanLine);
                            } else if (SiliconSplitTypeConstant.WAFERGRADE.equals(type)) {
                                //5.4 硅片等级类型拆分
                                Long id = MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.WAFER_GRADE, rule.getWaferGrade());
                                cellPlanLine.setWaferGradeId(id);
                                cellPlanLine.setWaferGrade(rule.getWaferGrade());
                                //数量=当天的数量/电池良率
                                BigDecimal qty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(cellPlanLine.getQtyPc());
                                if (rule.getCellFine() != null && rule.getCellFine().compareTo(BigDecimal.ZERO) != 0) {
                                    cellPlanLine.setQtyPc(qty.divide(rule.getCellFine(), 6, RoundingMode.HALF_UP));
                                    cellPlanLine.setOldQtyPc(qty);
                                    cellPlanLine.setIsWaferGrade(1);
                                    cellPlanLine.setCellFine(rule.getCellFine());
                                }
                                //版本管理记录开始
                                if (cellPlanLineVersionSaveDTOOfWaferGradeInland[0] == null) {
                                    if (cellPlanLine.getIsOversea().equals(OverseaConstant.INLAND)) {
                                        cellPlanLineVersionSaveDTOOfWaferGradeInland[0] = cellPlanLineVersionRepository.selectByVersion(cellPlanLine.getIsOversea(), query.getMonth(), cellPlanLine.getVersion());
                                        if (cellPlanLineVersionSaveDTOOfWaferGradeInland[0] == null) {
                                            throw new BizException(String.format("（%s）未点击“查询”创建%s月的投产计划，请点击查询后再进行硅片等级指定操作", cellPlanLine.getIsOversea(), query.getMonth()));
                                        }
                                        cellPlanLineVersionSaveDTOOfWaferGradeInland[0].setIsWaferGrade(1);
                                        cellPlanLineVersionRepository.save(cellPlanLineVersionSaveDTOOfWaferGradeInland[0]);
                                    }
                                }
                                if (cellPlanLineVersionSaveDTOOfWaferGradeOversea[0] == null) {
                                    if (cellPlanLine.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                                        cellPlanLineVersionSaveDTOOfWaferGradeOversea[0] = cellPlanLineVersionRepository.selectByVersion(cellPlanLine.getIsOversea(), query.getMonth(), cellPlanLine.getVersion());
                                        if (cellPlanLineVersionSaveDTOOfWaferGradeOversea[0] == null) {
                                            throw new BizException(String.format("（%s）未点击“查询”创建%s月的投产计划，请点击查询后再进行硅片等级指定操作", cellPlanLine.getIsOversea(), query.getMonth()));
                                        }
                                        cellPlanLineVersionSaveDTOOfWaferGradeOversea[0].setIsWaferGrade(1);
                                        cellPlanLineVersionRepository.save(cellPlanLineVersionSaveDTOOfWaferGradeOversea[0]);
                                    }
                                }
                                //版本管理记录结束
                                datas.add(cellPlanLine);
                            }

                        }
                    });

                }

            });
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段4：数据存储"));
            if (CollectionUtils.isNotEmpty(datas)) {
                cellPlanLineRepository.saveAll(cellPlanLineDEConvert.toEntity(datas));
            }
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");

        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    /**
     * 手动设置硅片等级
     *
     * @param listDto
     */
    @Override
    @CacheEvict(cacheNames = "CellPlanLineService_queryByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)

    public void handSetWaferGrade(HandSetWaferGradeListDto listDto) {

        List<CellPlanLineDTO> datas = new ArrayList<>();
        listDto.getDtos().stream().forEach(dto -> {
            CellPlanLine plan = cellPlanLineRepository.getOne(dto.getId());
            CellPlanLineDTO cellPlanLine = cellPlanLineDEConvert.toDto(plan);

            BigDecimal qty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(cellPlanLine.getQtyPc());
            BigDecimal cellFine = dto.getCellFine().divide(new BigDecimal(100), 6, RoundingMode.HALF_UP);
            BigDecimal result = qty.divide(cellFine, 6, RoundingMode.HALF_UP);
            cellPlanLine.setQtyPc(result);
            cellPlanLine.setOldQtyPc(qty);
            LovLineDTO lovLineDTO = LovUtils.getByLang(LovHeaderCodeConstant.WAFER_GRADE, dto.getWaferGrade(), LovHeaderCodeConstant.LANGUAGE_CN);
            if (lovLineDTO != null) {
                cellPlanLine.setWaferGrade(lovLineDTO.getLovName());
                cellPlanLine.setWaferGradeId(lovLineDTO.getLovLineId());
                cellPlanLine.setIsWaferGrade(1);
                cellPlanLine.setCellFine(cellFine);
            }
            datas.add(cellPlanLine);


        });
        cellPlanLineRepository.saveAll(cellPlanLineDEConvert.toEntity(datas));


    }

    /**
     * 手动设置硅料厂家
     *
     * @param listDto
     */
    @Override
    @CacheEvict(cacheNames = "CellPlanLineService_queryByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)

    public void handSetSiMfrs(HandSetSiMfrsListDto listDto) {
        List<CellPlanLineDTO> datas = new ArrayList<>();
        listDto.getDtos().stream().forEach(dto -> {
            CellPlanLine cellPlanLine = cellPlanLineRepository.getOne(dto.getId());
            CellPlanLineDTO cellPlanLineDTO = cellPlanLineDEConvert.toDto(cellPlanLine);

            LovLineDTO lovLineDTO = LovUtils.getByLang(LovHeaderCodeConstant.SI_MFRS, dto.getSiMfrs(), LovHeaderCodeConstant.LANGUAGE_CN);
            if (lovLineDTO != null) {
                cellPlanLineDTO.setSiMfrs(lovLineDTO.getLovName());
                cellPlanLineDTO.setSiMfrsId(lovLineDTO.getLovLineId());
                cellPlanLineDTO.setIsSiMfrs(1);
            }
            datas.add(cellPlanLineDTO);


        });
        cellPlanLineRepository.saveAll(cellPlanLineDEConvert.toEntity(datas));

    }

    private void setZero(CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto) {
        cellInstockPlanVersionInlandDto.setIsConfirmPlan(0);
        cellInstockPlanVersionInlandDto.setIsSendEmail(0);
        cellInstockPlanVersionInlandDto.setIsASplit(0);
        cellInstockPlanVersionInlandDto.setIsTransparentDoubleGlass(0);
        cellInstockPlanVersionInlandDto.setIsGradeRule(0);
    }

    /**
     * 执行A-拆分
     *
     * @param query
     */
    @Override
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)

    public void siliconASplit(CellInstockPlanQuery query) {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(query.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner redis_joiner = Joiner.on("-");
        String localKey = redis_joiner.join(RedisLockKey.BAPS_PREFIX, query.getIsOversea(), query.getMonth(), "instock", "Asplite");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行A-拆分
                doSiliconASplit(query);
            } catch (Exception e) {
                throw e;
            } finally {

                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }


    }

    @Override
    public void siliconLowEfficiencySplit(CellInstockPlanQuery query) {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(query.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner redis_joiner = Joiner.on("-");
        String localKey = redis_joiner.join(RedisLockKey.BAPS_PREFIX, query.getIsOversea(), query.getMonth(), "instock", "LEsplite");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行低效拆分
                doSiliconLESplit(query);
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }
    }

    private void doSiliconLESplit(CellInstockPlanQuery query) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_LE_SPLIT);
            //1、获取要进行拆分的数据
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行拆分的数据", query.getMonth()));
            List<CellInstockPlan> splitDatasCollect = getSplitDatasByCellInstock(query, SiliconSplitTypeConstant.LESPLIT, false);
            List<CellInstockPlanDTO> splitDatas = cellInstockPlanDEConvert.toDto(splitDatasCollect);
            splitDatas = splitDatas.stream().filter(item -> {
                return !Objects.equals(item.getDataType(), 1);
            }).collect(Collectors.toList());
            //2、获取低效比例
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING,"阶段2：获取低效比例数据");
            LowEfficiencyCellPercentQuery lowEfficiencyCellPercentQuery = new LowEfficiencyCellPercentQuery();
            List<LowEfficiencyCellPercentDTO> configCellAMinusPercentDTOs = siliconLESplitRules(lowEfficiencyCellPercentQuery);
            Map<String, LowEfficiencyCellPercentDTO> configCellAMinusPercentDTOMap = configCellAMinusPercentDTOs.stream().collect(Collectors.toMap(
                    entity -> StringTools.joinWith(",", entity.getYear(), entity.getBasePlace(), entity.getWorkshop(), entity.getCellType()),
                    Function.identity(),
                    (existing, replacement) -> existing
            ));
            //构建电池类型到电池型号的map
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING,"阶段3：构建电池类型到电池型号对应关系");
            Map<String, String> mapCelltypeToCellModel = MapStrutUtil.getMapCelltypeToCellModel();
            //3、进行拆分
            String month = query.getMonth();
            String finalYear = month.substring(0, 4);
            //String month_value = month.substring(4, 6).contains("0") ? month.substring(5, 6) : month.substring(4, 6);
            int month_value = DateUtil.month2LocalDate(month).getMonthValue();
            List<CellInstockPlanDTO> datas = new ArrayList<>();
            List<CellInstockPlanDTO> datasLE = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段3：开始进行低效拆分");
            Set<String> configCellAMinusSet = new LinkedHashSet<>();
            for (CellInstockPlanDTO cellPlanLine : splitDatas) {
                LovLineDTO lovLineDTO = LovUtils.get(cellPlanLine.getCellsTypeId());
                String cellTypeLovValue = Optional.ofNullable(lovLineDTO).map(LovLineDTO::getLovValue).orElse("");
                String key = StringTools.joinWith(",", finalYear, cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(),cellTypeLovValue);
                //A-比例系数=aop比例系数+片源A-系数
                BigDecimal percent_value = BigDecimal.ZERO;
                //3.1获取aop系数对象
                LowEfficiencyCellPercentDTO result = configCellAMinusPercentDTOMap.get(key);
                if (result != null) {
                    String percent_str = ReflectUtil.invoke(result, "getM" + month_value + "Percent");
                    //系数值
                    BigDecimal value = MapStrutUtil.removePercentage(percent_str, 4);
                    if (value != null) {
                        percent_value = percent_value.add(value);
                    } else {
                        //记录没有bbom的低效比例系数
                        configCellAMinusSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType(), "没有对应的低效比例系数！请联系bbom管理维护低效比例。"));
                    }
                } else {
                    configCellAMinusSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType(), "没有对应的低效比例系数！请联系bbom管理维护低效比例。"));
                }

                if (percent_value.compareTo(BigDecimal.ZERO) != 0) {
                    //3.2 根据系数值进行拆分
                    BigDecimal qty = cellPlanLine.getQtyPc();
                    BigDecimal oldQty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(qty);
                    BigDecimal a_qty = qty.multiply(percent_value);
                    BigDecimal old_a_qty = oldQty.multiply(percent_value);
                    qty = qty.subtract(a_qty);
                    oldQty = oldQty.subtract(old_a_qty);
                    cellPlanLine.setIsLESplit(1);
                    CellInstockPlanDTO cellPlanLineLE = BeanUtil.copyProperties(cellPlanLine, CellInstockPlanDTO.class);
                    cellPlanLine.setQtyPc(qty);
                    cellPlanLine.setOldQtyPc(oldQty);
                    cellPlanLineLE.setQtyPc(a_qty);
                    cellPlanLineLE.setOldQtyPc(old_a_qty);
                    cellPlanLineLE.setProductionGrade("Q1低效");
                    cellPlanLineLE.setIsLESplit(1);
                    cellPlanLineLE.setItemCode(null);
                    datas.add(cellPlanLine);
                    datasLE.add(cellPlanLineLE);

                }
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段4：调用bbom接口获取低效数据对应的料号");
            //2.2 获取透明双玻数据对应的料号
            //Map<Long, String> itemCodesMap = cellItemCodeService.getItemCodesByCellInstockPlan(cellInstockPlanDEConvert.toEntity(datasLE));
            CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto = null;
            CellInstockPlanVersionDTO cellInstockPlanVersionOverseaDto = null;
            //2.3 对透明双玻数据进行料号匹配
            for (CellInstockPlanDTO cellInstockPlan : datasLE) {
                long id = cellInstockPlan.getId();
                cellInstockPlan.setId(null);
               /*  if (itemCodesMap != null) {
                    if (StringUtils.isEmpty(itemCodesMap.get(id))) {
                        //构建料号匹配失败信息
                        String msg = getFailMsg(cellInstockPlan);
                        logService.addLog(task, ScheduleTaskStatusEnum.WARN, msg);
                    }
                    //老逻辑不赋值料号,由新方法触发料号匹配,并回写料号
//                    cellInstockPlan.setItemCode(itemCodesMap.get(id));

                } else {
                    //构建料号匹配失败信息
                    String msg = getFailMsg(cellInstockPlan);
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, msg);
                } */
                //入库版本记录开始
                CellInstockPlanDTO item = cellInstockPlan;
                if (cellInstockPlanVersionInlandDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.INLAND)) {

                        cellInstockPlanVersionInlandDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionInlandDto.setIsLESplit(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionInlandDto));

                    }
                }
                if (cellInstockPlanVersionOverseaDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                        cellInstockPlanVersionOverseaDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionOverseaDto.setIsLESplit(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionOverseaDto));
                    }
                }
                //入库版本记录结束
            }
            //没有低效比例系数的数据进行日志记录
            if (CollectionUtils.isNotEmpty(configCellAMinusSet)) {
                for (String item : configCellAMinusSet
                ) {
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, item);
                }
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING,"阶段5：拆后数据存储");
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datas));
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datasLE));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");
        } catch (Exception exception) {
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    private void doSiliconASplit(CellInstockPlanQuery query) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_A_SPLIT);
            //1、获取要进行拆分的数据
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行拆分的数据", query.getMonth()));
            List<CellInstockPlan> splitDatasCollect = getSplitDatasByCellInstock(query, SiliconSplitTypeConstant.ASPLIT, false);
            List<CellInstockPlanDTO> splitDatas = cellInstockPlanDEConvert.toDto(splitDatasCollect);
            splitDatas = splitDatas.stream().filter(item -> {
                return !Objects.equals(item.getDataType(), 1);
            }).collect(Collectors.toList());
            //2、获取A-比例
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段2：获取A-比例数据"));
            CellAMinusDto cellAMinusDto = BeanUtil.copyProperties(query, CellAMinusDto.class);
            cellAMinusDto.setCellType(query.getCellsType());
            List<ConfigCellAMinusPercentDTO> configCellAMinusPercentDTOs = siliconASplitRules(cellAMinusDto);
            //构建一个map方便查询使用
            LinkedHashMap<String, ConfigCellAMinusPercentDTO> configCellAMinusPercentDTOMap = configCellAMinusPercentDTOs.stream().collect(Collectors.toMap(
                    entity -> {
                        return StringTools.joinWith(",", entity.getYear(), entity.getBasePlace(), entity.getWorkshop(), entity.getCellModel());
                    },
                    Function.identity(),
                    (existing, replacement) -> existing,
                    LinkedHashMap::new
            ));
            //构建电池类型到电池型号的map
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段3：构建电池类型到电池型号对应关系"));
            Map<String, String> mapCelltypeToCellModel = MapStrutUtil.getMapCelltypeToCellModel();
            //3、进行拆分
            String month = query.getMonth();
            String finalYear = month.substring(0, 4);
            //String month_value = month.substring(4, 6).contains("0") ? month.substring(5, 6) : month.substring(4, 6);
            int month_value = DateUtil.month2LocalDate(month).getMonthValue();
            List<CellInstockPlanDTO> datas = new ArrayList<>();
            List<CellInstockPlanDTO> datasA = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段3：开始进行A-拆分"));
            Set<String> configCellAMinusSet = new LinkedHashSet<>();
            Long LovId = null;
            LovLineDTO lovLineDTO = LovUtils.get(LovHeaderCodeConstant.PRODUCTION_GRADE, "A-");
            if (lovLineDTO != null) {
                LovId = lovLineDTO.getLovLineId();
            }
            LovLineDTO lovProcessCategory = LovUtils.get(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY, "无");
            LovLineDTO lovCellHtraceWu = LovUtils.get(LovHeaderCodeConstant.H_TRACE, "无");
            LovLineDTO lovCellSourceWu = LovUtils.get(LovHeaderCodeConstant.CELL_SOURCE, "无");
            for (CellInstockPlanDTO cellPlanLine : splitDatas) {
                String cellModel = mapCelltypeToCellModel.get(cellPlanLine.getCellsType());
                String key = StringTools.joinWith(",", finalYear, cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellModel);
                //A-比例系数=aop比例系数+片源A-系数
                BigDecimal percent_value = BigDecimal.ZERO;
                //3.1获取aop系数对象
                ConfigCellAMinusPercentDTO result = configCellAMinusPercentDTOMap.get(key);
                if (result != null) {
                    String percent_str = ReflectUtil.invoke(result, "getM" + month_value + "Percent");
                    //系数值
                    BigDecimal value = MapStrutUtil.removePercentage(percent_str, 4);
                    if (value != null) {
                        percent_value = percent_value.add(value);
                    } else {
                        //记录没有aop的A-比例系数
                        configCellAMinusSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellModel, "没有对应的A-比例系数！请联系AOP管理维护自制A-比例。"));
                    }
                } else {
                    configCellAMinusSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellModel, "没有对应的A-比例系数！请联系AOP管理维护自制A-比例。"));
                }
                //3.1.1获取片源A-系数
                if (cellPlanLine.getWaferGradeRatio() != null) {
                    if (cellPlanLine.getWaferGradeRatio().compareTo(BigDecimal.ZERO) != 0) {
                        percent_value = percent_value.add(cellPlanLine.getWaferGradeRatio());
                    }

                }
                if (null != percent_value && percent_value.compareTo(BigDecimal.ZERO) != 0) {
                    //3.2 根据系数值进行A-拆分

                    BigDecimal qty = cellPlanLine.getQtyPc();
                    BigDecimal oldQty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(qty);
                    BigDecimal a_qty = qty.multiply(percent_value);
                    BigDecimal old_a_qty = oldQty.multiply(percent_value);
                    qty = qty.subtract(a_qty);
                    oldQty = oldQty.subtract(old_a_qty);
                    cellPlanLine.setIsASplit(1);
                    CellInstockPlanDTO cellPlanLineA = BeanUtil.copyProperties(cellPlanLine, CellInstockPlanDTO.class);
                    cellPlanLine.setQtyPc(qty);
                    cellPlanLine.setOldQtyPc(oldQty);
                    cellPlanLineA.setQtyPc(a_qty);
                    cellPlanLineA.setOldQtyPc(old_a_qty);
                    cellPlanLineA.setProductionGradeId(LovId);
                    cellPlanLineA.setProductionGrade("A-");
                    cellPlanLineA.setIsASplit(1);
                    cellPlanLineA.setItemCode(null);
                    //对A-数据预处理（目的能匹配料号）
                    changeAData(lovCellHtraceWu, lovCellSourceWu, lovProcessCategory, cellPlanLineA);
                    datas.add(cellPlanLine);
                    datasA.add(cellPlanLineA);

                }
            }
            //3.3 没有A-比例系数的数据进行日志记录
            if (CollectionUtils.isNotEmpty(configCellAMinusSet)) {
                for (String item : configCellAMinusSet
                ) {
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, item);
                }
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段4：调用bbom接口获取A-数据对应的料号"));
            //3.4 获取A-数据的料号
            //Map<Long, String> itemCodesMap = cellItemCodeService.getItemCodesByCellInstockPlan(cellInstockPlanDEConvert.toEntity(datasA));
            CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto = null;
            CellInstockPlanVersionDTO cellInstockPlanVersionOverseaDto = null;
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段5：A-数据匹配料号"));
            //Set<String> itemCodeMateSet = new HashSet<>();
            Joiner joiner = Joiner.on("-").skipNulls();
            //3.4 A-数据进行料号匹配
            for (CellInstockPlanDTO cellInstockPlan : datasA
            ) {
                Long id = cellInstockPlan.getId();
                cellInstockPlan.setId(null);
                /* String key = joiner.join(cellInstockPlan.getBasePlace(), cellInstockPlan.getWorkshop(),
                        cellInstockPlan.getWorkunit(), cellInstockPlan.getCellsType(),
                        "H追溯：" + cellInstockPlan.getHTrace(), "美学:" + cellInstockPlan.getAesthetics(),
                        "透明双玻:" + cellInstockPlan.getTransparentDoubleGlass(), "小区域国家:" + cellInstockPlan.getRegionalCountry(),
                        "片源种类:" + cellInstockPlan.getCellSource(),
                        "硅片等级:" + cellInstockPlan.getWaferGrade(), "电池厂家:" + cellInstockPlan.getCellMfrs(),
                        "硅料厂家:" + cellInstockPlan.getSiMfrs(), "网版厂家:" + cellInstockPlan.getScreenPlateMfrs(), "银浆厂家:" + cellInstockPlan.getSilverPulpMfrs(),
                        "低阻:" + cellInstockPlan.getLowResistance(), "需求地:" + cellInstockPlan.getDemandBasePlace(),
                        "加工类型:" + cellInstockPlan.getProcessCategory(), "产品等级:" + cellInstockPlan.getProductionGrade(), "记录没有匹配上对应的5A料号");
                if (itemCodesMap != null) {
                    if (StringUtils.isEmpty(itemCodesMap.get(id))) {
                        itemCodeMateSet.add(key);
                    }
                    //老逻辑不赋值料号,由新方法触发料号匹配,并回写料号
//                    cellInstockPlan.setItemCode(itemCodesMap.get(id));

                } else {
                    itemCodeMateSet.add(key);
                } */
                //入库计划版本记录开始
                CellInstockPlanDTO item = cellInstockPlan;
                if (cellInstockPlanVersionInlandDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.INLAND)) {

                        cellInstockPlanVersionInlandDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionInlandDto.setIsASplit(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionInlandDto));

                    }
                }
                if (cellInstockPlanVersionOverseaDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                        cellInstockPlanVersionOverseaDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionOverseaDto.setIsASplit(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionOverseaDto));
                    }
                }
                //入库计划版本记录结束
            }
           /*  //3.5 没有匹配上料号日志记录
            if (CollectionUtils.isNotEmpty(itemCodeMateSet)) {
                for (String item : itemCodeMateSet
                ) {
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, item);
                }
            } */
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段6：拆后数据存储"));
            //3.6 数据存储
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datas));

            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datasA));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");
        } catch (Exception exception) {
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    private void changeAData(LovLineDTO lovCellHtraceWu, LovLineDTO lovCellSourceWu, LovLineDTO lovProcessCategory, CellInstockPlanDTO dtoA) {
        //(5月9日添加的条件)
        //1、A-拆分时，对应拆分出的A-行数据：如果电池类型的 P/N型（电池类型LOV） 等于 P型 ，那么把 H追溯 改为 无 。
        //2、A-拆分时，对应拆分出的A-行数据：把 片源种类 改成 “无”。
        //3、A-拆分时，对应拆分出的A-行数据：如果电池型号（电池类型LOV）为 210R-N，把 H追溯 改为 无。 。
        //4、片源为无
        dtoA.setCellSource("无");
        dtoA.setCellSourceId(lovCellSourceWu.getLovLineId());
        if (Objects.equals(dtoA.getHTrace(), "H")) {
            Long cellTypeId = dtoA.getCellsTypeId();
            LovLineDTO lovCellType = LovUtils.get(LovHeaderCodeConstant.BATTERY_TYPE, cellTypeId);
            if (lovCellType != null &&
                    (Objects.equals(lovCellType.getAttribute2(), LovHeaderCodeConstant.BATTERY_TYPE_PN_P)
                            || Objects.equals(lovCellType.getAttribute7(), LovHeaderCodeConstant.BATTERY_TYPE_MODEL_210RN)
                    )
            ) {
                dtoA.setHTrace("无");
                dtoA.setHTraceId(lovCellHtraceWu.getLovLineId());
            }

        }
        //(5月10日添加的条件)
        if (StringUtils.isNotEmpty(dtoA.getProcessCategory())) {
            dtoA.setProcessCategory("无");
            dtoA.setProcessCategoryId(Optional.ofNullable(lovProcessCategory.getLovLineId()).orElse(null));
        }
    }

    /**
     * 获取A-比例
     *
     * @param dto
     * @return
     */
    @Override
    public List<ConfigCellAMinusPercentDTO> siliconASplitRules(CellAMinusDto dto) {

        ConfigCellAMinusQuery configCellAMinusQuery = new ConfigCellAMinusQuery();

        String month = dto.getMonth();
        String year = null;
        if (StringUtils.isNotEmpty(month)) {
            year = month.substring(0, 4);
            configCellAMinusQuery.setYear(year);
        }
        if (StringUtils.isNotEmpty(dto.getBasePlace())) {
            configCellAMinusQuery.setBasePlace(dto.getBasePlace());
        }
        if (StringUtils.isNotEmpty(dto.getWorkshop())) {
            configCellAMinusQuery.setWorkshop(dto.getWorkshop());
        }
        if (StringUtils.isNotEmpty(dto.getCellType())) {
            //依据电池类型获取电池型号
            Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BATTERY_TYPE);
            LovLineDTO lovLineDTO = allByHeaderCode.get(dto.getCellType());
            if (lovLineDTO != null) {
                //获取电池型号Id
                if (lovLineDTO.getAttribute7() != null) {
                    //依据电池型号Id找电池型号
                    LovLineDTO lovSeries = LovUtils.get(Long.parseLong(lovLineDTO.getAttribute7()));
                    if (lovSeries != null) {
                        configCellAMinusQuery.setCellModel(lovSeries.getLovValue());
                    }
                }
            }

        }
        configCellAMinusQuery.setPageNumber(1);
        configCellAMinusQuery.setPageSize(GlobalConstant.max_page_size);
        return configCellAMinusPercentService.getAll(configCellAMinusQuery);
    }

    @Override
    public List<LowEfficiencyCellPercentDTO> siliconLESplitRules(LowEfficiencyCellPercentQuery query) {
        return bbomFeign.list(query).getBody().getData();
    }

    /**
     * 加工类型拆分
     *
     * @param dto
     */
    @CacheEvict(cacheNames = "CellPlanLineService_queryByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CellPlanLineDTO> processCategorySplit(ProcessCategorySplitDto dto, String... isHand) {
        if (StringUtils.isEmpty(dto.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        List<CellPlanLineDTO> allPlanLines = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getIsOversea())) {
            List<CellPlanLineDTO> planLines = processCategorySplitByIsOvesea(dto, isHand);
            if (CollectionUtils.isNotEmpty(planLines)) {
                allPlanLines.addAll(planLines);
            }
        } else {
            dto.setIsOversea(OverseaConstant.INLAND);
            List<CellPlanLineDTO> planLinesInland = processCategorySplitByIsOvesea(dto, isHand);
            if (CollectionUtils.isNotEmpty(planLinesInland)) {
                allPlanLines.addAll(planLinesInland);
            }
            dto.setIsOversea(OverseaConstant.OVERSEA);
            List<CellPlanLineDTO> planLinesOutland = processCategorySplitByIsOvesea(dto, isHand);
            if (CollectionUtils.isNotEmpty(planLinesOutland)) {
                allPlanLines.addAll(planLinesOutland);
            }
        }
        return allPlanLines;
    }


    /**
     * 依据国内海外进行加工类型数据指定
     *
     * @param dto
     * @return
     */
    private List<CellPlanLineDTO> processCategorySplitByIsOvesea(ProcessCategorySplitDto dto, String... isHand) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, dto.getIsOversea(), "cellplan", " processcategorysplit");

        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                List<CellPlanLineDTO> dtos = doProcessCategorySplitByIsOvesea(dto, isHand);
                //翻译
                MyThreadLocal.get().setLang(oldLang);
                dtos = cellPlanLineDEConvert.toCellPlanLineDTONameFromCNName(dtos);
                return dtos;
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }

        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }


    }

    /**
     * 执行透明双玻拆分
     *
     * @param dto
     */
    @Override
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)

    public void siliconTransparentDoubleGlassSplit(TransparentDoubleGlassSplitDto dto) {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(dto.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, dto.getIsOversea(), dto.getMonth(), "instock", "transparentDoubleGlassSplit");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行透明双玻拆分
                doSiliconTransparentDoubleGlassSplit(dto);
            } catch (Exception e) {
                throw e;
            } finally {

                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }


    }

    private void doSiliconTransparentDoubleGlassSplit(TransparentDoubleGlassSplitDto dto) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_TRANSPARENTDOUBLEGLASS_SPLIT);
            //1、获取要进行透明双玻的处理的数据
            CellInstockPlanQuery query = BeanUtil.copyProperties(dto, CellInstockPlanQuery.class, "rate");
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行拆分的数据", query.getMonth()));
            List<CellInstockPlan> splitDatasCollect = getSplitDatasByCellInstock(query, SiliconSplitTypeConstant.TRANSPARENTDOUBLEGLASS, false);
            List<CellInstockPlanDTO> splitDatas = cellInstockPlanDEConvert.toDto(splitDatasCollect);
            splitDatas = splitDatas.stream().filter(item -> {
                return !Objects.equals(item.getDataType(), 1);
            }).collect(Collectors.toList());
            //2、进行拆分
            BigDecimal rate = dto.getRate();
            List<CellInstockPlanDTO> datas = new ArrayList<>();
            List<CellInstockPlanDTO> datasT = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段2：进行拆分数据");
            Long lovId = null;
            LovLineDTO lovLineDTO = LovUtils.get(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS, "透明双玻");
            if (lovLineDTO != null) {
                lovId = lovLineDTO.getLovLineId();
            }
            Long finalLovId = lovId;
            splitDatas.stream().forEach(cellPlanLine -> {
                //2.1 拆分处理
                CellInstockPlanDTO cellPlanLineTransparentDoubleGlassSplit = BeanUtil.copyProperties(cellPlanLine, CellInstockPlanDTO.class);
                BigDecimal qty = cellPlanLine.getQtyPc();
                BigDecimal oldQty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(qty);
                BigDecimal qtyOfTdg = qty.multiply(rate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                BigDecimal oldQtyOfTdg = oldQty.multiply(rate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                qty = qty.subtract(qtyOfTdg);
                oldQty = oldQty.subtract(oldQtyOfTdg);
                cellPlanLineTransparentDoubleGlassSplit.setIsTransparentDoubleGlass(1);
                cellPlanLine.setIsTransparentDoubleGlass(1);
                cellPlanLine.setQtyPc(qty);
                cellPlanLine.setOldQtyPc(oldQty);
                cellPlanLineTransparentDoubleGlassSplit.setQtyPc(qtyOfTdg);
                cellPlanLineTransparentDoubleGlassSplit.setOldQtyPc(oldQtyOfTdg);
                cellPlanLineTransparentDoubleGlassSplit.setTransparentDoubleGlass("透明双玻");
                cellPlanLineTransparentDoubleGlassSplit.setTransparentDoubleGlassId(finalLovId);
                datas.add(cellPlanLine);
                datasT.add(cellPlanLineTransparentDoubleGlassSplit);
            });
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段3：调用bbom接口获取透明双玻数据对应的料号信息");
            //2.2 获取透明双玻数据对应的料号
            //Map<Long, String> itemCodesMap = cellItemCodeService.getItemCodesByCellInstockPlan(cellInstockPlanDEConvert.toEntity(datasT));
            CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto = null;
            CellInstockPlanVersionDTO cellInstockPlanVersionOverseaDto = null;
            //2.3 对透明双玻数据进行料号匹配
            for (CellInstockPlanDTO cellInstockPlan : datasT) {
                long id = cellInstockPlan.getId();
                cellInstockPlan.setId(null);
                /* if (itemCodesMap != null) {
                    if (StringUtils.isEmpty(itemCodesMap.get(id))) {
                        //构建料号匹配失败信息
                        String msg = getFailMsg(cellInstockPlan);
                        logService.addLog(task, ScheduleTaskStatusEnum.WARN, msg);
                    }
                    //老逻辑不赋值料号,由新方法触发料号匹配,并回写料号
//                    cellInstockPlan.setItemCode(itemCodesMap.get(id));

                } else {
                    //构建料号匹配失败信息
                    String msg = getFailMsg(cellInstockPlan);
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, msg);
                } */
                //入库版本记录开始
                CellInstockPlanDTO item = cellInstockPlan;
                if (cellInstockPlanVersionInlandDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.INLAND)) {

                        cellInstockPlanVersionInlandDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionInlandDto.setIsTransparentDoubleGlass(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionInlandDto));

                    }
                }
                if (cellInstockPlanVersionOverseaDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                        cellInstockPlanVersionOverseaDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionOverseaDto.setIsTransparentDoubleGlass(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionOverseaDto));
                    }
                }
                //入库版本记录结束
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段4：拆后数据存储");
            //3、数据存储
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datas));
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datasT));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");

        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    /**
     * 构建料号匹配失败信息
     *
     * @param cellInstockPlan
     * @return
     */
    private String getFailMsg(CellInstockPlanDTO cellInstockPlan) {
        String msg = Joiner.on(",").useForNull("").join(cellInstockPlan.getBasePlace(), cellInstockPlan.getWorkshop(),
                cellInstockPlan.getWorkunit(), cellInstockPlan.getCellsType(),
                "H追溯：" + cellInstockPlan.getHTrace(), "美学:" + cellInstockPlan.getAesthetics(),
                "透明双玻:" + cellInstockPlan.getTransparentDoubleGlass(), "小区域国家:" + cellInstockPlan.getRegionalCountry(),
                "片源种类:" + cellInstockPlan.getCellSource(),
                "硅片等级:" + cellInstockPlan.getWaferGrade(), "电池厂家:" + cellInstockPlan.getCellMfrs(),
                "硅料厂家:" + cellInstockPlan.getSiMfrs(), "网版厂家:" + cellInstockPlan.getScreenPlateMfrs(), "银浆厂家:" + cellInstockPlan.getSilverPulpMfrs(),
                "低阻:" + cellInstockPlan.getLowResistance(), "需求地:" + cellInstockPlan.getDemandBasePlace(),
                "加工类型:" + cellInstockPlan.getProcessCategory(), "产品等级:" + cellInstockPlan.getProductionGrade(), "记录没有匹配上对应的5A料号");
        return msg;
    }

    private List<CellPlanLineDTO> doProcessCategorySplitByIsOvesea(ProcessCategorySplitDto dto, String[] isHand) {
        /** 整体思路
         * 1、获取待处理的数据
         * 1.1 获取待处理数据
         * 1.2 获取待处理数据对应的4A料号
         *
         * 2、获取供应能力
         *
         * 2.1 获取数据包含的月份
         * 2.2 依据月份统计出要处理哪些天
         * 2.3 获的第一天
         * 2.4 依据2.1 月份，2.3第一天获取供应能力
         * 供应能力结构:料号->基地->日期时间->供应能力类型-》数量
         * 3、数据处理
         * 3.1 对待处理数据依据日期分组
         * 3.2 循环要处理的那些天（2.2）依次按天处理数据
         * 3.2.1 获取当天数据
         * 	如果没有数据，直接叠加供应量
         *         否则数据处理
         */
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_PROCESS_CATEGORY_SPLIT);
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月的%s的计划数据", dto.getMonth(), dto.getIsOversea()));
            //1、获取待拆分的入库计划数据
            CellPlanLineQuery query = BeanUtil.copyProperties(dto, CellPlanLineQuery.class);
            //1。1、获取待拆分的入库计划数据
            List<CellPlanLine> splitDataEntities = getSplitDatasByProcessCategory(query);
            if (CollectionUtils.isEmpty(splitDataEntities)) {
                throw new BizException("没有可拆分数据");
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段2：调用bom接口获取4A料号数据");
            //1.2 获取待处理数据对应的4A料号（料号与供应能力有对应关系）
            Map<Long, List<String>> allItemCodes = itemCodeService.getItemCodesByCellPlanLine(splitDataEntities);
            CellPlanLine firstCellPlanLine = splitDataEntities.get(0);
            String isOversea = firstCellPlanLine.getIsOversea();
            String version = firstCellPlanLine.getVersion();
            //2.1 获取数据包含的月份
            List<String> months = splitDataEntities.stream().map(CellPlanLine::getOldMonth).distinct().collect(Collectors.toList());
            List<CellPlanLineDTO> splitDatas = cellPlanLineDEConvert.toDto(splitDataEntities);
            splitDatas.forEach(item -> {
                item.setCellPlanSplitDTOList(new ArrayList<>());//该集合存储拆分后的数据
            });
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段3：获取供应能力数据");
            // 准备供应能力
            // 2.2 依据月份统计出要处理哪些天
            List<LocalDate> days = getDaysFromMonths(months);
            //2.3 获取第一天用于获取库存数据
            LocalDate firstDay = days.get(0);
            //2.4 依据2.1 月份，2.3第一天获取供应能力
            //供应能力结构：料号->基地->时间->供应能力类型-》数量
            Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> supplyQtyMap =
                    siliconSliceSupplyLinesService.getSupplyByMonths(months, firstDay.minusDays(1));
            //用于存储手动指定后没有完全匹配的数据，最终返回
            List<CellPlanLineDTO> gapList = new ArrayList<>();
            //准备供应能力结束
            //3、数据处理
            //3.1 对splitDatas数据按startTime分组，用于方便处理
            Map<LocalDate, List<CellPlanLineDTO>> allCollectCellPlanLines = splitDatas.stream().collect(Collectors.groupingBy(item -> {
                if (item.getStartTime().toLocalDate().isBefore(firstDay)) {
                    item.setCulStartDate(firstDay);
                } else {
                    item.setCulStartDate(item.getStartTime().toLocalDate());
                }
                return item.getCulStartDate();
            }));
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段4：数据处理");
            //for遍历days
            Map<String, Map<String, Map<String, BigDecimal>>> leftQtyMap = new HashMap<>();
            //3.2 循环要处理的那些天（2.2）依次按天处理数据
            for (LocalDate day : days) {
                List<CellPlanLineDTO> cellPlanLinesByDay = allCollectCellPlanLines.get(day);
                // 如果当天有数据和当天没有数据需要分开处理
                if (CollectionUtils.isEmpty(cellPlanLinesByDay)) {
                    // 如果没有数据的话, 直接叠加供应数量
                    stackedQuantity(supplyQtyMap, leftQtyMap, day);
                } else {
                    // 如果有数据的话, 需要处理数据
                    //处理某天下的入库计划数据
                    doProcess(cellPlanLinesByDay, supplyQtyMap, leftQtyMap, day, allItemCodes, gapList);
                }
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段5：数据存储");
            doSave(splitDatas);
            //版本管理记录开始
            if (isHand == null || isHand.length == 0) {
                for (String curMonth : months) {
                    CellPlanLineVersion cellPlanLineVersionInland = null;
                    CellPlanLineVersion cellPlanLineVersionOversea = null;
                    if (cellPlanLineVersionInland == null) {
                        if (isOversea.equals(OverseaConstant.INLAND)) {
                            cellPlanLineVersionInland = cellPlanLineVersionRepository.selectByVersion(isOversea, curMonth, version);
                            if (cellPlanLineVersionInland == null) {
                                throw new BizException(String.format("（%s）未点击“查询”创建%s月的投产计划，请点击查询后再进行加工类型拆分操作", isOversea, curMonth));
                            }
                            cellPlanLineVersionInland.setIsProcessCategory(1);
                            cellPlanLineVersionRepository.save(cellPlanLineVersionInland);
                        }
                    }
                    if (cellPlanLineVersionOversea == null) {
                        if (isOversea.equals(OverseaConstant.OVERSEA)) {
                            cellPlanLineVersionOversea = cellPlanLineVersionRepository.selectByVersion(isOversea, curMonth, version);
                            if (cellPlanLineVersionOversea == null) {
                                throw new BizException(String.format("（%s）未点击“查询”创建%s月的投产计划，请点击查询后再进行加工类型拆分操作", isOversea, curMonth));
                            }
                            cellPlanLineVersionOversea.setIsProcessCategory(1);
                            cellPlanLineVersionRepository.save(cellPlanLineVersionOversea);
                        }
                    }
                }
            }
            //版本管理记录结束
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "阶段6：执行结束");


            if (CollectionUtils.isNotEmpty(gapList)) {
                return gapList;
            }
            return null;

        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    private void doProcess(List<CellPlanLineDTO> cellPlanLinesByDay,
                           Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> supplyQtyMap,
                           Map<String, Map<String, Map<String, BigDecimal>>> lastQtyMap,
                           LocalDate day,
                           Map<Long, List<String>> allItemCodes, List<CellPlanLineDTO> gapList) {
        Map<Integer, List<CellPlanLineDTO>> ishandMap = cellPlanLinesByDay.stream()
                .peek(i -> {
                    i.setCurQty(i.getQtyPc());
                    if (i.getIsHandProcessCategory() == null) {
                        i.setIsHandProcessCategory(0);
                    }
                })
                .collect(Collectors.groupingBy(CellPlanLineDTO::getIsHandProcessCategory));

        // 分组手工和非手工
        List<CellPlanLineDTO> isHand = ishandMap.getOrDefault(1, Collections.emptyList())
                .stream().peek(item -> {
                    List<String> itemCodes = allItemCodes.get(item.getId());
                    if (CollectionUtils.isEmpty(itemCodes)) {
                        item.setGap(item.getQtyPc());
                        item.setCurQty(BigDecimal.ZERO);
                        gapList.add(item);
                    }
                })
                .filter(item -> CollectionUtils.isNotEmpty(allItemCodes.get(item.getId()))).collect(Collectors.toList());

        List<CellPlanLineDTO> noHand = ishandMap.getOrDefault(0, Collections.emptyList());

        // 先处理手工和非手工的同基地的冲销,
        //5、手工指定加工类型处理开始
        // 自产处理
        // 处理 手工   的 同基地 自产
        doInHandHasBasePlaceProcess(isHand, allItemCodes, supplyQtyMap, lastQtyMap, day, ProcessCategoryConstant.QINGHAI);
        // 处理 非手工 的 同基地 自产
        doNoHandHasBasePlaceProcess(noHand, allItemCodes, supplyQtyMap, lastQtyMap, day, ProcessCategoryConstant.QINGHAI);
        // 处理 手工   的 不同基地 自产
        doInHandDiffBasePlaceProcess(isHand, allItemCodes, supplyQtyMap, lastQtyMap, gapList, day, ProcessCategoryConstant.QINGHAI);
        // 处理 非手工 的 不同基地 自产
        doNoHandDiffBasePlaceProcess(noHand, allItemCodes, supplyQtyMap, lastQtyMap, day, ProcessCategoryConstant.QINGHAI);

        // 代工处理
        // 处理 手工   的 同基地 代工
        doInHandHasBasePlaceProcess(isHand, allItemCodes, supplyQtyMap, lastQtyMap, day, ProcessCategoryConstant.DAIGONGM);
        // 处理 非手工 的 同基地 代工
        doNoHandHasBasePlaceProcess(noHand, allItemCodes, supplyQtyMap, lastQtyMap, day, ProcessCategoryConstant.DAIGONGM);
        // 处理 手工   的 不同基地 代工
        doInHandDiffBasePlaceProcess(isHand, allItemCodes, supplyQtyMap, lastQtyMap, gapList, day, ProcessCategoryConstant.DAIGONGM);
        // 处理 非手工 的 不同基地 代工
        doNoHandDiffBasePlaceProcess(noHand, allItemCodes, supplyQtyMap, lastQtyMap, day, ProcessCategoryConstant.DAIGONGM);

        // 外购
        // 处理剩余非手工外购
        doNoHandProcessProcess(noHand);

        // 当天的供应量如果还有剩余需要加在剩余量上面
        stackedQuantity(supplyQtyMap, lastQtyMap, day);
    }

    private void doNoHandProcessProcess(List<CellPlanLineDTO> noHand) {
        // 如果还有剩余则标注为无
        for (CellPlanLineDTO item : noHand) {
            if (item.getCurQty().compareTo(BigDecimal.ZERO) > 0) {
                item.getCellPlanSplitDTOList().add(new CellPlanSplitDTO(ProcessCategoryConstant.WU, item.getCurQty(), item.getItemCode()));
                item.setCurQty(BigDecimal.ZERO);
            }
        }

    }

    private void doNoHandDiffBasePlaceProcess(List<CellPlanLineDTO> datas,
                                              Map<Long, List<String>> allItemCodes,
                                              Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> supplyQtyMap,
                                              Map<String, Map<String, Map<String, BigDecimal>>> lastQtyMap,
                                              LocalDate day,
                                              String processType) {
        for (CellPlanLineDTO item : datas) {
            List<String> itemCodesList = allItemCodes.get(item.getId());

            // 供应能力冲销
            for (String itemCode : itemCodesList) {
                // 获取之前的剩余数量
                Map<String, Map<String, BigDecimal>> leftBasePlaceAndProcessTypeQtyMap = lastQtyMap.computeIfAbsent(itemCode, k -> new HashMap<>());
                Map<String, Map<LocalDate, Map<String, BigDecimal>>> basePlaceAndDayAndProcessTypeQtyMap = supplyQtyMap.getOrDefault(itemCode, new HashMap<>());
                Set<String> leftBasePlace = new HashSet<>(leftBasePlaceAndProcessTypeQtyMap.keySet());
                Set<String> curBasePlace = basePlaceAndDayAndProcessTypeQtyMap.keySet();

                // 并集
                leftBasePlace.addAll(curBasePlace);
                for (String basePlace : leftBasePlace) {
                    Map<String, BigDecimal> leftProcessTypeQtyMap = leftBasePlaceAndProcessTypeQtyMap.computeIfAbsent(basePlace, k -> new HashMap<>());
                    BigDecimal leftQty = leftProcessTypeQtyMap.computeIfAbsent(processType, k -> BigDecimal.ZERO);

                    // 去获取供应数量
                    Map<LocalDate, Map<String, BigDecimal>> dayAndProcessTypeQtyMap = basePlaceAndDayAndProcessTypeQtyMap.getOrDefault(basePlace, new HashMap<>());
                    Map<String, BigDecimal> processTypeQtyMap = dayAndProcessTypeQtyMap.getOrDefault(day, new HashMap<>());

                    // 供应数量 = 之前剩余数量 + 当天供应数量
                    BigDecimal supplyQty = processTypeQtyMap.getOrDefault(processType, BigDecimal.ZERO).add(leftQty);
                    processTypeQtyMap.put(processType, BigDecimal.ZERO);

                    if (item.getCurQty().compareTo(supplyQty) >= 0) {
                        // 如果当前数量大于供应数量, 则直接冲销  供应不满足
                        item.getCellPlanSplitDTOList().add(new CellPlanSplitDTO(processType, supplyQty, itemCode));
                        item.setCurQty(item.getCurQty().subtract(supplyQty));
                        leftProcessTypeQtyMap.put(processType, BigDecimal.ZERO);
                    } else {
                        // 如果当前数量小于供应数量, 则剩余数量为供应数量 - 当前数量
                        item.getCellPlanSplitDTOList().add(new CellPlanSplitDTO(processType, item.getCurQty(), itemCode));
                        leftProcessTypeQtyMap.put(processType, supplyQty.subtract(item.getCurQty()));
                        item.setCurQty(BigDecimal.ZERO);
                    }
                }
            }
        }
    }

    /**
     * 非手工 同基地冲销
     */
    private void doNoHandHasBasePlaceProcess(List<CellPlanLineDTO> datas, Map<Long, List<String>> allItemCodes,
                                             Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> supplyQtyMap,
                                             Map<String, Map<String, Map<String, BigDecimal>>> lastQtyMap,
                                             LocalDate day, String processType) {
        for (CellPlanLineDTO item : datas) {
            String basePlace = item.getBasePlace();
            List<String> itemCodesList = allItemCodes.getOrDefault(item.getId(), new ArrayList<>());

            // 供应能力冲销
            for (String itemCode : itemCodesList) {
                // 获取之前的剩余数量
                Map<String, Map<String, BigDecimal>> leftBasePlaceAndProcessTypeQtyMap = lastQtyMap.computeIfAbsent(itemCode, k -> new HashMap<>());
                Map<String, BigDecimal> leftProcessTypeQtyMap = leftBasePlaceAndProcessTypeQtyMap.computeIfAbsent(basePlace, k -> new HashMap<>());

                // 去获取供应数量
                Map<String, Map<LocalDate, Map<String, BigDecimal>>> basePlaceAndDayAndProcessTypeQtyMap = supplyQtyMap.getOrDefault(itemCode, new HashMap<>());
                Map<LocalDate, Map<String, BigDecimal>> dayAndProcessTypeQtyMap = basePlaceAndDayAndProcessTypeQtyMap.getOrDefault(basePlace, new HashMap<>());
                Map<String, BigDecimal> processTypeQtyMap = dayAndProcessTypeQtyMap.getOrDefault(day, new HashMap<>());

                Set<String> processTypeSet = new HashSet<>(leftProcessTypeQtyMap.keySet());
                processTypeSet.addAll(processTypeQtyMap.keySet());

                // 加工类型排序
                BigDecimal leftQty = leftProcessTypeQtyMap.computeIfAbsent(processType, k -> BigDecimal.ZERO);
                // 供应数量 = 之前剩余数量 + 当天供应数量
                BigDecimal supplyQty = processTypeQtyMap.getOrDefault(processType, BigDecimal.ZERO).add(leftQty);
                processTypeQtyMap.put(processType, BigDecimal.ZERO);

                if (item.getCurQty().compareTo(supplyQty) >= 0) {
                    // 如果当前数量大于供应数量, 则直接冲销  供应不满足
                    item.setCurQty(item.getCurQty().subtract(supplyQty));
                    leftProcessTypeQtyMap.put(processType, BigDecimal.ZERO);
                    item.getCellPlanSplitDTOList().add(new CellPlanSplitDTO(processType, supplyQty, itemCode));
                } else {
                    // 如果当前数量小于供应数量, 则剩余数量为供应数量 - 当前数量
                    leftProcessTypeQtyMap.put(processType, supplyQty.subtract(item.getCurQty()));
                    item.getCellPlanSplitDTOList().add(new CellPlanSplitDTO(processType, item.getCurQty(), itemCode));
                    item.setCurQty(BigDecimal.ZERO);
                }
            }
        }
    }

    private void doInHandDiffBasePlaceProcess(List<CellPlanLineDTO> handDatas,
                                              Map<Long, List<String>> allItemCodes,
                                              Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> supplyQtyMap,
                                              Map<String, Map<String, Map<String, BigDecimal>>> lastQtyMap,
                                              List<CellPlanLineDTO> gapList,
                                              LocalDate day, String processType) {
        // 只处理青海->1 和代工->2 的
        List<CellPlanLineDTO> datas = handDatas.stream().filter(i ->
                        i.getProcessCategory().equalsIgnoreCase(processType))
                .collect(Collectors.toList());

        for (CellPlanLineDTO item : datas) {
            List<String> itemCodesList = allItemCodes.get(item.getId());

            // 供应能力冲销
            for (String itemCode : itemCodesList) {
                // 获取之前的剩余数量
                Map<String, Map<String, BigDecimal>> leftBasePlaceAndProcessTypeQtyMap = lastQtyMap.computeIfAbsent(itemCode, k -> new HashMap<>());
                Map<String, Map<LocalDate, Map<String, BigDecimal>>> basePlaceAndDayAndProcessTypeQtyMap = supplyQtyMap.getOrDefault(itemCode, new HashMap<>());
                Set<String> leftBasePlace = new HashSet<>(leftBasePlaceAndProcessTypeQtyMap.keySet());
                Set<String> curBasePlace = basePlaceAndDayAndProcessTypeQtyMap.keySet();

                // 并集
                leftBasePlace.addAll(curBasePlace);
                for (String basePlace : leftBasePlace) {
                    Map<String, BigDecimal> leftProcessTypeQtyMap = leftBasePlaceAndProcessTypeQtyMap.computeIfAbsent(basePlace, k -> new HashMap<>());
                    BigDecimal leftQty = leftProcessTypeQtyMap.computeIfAbsent(processType, k -> BigDecimal.ZERO);

                    // 去获取供应数量
                    Map<LocalDate, Map<String, BigDecimal>> dayAndProcessTypeQtyMap = basePlaceAndDayAndProcessTypeQtyMap.getOrDefault(basePlace, new HashMap<>());
                    Map<String, BigDecimal> processTypeQtyMap = dayAndProcessTypeQtyMap.getOrDefault(day, new HashMap<>());

                    // 供应数量 = 之前剩余数量 + 当天供应数量
                    BigDecimal supplyQty = processTypeQtyMap.getOrDefault(processType, BigDecimal.ZERO).add(leftQty);
                    processTypeQtyMap.put(processType, BigDecimal.ZERO);

                    if (item.getCurQty().compareTo(supplyQty) >= 0) {
                        // 如果当前数量大于供应数量, 则直接冲销  供应不满足
                        item.setCurQty(item.getCurQty().subtract(supplyQty));
                        leftProcessTypeQtyMap.put(processType, BigDecimal.ZERO);
                    } else {
                        // 如果当前数量小于供应数量, 则剩余数量为供应数量 - 当前数量
                        leftProcessTypeQtyMap.put(processType, supplyQty.subtract(item.getCurQty()));
                        item.setCurQty(BigDecimal.ZERO);
                    }
                }
            }

            // 如果还有Gap数量,则加入GapList
            if (item.getCurQty().compareTo(BigDecimal.ZERO) > 0) {
                item.setGap(item.getCurQty());
                gapList.add(item);
            }
        }
    }

    /**
     * 直接叠加数量到剩余数量Map中
     *
     * @param supplyQtyMap
     * @param lastQtyMap
     * @param day
     */
    private void stackedQuantity(Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> supplyQtyMap, Map<String, Map<String, Map<String, BigDecimal>>> lastQtyMap, LocalDate day) {
        supplyQtyMap.forEach((itemCode, itemMap) -> {
            itemMap.forEach((basePlace, basePlaceMap) -> {
                Map<String, Map<String, BigDecimal>> basePlaceLeftQty = lastQtyMap.computeIfAbsent(itemCode, k -> new HashMap<>());
                Map<String, BigDecimal> leftProcessTypeAndQty = basePlaceLeftQty.getOrDefault(basePlace, new HashMap<>());
                Map<String, BigDecimal> processTypeAndQtyMap = basePlaceMap.getOrDefault(day, new HashMap<>());

                for (String processType : processTypeAndQtyMap.keySet()) {
                    BigDecimal leftQty = leftProcessTypeAndQty.getOrDefault(processType, BigDecimal.ZERO);
                    BigDecimal curQty = processTypeAndQtyMap.getOrDefault(processType, BigDecimal.ZERO);
                    leftProcessTypeAndQty.put(processType, leftQty.add(curQty));
                }
            });
        });
    }

    private void doSave(List<CellPlanLineDTO> datas) {
        if (CollectionUtils.isNotEmpty(datas)) {
            List<CellPlanLineSaveDTO> saveDatas = new ArrayList<>();

            for (CellPlanLineDTO data : datas) {
                if (Objects.equals(data.getDataType(), 1)) {
                    continue;
                }
                if (!"H".equalsIgnoreCase(data.getHTrace())) {
                    continue;
                }
                List<CellPlanSplitDTO> cellPlanSplitDTOList = Optional.ofNullable(data.getCellPlanSplitDTOList())
                        .orElse(new ArrayList<>()).stream().filter(i -> i.getQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(cellPlanSplitDTOList)) {
                    CellPlanLineSaveDTO saveDTO = cellPlanLineDEConvert.toSaveDto(data);
                    saveDatas.add(saveDTO);
                } else {
                    // 需要拆分的部分
                    Map<String, List<CellPlanSplitDTO>> processTypeAndSplitMap = cellPlanSplitDTOList.stream()
                            .collect(Collectors.groupingBy(CellPlanSplitDTO::getProcessType));
                    AtomicBoolean first = new AtomicBoolean(true);
                    List<CellPlanLineSaveDTO> saveDTOList = new ArrayList<>();
                    processTypeAndSplitMap.forEach((processType, splitList) -> {
                        CellPlanLineSaveDTO saveDTO = cellPlanLineDEConvert.toSaveDto(data);
                        if (!first.get()) {
                            saveDTO.setId(null);
                            first.set(false);
                        }
                        //获取加工类型Id
                        Long processCategoryId = MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY, processType);
                        saveDTO.setProcessCategoryId(processCategoryId);
                        saveDTO.setProcessCategory(processType);
                        saveDTO.setQtyPc(splitList.stream().map(CellPlanSplitDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        saveDTOList.add(saveDTO);
                        // saveDatas.add(saveDTO);
                    });
                    //处理oldQtyPc
                    if (CollectionUtils.isNotEmpty(saveDTOList)) {
                        BigDecimal oldQtyPc = saveDTOList.get(0).getOldQtyPc();
                        BigDecimal leftOldQtyPc = oldQtyPc;
                        //遍历saveDTOList的qtyPc属性求和
                        BigDecimal allQtyPc = saveDTOList.stream()
                                .map(i -> i.getQtyPc())
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO);
                        for (int i = 0; i < saveDTOList.size(); i++) {
                            if (i == saveDTOList.size() - 1) {
                                saveDTOList.get(i).setOldQtyPc(leftOldQtyPc);
                            } else {
                                BigDecimal rate = saveDTOList.get(i).getQtyPc().divide(allQtyPc, 2, RoundingMode.HALF_UP);
                                BigDecimal midOldQtyPc = oldQtyPc.multiply(rate);
                                saveDTOList.get(i).setOldQtyPc(midOldQtyPc);
                                leftOldQtyPc = leftOldQtyPc.subtract(midOldQtyPc);
                            }
                        }
                    }
                    saveDatas.addAll(saveDTOList);


                }
            }

            saveDatas.forEach(cellPlanLineService::save);
        }


    }

    /**
     * 手工同基地的冲销
     */
    private void doInHandHasBasePlaceProcess(
            List<CellPlanLineDTO> handDatas,
            Map<Long, List<String>> allItemCodes,
            Map<String, Map<String, Map<LocalDate, Map<String, BigDecimal>>>> supplyQtyMap,
            Map<String, Map<String, Map<String, BigDecimal>>> lastQtyMap,
            LocalDate day, String processType) {
        // 只处理青海->1 和代工->2 的
        List<CellPlanLineDTO> datas = handDatas.stream().filter(i ->
                        i.getProcessCategory().equalsIgnoreCase(processType))
                .collect(Collectors.toList());
        for (CellPlanLineDTO item : datas) {
            String basePlace = item.getBasePlace();
            List<String> itemCodesList = allItemCodes.getOrDefault(item.getId(), new ArrayList<>());

            // 供应能力冲销
            for (String itemCode : itemCodesList) {
                // 获取之前的剩余数量
                Map<String, Map<String, BigDecimal>> leftBasePlaceAndProcessTypeQtyMap = lastQtyMap.computeIfAbsent(itemCode, k -> new HashMap<>());
                Map<String, BigDecimal> leftProcessTypeQtyMap = leftBasePlaceAndProcessTypeQtyMap.computeIfAbsent(basePlace, k -> new HashMap<>());
                BigDecimal leftQty = leftProcessTypeQtyMap.computeIfAbsent(processType, k -> BigDecimal.ZERO);

                // 去获取供应数量
                Map<String, Map<LocalDate, Map<String, BigDecimal>>> basePlaceAndDayAndProcessTypeQtyMap = supplyQtyMap.getOrDefault(itemCode, new HashMap<>());
                Map<LocalDate, Map<String, BigDecimal>> dayAndProcessTypeQtyMap = basePlaceAndDayAndProcessTypeQtyMap.getOrDefault(basePlace, new HashMap<>());
                Map<String, BigDecimal> processTypeQtyMap = dayAndProcessTypeQtyMap.getOrDefault(day, new HashMap<>());
                // 供应数量 = 之前剩余数量 + 当天供应数量
                BigDecimal supplyQty = processTypeQtyMap.getOrDefault(processType, BigDecimal.ZERO).add(leftQty);
                processTypeQtyMap.put(processType, BigDecimal.ZERO);

                if (item.getCurQty().compareTo(supplyQty) >= 0) {
                    // 如果当前数量大于供应数量, 则直接冲销  供应不满足
                    item.setCurQty(item.getCurQty().subtract(supplyQty));
                    leftProcessTypeQtyMap.put(processType, BigDecimal.ZERO);
                } else {
                    // 如果当前数量小于供应数量, 则剩余数量为供应数量 - 当前数量
                    leftProcessTypeQtyMap.put(processType, supplyQty.subtract(item.getCurQty()));
                    item.setCurQty(BigDecimal.ZERO);
                }
            }
        }
    }

    private Map<String, List<SiliconSliceSupplyLinesDTO>> getItemCodeBySiliconSliceSupply(ProcessCategorySplitDto dto, ScheduledTaskLinesDTO task, LovLineDTO lovLineDTOOversea) {
        //2、供应能力的数据（接口获取）
        List<SiliconSliceSupplyLinesDTO> supplyLinesDTOS = getSiliconSliceSupplyLinesDTOS(dto, task, lovLineDTOOversea);

        //3 以料号维度供应能力分组
        Map<String, List<SiliconSliceSupplyLinesDTO>> supplyCollectMap =
                supplyLinesDTOS.stream().collect(Collectors.groupingBy(SiliconSliceSupplyLinesDTO::getItemCode
                ));
        return supplyCollectMap;
    }

    private List<SiliconSliceSupplyLinesDTO> getSiliconSliceSupplyLinesDTOS(ProcessCategorySplitDto dto, ScheduledTaskLinesDTO task, LovLineDTO lovLineDTOOversea) {
        logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段3：调用mrp接口获取供应能力数据");
        // TODO 供应能力获取需要不按月份获取
        List<SiliconSliceSupplyLinesDTO> allSupplyLinesDTOS = siliconSliceSupplyLinesService.getSupplyByMonth(dto.getMonth());

        //供应能力国内外筛选
        // 使用基地分组供应能力
        Map<String, List<SiliconSliceSupplyLinesDTO>> collectSupplyLinesDTO = allSupplyLinesDTOS.stream().collect(Collectors.groupingBy(SiliconSliceSupplyLinesDTO::getBasePlace));
        List<SiliconSliceSupplyLinesDTO> preSupplyLinesDTOS = new ArrayList<>();
        collectSupplyLinesDTO.entrySet().stream().forEach(basePlaceEntry -> {
            LovLineDTO lovDto = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, basePlaceEntry.getKey());
            if (lovDto != null) {
                // 只筛选当前国内海外数据
                if (lovDto.getAttribute2().equalsIgnoreCase(lovLineDTOOversea.getLovLineId().toString())) {
                    List<SiliconSliceSupplyLinesDTO> value = basePlaceEntry.getValue();
                    if (value != null) {
                        preSupplyLinesDTOS.addAll(value);
                    }
                }
            }
        });

        //供应能力加工类型处理
        Map<String, String> processCategoryMap = getProcessCategoryLovMap();
        preSupplyLinesDTOS.stream().forEach(item -> {
            if (processCategoryMap.containsKey(item.getProcessType())) {
                item.setProcessType(processCategoryMap.get(item.getProcessType()));
            }
        });

        List<SiliconSliceSupplyLinesDTO> supplyLinesDTOS = preSupplyLinesDTOS.stream().filter(item -> {
            String type = item.getProcessType();
            return type.equals(ProcessCategoryConstant.QINGHAI) || type.equals(ProcessCategoryConstant.DAIGONGM) || type.equals(ProcessCategoryConstant.ZHICAIM);
        }).collect(Collectors.toList());
        return supplyLinesDTOS;
    }

    /**
     * 决定4A料号的属性
     *
     * @param cellPlanLine
     * @return
     */
    private String get4AKey(CellPlanLine cellPlanLine) {

        return StringTools.joinWith(",", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getWorkunit(), cellPlanLine.getCellsType(),
                cellPlanLine.getRegionalCountry(), cellPlanLine.getIsSpecialRequirement(), cellPlanLine.getScreenPlateMfrs(), cellPlanLine.getSilverPulpMfrs(),
                cellPlanLine.getLowResistance(), cellPlanLine.getDemandBasePlace(), cellPlanLine.getAesthetics(), cellPlanLine.getTransparentDoubleGlass(),
                cellPlanLine.getHTrace(), cellPlanLine.getCellSource(), cellPlanLine.getCellMfrs(), cellPlanLine.getSiMfrs(),
                cellPlanLine.getWaferGrade(), cellPlanLine.getProcessCategory(),
                cellPlanLine.getProductionGrade()
        );
    }

    @Override
    public Page<CellPlanLineProcessCategoryTotalDTO> processCategoryQuery(CellPlanLineQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        //1、获取兆瓦转化系数
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();

        Sort sort = Sort.by(Sort.Direction.DESC, "workshop");
        Pageable pageable = PageRequest.of(query.getPageNumber(), query.getPageSize(), sort);
        //2、获取投产计划数据
        List<CellPlanLineDTO> dtos = cellPlanLineService.query(query);
        Map<String, List<CellPlanLineDTO>> collect = dtos.stream().collect(Collectors.groupingBy(
                dto -> Joiner.on(",").useForNull("无").join(
                        dto.getIsOversea(),
                        dto.getBasePlace(),
                        dto.getWorkshop(),
                        dto.getCellsType(),
                        dto.getHTrace(),
                        dto.getProductionGrade(),
                        dto.getProcessCategory(),
                        dto.getWaferGrade(),
                        dto.getSiMfrs(),
                        dto.getTransparentDoubleGlass(),
                        dto.getMonth()
                )
        ));
        //2.1 依据key排序
        List<String> keys = collect.keySet().stream().sorted().collect(Collectors.toList());
        //2.2 准备分页参数
        Integer total = keys.size();
        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer endPos = Math.min(startPos + pageSize, total);
        //3、分页计算
        List<CellPlanLineProcessCategoryTotalDTO> datas = new ArrayList<>();
        for (int i = startPos; i < endPos; i++) {
            String key = keys.get(i);
            CellConversionFactorDTO cellConversionFactorDTO = null;
            CellPlanLineProcessCategoryTotalDTO cellPlanLineProcessCategoryTotalDTO = null;
            AtomicReference<BigDecimal> allQtyPc = new AtomicReference<>(BigDecimal.ZERO);
            AtomicReference<BigDecimal> allGap = new AtomicReference<>(BigDecimal.ZERO);
            List<CellPlanLineDTO> cellPlanLineDTOs = collect.getOrDefault(key, Lists.newArrayList());
            for (CellPlanLineDTO cellPlanLineDTO : cellPlanLineDTOs) {
                if (cellConversionFactorDTO == null) {
                    cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", cellPlanLineDTO.getIsOversea(), cellPlanLineDTO.getCellsType()));
                }
                if (Objects.isNull(cellPlanLineProcessCategoryTotalDTO)) {
                    cellPlanLineProcessCategoryTotalDTO = BeanUtil.copyProperties(cellPlanLineDTO, CellPlanLineProcessCategoryTotalDTO.class);
                    cellPlanLineProcessCategoryTotalDTO.setCellType(cellPlanLineDTO.getCellsType());
                }
                int day = cellPlanLineDTO.getStartTime().getDayOfMonth();
                BigDecimal val = ReflectUtil.invoke(cellPlanLineProcessCategoryTotalDTO, "getD" + day);
                val = Optional.ofNullable(val).orElse(BigDecimal.ZERO);
                BigDecimal qtyPc = Optional.ofNullable(cellPlanLineDTO.getQtyPc()).orElse(BigDecimal.ZERO);
                BigDecimal sum = val.add(qtyPc);
                ReflectUtil.invoke(cellPlanLineProcessCategoryTotalDTO, "setD" + day, sum);
                allQtyPc.set(allQtyPc.get().add(qtyPc));
                BigDecimal gap = ReflectUtil.invoke(cellPlanLineDTO, "getGap");
                gap = Optional.ofNullable(gap).orElse(BigDecimal.ZERO);
                allGap.set(allGap.get().add(gap));
            }
            cellPlanLineProcessCategoryTotalDTO.setGap(allGap.get());
            cellPlanLineProcessCategoryTotalDTO.setQtyThousandPc(allQtyPc.get());
            if (cellConversionFactorDTO != null) {
                if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                    BigDecimal mv = allQtyPc.get().divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                    cellPlanLineProcessCategoryTotalDTO.setCellMv(mv);
                }

            }
            BigDecimal allQtyPcValue = allQtyPc.get().setScale(2, RoundingMode.HALF_UP);
            cellPlanLineProcessCategoryTotalDTO.setQtyThousandPc(allQtyPcValue);
            datas.add(cellPlanLineProcessCategoryTotalDTO);
        }
        //4、翻译转换
        MyThreadLocal.get().setLang(oldLang);
        datas = cellPlanLineTotalDEConvert.changeSiliconProcessCategoryTotalDTONameByCnName(datas);
        return new PageImpl(datas, pageable, total);
    }

    /**
     * 手动设置加工类型
     *
     * @param listDto
     * @return
     */
    @Override
    @CacheEvict(cacheNames = "CellPlanLineService_queryByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)

    public List<CellPlanLineDTO> handSetProcessCategory(HandSetProcessCategoryListDto listDto) {
        ProcessCategorySplitDto processCategorySplitDto = listDto.getProcessCategorySplitDto();
        if (StringUtils.isEmpty(processCategorySplitDto.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY);

        listDto.getDtos().stream().forEach(dto -> {
            CellPlanLine cellPlanLine = cellPlanLineRepository.getOne(dto.getId());
            if ("H".equalsIgnoreCase(cellPlanLine.getHTrace()) && !Objects.equals(cellPlanLine.getDataType(), 1)) {
                LovLineDTO lovLineDTO = allByHeaderCode.get(dto.getProcessCategory());
                Integer priority = 100;
                if(lovLineDTO != null){
                    cellPlanLine.setProcessCategory(lovLineDTO.getLovName());
                    cellPlanLine.setProcessCategoryId(lovLineDTO.getLovLineId());
                    if (lovLineDTO.getAttribute1() != null) {
                        priority = Integer.valueOf(lovLineDTO.getAttribute1());
                    }
                }
                cellPlanLine.setIsHandProcessCategory(1);
                cellPlanLine.setProcessCategoryPriority(priority);
                cellPlanLineRepository.save(cellPlanLine);
            }

        });
        MyThreadLocal.get().setLang(oldLang);
        return processCategorySplit(listDto.getProcessCategorySplitDto(), "hand");
    }

    @Override
    public List<SiliconSliceSupplyLinesDTO> supplyLinesList(SiliconSliceSupplyLinesQuery query) {
//        Sort sort = Sort.by(Sort.Direction.ASC, "id");
//        Pageable pageable = PageRequest.of(query.getPageNumber(), query.getPageSize(), sort);
        //供应能力的数据（接口获取）
        List<SiliconSliceSupplyLinesDTO> supplyLinesDTOS = siliconSliceSupplyLinesService.getSupplyByMonth(query.getMonth());
        //供应能力加工类型处理
        Map<String, String> processCategoryMap = getProcessCategoryLovMap();
        supplyLinesDTOS.stream().forEach(item -> {
            if (processCategoryMap.containsKey(item.getProcessType())) {
                item.setProcessType(processCategoryMap.get(item.getProcessType()));
            }
        });
        supplyLinesDTOS = supplyLinesDTOS.stream().filter(item -> {
            String type = item.getProcessType();
            return type.equals(ProcessCategoryConstant.QINGHAI) || type.equals(ProcessCategoryConstant.DAIGONGM) || type.equals(ProcessCategoryConstant.ZHICAIM);
        }).collect(Collectors.toList());
        return supplyLinesDTOS;
       // return new PageImpl<SiliconSliceSupplyLinesDTO>(supplyLinesDTOS, pageable, supplyLinesDTOS.size());
    }

    /**
     * 入库计划分档规则匹配执行
     *
     * @param dto
     * @param cellsType
     * @return
     */
    @Override
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)

    public List<CellInstockPlanDTO> cellGradeRuleMate(CellGradeRuleMateDto dto, String cellsType) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(dto.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, dto.getIsOversea(), dto.getMonth(), "instock", "gradeRuleMate");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行分档规则匹配
                return doCellGradeRuleMate(dto, cellsType, oldLang);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }


    }

    private List<CellInstockPlanDTO> doCellGradeRuleMate(CellGradeRuleMateDto dto, String cellsType, String oldLang) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_GRADERULE_MATE);
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行分档规则匹配的入库计划数据", dto.getMonth()));
            CellInstockPlanQuery query = cellInstockPlanDEConvert.toCellInstockPlanQueryFromCellGradeRuleMateDto(dto);
            //1、获取要进行分档规则匹配的入库计划数据
            List<CellInstockPlanDTO> dtos = cellInstockPlanService.query(query);
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段2：调用aps接口获取匹配规则"));
            //2、获取匹配规则并排序
            List<RecordTransitionDTO> recordTransitionDTOS = null;
            if (StringUtils.isNotEmpty(cellsType)) {
                recordTransitionDTOS = apsService.queryCellMateRules(cellsType);
            } else {
                recordTransitionDTOS = apsService.queryCellMateRules();
            }
            //2.1 依据电池类型分组构建map，key是电池类型，value是该电池类型的分档规则list
            Map<String, List<RecordTransitionDTO>> cellTypeCollectMap = recordTransitionDTOS.stream().collect(Collectors.groupingBy(RecordTransitionDTO::getCellTypeName));
            //2.2 、 对map中的value，list进行排序
            //   有国内外、生产车间、开始时间、结束时间的排在最前面
            //   有国内外、有生产车间、开始时间、无结束时间的排在其次
            //   有国内外、有生产车间、无开始时间、无结束时间的排在其次
            //   有国内外、无生产车间、无开始时间、无结束时间的排在其次
            //   无国内外、无生产车间、无开始时间、无结束时间的排在其次
            for (Map.Entry<String, List<RecordTransitionDTO>> entry : cellTypeCollectMap.entrySet()) {
                List<RecordTransitionDTO> items = entry.getValue();
                List<RecordTransitionDTO> sortedItems = items.stream().sorted(
                        Comparator.comparing((RecordTransitionDTO item) -> "*".equals(item.getIsOverseaName()) ? null : item.getIsOverseaName(), Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(
                                        (RecordTransitionDTO item) -> "*".equals(item.getSupplierName()) ? null : item.getSupplierName(), Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(
                                        RecordTransitionDTO::getStartTime, Comparator.nullsLast(Comparator.naturalOrder())
                                )
                                .thenComparing(
                                        RecordTransitionDTO::getEndTime, Comparator.nullsLast(Comparator.naturalOrder())
                                )
                ).collect(Collectors.toList());
                cellTypeCollectMap.put(entry.getKey(), sortedItems);
            }
            //3、开始匹配
/*
匹配规则：

1）先进行精确匹配再逐步进行模糊匹配。

1.1）如果匹配到唯一行数据，则将对应分档规则写入入库计划分档规则字段。

1.2）如果未匹配到数据，则逐步开始模糊匹配。

1.3）如果模糊匹配最终未匹配数据，则记录“未匹配到电池分档规则”

2）精确匹配：按国内海外、生产车间、电池类型、开始时间精确匹配数据。

3）循环模糊匹配：当精确匹配不到数据时，逐步循环进行模糊匹配。

3.1）匹配结束时间为空的数据。

3.2）匹配开始时间为空的数据。

3.3）匹配生产车间为空或者“*”的数据。

3.4）匹配国内海外为空或者“*”的数据。
*/
            CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto = null;
            CellInstockPlanVersionDTO cellInstockPlanVersionOverseaDto = null;
            List<CellInstockPlanDTO> noFindRuleCellPlanLines = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段3：进行匹配"));
            Set<String> ruleSet = new LinkedHashSet<>();
            //3、开始匹配
            for (CellInstockPlanDTO cellPlanLineDTO : dtos) {
                //入库版本记录开始
                CellInstockPlanDTO item = cellPlanLineDTO;
                if (cellInstockPlanVersionInlandDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.INLAND)) {

                        cellInstockPlanVersionInlandDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionInlandDto.setIsGradeRule(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionInlandDto));

                    }
                }
                if (cellInstockPlanVersionOverseaDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                        cellInstockPlanVersionOverseaDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionOverseaDto.setIsGradeRule(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionOverseaDto));
                    }
                }
                //入库版本记录结束
                //3.1 获取某电池类型的匹配规则
                List<RecordTransitionDTO> recordTransitionDTOList = cellTypeCollectMap.get(cellPlanLineDTO.getCellsType());
                //根本就没有匹配规则的情况
                if (CollectionUtils.isEmpty(recordTransitionDTOList)) {
                    cellPlanLineDTO.setGradeRule(NOFINDMATCHRULE);
                    noFindRuleCellPlanLines.add(cellPlanLineDTO);
                    ruleSet.add(StringTools.joinWith("-", cellPlanLineDTO.getCellsType(), "缺少分档规则，请【工艺整合】在电池分配维护电池档位对应信息。"));
                    continue;
                }
                Boolean isFind = false;
                //3.2 开始依据规则匹配
                for (RecordTransitionDTO recordTransitionDTO : recordTransitionDTOList) {

                    if (cellPlanLineDTO.getIsOversea().equals(recordTransitionDTO.getIsOverseaName()) && cellPlanLineDTO.getWorkshop().equals(recordTransitionDTO.getSupplierName())) {

                        if (Objects.nonNull(recordTransitionDTO.getStartTime()) && Objects.nonNull(recordTransitionDTO.getEndTime()) &&
                                (cellPlanLineDTO.getStartTime().toLocalDate().compareTo(recordTransitionDTO.getStartTime()) >= 0
                                        &&
                                        cellPlanLineDTO.getStartTime().toLocalDate().compareTo(recordTransitionDTO.getEndTime()) <= 0
                                )
                        ) {
                            //精确匹配上的情况
                            cellPlanLineDTO.setGradeRule(recordTransitionDTO.getRule());
                            isFind = true;

                        } else if (Objects.nonNull(recordTransitionDTO.getStartTime()) && Objects.isNull(recordTransitionDTO.getEndTime()) &&
                                (cellPlanLineDTO.getStartTime().toLocalDate().compareTo(recordTransitionDTO.getStartTime()) >= 0)) {
                            //考虑结束时间是空匹配上的情况
                            cellPlanLineDTO.setGradeRule(recordTransitionDTO.getRule());
                            isFind = true;
                        } else if (Objects.isNull(recordTransitionDTO.getStartTime())) {
                            //考虑开始时间是空匹配上的情况
                            cellPlanLineDTO.setGradeRule(recordTransitionDTO.getRule());
                            isFind = true;
                        }

                    } else {
                        if (cellPlanLineDTO.getIsOversea().equals(recordTransitionDTO.getIsOverseaName()) && (StringUtils.isEmpty(recordTransitionDTO.getSupplierName()) || "*".equals(recordTransitionDTO.getSupplierName()))) {
                            //考虑供应方是空的情况
                            cellPlanLineDTO.setGradeRule(recordTransitionDTO.getRule());
                            isFind = true;
                        } else if (StringUtils.isEmpty(recordTransitionDTO.getIsOverseaName()) || "*".equals(recordTransitionDTO.getIsOverseaName())) {
                            //考虑国内外是空的情况
                            cellPlanLineDTO.setGradeRule(recordTransitionDTO.getRule());
                            isFind = true;
                        }
                    }
                    if (isFind) {
                        break;
                    }
                }
                //3.3 没有匹配上的进行记录
                if (!isFind) {
                    cellPlanLineDTO.setGradeRule(NOFINDMATCHRULE);
                    ruleSet.add(StringTools.joinWith("-", cellPlanLineDTO.getCellsType(), "缺少分档规则，请【工艺整合】在电池分配维护电池档位对应信息。"));
                    noFindRuleCellPlanLines.add(cellPlanLineDTO);
                }

            }
            //4、没有匹配上添加日志记录
            if (CollectionUtils.isNotEmpty(ruleSet)) {
                for (String item : ruleSet) {
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, item);
                }

            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段4：数据存储"));
            //5、数据存储
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(dtos));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");

            //结束匹配
            //6、翻译转换
            MyThreadLocal.get().setLang(oldLang);
            noFindRuleCellPlanLines = cellInstockPlanDEConvert.toCellInstockPlanDTONameByCnName(noFindRuleCellPlanLines);
            return noFindRuleCellPlanLines;
        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    /**
     * 对入库计划依据分档规则匹配维度汇总查询
     *
     * @param query
     * @return
     */
    @Override
    public Page<CellGradeRuleMateTotalDTO> gradeRuleTotalQuery(CellInstockPlanQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        //1、获取兆瓦系数
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        List<CellGradeRuleMateTotalDTO> cellGradeRuleMateTotals = new ArrayList<>();
        //2、获取入库计划数据
        List<CellInstockPlanDTO> datas = cellInstockPlanService.query(query);
        Map<String, List<CellInstockPlanDTO>> collect = datas.stream().collect(
                Collectors.groupingBy(item -> {
                    return StringTools.joinWith(",", item.getIsOversea(), item.getBasePlace(), item.getWorkshop(), item.getCellsType(), item.getGradeRule());
                })
        );
        //3、依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        Collections.sort(keys);
        //4、准备分页参数
        Integer total = keys.size();
        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer endPos = Math.min(startPos + pageSize, total);
        //5、分页计算
        for (int i = startPos; i < endPos; i++) {
            String key = keys.get(i);
            CellConversionFactorDTO cellConversionFactorDTO = null;
            CellGradeRuleMateTotalDTO cellGradeRuleMateTotalDTO = null;
            List<CellInstockPlanDTO> dtos = collect.get(key);
            //统计数据
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            for (CellInstockPlanDTO dto : dtos) {
                if (cellConversionFactorDTO == null) {
                    cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", dto.getIsOversea(), dto.getCellsType()));
                }
                if (Objects.isNull(cellGradeRuleMateTotalDTO)) {
                    cellGradeRuleMateTotalDTO = cellInstockPlanDEConvert.toCellGradeRuleMateTotalDTO(dto);
                    cellGradeRuleMateTotalDTO.setFromVersion(dto.getVersion());
                }
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellGradeRuleMateTotalDTO, "getD" + day);
                BigDecimal value = dto.getQtyPc();
                value = value.setScale(2, RoundingMode.HALF_UP);
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellGradeRuleMateTotalDTO, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellGradeRuleMateTotalDTO, "setD" + day, addVal);
                }
                if (Objects.nonNull(value)) {
                    qtyCount = qtyCount.add(value);
                }

            }
            //依据瓦片折算系数计算MV
            if (Objects.nonNull(cellConversionFactorDTO)) {
                if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                    BigDecimal mv = qtyCount.divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                    cellGradeRuleMateTotalDTO.setCellMv(mv);
                }

            } else {
                log.warn("没有《" + cellGradeRuleMateTotalDTO.getCellsType() + "》电池类型折算系数数据，不能进行对应的mv计算");
            }
            qtyCount = qtyCount.setScale(2, RoundingMode.HALF_UP);
            cellGradeRuleMateTotalDTO.setQtyThousandPc(qtyCount);
            cellGradeRuleMateTotals.add(cellGradeRuleMateTotalDTO);
        }
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //6、翻译转化
        MyThreadLocal.get().setLang(oldLang);
        cellGradeRuleMateTotals = cellInstockPlanTotalDEConvert.toCellGradeRuleMateTotalDTONameByCNName(cellGradeRuleMateTotals);
        return new PageImpl(cellGradeRuleMateTotals, pageable, total);
    }

    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void siliconAestheticsSplit(AestheticsSplitDto dto) {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(dto.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, dto.getIsOversea(), dto.getMonth(), "instock", "aestheticsSplit");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行美学拆分
                doSiliconAestheticsSplit(dto);
            } catch (Exception e) {
                throw e;
            } finally {

                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }
    }

    private void doSiliconAestheticsSplit(AestheticsSplitDto dto) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_AESTHETICS_SPLIT);
            //1、获取要进行美学拆分的处理的数据
            CellInstockPlanQuery query = BeanUtil.copyProperties(dto, CellInstockPlanQuery.class, "rate");
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行拆分的数据", query.getMonth()));
            List<CellInstockPlan> splitDatasCollect = getSplitDatasByCellInstock(query, SiliconSplitTypeConstant.AESTHETICS, false);
            List<CellInstockPlanDTO> splitDatas = cellInstockPlanDEConvert.toDto(splitDatasCollect);
            splitDatas = splitDatas.stream().filter(item -> {
                return !Objects.equals(item.getDataType(), 1);
            }).collect(Collectors.toList());
            //2、进行拆分
            BigDecimal rate = dto.getRate();
            List<CellInstockPlanDTO> datas = new ArrayList<>();
            List<CellInstockPlanDTO> datasM = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段2：进行拆分数据");
            Long lovId = null;
            LovLineDTO lovLineDTO = LovUtils.get(LovHeaderCodeConstant.AESTHETICS, "美学");
            if (lovLineDTO != null) {
                lovId = lovLineDTO.getLovLineId();
            }
            Long finalLovId = lovId;
            splitDatas.stream().forEach(cellPlanLine -> {
                //2.1 拆分处理
                CellInstockPlanDTO cellPlanLineAestheticsSplit = BeanUtil.copyProperties(cellPlanLine, CellInstockPlanDTO.class);
                BigDecimal qty = cellPlanLine.getQtyPc();
                BigDecimal oldQty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(qty);
                BigDecimal qtyOfTdg = qty.multiply(rate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                BigDecimal oldQtyOfTdg = oldQty.multiply(rate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                qty = qty.subtract(qtyOfTdg);
                oldQty = oldQty.subtract(oldQtyOfTdg);
                cellPlanLineAestheticsSplit.setIsAestheticsSplit(1);
                cellPlanLine.setIsAestheticsSplit(1);
                cellPlanLine.setQtyPc(qty);
                cellPlanLine.setOldQtyPc(oldQty);
                cellPlanLineAestheticsSplit.setQtyPc(qtyOfTdg);
                cellPlanLineAestheticsSplit.setOldQtyPc(oldQtyOfTdg);
                cellPlanLineAestheticsSplit.setAesthetics("美学");
                cellPlanLineAestheticsSplit.setAestheticsId(finalLovId);
                datas.add(cellPlanLine);
                datasM.add(cellPlanLineAestheticsSplit);
            });
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段3：调用bbom接口获取透明双玻数据对应的料号信息");
            //2.2 获取美学数据对应的料号
            //Map<Long, String> itemCodesMap = cellItemCodeService.getItemCodesByCellInstockPlan(cellInstockPlanDEConvert.toEntity(datasM));
            CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto = null;
            CellInstockPlanVersionDTO cellInstockPlanVersionOverseaDto = null;
            //2.3 对美学数据进行料号匹配
            for (CellInstockPlanDTO cellInstockPlan : datasM) {
                long id = cellInstockPlan.getId();
                cellInstockPlan.setId(null);
              /*   if (itemCodesMap != null) {
                    if (StringUtils.isEmpty(itemCodesMap.get(id))) {
                        //构建料号匹配失败信息
                        String msg = getFailMsg(cellInstockPlan);
                        logService.addLog(task, ScheduleTaskStatusEnum.WARN, msg);
                    }
                    //老逻辑不赋值料号,由新方法触发料号匹配,并回写料号
//                    cellInstockPlan.setItemCode(itemCodesMap.get(id));

                } else {
                    //构建料号匹配失败信息
                    String msg = getFailMsg(cellInstockPlan);
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, msg);
                } */
                //入库版本记录开始
                CellInstockPlanDTO item = cellInstockPlan;
                if (cellInstockPlanVersionInlandDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.INLAND)) {

                        cellInstockPlanVersionInlandDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionInlandDto.setIsAesthetics(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionInlandDto));

                    }
                }
                if (cellInstockPlanVersionOverseaDto == null) {
                    if (item.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                        cellInstockPlanVersionOverseaDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getMonth(), item.getVersion()));
                        cellInstockPlanVersionOverseaDto.setIsAesthetics(1);
                        cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionOverseaDto));
                    }
                }
                //入库版本记录结束
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段4：拆后数据存储");
            //3、数据存储
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datas));
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datasM));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");

        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    /**
     * 获取供应能力加工类型Map(key->lov-value,value->attribute1)
     *
     * @return
     */
    private Map<String, String> getProcessCategoryLovMap() {
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.SILICON_PROCESS_CATEGORY);
        Map<String, String> map = new HashMap<>();
        allByHeaderCode.values().stream().forEach(entry -> {
            String attribute1 = Optional.ofNullable(entry.getAttribute1()).orElse("");

            if (attribute1.equalsIgnoreCase(ProcessCategoryConstant.DAIGONGM) ||
                    attribute1.equalsIgnoreCase(ProcessCategoryConstant.ZHICAIM) ||
                    attribute1.equalsIgnoreCase(ProcessCategoryConstant.QINGHAI))
                map.put(entry.getLovValue(), attribute1);
        });
        return map;
    }


    /**
     * 获取要进行加工类型处理的数据
     *
     * @param query
     * @return
     */
    private List<CellPlanLine> getSplitDatasByProcessCategory(CellPlanLineQuery query) {

        Pair<String, String> lastVersion = cellPlanLineService.getLastVersion(query);
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;

        List<CellPlanLine> cellPlanLines = new ArrayList<>();
        if (lastVersion == null || lastVersion.getLeft() == null && lastVersion.getRight() == null) {
            return cellPlanLines;
        }

        JPAQuery<CellPlanLine> where = jpaQueryFactory.select(qCellPlanLine).from(qCellPlanLine);
//        where.where(
//                qCellPlanLine.oldMonth.eq(query.getMonth())
//        );
        if (StringUtils.isNotEmpty(lastVersion.getRight()) && StringUtils.isNotEmpty(lastVersion.getLeft())) {
            where.where(
                    qCellPlanLine.version.eq(lastVersion.getLeft()).or(qCellPlanLine.version.eq(lastVersion.getRight()))
            );
        } else if (StringUtils.isNotEmpty(lastVersion.getLeft())) {
            where.where(
                    qCellPlanLine.version.eq(lastVersion.getLeft())
            );
        } else {
            where.where(
                    qCellPlanLine.version.eq(lastVersion.getRight())
            );
        }
        //取两条数据测试
//       where.where(
//               qCellPlanLine.id.eq(18054894L).or(qCellPlanLine.id.eq(18054906L)).or(qCellPlanLine.id.eq(18054908L)).or(qCellPlanLine.id.eq(18054910L))
//       );
        //手动优先，靠前时间优先
        //手动数据数据库里1标识，非手动0标识
        //加工类型 青海 ->1，代工 ->2, 直采M-》3
        where.orderBy(qCellPlanLine.isHandProcessCategory.desc()).
                orderBy(qCellPlanLine.startTime.asc()).
                orderBy(qCellPlanLine.processCategoryPriority.asc());
        cellPlanLines = where.fetch();
        return cellPlanLines;

    }

    /**
     * 获取要进行拆分的数据（来源入库计划）
     *
     * @param query
     * @return
     */
    private List<CellInstockPlan> getSplitDatasByCellInstock(CellInstockPlanQuery query, String type, Boolean isCover) {
        //获取版本号
        Pair<String, String> lastVersion = cellInstockPlanService.getLastVersion(query);
        QCellInstockPlan qCellInstockPlan = QCellInstockPlan.cellInstockPlan;
        List<CellInstockPlan> cellInstockPlans = new ArrayList<>();
        if (lastVersion == null || lastVersion.getLeft() == null && lastVersion.getRight() == null) {
            return cellInstockPlans;
        }
        JPAQuery<CellInstockPlan> where = jpaQueryFactory.select(qCellInstockPlan).from(qCellInstockPlan);
        where.where(
                qCellInstockPlan.month.eq(query.getMonth())
        );
        if (StringUtils.isNotEmpty(lastVersion.getRight()) && StringUtils.isNotEmpty(lastVersion.getLeft())) {
            where.where(
                    qCellInstockPlan.version.in(lastVersion.getLeft(), lastVersion.getRight())
            );
        } else if (StringUtils.isNotEmpty(lastVersion.getLeft())) {
            where.where(
                    qCellInstockPlan.version.eq(lastVersion.getLeft())
            );
        } else {
            where.where(
                    qCellInstockPlan.version.eq(lastVersion.getRight())
            );
        }

        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            where.where(
                    qCellInstockPlan.basePlace.eq(query.getBasePlace())
            );
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            where.where(
                    qCellInstockPlan.workshop.eq(query.getWorkshop())
            );
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            where.where(
                    qCellInstockPlan.cellsType.eq(query.getCellsType())
            );
        }
        if (type.equals(SiliconSplitTypeConstant.ASPLIT)) {
            where.where(
                    qCellInstockPlan.isASplit.eq(0).or(qCellInstockPlan.isASplit.isNull())
            );
        }
        if (type.equals(SiliconSplitTypeConstant.LESPLIT)) {
            where.where(qCellInstockPlan.productionGrade.ne("A-").or(qCellInstockPlan.productionGrade.isNull()).and(
                    qCellInstockPlan.isLESplit.eq(0).or(qCellInstockPlan.isLESplit.isNull())));
        }
        if (type.equals(SiliconSplitTypeConstant.TRANSPARENTDOUBLEGLASS)) {
            //透明双玻拆分排除片源种类等于"DT"的数据
            where.where(
                    qCellInstockPlan.cellSource.ne("DT")
            );
            where.where(
                    qCellInstockPlan.productionGrade.ne("A-").or(qCellInstockPlan.productionGrade.isNull())
                            .and(qCellInstockPlan.productionGrade.ne("低效").or(qCellInstockPlan.productionGrade.isNull()))
                            .and(
                                    qCellInstockPlan.isTransparentDoubleGlass.eq(0).or(qCellInstockPlan.isTransparentDoubleGlass.isNull())
                            )
            );
            if (Objects.nonNull(query.getStartDate())) {
                where.where(
                        qCellInstockPlan.startTime.goe(query.getStartDate().atTime(LocalTime.MIN))
                );
            }
            if (Objects.nonNull(query.getEndDate())) {
                where.where(
                        qCellInstockPlan.startTime.loe(query.getEndDate().atTime(LocalTime.MAX))
                );
            }

        }
        if (type.equals(SiliconSplitTypeConstant.AESTHETICS)) {
            //美学拆分排除片源种类等于"DT"的数据
            where.where(
                    qCellInstockPlan.cellSource.ne("DT")
            );
            where.where(
                    qCellInstockPlan.productionGrade.ne("A-").or(qCellInstockPlan.productionGrade.isNull()).and(
                                    qCellInstockPlan.isAestheticsSplit.eq(0).or(qCellInstockPlan.isAestheticsSplit.isNull())
                            ).and(qCellInstockPlan.productionGrade.ne("低效").or(qCellInstockPlan.productionGrade.isNull()))
                            .and(
                                    qCellInstockPlan.aesthetics.eq("无").or(
                                            qCellInstockPlan.aesthetics.isNull()
                                    )
                            )
            );
            if (Objects.nonNull(query.getStartDate())) {
                where.where(
                        qCellInstockPlan.startTime.goe(query.getStartDate().atTime(LocalTime.MIN))
                );
            }
            if (Objects.nonNull(query.getEndDate())) {
                where.where(
                        qCellInstockPlan.startTime.loe(query.getEndDate().atTime(LocalTime.MAX))
                );
            }

        }
        cellInstockPlans = where.fetch();
        return cellInstockPlans;
    }

    /**
     * 获取要进行硅片拆分的数据
     *
     * @param query
     * @return
     */
    private List<CellPlanLine> getSplitDatas(CellPlanLineQuery query, String type, Boolean isCover) {
        //获取版本号
        Pair<String, String> lastVersion = cellPlanLineService.getLastVersion(query);
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;
        List<CellPlanLine> cellPlanLines = new ArrayList<>();
        if (lastVersion == null || lastVersion.getLeft() == null && lastVersion.getRight() == null) {
            return cellPlanLines;
        }
        JPAQuery<CellPlanLine> where = jpaQueryFactory.select(qCellPlanLine).from(qCellPlanLine);
        if (StringUtils.isNotEmpty(lastVersion.getRight()) && StringUtils.isNotEmpty(lastVersion.getLeft())) {
            where.where(
                    qCellPlanLine.version.eq(lastVersion.getLeft()).or(qCellPlanLine.version.eq(lastVersion.getRight()))
            );
        } else if (StringUtils.isNotEmpty(lastVersion.getLeft())) {
            where.where(
                    qCellPlanLine.version.eq(lastVersion.getLeft())
            );
        } else {
            where.where(
                    qCellPlanLine.version.eq(lastVersion.getRight())
            );
        }

        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            where.where(
                    qCellPlanLine.basePlace.eq(query.getBasePlace())
            );
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            where.where(
                    qCellPlanLine.workshop.eq(query.getWorkshop())
            );
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            where.where(
                    qCellPlanLine.cellsType.eq(query.getCellsType())
            );
        }
        if (type.equals(SiliconSplitTypeConstant.WAFERGRADE)) {
            //针对非H处理，03月27日业务说不做限制
//            where.where(
//                    qCellPlanLine.hTrace.ne("H")
//            );
        } else if (type.equals(SiliconSplitTypeConstant.SIMFRS)) {
            //只处理H，03月27日业务说不做限制
//            where.where(
//                    qCellPlanLine.hTrace.equalsIgnoreCase("H")
//            );
        }
        if (!isCover) {
            if (type.equals(SiliconSplitTypeConstant.WAFERGRADE)) {
                where.where(
                        qCellPlanLine.isWaferGrade.eq(0).or(qCellPlanLine.isWaferGrade.isNull())
                );
            } else if (type.equals(SiliconSplitTypeConstant.SIMFRS)) {
                where.where(
                        qCellPlanLine.isSiMfrs.eq(0).or(qCellPlanLine.isSiMfrs.isNull())
                );
            }
        }
        if (type.equals(SiliconSplitTypeConstant.ASPLIT)) {

            where.where(
                    qCellPlanLine.isASplit.eq(0).or(qCellPlanLine.isASplit.isNull())
            );
        }
        if (type.equals(SiliconSplitTypeConstant.TRANSPARENTDOUBLEGLASS)) {

            where.where(
                    qCellPlanLine.productionGrade.ne("A-").or(qCellPlanLine.productionGrade.isNull()).and(
                            qCellPlanLine.isTransparentDoubleGlass.eq(0).or(qCellPlanLine.isTransparentDoubleGlass.isNull())
                    )
            );
        }

        cellPlanLines = where.fetch();
        return cellPlanLines;
    }


    @Transactional(rollbackFor = Exception.class)
    public void itemCodeMatching(CellInstockPlanQuery query) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_CELL_INSTOCK_LINE_MATCH);
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月入库数据", query.getMonth()));
            //1、获取入库料号为null的数据
//            QCellInstockPlan cellInstockPlan = QCellInstockPlan.cellInstockPlan;
//            List<CellInstockPlan> instockPlanList = jpaQueryFactory.select(cellInstockPlan).from(cellInstockPlan)
//                    .where(cellInstockPlan.month.eq(query.getMonth())
//                    .and(cellInstockPlan.isDeleted.eq(0)
//                    .and(cellInstockPlan.itemCode.isNull()))).fetch();

            if(OverseaConstant.INLAND_VALUE.equals(query.getIsOversea())){
                query.setIsOversea(OverseaConstant.INLAND);
            }else if (OverseaConstant.OVERSEA_VALUE.equals(query.getIsOversea())){
                query.setIsOversea(OverseaConstant.OVERSEA);
            }

            Pair<String, String> versions = getLastVersion(query);
            if (StringUtils.isNotBlank(query.getVersion())) {
                versions = new ImmutablePair<>(query.getVersion(), null);
            }
            List<CellInstockPlanDTO> sourceList = this.query(query, versions);

            sourceList = sourceList.stream().filter(item->StringUtils.isBlank(item.getItemCode())).collect(Collectors.toList());

            List<CellPlanLineDTO> cellPlanLineDTOS = Lists.newArrayList();
            sourceList.forEach(item->{
                CellPlanLineDTO cellPlanLineDTO = new CellPlanLineDTO();
                BeanUtils.copyProperties(item, cellPlanLineDTO);
                cellPlanLineDTO.setPlanType(PlanConstant.INSTOCK_TYPE);
                cellPlanLineDTOS.add(cellPlanLineDTO);
            });
            cellPlanLineTotalService.matchEcsCodeByWorkshop(cellPlanLineDTOS);
            cellPlanLineTotalService.setCellSource(cellPlanLineDTOS);
            //分批处理
            Integer remainder = cellPlanLineDTOS.size()%loppLimit;
            Integer sizeTime = cellPlanLineDTOS.size()/loppLimit;
            Integer remainderLeft = remainder==0?0:1;
            Integer times = sizeTime+remainderLeft;
//            List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
            for(Integer i=0;i<times;i++){
                List<CellPlanLineDTO> cellPlanLineLoopDTOS = Lists.newArrayList();
                if(i+1 == times){
                    cellPlanLineLoopDTOS.addAll(cellPlanLineDTOS.subList(i*loppLimit,cellPlanLineDTOS.size()));
                }else{
                    cellPlanLineLoopDTOS.addAll(cellPlanLineDTOS.subList(i*loppLimit,(i+1)*loppLimit));
                }
                if(i!=0){
                    //第一次要把以前的数据清理,其他循环不清
                    cellPlanLineLoopDTOS.forEach(item->{
                        item.setNoLastTime("Y");
                    });
                }
                //发给bom入库计划的old month和month保持一直
                cellPlanLineLoopDTOS.forEach(p->{
                    p.setOldMonth(p.getMonth());
                });
                cellPlanLineService.summaryGroupByCellPlanLine(cellPlanLineLoopDTOS, query.getMonth());
//                completableFutures.add(future);
                logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");
            }
            //线程同步结束后再触发成批料号匹配
//            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();

        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    @Override
    public void matchItemCallBack(CellInstockPlanQuery query) {
        try {
//数据跑入料号匹配后,触发一下批量匹配
            MaterielMatchHeaderQuery materielMatchHeaderQuery = new MaterielMatchHeaderQuery();
            materielMatchHeaderQuery.setMonth(query.getMonth());
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 这里等待7分钟，避免出现计划确认的时候调用BOM接口落库计划还未落库，此时调用料号匹配还无待匹配的数据
                try {
                    Thread.sleep(1000 * 60 * 7);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                ResponseEntity<Results<Object>> response = bbomFeign.allMatchItem(materielMatchHeaderQuery);
//                if(!response.getBody().isSuccess()){
//                    logService.addLog(task, ScheduleTaskStatusEnum.ERROR, "批量料号匹配失败");
//                }
            }, threadPoolExecutor);
        } catch (Exception ex) {
            log.info("入库计划 matchItemCallBack {}", JSON.toJSONString(ex));
            throw ex;
        }
    }

//    @Override
//    public List<CellInstockPlanDTO> query(CellInstockPlanQuery query) {
//        Pair<String, String> versions = getLastVersion(query);
//        if (StringUtils.isNotBlank(query.getVersion())) {
//            versions = new ImmutablePair<>(query.getVersion(), null);
//        }
//        return this.query(query, versions);
//    }

    @Override
    public List<CellInstockPlanDTO> query(CellInstockPlanQuery query, Pair<String, String> versions) {
        List<CellInstockPlanDTO> cellPlanLineDTOS = this.queryByVersion(versions);
        return cellPlanLineDTOS.parallelStream()
                .filter(i -> {
                    if (!i.getMonth().equals(query.getMonth())) {
                        return false;
                    }
                    if (StringUtils.isNotEmpty(query.getIsOversea()) && !i.getIsOversea().equals(query.getIsOversea())) {
                        return false;
                    }
                    if (StringUtils.isNotEmpty(query.getBasePlace()) && !i.getBasePlace().equals(query.getBasePlace())) {
                        return false;
                    }
                    if (StringUtils.isNotEmpty(query.getWorkshop()) && !i.getWorkshop().equals(query.getWorkshop())) {
                        return false;
                    }
                    return StringUtils.isBlank(query.getCellsType()) || i.getCellsType().equals(query.getCellsType());
                }).sorted(Comparator
                        .comparing(CellInstockPlanDTO::getIsOversea)
                        .thenComparing(CellInstockPlanDTO::getStartTime)
                )
                .collect(Collectors.toList());
    }

    @Override
    public List<CellInstockPlanDTO> queryByVersion(Pair<String, String> versions) {
        List<CellInstockPlanDTO> result = new LinkedList<>();
        if (StringUtils.isNotBlank(versions.getLeft())) {
            List<CellInstockPlanDTO> cellPlanLineDTOS = this.queryByVersion(versions.getLeft());
            result.addAll(cellPlanLineDTOS);
        }
        if (StringUtils.isNotBlank(versions.getRight())) {
            List<CellInstockPlanDTO> cellPlanLineDTOS = this.queryByVersion(versions.getRight());
            result.addAll(cellPlanLineDTOS);
        }
        return result;
    }

    @Override
    @Cacheable(cacheNames = "CellInstockPlanService_queryByVersion", key = "#p0", unless = "#result == null", condition = "#p0!=null")
    public List<CellInstockPlanDTO> queryByVersion(String version) {
        QCellInstockPlan cellPlanLine = QCellInstockPlan.cellInstockPlan;
        JPAQuery<CellInstockPlan> where = jpaQueryFactory.select(cellPlanLine).
                from(cellPlanLine).
                where(
                        cellPlanLine.version.eq(version)
                );

        List<CellInstockPlanDTO> cellPlanLines = cellInstockPlanDEConvert.toDto(where.fetch());
        return cellPlanLines;
    }

    /**
     * 获取版本号
     *
     * @param query
     * @return
     */
    @Override
    public Pair<String, String> getLastVersion(CellInstockPlanQuery query) {

        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getLastVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getLastVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getLastVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }
    }

    private String getLastVersion(String month, String isOversea) {
        QCellInstockPlan cellInstockPlan = QCellInstockPlan.cellInstockPlan;
        String version = jpaQueryFactory.select(cellInstockPlan.version.max()).from(cellInstockPlan).where(
                cellInstockPlan.month.eq(month)
        ).where(
                cellInstockPlan.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    @Override
    public List<CellInstockPlanSplitDTO> cellInstockPlanSplit(CellInstockPlanQuery query) {
        List<CellInstockPlanSplitDTO> resultList = new ArrayList<>();

        //拆分类型
        SplitTypeEnum splitTypeEnum = query.getSplitType();
        if(Objects.isNull(splitTypeEnum)){
            throw new BizException("拆分类型不能为空，请检查数据！");
        }

        //第一步：列表数据查询
        List<CellInstockPlanSiliconTotalDTO> cellInstockPlanSiliconTotalDTOS = getCellInstockPlanSiliconTotalDTOS(query);
        if(CollectionUtils.isEmpty(cellInstockPlanSiliconTotalDTOS)){
            return resultList;
        }

        //第二步：筛选产品等级为空 或者 透明双玻为空 或者 美学为空的数据
        List<CellInstockPlanSiliconTotalDTO> productionGradeNullList = cellInstockPlanSiliconTotalDTOS.stream().
                filter(dto -> {
                    if(SplitTypeEnum.A.equals(splitTypeEnum) || SplitTypeEnum.LOW_EFFICIENCY.equals(splitTypeEnum)){
                        return StringUtils.isBlank(dto.getProductionGrade());
                    }
                    if(SplitTypeEnum.TRANSPARENT_DOUBLE_GLASS.equals(splitTypeEnum)){
                        return StringUtils.isBlank(dto.getTransparentDoubleGlass()) || "无".equals(dto.getTransparentDoubleGlass());
                    }
                    if(SplitTypeEnum.AESTHETICS.equals(splitTypeEnum)){
                        return StringUtils.isBlank(dto.getAesthetics()) || "无".equals(dto.getAesthetics());
                    }
                    return false;
                }).collect(Collectors.toList());

        //第三部：进行维度汇总(生产车间，电池类型，H追溯，供应方式，片源种类，小区域国家，透明双玻，美学，主栅间距，产品等级)
        Map<String, List<CellInstockPlanSiliconTotalDTO>> collectMap = productionGradeNullList.stream().collect(Collectors.groupingBy(dto -> String.join("-",
                dto.getWorkshop(),dto.getCellType(),dto.getHTrace(),dto.getSupplyMethod(),dto.getCellSource(),dto.getRegionalCountry(),dto.getTransparentDoubleGlass(),dto.getAesthetics(),dto.getMainGridSpace(),dto.getProductionGrade())));

        //第四步：构建拆分数据列表
        for (Map.Entry<String, List<CellInstockPlanSiliconTotalDTO>> entry : collectMap.entrySet()){
            List<CellInstockPlanSiliconTotalDTO> cellInstockPlanSiliconTotalDtoList = entry.getValue();
            if(CollectionUtils.isNotEmpty(cellInstockPlanSiliconTotalDtoList)){
                List<Long> idList = cellInstockPlanSiliconTotalDtoList.stream().map(CellInstockPlanSiliconTotalDTO::getId).collect(Collectors.toList());
                //收集分组信息
                CellInstockPlanSiliconTotalDTO cellInstockPlanSiliconTotalDTO = cellInstockPlanSiliconTotalDtoList.stream().findFirst().get();
                CellInstockPlanSplitDTO splitDto = cellPlanLineTotalDEConvert.toSplitDto(cellInstockPlanSiliconTotalDTO);
                //特殊字段转换
                splitDto.setCellsType(cellInstockPlanSiliconTotalDTO.getCellType());
                splitDto.setCellsTypeId(cellInstockPlanSiliconTotalDTO.getCellTypeId());
                //默认未选中
                splitDto.setIsSelected(SelectedEnum.NO);
                //收集入库计划原始数据id
                splitDto.setIds(idList);
                resultList.add(splitDto);
            }
        }

        return resultList;
    }

    private List<CellInstockPlanSiliconTotalDTO> getCellInstockPlanSiliconTotalDTOS(CellInstockPlanQuery query) {
        //入库计划拆分列表分页查询接口
        query = cellInstockPlanDEConvert.toCellInstockPlanQueryByName(query);
        //查询所有数据
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        List<CellInstockPlanDTO> cellInstockPlanDTOS = cellInstockPlanService.query(query);
        List<CellInstockPlanSiliconTotalDTO> cellInstockPlanSiliconTotalDTOS = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(cellInstockPlanDTOS)){
            for (CellInstockPlanDTO cellInstockPlanDTO : cellInstockPlanDTOS){
                CellInstockPlanSiliconTotalDTO totalDTO = cellInstockPlanDEConvert.toCellInstockPlanSiliconTotalDTO(cellInstockPlanDTO);
                totalDTO.setCellType(cellInstockPlanDTO.getCellsType());
                totalDTO.setCellTypeId(cellInstockPlanDTO.getCellsTypeId());
                cellInstockPlanSiliconTotalDTOS.add(totalDTO);
            }
        }
        //List<CellInstockPlanSiliconTotalDTO> cellInstockPlanSiliconTotalDTOS = this.query(query).getContent();
        return cellInstockPlanSiliconTotalDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Override
    public void aSplitSubmit(CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        CellInstockPlanQuery cellInstockPlanQuery = splitSubmitDTO.getCellInstockPlanQuery();
        String isOversea = cellInstockPlanQuery.getIsOversea();
        String month = cellInstockPlanQuery.getMonth();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(isOversea)) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner redis_joiner = Joiner.on("-");
        String localKey = redis_joiner.join(RedisLockKey.BAPS_PREFIX, isOversea, month, "instock", "ASplitSubmit");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行A-拆分
                runSiliconASplit(splitSubmitDTO);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }
    }

    private void runSiliconASplit(CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        //查询条件 和 页面数据
        CellInstockPlanQuery query = splitSubmitDTO.getCellInstockPlanQuery();
        List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOS = splitSubmitDTO.getCellInstockPlanSplitDTOS();

        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_A_SPLIT);
            //1、获取要进行拆分的数据
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行拆分的数据", query.getMonth()));
            List<CellInstockPlanDTO> splitDatas = this.getNewSplitDatas(cellInstockPlanSplitDTOS,true);
            //2、获取A-比例
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段2：获取A-比例数据"));
            CellAMinusDto cellAMinusDto = BeanUtil.copyProperties(query, CellAMinusDto.class);
            cellAMinusDto.setCellType(query.getCellsType());
            List<ConfigCellAMinusPercentDTO> configCellAMinusPercentDTOs = siliconASplitRules(cellAMinusDto);
            //构建一个map方便查询使用
            LinkedHashMap<String, ConfigCellAMinusPercentDTO> configCellAMinusPercentDTOMap = configCellAMinusPercentDTOs.stream().collect(Collectors.toMap(
                    entity -> StringTools.joinWith(",", entity.getYear(), entity.getBasePlace(), entity.getWorkshop(), entity.getCellModel()),
                    Function.identity(),
                    (existing, replacement) -> existing,
                    LinkedHashMap::new
            ));
            //构建电池类型到电池型号的map
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段3：构建电池类型到电池型号对应关系"));
            Map<String, String> mapCelltypeToCellModel = MapStrutUtil.getMapCelltypeToCellModel();
            //4、进行拆分
            String month = query.getMonth();
            String finalYear = month.substring(0, 4);
            int month_value = DateUtil.month2LocalDate(month).getMonthValue();
            List<CellInstockPlanDTO> datas = new ArrayList<>();
            List<CellInstockPlanDTO> datasA = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段4：开始进行A-拆分"));
            Set<String> configCellAMinusSet = new LinkedHashSet<>();

            LovLineDTO productionGradeALov = LovUtils.get(LovHeaderCodeConstant.PRODUCTION_GRADE, "A-");
            LovLineDTO aestheticsNVLLov = LovUtils.get(LovHeaderCodeConstant.AESTHETICS, "无");
            LovLineDTO cellHtraceNVLLov = LovUtils.get(LovHeaderCodeConstant.H_TRACE, "无");
            LovLineDTO cellSourceNVLLov = LovUtils.get(LovHeaderCodeConstant.CELL_SOURCE, "无");
            LovLineDTO supplyMethodVLLov = LovUtils.get(LovHeaderCodeConstant.SUPPLY_METHOD, "无");
            LovLineDTO regionalCountryNVLLov = LovUtils.get(LovHeaderCodeConstant.REGIONAL_COUNTRY, "无");

            for (CellInstockPlanDTO cellPlanLine : splitDatas) {
                String cellModel = mapCelltypeToCellModel.get(cellPlanLine.getCellsType());
                String key = StringTools.joinWith(",", finalYear, cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellModel);
                //A-比例系数=aop比例系数+片源A-系数
                BigDecimal percent_value = BigDecimal.ZERO;
                //4.1获取aop系数对象
                ConfigCellAMinusPercentDTO result = configCellAMinusPercentDTOMap.get(key);
                if (result != null) {
                    String percent_str = ReflectUtil.invoke(result, "getM" + month_value + "Percent");
                    //系数值
                    BigDecimal value = MapStrutUtil.removePercentage(percent_str, 4);
                    if (value != null) {
                        percent_value = percent_value.add(value);
                    } else {
                        //记录没有aop的A-比例系数
                        configCellAMinusSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType(), "没有对应的A-比例系数！请联系AOP管理维护自制A-比例。"));
                    }
                } else {
                    configCellAMinusSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType(), "没有对应的A-比例系数！请联系AOP管理维护自制A-比例。"));
                }
                //4.1.1获取片源A-系数
                if (cellPlanLine.getWaferGradeRatio() != null) {
                    if (cellPlanLine.getWaferGradeRatio().compareTo(BigDecimal.ZERO) != 0) {
                        percent_value = percent_value.add(cellPlanLine.getWaferGradeRatio());
                    }
                }
                if (null != percent_value && percent_value.compareTo(BigDecimal.ZERO) != 0) {
                    //3.2 根据系数值进行A-拆分
                    SelectedEnum selectedEnum = cellPlanLine.getIsSelected();
                    if(Objects.isNull(selectedEnum)){
                        continue;
                    }
                    BigDecimal qty = cellPlanLine.getQtyPc();
                    BigDecimal oldQty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(qty);
                    BigDecimal a_qty = qty.multiply(percent_value);
                    BigDecimal old_a_qty = oldQty.multiply(percent_value);
                    qty = qty.subtract(a_qty);
                    oldQty = oldQty.subtract(old_a_qty);
                    //原数据
                    CellInstockPlanDTO cellPlanLineA = BeanUtil.copyProperties(cellPlanLine, CellInstockPlanDTO.class);
                    cellPlanLine.setQtyPc(qty);
                    cellPlanLine.setOldQtyPc(oldQty);
                    cellPlanLine.setIsASplit(1);
                    //A-数据初始化
                    cellPlanLineA.setId(null);
                    cellPlanLineA.setQtyPc(a_qty);
                    cellPlanLineA.setOldQtyPc(old_a_qty);
                    cellPlanLineA.setProductionGradeId(Objects.nonNull(productionGradeALov) ? productionGradeALov.getLovLineId() : null);
                    cellPlanLineA.setProductionGrade(Objects.nonNull(productionGradeALov) ? productionGradeALov.getLovName() : "A-");
                    cellPlanLineA.setIsASplit(1);
                    cellPlanLineA.setItemCode(null);
                    // 用户未确认数据处理
                    if(SelectedEnum.NO.equals(selectedEnum)){
                        //A-数据初始化
                        //H追溯为无，供应方式为无，片源种类为无，小区域国家为空，相同透明双玻，美学为空
                        cellPlanLineA.setHTrace(cellHtraceNVLLov.getLovName());
                        cellPlanLineA.setHTraceId(cellHtraceNVLLov.getLovLineId());
                        cellPlanLineA.setSupplyMethod(supplyMethodVLLov.getLovName());
                        cellPlanLineA.setSupplyMethodId(supplyMethodVLLov.getLovLineId());
                        cellPlanLineA.setCellSource(cellSourceNVLLov.getLovName());
                        cellPlanLineA.setCellSourceId(cellSourceNVLLov.getLovLineId());
                        cellPlanLineA.setRegionalCountry(regionalCountryNVLLov.getLovName());
                        cellPlanLineA.setRegionalCountryId(regionalCountryNVLLov.getLovLineId());
                        cellPlanLineA.setAesthetics(aestheticsNVLLov.getLovName());
                        cellPlanLineA.setAestheticsId(aestheticsNVLLov.getLovLineId());
                        cellPlanLineA.setProcessCategory(null);
                        cellPlanLineA.setRatioCode(null);
                        cellPlanLineA.setCertCode(null);
                    }
                    datas.add(cellPlanLine);
                    datasA.add(cellPlanLineA);
                }
            }
            //4.3 没有A-比例系数的数据进行日志记录
            if (CollectionUtils.isNotEmpty(configCellAMinusSet)) {
                for (String item : configCellAMinusSet) {
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, item);
                }
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段5：拆后数据存储"));
            //5.1 合并数据
            this.mergeAData(query,datas,datasA);
            //5.2 数据存储
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datas));
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datasA));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");
        } catch (Exception exception) {
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    private void mergeAData(CellInstockPlanQuery query, List<CellInstockPlanDTO> updateDatas, List<CellInstockPlanDTO> addDatasA) {
        //查询所有数据
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        List<CellInstockPlanDTO> cellInstockPlanDTOS = cellInstockPlanService.query(query);
        if(CollectionUtils.isEmpty(cellInstockPlanDTOS) || CollectionUtils.isEmpty(addDatasA)){
            return;
        }
        //分组
        Map<Object, List<CellInstockPlanDTO>> addDTOMap = addDatasA.stream().collect(Collectors.groupingBy(dto -> String.join(",",
                dto.getIsOversea(),
                dto.getBasePlace(),
                dto.getWorkshop(),
                dto.getCellsType(),
                dto.getHTrace(),
                dto.getProductionGrade(),
                dto.getProcessCategory(),
                dto.getWaferGrade(),
                dto.getSiMfrs(),
                dto.getTransparentDoubleGlass(),
                dto.getAesthetics(),
                dto.getSupplyMethod(),
                dto.getMonth())));

        List<CellInstockPlanDTO> removeDtoList = new ArrayList<>();
        for (CellInstockPlanDTO dbDto : cellInstockPlanDTOS){
            //列表原始数据
            BigDecimal qtyPc = dbDto.getQtyPc();
            BigDecimal oldQtyPc = dbDto.getOldQtyPc();

            //业务唯一标识
            String key = String.join(",",
                    dbDto.getIsOversea(),
                    dbDto.getBasePlace(),
                    dbDto.getWorkshop(),
                    dbDto.getCellsType(),
                    dbDto.getHTrace(),
                    dbDto.getProductionGrade(),
                    dbDto.getProcessCategory(),
                    dbDto.getWaferGrade(),
                    dbDto.getSiMfrs(),
                    dbDto.getTransparentDoubleGlass(),
                    dbDto.getAesthetics(),
                    dbDto.getSupplyMethod(),
                    dbDto.getMonth());

            List<CellInstockPlanDTO> cellInstockPlanDTOList = addDTOMap.get(key);
            for (CellInstockPlanDTO cellInstockPlanDTO : cellInstockPlanDTOList){
                qtyPc = qtyPc.add(cellInstockPlanDTO.getQtyPc());
                oldQtyPc = oldQtyPc.add(cellInstockPlanDTO.getOldQtyPc());
                removeDtoList.add(cellInstockPlanDTO);
            }
            dbDto.setQtyPc(qtyPc);
            dbDto.setOldQtyPc(oldQtyPc);
            //合并数据
            updateDatas.add(dbDto);
        }
        //移除已合并数据
        addDatasA.removeAll(removeDtoList);
    }

    private List<CellInstockPlanDTO> getNewSplitDatas(List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOS,Boolean flag) {
        List<CellInstockPlanDTO> cellInstockPlanDTOS = new ArrayList<>();
        if(CollectionUtils.isEmpty(cellInstockPlanSplitDTOS)){
            return cellInstockPlanDTOS;
        }
        //用户已确认
        List<CellInstockPlanDTO> splitSelectedDatas = this.getSplitSelectedDatas(cellInstockPlanSplitDTOS, SelectedEnum.YES, flag);
        if(CollectionUtils.isNotEmpty(splitSelectedDatas)){
            cellInstockPlanDTOS.addAll(splitSelectedDatas);
        }
        //用户未确认
        List<CellInstockPlanDTO> splitNoSelectedDatas = this.getSplitSelectedDatas(cellInstockPlanSplitDTOS, SelectedEnum.NO, flag);
        if(CollectionUtils.isNotEmpty(splitNoSelectedDatas)){
            cellInstockPlanDTOS.addAll(splitNoSelectedDatas);
        }
        return  cellInstockPlanDTOS;
    }

    private List<CellInstockPlanDTO> getSplitSelectedDatas(List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOS, SelectedEnum selectedEnum,Boolean flag) {
        List<CellInstockPlanDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isEmpty(cellInstockPlanSplitDTOS)){
            return resultList;
        }

        //原始数据
        List<CellInstockPlanSplitDTO> selectedCellInstockPlanSplit = cellInstockPlanSplitDTOS.stream().filter(
                dto -> selectedEnum.equals(dto.getIsSelected())).collect(Collectors.toList());

        //分组汇总（唯一标识获取业务属性）
        Map<String, List<CellInstockPlanSplitDTO>> collectMap = selectedCellInstockPlanSplit.stream().collect(Collectors.groupingBy(dto ->{
            String hTrace = StringUtils.isBlank(dto.getHTrace()) ? "无" : dto.getHTrace();
            String cellSource = StringUtils.isBlank(dto.getCellSource()) ? "无" : dto.getCellSource();
            String supplyMethod = StringUtils.isBlank(dto.getSupplyMethod()) ? "无" : dto.getSupplyMethod();
            String regionalCountry = StringUtils.isBlank(dto.getRegionalCountry()) ? "无" : dto.getRegionalCountry();
            String aesthetics = StringUtils.isBlank(dto.getAesthetics()) ? "无" : dto.getAesthetics();
            String mainGridSpace = StringUtils.isBlank(dto.getMainGridSpace()) ? "无" : dto.getMainGridSpace();
            String productionGrade = StringUtils.isBlank(dto.getProductionGrade()) ? "无" : dto.getProductionGrade();
            return String.join("-",dto.getWorkshop(),dto.getCellsType(),hTrace,
                    supplyMethod,cellSource,regionalCountry,dto.getTransparentDoubleGlass(),
                    aesthetics,mainGridSpace,productionGrade);
        }));

        //收集原始数据id
        List<Long> selectedIds = new ArrayList<>();
        selectedCellInstockPlanSplit.stream().forEach(dto->{
            List<Long> ids = dto.getIds();
            selectedIds.addAll(ids);
        });

        //查询数据库
        QCellInstockPlan qCellInstockPlan = QCellInstockPlan.cellInstockPlan;
        JPAQuery<CellInstockPlan> where = jpaQueryFactory.select(qCellInstockPlan).from(qCellInstockPlan);
        where.where(qCellInstockPlan.id.in(selectedIds));
        //where.where(qCellInstockPlan.isDeleted.eq(DeleteEnum.NO.getCode()));
        List<CellInstockPlan> cellInstockPlans = where.fetch();

        //将原始数据填充关联对应分组信息
        List<CellInstockPlanDTO> splitDatas = cellInstockPlanDEConvert.toDto(cellInstockPlans);
        if(CollectionUtils.isNotEmpty(splitDatas)){
            splitDatas.stream().forEach(dto->{
                LocalDateTime startTime = dto.getStartTime();
                //用户是否选择状态
                dto.setIsSelected(selectedEnum);
                //用户拆分比例和拆分时间
                String hTrace = StringUtils.isBlank(dto.getHTrace()) ? "无" : dto.getHTrace();
                String cellSource = StringUtils.isBlank(dto.getCellSource()) ? "无" : dto.getCellSource();
                String supplyMethod = StringUtils.isBlank(dto.getSupplyMethod()) ? "无" : dto.getSupplyMethod();
                String regionalCountry = StringUtils.isBlank(dto.getRegionalCountry()) ? "无" : dto.getRegionalCountry();
                String aesthetics = StringUtils.isBlank(dto.getAesthetics()) ? "无" : dto.getAesthetics();
                String mainGridSpace = StringUtils.isBlank(dto.getMainGridSpace()) ? "无" : dto.getMainGridSpace();
                String productionGrade = StringUtils.isBlank(dto.getProductionGrade()) ? "无" : dto.getProductionGrade();

                String key = String.join("-",dto.getWorkshop(),dto.getCellsType(),hTrace,
                        supplyMethod,cellSource,regionalCountry,dto.getTransparentDoubleGlass(),
                        aesthetics,mainGridSpace,productionGrade);
                List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOList = collectMap.get(key);

                //透明双玻或者美学拆分
                if(CollectionUtils.isNotEmpty(cellInstockPlanSplitDTOList)){
                    CellInstockPlanSplitDTO cellInstockPlanSplitDTO = cellInstockPlanSplitDTOList.stream().findFirst().get();
                    if(Objects.nonNull(cellInstockPlanSplitDTO)){
                        LocalDate splitStartDate = cellInstockPlanSplitDTO.getSplitStartDate();
                        LocalDate splitEndDate = cellInstockPlanSplitDTO.getSplitEndDate();
                        BigDecimal splitRate = cellInstockPlanSplitDTO.getSplitRate();
                        //符合条件（拆分时间）
                        if(Objects.nonNull(startTime) &&
                                Objects.nonNull(splitStartDate) && Objects.nonNull(splitEndDate) && Objects.nonNull(splitRate) &&
                                startTime.isAfter(splitStartDate.atTime(LocalTime.MIN))  &&
                                startTime.isBefore(splitEndDate.atTime(LocalTime.MAX))){
                            dto.setSplitRate(splitRate);
                            dto.setSplitStartDate(splitStartDate);
                            dto.setSplitEndDate(splitEndDate);
                            //收集数据
                            resultList.add(dto);
                        }else if(flag){
                            //A- 或者 低效电池
                            resultList.add(dto);
                        }
                    }
                }
            });
        }
        return resultList;
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Override
    public void lowEfficiencySplitSubmit(CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        CellInstockPlanQuery cellInstockPlanQuery = splitSubmitDTO.getCellInstockPlanQuery();
        String isOversea = cellInstockPlanQuery.getIsOversea();
        String month = cellInstockPlanQuery.getMonth();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(isOversea)) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner redis_joiner = Joiner.on("-");
        String localKey = redis_joiner.join(RedisLockKey.BAPS_PREFIX, isOversea, month, "instock", "LEspliteSubmit");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行低效拆分
                runSiliconLESplit(splitSubmitDTO);
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }
    }

    private void runSiliconLESplit(CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        //查询条件 和 页面数据
        CellInstockPlanQuery query = splitSubmitDTO.getCellInstockPlanQuery();
        List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOS = splitSubmitDTO.getCellInstockPlanSplitDTOS();

        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_LE_SPLIT);
            //1、获取要进行拆分的数据
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行拆分的数据", query.getMonth()));
            List<CellInstockPlanDTO> splitDatas = this.getNewSplitDatas(cellInstockPlanSplitDTOS,true);
            //2、获取低效比例
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING,"阶段2：获取低效比例数据");
            LowEfficiencyCellPercentQuery lowEfficiencyCellPercentQuery = new LowEfficiencyCellPercentQuery();
            List<LowEfficiencyCellPercentDTO> configCellAMinusPercentDTOs = siliconLESplitRules(lowEfficiencyCellPercentQuery);
            Map<String, LowEfficiencyCellPercentDTO> configCellAMinusPercentDTOMap = configCellAMinusPercentDTOs.stream().collect(Collectors.toMap(
                    entity -> StringTools.joinWith(",", entity.getYear(), entity.getBasePlace(), entity.getWorkshop(), entity.getCellType()),
                    Function.identity(),
                    (existing, replacement) -> existing
            ));
            //3、进行拆分
            String month = query.getMonth();
            String finalYear = month.substring(0, 4);
            int month_value = DateUtil.month2LocalDate(month).getMonthValue();
            List<CellInstockPlanDTO> datas = new ArrayList<>();
            List<CellInstockPlanDTO> datasLE = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段3：开始进行低效拆分");
            LovLineDTO productionGradeALov = LovUtils.get(LovHeaderCodeConstant.PRODUCTION_GRADE, "Q1低效");
            LovLineDTO aestheticsNVLLov = LovUtils.get(LovHeaderCodeConstant.AESTHETICS, "无");
            LovLineDTO cellHtraceNVLLov = LovUtils.get(LovHeaderCodeConstant.H_TRACE, "无");
            LovLineDTO cellSourceNVLLov = LovUtils.get(LovHeaderCodeConstant.CELL_SOURCE, "无");
            LovLineDTO supplyMethodVLLov = LovUtils.get(LovHeaderCodeConstant.SUPPLY_METHOD, "无");
            LovLineDTO regionalCountryNVLLov = LovUtils.get(LovHeaderCodeConstant.REGIONAL_COUNTRY, "无");
            Set<String> configCellAMinusSet = new LinkedHashSet<>();
            for (CellInstockPlanDTO cellPlanLine : splitDatas) {
                LovLineDTO cellsTypeLov = LovUtils.get(cellPlanLine.getCellsTypeId());
                String cellTypeLovValue = Optional.ofNullable(cellsTypeLov).map(LovLineDTO::getLovValue).orElse("");
                String key = StringTools.joinWith(",", finalYear, cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(),cellTypeLovValue);
                //A-比例系数=aop比例系数+片源A-系数
                BigDecimal percent_value = BigDecimal.ZERO;
                //3.1获取aop系数对象
                LowEfficiencyCellPercentDTO result = configCellAMinusPercentDTOMap.get(key);
                if (result != null) {
                    String percent_str = ReflectUtil.invoke(result, "getM" + month_value + "Percent");
                    //系数值
                    BigDecimal value = MapStrutUtil.removePercentage(percent_str, 4);
                    if (value != null) {
                        percent_value = percent_value.add(value);
                    } else {
                        //记录没有bbom的低效比例系数
                        configCellAMinusSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType(), "没有对应的低效比例系数！请联系bbom管理维护低效比例。"));
                    }
                } else {
                    configCellAMinusSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType(), "没有对应的低效比例系数！请联系bbom管理维护低效比例。"));
                }
                if (percent_value.compareTo(BigDecimal.ZERO) != 0) {
                    //3.2 根据系数值进行拆分
                    SelectedEnum selectedEnum = cellPlanLine.getIsSelected();
                    if(Objects.isNull(selectedEnum)){
                        continue;
                    }
                    BigDecimal qty = cellPlanLine.getQtyPc();
                    BigDecimal oldQty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(qty);
                    BigDecimal a_qty = qty.multiply(percent_value);
                    BigDecimal old_a_qty = oldQty.multiply(percent_value);
                    qty = qty.subtract(a_qty);
                    oldQty = oldQty.subtract(old_a_qty);
                    CellInstockPlanDTO cellPlanLineLE = BeanUtil.copyProperties(cellPlanLine, CellInstockPlanDTO.class);
                    cellPlanLine.setQtyPc(qty);
                    cellPlanLine.setOldQtyPc(oldQty);
                    cellPlanLine.setIsLESplit(1);
                    //低效拆分初始化
                    cellPlanLineLE.setId(null);
                    cellPlanLineLE.setQtyPc(a_qty);
                    cellPlanLineLE.setOldQtyPc(old_a_qty);
                    //产品等级
                    cellPlanLineLE.setProductionGrade(Objects.nonNull(productionGradeALov)? productionGradeALov.getLovName() : "Q1低效");
                    cellPlanLineLE.setProductionGradeId(Objects.nonNull(productionGradeALov)? productionGradeALov.getLovLineId() : null);
                    cellPlanLineLE.setIsLESplit(1);
                    cellPlanLineLE.setItemCode(null);
                    // 用户未确认数据处理
                    if(SelectedEnum.NO.equals(selectedEnum)){
                        //H追溯为无，供应方式为无，片源种类为无，小区域国家为空，相同透明双玻，美学为空
                        cellPlanLineLE.setHTrace(cellHtraceNVLLov.getLovName());
                        cellPlanLineLE.setHTraceId(cellHtraceNVLLov.getLovLineId());
                        cellPlanLineLE.setSupplyMethod(supplyMethodVLLov.getLovName());
                        cellPlanLineLE.setSupplyMethodId(supplyMethodVLLov.getLovLineId());
                        cellPlanLineLE.setCellSource(cellSourceNVLLov.getLovName());
                        cellPlanLineLE.setCellSourceId(cellSourceNVLLov.getLovLineId());
                        cellPlanLineLE.setRegionalCountry(regionalCountryNVLLov.getLovName());
                        cellPlanLineLE.setRegionalCountryId(regionalCountryNVLLov.getLovLineId());
                        cellPlanLineLE.setAesthetics(aestheticsNVLLov.getLovName());
                        cellPlanLineLE.setAestheticsId(aestheticsNVLLov.getLovLineId());
                        cellPlanLineLE.setProcessCategory(null);
                        cellPlanLineLE.setCertCode(null);
                        cellPlanLineLE.setRatioCode(null);
                    }
                    datas.add(cellPlanLine);
                    datasLE.add(cellPlanLineLE);
                }
            }
            //3.4 没有低效比例系数的数据进行日志记录
            if (CollectionUtils.isNotEmpty(configCellAMinusSet)) {
                for (String item : configCellAMinusSet
                ) {
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, item);
                }
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING,"阶段4：拆后数据存储");
            //3.4 合并数据
            this.mergeAData(query,datas,datasLE);
            //3.5 数据存储
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datas));
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datasLE));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");
        } catch (Exception exception) {
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Override
    public void transparentDoubleGlassSplitSubmit(CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        CellInstockPlanQuery cellInstockPlanQuery = splitSubmitDTO.getCellInstockPlanQuery();
        String isOversea = cellInstockPlanQuery.getIsOversea();
        String month = cellInstockPlanQuery.getMonth();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(isOversea)) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, isOversea, month, "instock", "transparentDoubleGlassSplitSubmit");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行透明双玻拆分
                runSiliconTransparentDoubleGlassSplit(splitSubmitDTO);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }
    }

    private void runSiliconTransparentDoubleGlassSplit(CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        //查询条件 和 页面数据
        CellInstockPlanQuery query = splitSubmitDTO.getCellInstockPlanQuery();
        List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOS = splitSubmitDTO.getCellInstockPlanSplitDTOS();
        List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOSTemp = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(cellInstockPlanSplitDTOS)){
            cellInstockPlanSplitDTOSTemp = cellInstockPlanSplitDTOS.stream().filter(
                    dto -> SelectedEnum.YES.equals(dto.getIsSelected())).collect(Collectors.toList());
            log.info("透明双玻过滤拆分数据--->{}", JSON.toJSONString(cellInstockPlanSplitDTOSTemp));
        }
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_TRANSPARENTDOUBLEGLASS_SPLIT);
            //1、获取要进行透明双玻的处理的数据
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行拆分的数据", query.getMonth()));
            List<CellInstockPlanDTO> splitDatas = this.getNewSplitDatas(cellInstockPlanSplitDTOSTemp, false);
            //2、进行拆分
            List<CellInstockPlanDTO> datas = new ArrayList<>();
            List<CellInstockPlanDTO> datasT = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段2：进行拆分数据");
            LovLineDTO transparentDoubleGlassLov = LovUtils.get(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS, "透明双玻");
            splitDatas.stream().forEach(cellPlanLine -> {
                //2.1 拆分处理
                SelectedEnum selectedEnum = cellPlanLine.getIsSelected();
                BigDecimal splitRate = cellPlanLine.getSplitRate();
                LocalDate splitStartDate = cellPlanLine.getSplitStartDate();
                LocalDate splitEndDate = cellPlanLine.getSplitEndDate();
                //用户已确认
                if(Objects.nonNull(selectedEnum) && SelectedEnum.YES.equals(selectedEnum)){
                    if(Objects.isNull(splitRate) || Objects.isNull(splitStartDate) || Objects.isNull(splitEndDate)){
                        throw new BizException("拆分比例，拆分时间不能为空，请检查数据！");
                    }
                    CellInstockPlanDTO cellPlanLineTransparentDoubleGlassSplit = BeanUtil.copyProperties(cellPlanLine, CellInstockPlanDTO.class);
                    BigDecimal qty = cellPlanLine.getQtyPc();
                    BigDecimal oldQty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(qty);
                    BigDecimal qtyOfTdg = qty.multiply(splitRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    BigDecimal oldQtyOfTdg = oldQty.multiply(splitRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    qty = qty.subtract(qtyOfTdg);
                    oldQty = oldQty.subtract(oldQtyOfTdg);
                    cellPlanLineTransparentDoubleGlassSplit.setId(null);
                    cellPlanLineTransparentDoubleGlassSplit.setIsTransparentDoubleGlass(1);
                    cellPlanLine.setQtyPc(qty);
                    cellPlanLine.setOldQtyPc(oldQty);
                    cellPlanLine.setIsTransparentDoubleGlass(1);
                    cellPlanLineTransparentDoubleGlassSplit.setQtyPc(qtyOfTdg);
                    cellPlanLineTransparentDoubleGlassSplit.setOldQtyPc(oldQtyOfTdg);
                    cellPlanLineTransparentDoubleGlassSplit.setIsTransparentDoubleGlass(1);
                    cellPlanLineTransparentDoubleGlassSplit.setTransparentDoubleGlass(Objects.nonNull(transparentDoubleGlassLov) ? transparentDoubleGlassLov.getLovName() : "透明双玻");
                    cellPlanLineTransparentDoubleGlassSplit.setTransparentDoubleGlassId(Objects.nonNull(transparentDoubleGlassLov) ? transparentDoubleGlassLov.getLovLineId() : null);
                    cellPlanLineTransparentDoubleGlassSplit.setItemCode(null);
                    datas.add(cellPlanLine);
                    datasT.add(cellPlanLineTransparentDoubleGlassSplit);
                }
            });
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段3：拆后数据存储");
            //3.1 合并数据
            this.mergeAData(query,datas,datasT);
            //3.2 数据存储
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datas));
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datasT));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");
        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Override
    public void aestheticsSplitSubmit(CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        CellInstockPlanQuery cellInstockPlanQuery = splitSubmitDTO.getCellInstockPlanQuery();
        String isOversea = cellInstockPlanQuery.getIsOversea();
        String month = cellInstockPlanQuery.getMonth();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isEmpty(isOversea)) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, isOversea, month, "instock", "aestheticsSplitSubmit");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行美学拆分
                runAestheticsSplit(splitSubmitDTO);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }
    }

    private void runAestheticsSplit(CellInstockPlanSplitSubmitDTO splitSubmitDTO) {
        //查询条件 和 页面数据
        CellInstockPlanQuery query = splitSubmitDTO.getCellInstockPlanQuery();
        List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOS = splitSubmitDTO.getCellInstockPlanSplitDTOS();
        List<CellInstockPlanSplitDTO> cellInstockPlanSplitDTOSTemp = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(cellInstockPlanSplitDTOS)){
            cellInstockPlanSplitDTOSTemp = cellInstockPlanSplitDTOS.stream().filter(
                    dto -> SelectedEnum.YES.equals(dto.getIsSelected())).collect(Collectors.toList());
            log.info("美学过滤拆分数据--->{}", JSON.toJSONString(cellInstockPlanSplitDTOSTemp));
        }

        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_AESTHETICS_SPLIT);
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月要进行拆分的数据", query.getMonth()));
            List<CellInstockPlanDTO> splitDatas = this.getNewSplitDatas(cellInstockPlanSplitDTOSTemp, false);
            //2、进行拆分
            List<CellInstockPlanDTO> datas = new ArrayList<>();
            List<CellInstockPlanDTO> datasM = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段2：进行拆分数据");
            LovLineDTO aestheticsLov = LovUtils.get(LovHeaderCodeConstant.AESTHETICS, "美学");
            splitDatas.stream().forEach(cellPlanLine -> {
                //2.1 拆分处理
                SelectedEnum selectedEnum = cellPlanLine.getIsSelected();
                BigDecimal splitRate = cellPlanLine.getSplitRate();
                LocalDate splitStartDate = cellPlanLine.getSplitStartDate();
                LocalDate splitEndDate = cellPlanLine.getSplitEndDate();
                //用户已确认
                if(Objects.nonNull(selectedEnum) && SelectedEnum.YES.equals(selectedEnum)){
                    if(Objects.isNull(splitRate) || Objects.isNull(splitStartDate) || Objects.isNull(splitEndDate)){
                        throw new BizException("拆分比例，拆分时间不能为空，请检查数据！");
                    }
                    CellInstockPlanDTO cellPlanLineAestheticsSplit = BeanUtil.copyProperties(cellPlanLine, CellInstockPlanDTO.class);
                    BigDecimal qty = cellPlanLine.getQtyPc();
                    BigDecimal oldQty = Optional.ofNullable(cellPlanLine.getOldQtyPc()).orElse(qty);
                    BigDecimal qtyOfTdg = qty.multiply(splitRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    BigDecimal oldQtyOfTdg = oldQty.multiply(splitRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    qty = qty.subtract(qtyOfTdg);
                    oldQty = oldQty.subtract(oldQtyOfTdg);
                    cellPlanLine.setQtyPc(qty);
                    cellPlanLine.setOldQtyPc(oldQty);
                    cellPlanLine.setIsAestheticsSplit(1);
                    cellPlanLineAestheticsSplit.setId(null);
                    cellPlanLineAestheticsSplit.setQtyPc(qtyOfTdg);
                    cellPlanLineAestheticsSplit.setOldQtyPc(oldQtyOfTdg);
                    cellPlanLineAestheticsSplit.setIsAestheticsSplit(1);
                    cellPlanLineAestheticsSplit.setAesthetics(Objects.nonNull(aestheticsLov) ? aestheticsLov.getLovName() : "美学");
                    cellPlanLineAestheticsSplit.setAestheticsId(Objects.nonNull(aestheticsLov) ? aestheticsLov.getLovLineId() : null);
                    cellPlanLineAestheticsSplit.setItemCode(null);
                    datas.add(cellPlanLine);
                    datasM.add(cellPlanLineAestheticsSplit);
                }
            });
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, "阶段3：拆后数据存储");
            //3.1 合并数据
            this.mergeAData(query,datas,datasM);
            //3.2 数据存储
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datas));
            cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(datasM));
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");
        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CellPlanLineDTO> processCategorySplitByCellPlanLine(ProcessCategorySplitSubmitDTO query){
        ProcessCategorySplitDto processCategorySplitQuery = query.getProcessCategorySplitQuery();
        processCategorySplitQuery = cellPlanLineDEConvert.toProcessCategorySplitDtoByName(processCategorySplitQuery);
        // 页面待拆分的集合
        List<CellPlanLineSplitDTO> cellPlanLineSplitDTOS = query.getCellPlanLineSplitDTOS();
        // 1. 同时点击确认时，如果业务选择的日期范围中存在process_category不为空的投产计划行，提示用户修改日期范围
        CellPlanLineQuery cellPlanLineQuery = new CellPlanLineQuery();
        cellPlanLineQuery.setIsOversea(processCategorySplitQuery.getIsOversea());
        cellPlanLineQuery.setMonth(processCategorySplitQuery.getMonth());
        List<CellPlanLineDTO> cellPlanLineDTOList = cellPlanLineService.query(cellPlanLineQuery);

        if (CollectionUtils.isEmpty(cellPlanLineDTOList) || CollectionUtils.isEmpty(cellPlanLineSplitDTOS) ){
            return cellPlanLineDTOList;
        }
        List<CellPlanLineDTO> nonProcessCategoryCellPlanLineList = cellPlanLineDTOList.stream().filter(dto -> !ProcessCategoryConstant.WU.equals(dto.getHTrace()))
                .filter(dto -> StringUtils.isNotBlank(dto.getProcessCategory()))
                .filter(dto -> !ProcessCategoryConstant.WU.equals(dto.getProcessCategory())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(nonProcessCategoryCellPlanLineList)){
            checkEffectiveRange(cellPlanLineSplitDTOS, nonProcessCategoryCellPlanLineList);
        }
        List<CellPlanLineDTO> nonHTraceCellPlanLineList = cellPlanLineDTOList.stream().filter(dto -> !ProcessCategoryConstant.WU.equals(dto.getHTrace())).collect(Collectors.toList());
        // 更新保存 拆分结果
        setProcessCategoryBySplitDate(cellPlanLineSplitDTOS, nonHTraceCellPlanLineList);
        return nonHTraceCellPlanLineList;
    }

    // 根据拆分区间来设置 ProcessCategory
       private void setProcessCategoryBySplitDate(List<CellPlanLineSplitDTO> cellPlanLineSplitDTOS, List<CellPlanLineDTO> nonHTraceCellPlanLineList){
           Map<String, List<CellPlanLineDTO>> cellPlanLineSplitMap = nonHTraceCellPlanLineList.stream().collect(Collectors.groupingBy(CellPlanLineDTO::splitGroup));
           for (CellPlanLineSplitDTO cellPlanLineSplitDTO : cellPlanLineSplitDTOS) {
               List<CellPlanLineDTO> cellPlanLineDTOS= cellPlanLineSplitMap.get(cellPlanLineSplitDTO.splitGroup());
               if (CollectionUtils.isEmpty(cellPlanLineDTOS)) {
                   continue;
               }
               List<CellPlanLineDTO> cellPlanLineDTOSBySplitData= new ArrayList<>(cellPlanLineDTOS.size());
               for (CellPlanLineDTO cellPlanLineDTO : cellPlanLineDTOS) {
                   // 根据拆分区间 来设置待拆分的 投产计划List
                   if(isEffectiveRange(cellPlanLineSplitDTO, cellPlanLineDTO)){
                       cellPlanLineDTO.setProcessCategory(cellPlanLineSplitDTO.getProcessCategory());
                       Long processCategoryId = MapStrutUtil.getIdByCNName(LovHeaderCodeConstant.CELL_PROCESS_CATEGORY, cellPlanLineSplitDTO.getProcessCategory());
                       cellPlanLineDTO.setProcessCategoryId(processCategoryId);
                       cellPlanLineDTOSBySplitData.add(cellPlanLineDTO);
                   }
               }

               if(CollectionUtils.isNotEmpty(cellPlanLineDTOSBySplitData)){
                 cellPlanLineRepository.saveAll(cellPlanLineDEConvert.toEntity(cellPlanLineDTOSBySplitData));
               }
           }
       }

    /**
     *
     * @param cellPlanLineSplitDTOS 待拆分的数据
     * @param cellPlanLineDTOList 数据库投产计划
     */
    private void checkEffectiveRange(List<CellPlanLineSplitDTO> cellPlanLineSplitDTOS, List<CellPlanLineDTO> cellPlanLineDTOList) {
       // 根据 选择区间内是否 有数据

        // 序列号为 【1】的 拆分日期范围在【%s】内的加工类型不为空，,请修改拆分日期范围！
        StringBuffer msg = new StringBuffer();
        for (CellPlanLineSplitDTO cellPlanLineSplit : cellPlanLineSplitDTOS) {
            String orderNo = cellPlanLineSplit.getOrderNo();
            for (CellPlanLineDTO cellPlanLineDTO : cellPlanLineDTOList) {
                if (isEffectiveRange(cellPlanLineSplit, cellPlanLineDTO)) {
                    String splitDataRange = cellPlanLineSplit.getSplitStartDate().toString() + " ~ " + cellPlanLineSplit.getSplitEndDate().toString();
                    msg.append("序列号为【").append(orderNo).append("】的 拆分日期范围在【").append(splitDataRange).append("】内的加工类型不为空，请修改拆分日期范围！");
                    break;
                }
            }
        }
        if (StringUtils.isNotBlank(msg)){
            throw new BizException(msg.toString());
        }
    }

    private boolean isEffectiveRange(CellPlanLineSplitDTO cellPlanLineSplitDTO, CellPlanLineDTO cellPlanLineDTO) {
        if (!cellPlanLineSplitDTO.splitGroup().equals(cellPlanLineDTO.splitGroup())){
            return false;
        }
        LocalDate splitStartDate = cellPlanLineSplitDTO.getSplitStartDate();
        LocalDate splitEndDate = cellPlanLineSplitDTO.getSplitEndDate();
        return isRangeWithin(splitStartDate, splitEndDate, cellPlanLineDTO.getStartTime().toLocalDate(), cellPlanLineDTO.getEndTime().toLocalDate());
    }

    /**
     * 检查 split 区间是否完全包含在 cell 区间内
     *
     * @param splitStart split的开始日期
     * @param splitEnd   split的结束日期
     * @param cellStart  cell的开始日期
     * @param cellEnd    cell的结束日期
     * @return true 如果 cell 区间在 split 区间内，否则 false
     */
    public static boolean isRangeWithin(LocalDate splitStart, LocalDate splitEnd, LocalDate cellStart, LocalDate cellEnd) {
        return !cellStart.isAfter(splitEnd) && !cellEnd.isBefore(splitStart);
    }

}


