package com.trinasolar.scp.baps.service.repository;

import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.entity.CellPlanLine;
import com.trinasolar.scp.baps.domain.entity.CellProductionPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 入库计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Repository
public interface CellPlanLineRepository extends JpaRepository<CellPlanLine, Long>, QuerydslPredicateExecutor<CellPlanLine> {
    @Query(value = "from CellPlanLine c where c.bbomId = :bbomId")
    public CellPlanLine selectByBbomid(@Param("bbomId") Long bbomId);
    @Modifying
    @Query(value = "update CellPlanLine c set  c.itemCode = :itemCode where c.bbomId = :bbomId")
    public  void updateItemCode(@Param("bbomId") Long bbomId,@Param("itemCode") String itemCode);

    /**
     * 根据oldMonth查询投产计划数据
     * @param oldMonth 月份
     * @return 投产计划列表
     */
    List<CellPlanLine> findByOldMonth(String oldMonth);
}
