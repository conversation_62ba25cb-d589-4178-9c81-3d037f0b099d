package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellVersionQopIeDTO;
import com.trinasolar.scp.baps.domain.entity.CellVersionQopIe;
import com.trinasolar.scp.baps.domain.query.CellVersionQopIeQuery;
import com.trinasolar.scp.baps.domain.save.CellVersionQopIeSaveDTO;
import lombok.SneakyThrows;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * qop与ie对比 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:08
 */
public interface CellVersionQopIeService {
    /**
     * 分页获取qop与ie对比
     *
     * @param query 查询对象
     * @return qop与ie对比分页对象
     */
    Page<CellVersionQopIeDTO> queryByPage(CellVersionQopIeQuery query);

    /**
     * 根据主键获取qop与ie对比详情
     *
     * @param id 主键
     * @return qop与ie对比详情
     */
        CellVersionQopIeDTO queryById(Long id);

    /**
     * 保存或更新qop与ie对比
     *
     * @param saveDTO qop与ie对比保存对象
     * @return qop与ie对比对象
     */
    CellVersionQopIeDTO save(CellVersionQopIeSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除qop与ie对比
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellVersionQopIeQuery query, HttpServletResponse response);

    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public Map<String, Object> makeReport(CellVersionQopIeQuery query);
}

