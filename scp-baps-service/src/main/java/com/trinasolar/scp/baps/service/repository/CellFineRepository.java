package com.trinasolar.scp.baps.service.repository;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.entity.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 电池良率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Repository
public interface CellFineRepository extends JpaRepository<CellFine, Long>, QuerydslPredicateExecutor<CellFine> {
    /**
     * 根据电池良率中间表和电池类型转换表连接组合获取电池良率信息
     * @return
     */
@Query(value = "select new CellFine(cellFineMid.isOversea,cellTypeMid.cellsType,cellFineMid.workshop,cellFineMid.year,cellFineMid.m1," +
        "cellFineMid.m2,cellFineMid.m3,cellFineMid.m4,cellFineMid.m5,cellFineMid.m6,cellFineMid.m7,cellFineMid.m8,cellFineMid.m9,cellFineMid.m10," +
        "cellFineMid.m11,cellFineMid.m12) from CellTypeMid  cellTypeMid" +
        " inner join CellFineMid  cellFineMid on cellTypeMid.cellsTypeLeft=cellFineMid.cellsTypeLeft " +
        " where cellTypeMid.isDeleted=0 and cellFineMid.isDeleted=0")
    public List<CellFine> getCellFineFromMid();
    @Modifying
    @Query(value = "delete from CellFine cellfine where cellfine.year = :year")
    public void deleteByYear(@Param("year") Integer year);

}
