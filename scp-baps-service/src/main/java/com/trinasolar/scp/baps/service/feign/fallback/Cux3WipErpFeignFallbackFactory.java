package com.trinasolar.scp.baps.service.feign.fallback;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.baps.domain.query.erp.SyncWipIssueQuery;
import com.trinasolar.scp.baps.service.feign.Cux3WipErpFeign;
import feign.hystrix.FallbackFactory;

public class Cux3WipErpFeignFallbackFactory implements FallbackFactory<Cux3WipErpFeign> {
    @Override
    public Cux3WipErpFeign create(Throwable throwable) {
        return new Cux3WipErpFeign() {
            @Override
            public JSONObject cux3WipPage(SyncWipIssueQuery cux3WipDto) {
                return null;
            }
        };
    }
}
