package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellTypeRuleDTO;
import com.trinasolar.scp.baps.domain.entity.CellTypeRule;
import com.trinasolar.scp.baps.domain.query.CellTypeRuleQuery;
import com.trinasolar.scp.baps.domain.save.CellTypeRuleSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池类型转化规则 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-11 01:42:00
 */
public interface CellTypeRuleService{
    /**
     * 分页获取电池类型转化规则
     *
     * @param query 查询对象
     * @return 电池类型转化规则分页对象
     */
    Page<CellTypeRuleDTO> queryByPage(CellTypeRuleQuery query);

    /**
     * 根据主键获取电池类型转化规则详情
     *
     * @param id 主键
     * @return 电池类型转化规则详情
     */
        CellTypeRuleDTO queryById(Long id);

    /**
     * 保存或更新电池类型转化规则
     *
     * @param saveDTO 电池类型转化规则保存对象
     * @return 电池类型转化规则对象
     */
    CellTypeRuleDTO save(CellTypeRuleSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池类型转化规则
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellTypeRuleQuery query, HttpServletResponse response);

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

