package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellProductionLeadTimeDTO;
import com.trinasolar.scp.baps.domain.convert.CellProductionLeadTimeDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellProductionLeadTime;
import com.trinasolar.scp.baps.domain.entity.QCellProductionLeadTime;
import com.trinasolar.scp.baps.domain.excel.CellLossExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellProductionLeadTimeExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellProductionLeadTimeQuery;
import com.trinasolar.scp.baps.domain.save.CellProductionLeadTimeSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellProductionLeadTimeRepository;
import com.trinasolar.scp.baps.service.service.CellProductionLeadTimeService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * 投产提前期
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Slf4j
@Service("cellProductionLeadTimeService")
@RequiredArgsConstructor
public class CellProductionLeadTimeServiceImpl implements CellProductionLeadTimeService {
    private static final QCellProductionLeadTime qCellProductionLeadTime = QCellProductionLeadTime.cellProductionLeadTime;

    private final CellProductionLeadTimeDEConvert convert;

    private final CellProductionLeadTimeRepository repository;

    @Override
    public Page<CellProductionLeadTimeDTO> queryByPage(CellProductionLeadTimeQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query=  convert.toCellProductionLeadTimeQueryCN(query,MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellProductionLeadTime> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellProductionLeadTimeQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellProductionLeadTime.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlace())){
            booleanBuilder.and(qCellProductionLeadTime.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellProductionLeadTime.basePlaceId.eq(query.getBasePlaceId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qCellProductionLeadTime.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellProductionLeadTime.workshopId.eq(query.getWorkshopId()));
        }
        if(StringUtils.isNotEmpty(query.getWorkunit())){
            booleanBuilder.and(qCellProductionLeadTime.workunit.eq(query.getWorkunit()));
        }
        if (query.getWorkunitId()!= null) {
            booleanBuilder.and(qCellProductionLeadTime.workunitId.eq(query.getWorkunitId()));
        }
        if (query.getLeadTime() != null) {
            booleanBuilder.and(qCellProductionLeadTime.leadTime.eq(query.getLeadTime()));
        }
    }

    @Override
    public CellProductionLeadTimeDTO queryById(Long id) {
        CellProductionLeadTime queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellProductionLeadTimeDTO save(CellProductionLeadTimeSaveDTO saveDTO) {
        CellProductionLeadTime newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new CellProductionLeadTime());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellProductionLeadTimeQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellProductionLeadTimeDTO> dtos = queryByPage(query).getContent();
        // dto数据转为ExcelData数据
        List<CellProductionLeadTimeExcelDTO> excelDtos = convert.toExcelDTO(dtos);
        ExcelPara excelPara =  query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(excelDtos, excelPara);
        // 导出调用excelUtils
        String fileName= BapsMessgeHelper.getMessage("export.cellproductionleadtime.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);
    }
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellProductionLeadTimeExcelDTO> excelDtos = Lists.newArrayList();
        excelDtos=  ExcelUtils.readExcel(multipartFile.getInputStream(),null, CellProductionLeadTimeExcelDTO.class,excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        //验证数据
        checkInput(excelDtos);
        List<CellProductionLeadTimeSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        repository.deleteAll();
        saveDTOS= convert.toCellProductionLeadTimeSaveDTOCnName(saveDTOS);
        saveDTOS.stream().forEach(this::save);
    }

    public void checkInput(List<CellProductionLeadTimeExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors=new ArrayList<>();
        excelDTOS.stream().forEach(excelDTO -> {

            //验证生产基地
            LovLineDTO   lovLineDTO_BasePlace = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace());
            if (lovLineDTO_BasePlace == null) {
                String message= BapsMessgeHelper.getMessage("the.row.baseplace.not.exists", new Object[]{i[0], excelDTO.getBasePlace()});
                errors.add(message);
            }
            //验证生产车间
            LovLineDTO   lovLineDTO_WorkShop = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop());
            if (lovLineDTO_WorkShop == null) {
                String message = BapsMessgeHelper.getMessage("the.row.workshop.not.exists", new Object[]{i[0], excelDTO.getWorkshop()});
                errors.add(message);
            }
            //生产单元可以为空 当不为空时校验
            if (StringUtils.isNotEmpty(excelDTO.getWorkunit())){
                //验证生产单元
                LovLineDTO lovLineDTO_WorkUnit=LovUtils.getByName(LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit());
                if (lovLineDTO_WorkUnit == null) {
                    String message = BapsMessgeHelper.getMessage("the.row.workunit.not.exists", new Object[]{i[0], excelDTO.getWorkunit()});
                    errors.add(message);
                }

                if (lovLineDTO_WorkShop != null && lovLineDTO_WorkUnit != null) {
                    //验证单元与车间的关系
                    if (!(lovLineDTO_WorkUnit.getAttribute2() != null && lovLineDTO_WorkUnit.getAttribute2().equals(lovLineDTO_WorkShop.getLovLineId().toString()))) {
                        String message = BapsMessgeHelper.getMessage("the.row.workunit.not.in.workshop", new Object[]{i[0], excelDTO.getWorkunit(), excelDTO.getWorkshop()});
                        errors.add(message);
                    }
                }
            }
            //验证车间与基地的关系
            if (lovLineDTO_WorkShop!=null && lovLineDTO_BasePlace!=null){

                if (!(lovLineDTO_WorkShop.getAttribute1()!=null&&lovLineDTO_WorkShop.getAttribute1().equals(lovLineDTO_BasePlace.getLovLineId().toString()))){
                    String message = BapsMessgeHelper.getMessage("the.row.workshop.not.in.baseplace", new Object[]{i[0], excelDTO.getWorkshop(), excelDTO.getBasePlace()});
                    errors.add(message);
                }
            }
            i[0]++;
        });
        if (errors.size()>0){
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }

    }
}
