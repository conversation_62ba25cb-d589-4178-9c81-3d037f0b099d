package com.trinasolar.scp.baps.service.feign;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Data
public class PageFeign<T>   implements  Serializable {
    private static final long serialVersionUID = 867755909294344406L;

    private List<T> content = new ArrayList<>();
    private boolean last;
    private boolean first;
    private boolean empty;
    private int totalPages;
    private int totalElements;
    private int numberOfElements;
    private int size;
    private int number;

    //此处本来是 Pageable,  Sort对象，自己定义对象也可以，但用Map也能满足
    private Map<String, Object> pageable = new HashMap<>();
    private Map<String, Boolean> sort = new HashMap<>();

}

