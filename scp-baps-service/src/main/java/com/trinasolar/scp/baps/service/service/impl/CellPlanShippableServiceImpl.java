package com.trinasolar.scp.baps.service.service.impl;


import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.*;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.dto.ScheduleTaskStatusEnum;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.excel.CellPlanShippableExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanShippableQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanShippableSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.StringTools;
import com.trinasolar.scp.baps.service.feign.BmrpFeign;
import com.trinasolar.scp.baps.service.repository.CellPlanShippableRepository;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.baps.service.service.log.LogService;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 可发货计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-09 10:05:37
 */
@Slf4j
@Service("cellPlanShippableService")
@RequiredArgsConstructor
public class CellPlanShippableServiceImpl implements CellPlanShippableService {
    private static final QCellPlanShippable qCellPlanShippable = QCellPlanShippable.cellPlanShippable;

    private final CellPlanShippableDEConvert convert;

    private final CellPlanShippableRepository repository;

    private final JPAQueryFactory jpaQueryFactory;

    private final QCellShippableLeadTime qCellShippableLeadTime=QCellShippableLeadTime.cellShippableLeadTime;

    private final CellProductionLeadTimeDEConvert leadTimeDEConvert;

    private final QCellClimbCapacityLead qCellClimbCapacityLead=QCellClimbCapacityLead.cellClimbCapacityLead;

    private final CellClimbCapacityLeadDEConvert leadDEConvert;

    private final CellInstockPlanService cellInstockPlanService;

    private final CellInstockPlanDEConvert cellInstockPlanDEConvert;

    private final CellShipmentPlanDEConvert shipmentPlanDEConvert;
    private final CellShipmentPlanService cellShipmentPlanService;

    private final QCellInstockPlan qCellInstockPlan=QCellInstockPlan.cellInstockPlan;

    private final LogService logService;

    @Autowired
    RedissonClient redissonClient;

    public Page<CellPlanShippableDTO> queryByPage(CellPlanShippableQuery query) {

        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellPlanShippable> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
        //return new PageImpl(cellPlanShippables.subList((query.getPageNumber() - 1) * query.getPageSize(), Math.min(query.getPageNumber() * query.getPageSize(), cellPlanShippables.size())), pageable, cellPlanShippables.size());
    }
    //获取爬坡数据  对应可靠性验证
    private List<CellClimbCapacityLeadDTO> getCellClimbCapacityLeadDTOS() {
        //获取爬坡数据  对应可靠性验证
        List<CellClimbCapacityLead>capacityLeadList=jpaQueryFactory.select(qCellClimbCapacityLead).from(qCellClimbCapacityLead).where(
                qCellClimbCapacityLead.isDeleted.eq(0)
        ).fetch();
        List<CellClimbCapacityLeadDTO> capacityLeadDTOList = leadDEConvert.toDto(capacityLeadList);
        return capacityLeadDTOList;
    }

    //获取可计划提前期  对应常规数据
    private List<CellShippableLeadTimeDTO> getCellShippableLeadTimeDTOS() {
        //获取可计划提前期  对应常规数据
        List<CellShippableLeadTime>leadTimeDTOLists=jpaQueryFactory.select(qCellShippableLeadTime).from(qCellShippableLeadTime).where(
                qCellShippableLeadTime.isDeleted.eq(0)
        ).fetch();
        List<CellShippableLeadTimeDTO> leadTimeDTOList = leadTimeDEConvert.entityToDto(leadTimeDTOLists);
        return leadTimeDTOList;
    }




    private void buildWhere(BooleanBuilder booleanBuilder, CellPlanShippableQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellPlanShippable.id.eq(query.getId()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qCellPlanShippable.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellPlanShippable.basePlace.eq(query.getBasePlace()));
        }
        if (query.getWorkShopId() != null) {
            booleanBuilder.and(qCellPlanShippable.workShopId.eq(query.getWorkShopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkShop())) {
            booleanBuilder.and(qCellPlanShippable.workShop.eq(query.getWorkShop()));
        }
        if (query.getWorkUnitId() != null) {
            booleanBuilder.and(qCellPlanShippable.workUnitId.eq(query.getWorkUnitId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkUnit())) {
            booleanBuilder.and(qCellPlanShippable.workUnit.eq(query.getWorkUnit()));
        }
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qCellPlanShippable.cellTypeId.eq(query.getCellTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellType())) {
            booleanBuilder.and(qCellPlanShippable.cellType.eq(query.getCellType()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellPlanShippable.month.eq(query.getMonth()));
        }

    }

    @Override
    public CellPlanShippableDTO queryById(Long id) {
        CellPlanShippable queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellPlanShippableDTO save(CellPlanShippableSaveDTO saveDTO) {
        CellPlanShippable newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellPlanShippable());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellPlanShippableQuery query, HttpServletResponse response) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<CellPlanShippableDTO>planShippableDTOList=new ArrayList<>();
        //获取数据库中数据
        List<CellPlanShippableDTO> dtos = queryByPage(query).getContent();
        if(CollectionUtils.isNotEmpty(dtos)){
            dtos.stream().filter(x->null!=x.getSubMap()).forEach(y->{
                CellPlanShippableDTO cellPlanShippableDTO=new CellPlanShippableDTO();
                cellPlanShippableDTO.setBasePlace(y.getBasePlace());
                cellPlanShippableDTO.setWorkShop(y.getWorkShop());
                cellPlanShippableDTO.setWorkUnit(y.getWorkUnit());
                cellPlanShippableDTO.setCellsType(y.getCellsType());
                cellPlanShippableDTO.setMonth(y.getMonth());
                y.getSubMap().entrySet().stream().forEach(subMap->{
                    LocalDateTime localDate= LocalDate.parse(subMap.getKey(), fmt).atStartOfDay();
                    int day = localDate.getDayOfMonth();
                    Class cls = CellPlanShippableDTO.class;
                    try {
                        Method getMethod = cls.getMethod("getD" + day);
                        Method setMethod = cls.getMethod("setD" + day, BigDecimal.class);
                        Object val = getMethod.invoke(cellPlanShippableDTO);
                        if (val == null) {
                            setMethod.invoke(cellPlanShippableDTO, subMap.getValue());
                        } else {
                            BigDecimal addVal = ((BigDecimal) val).add(subMap.getValue());
                            setMethod.invoke(cellPlanShippableDTO, addVal);
                        }
                    } catch (NoSuchMethodException e) {
                        e.printStackTrace();
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                });
                planShippableDTOList.add(cellPlanShippableDTO);
            });

        }

        // dto数据转为ExcelData数据
        List<CellPlanShippableExcelDTO> datas = convert.toExcelDTO(planShippableDTOList);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellPlanShippableExcelDTO.class, datas, JSON.toJSONString(query), "可发货计划表", response);

    }


    //获取国内数据
    public List<CellInstockPlanDTO>getDomesticCellInstockPlan(String finalVersion,String month){
        if(StringUtils.isNotEmpty(finalVersion)){
            JPAQuery<CellInstockPlan>instockPlanJPAQuery=jpaQueryFactory.select(qCellInstockPlan).from(qCellInstockPlan)
                    .where(qCellInstockPlan.finalVersion.eq(finalVersion).and(qCellInstockPlan.isOversea.eq("国内")));
            if(StringUtils.isEmpty(month)){
                 month= DateUtil.getMonth(LocalDate.now());
                 instockPlanJPAQuery.where(qCellInstockPlan.month.eq(month));
            }else{
                instockPlanJPAQuery.where(qCellInstockPlan.month.eq(month));
            }
            return cellInstockPlanDEConvert.toDto(instockPlanJPAQuery.fetch());
        }
        return new ArrayList<>();
    }
    //获取国外数据
    public List<CellInstockPlanDTO>getAbroadCellInstockPlan(String finalVersion,String month){
        if(StringUtils.isNotEmpty(finalVersion)){
            JPAQuery<CellInstockPlan>instockPlanJPAQuery=jpaQueryFactory.select(qCellInstockPlan).from(qCellInstockPlan)
                    .where(qCellInstockPlan.finalVersion.eq(finalVersion).and(qCellInstockPlan.isOversea.eq("海外")));
            if(StringUtils.isEmpty(month)){
                month= DateUtil.getMonth(LocalDate.now());
                instockPlanJPAQuery.where(qCellInstockPlan.month.eq(month));
            }else{
                instockPlanJPAQuery.where(qCellInstockPlan.month.eq(month));
            }
            return cellInstockPlanDEConvert.toDto(instockPlanJPAQuery.fetch());
        }
        return new ArrayList<>();
    }

    /**
     * 数据同步入库计划
     * 国内、国外版本分别获取当月的 之后add.all
     * 以202402月份为例
     * 数据同步入库计划 通过可靠性验证和预留天数计算转换 获取出库日期
     * 落表
     * 当前2月份 1-29天 出库日期集合且添加入库时间<2424-02-01且出库日期>2024-02-01的日期集合
     * 如果存在多个相同出库日期  list添加  然后map分组推送
     */
    public void syncDataInstockPlan(CellPlanShippableQuery query) {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        String redisKey = "CellPlanShippableServiceImpl.syncDataInstockPlan";
        RLock lock = redissonClient.getLock(redisKey);
        if (!lock.isLocked()) {
            try {
                // 获取锁
                lock.lock();
                //每次同步之前先删除当前月的数据
                cellShipmentPlanService.deleteByMonth(query.getMonth());
                //常规数据
                List<CellShippableLeadTimeDTO> leadTimeDTOList = getCellShippableLeadTimeDTOS();
                //可靠性数据
                List<CellClimbCapacityLeadDTO> capacityLeadDTOList = getCellClimbCapacityLeadDTOS();
                CellInstockPlanQuery instockPlanQuery=new CellInstockPlanQuery();
                instockPlanQuery.setMonth(query.getMonth());
                /**********************************获取当月数据START*************************************************/
                //左国内 右国外
                Pair<String, String> stringPair= cellInstockPlanService.getFinalVersion(instockPlanQuery);
                List<CellInstockPlanDTO> domesticCellInstockPlan= getDomesticCellInstockPlan(stringPair.getLeft(),query.getMonth());
                List<CellInstockPlanDTO> abroadCellInstockPlan= getAbroadCellInstockPlan(stringPair.getRight(),query.getMonth());
                //国内数据加上国外数据
                domesticCellInstockPlan.addAll(abroadCellInstockPlan);
                /**********************************获取当月数据END*************************************************/
                /**********************************获取当月前一个月的数据START*************************************************/
                //左国内 右国外
                instockPlanQuery.setMinusMonthsFlag("Y");
                String month=DateUtil.getNewMonth(query.getMonth(),-1);;
                //每次同步之前先删除当前月前一个月的数据
                cellShipmentPlanService.deleteByMonth(month);
                Pair<String, String> stringPairLastMonth= cellInstockPlanService.getFinalVersion(instockPlanQuery);
                List<CellInstockPlanDTO> domesticCellInstockPlanLastMonth= getDomesticCellInstockPlan(stringPairLastMonth.getLeft(),month);
                List<CellInstockPlanDTO> abroadCellInstockPlanLastMonth= getAbroadCellInstockPlan(stringPairLastMonth.getRight(),month);
                //国内数据加上国外数据
                domesticCellInstockPlan.addAll(domesticCellInstockPlanLastMonth);
                domesticCellInstockPlan.addAll(abroadCellInstockPlanLastMonth);
                /**********************************获取当月前一个月END*************************************************/
                //数据匹配预留天数、可靠性验证 保存
                ScheduledTaskLinesDTO task= logService.createLogTask(LovHeaderCodeConstant.TASK_LOG_02);;
                if(CollectionUtils.isEmpty(domesticCellInstockPlan)){
                    logService.addLog(task,ScheduleTaskStatusEnum.WARN, String.format("%s月未查到入库数据", query.getMonth()));
                    logService.saveTaskLog(task);
                    return;
                }
                saveCalcCellPlanShippable(leadTimeDTOList,capacityLeadDTOList,domesticCellInstockPlan,task);
                lock.unlock();  // 释放锁
            }catch (Exception e){
                throw new RuntimeException(e.getMessage());
            }finally {
                // 确保释放锁
                if (lock != null && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }else {
            throw new RuntimeException("其他用户正在操作中，请稍后");
        }
    }
    //数据匹配预留天数、可靠性验证 保存
    public void saveCalcCellPlanShippable(List<CellShippableLeadTimeDTO> leadTimeDTOList, List<CellClimbCapacityLeadDTO> capacityLeadDTOList,List<CellInstockPlanDTO> datas,ScheduledTaskLinesDTO task) {
        try{


            logService.addLog(task,ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取当前月份国内外数据以及上个月的国内外数据集合，%s", LocalDateTime.now()));
            //常规
            Map<String, CellShippableLeadTimeDTO> leadTimeDTOMap = leadTimeDTOList.stream().collect(Collectors.toMap(CellShippableLeadTimeDTO::filedGroupTwo, Function.identity()));
            Map<String, CellShippableLeadTimeDTO> leadTimeDTOMapNoWorkunit = leadTimeDTOList.stream().collect(Collectors.toMap(CellShippableLeadTimeDTO::filedGroupTwoNoWorkUnit, Function.identity(),(v1,v2)->v1));
            logService.addLog(task,ScheduleTaskStatusEnum.RUNNING, String.format("阶段2：获取可发货计划提前期数据集合，%s", LocalDateTime.now()));
            //可靠性
            Map<String, CellClimbCapacityLeadDTO> capacityLeadDTOMap = capacityLeadDTOList.stream().collect(Collectors.toMap(CellClimbCapacityLeadDTO::filedGroupTwo, Function.identity(), (user1, user2) -> user2));
            Map<String, CellClimbCapacityLeadDTO> capacityLeadDTOMapNoWorkunit = capacityLeadDTOList.stream().collect(Collectors.toMap(CellClimbCapacityLeadDTO::filedGroupTwoNoWorkUnit, Function.identity(), (user1, user2) -> user1));
            logService.addLog(task,ScheduleTaskStatusEnum.RUNNING, String.format("阶段3：获取可靠性验证日期维护数据集合，%s", LocalDateTime.now()));
            if (CollectionUtils.isNotEmpty(datas)) {
                datas.stream().forEach(x -> {
                    String mapLeadKey = x.getBasePlace() + "/" + x.getWorkshop() + "/" + x.getWorkunit() + "/" + x.getCellsType() ;
                    String mapCapacityLeadKey = StringTools.joinWith("/",x.getBasePlace(),x.getWorkshop(),x.getWorkunit(),x.getCellsType(),x.getLineName()) ;
                    String leadType = "";
                    if ("可靠性验证".equals(x.getVerificationMark())) {
                        leadType = "可靠性验证";
                        CellShippableLeadTimeDTO leadTimeDTO=null;
                        //预留天数获取
                        if (StringUtils.isNotBlank(x.getWorkunit())){
                            leadTimeDTO = leadTimeDTOMap.get(mapLeadKey);
                        }else {
                            leadTimeDTO = leadTimeDTOMapNoWorkunit.get(mapLeadKey);
                        }
                        //可靠性验证日期获取
                        CellClimbCapacityLeadDTO capacityLeadDTO =null;
                        if (StringUtils.isNotBlank(x.getWorkunit())){
                            capacityLeadDTO= capacityLeadDTOMap.get(mapCapacityLeadKey);
                        }
                        else{
                            capacityLeadDTO= capacityLeadDTOMapNoWorkunit.get(mapCapacityLeadKey);
                        }
                        if (capacityLeadDTO!=null) {

                            if(null==capacityLeadDTO.getValidateTime()){
                                logService.addLog(task,ScheduleTaskStatusEnum.ERROR, String.format("错误信息：%s数据未维护验证时间，%s", mapCapacityLeadKey,LocalDateTime.now()));
                                logService.saveTaskLog(task);
                                throw  new BizException(mapCapacityLeadKey+"数据未维护验证时间");
                            }
                            if(null!=leadTimeDTO){
                                LocalDateTime  resultDate = x.getStartTime().plusDays(leadTimeDTO.getBuffDays());
                                if (resultDate.compareTo(capacityLeadDTO.getValidateTime()) > 0) {
                                    //出库日期
                                    x.setOutBoundDate(resultDate);
                                } else {
                                    x.setOutBoundDate(capacityLeadDTO.getValidateTime().plusDays(1));
                                }
                                //可发货数量上限=入库数量*(1+可发货浮动系数)5月14日新增逻辑
                                x.setMaxQtyPc(x.getQtyPc().multiply(BigDecimal.ONE.add(Optional.ofNullable(leadTimeDTO.getRate()).orElse(BigDecimal.ZERO))));
                            }else{

                                logService.addLog(task,ScheduleTaskStatusEnum.ERROR, String.format("错误信息：%s未匹配上可发货计划提前期，%s", mapLeadKey,LocalDateTime.now()));
                                logService.saveTaskLog(task);
                                throw  new BizException(mapLeadKey+"未匹配上可发货计划提前期");
                            }
                        }else{
                            logService.addLog(task,ScheduleTaskStatusEnum.ERROR, String.format("错误信息：%s未匹配上可靠性验证日期维护，%s", mapCapacityLeadKey,LocalDateTime.now()));
                            logService.saveTaskLog(task);
                            throw  new BizException(mapCapacityLeadKey+"未匹配上可靠性验证日期维护");
                        }
                    } else {
                        //常规
                        CellShippableLeadTimeDTO leadTimeDTO=null;
                        leadType = "常规";
                        if (StringUtils.isNotBlank(x.getWorkunit())){
                            leadTimeDTO = leadTimeDTOMap.get(mapLeadKey);
                        }else {
                            leadTimeDTO = leadTimeDTOMapNoWorkunit.get(mapLeadKey);
                        }
                        if (leadTimeDTO!=null) {
                            //常规获取对应维度的预留天数+入库计划日期
                            leadTimeDTO=Optional.ofNullable(leadTimeDTO).orElse(leadTimeDTOMapNoWorkunit.get(mapLeadKey));
                            LocalDateTime resultDate = x.getStartTime().plusDays(leadTimeDTO.getBuffDays());
                            x.setOutBoundDate(resultDate);
                            //可发货数量上限=入库数量*(1+可发货浮动系数)5月14日新增逻辑
                            x.setMaxQtyPc(x.getQtyPc().multiply(BigDecimal.ONE.add(Optional.ofNullable(leadTimeDTO.getRate()).orElse(BigDecimal.ZERO))));
                        }else{

                            logService.addLog(task,ScheduleTaskStatusEnum.ERROR, String.format("错误信息：%s未匹配上可发货计划提前期，%s", mapLeadKey,LocalDateTime.now()));
                            logService.saveTaskLog(task);
                            throw  new BizException(mapLeadKey+"未匹配上可发货计划提前期");
                        }
                    }
                    x.setLeadType(leadType);
                    x.setInstockPlanId(x.getId());
                });
                logService.addLog(task,ScheduleTaskStatusEnum.SUCCESS, String.format("阶段4：数据计算成功，%s",LocalDateTime.now()));
                logService.saveTaskLog(task);
                // 获取当前月份
                List<CellShipmentPlanDTO>shipmentPlanDTOList= shipmentPlanDEConvert.toShipmentPlanDTO(datas);
                cellShipmentPlanService.saveAll(shipmentPlanDTOList);
            }
        }catch (Exception e){
            throw  new BizException(e.getLocalizedMessage());
        }
    }
}
