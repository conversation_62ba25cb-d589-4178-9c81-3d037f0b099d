package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.aps.CellBooksDTO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 实际入库表（带转化） 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
public interface CellInstockPlanNoFinishService {

    void makeInstockPlanFinish();

    @Transactional(rollbackFor = Exception.class)
    void makeInstockPlanFinish(List<CellBooksDTO> cellBooksDTOS);
}

