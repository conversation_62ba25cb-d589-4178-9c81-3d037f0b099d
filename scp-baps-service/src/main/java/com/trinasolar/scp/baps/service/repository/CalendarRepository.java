package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.Calendar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 生产日历
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Repository
public interface CalendarRepository extends JpaRepository<Calendar, Long>, QuerydslPredicateExecutor<Calendar> {
    @Modifying
//    @Query("update  Calendar  c set c.isDeleted=1 WHERE c.fromid = :fromid")
    @Query("delete from Calendar c WHERE c.fromid = :fromid")
    void deleteByFromId(@Param("fromid") Long fromid);
    @Modifying
    @Query("delete from Calendar c WHERE c.ieorgrade = :ieorgrade")
    void deleteByIeOrGrade(@Param("ieorgrade") int ieorgrade);
    /**
     * 设置默认值
     * @param calendar
     */
    static void setDefault(Calendar calendar) {
        if ( calendar.getType() == null ) {
            calendar.setType("标准日历");
        }
        if ( calendar.getShiftcode() == null ) {
            calendar.setShiftcode("全天");
        }
        if ( calendar.getSortorder() == null ) {
            calendar.setSortorder(10);
        }
    }

    /**
     * 根据唯一联合主键查询
     * @param workunit
     * @param startDate
     * @param endDate
     * @return
     */
    @Query(value = "from Calendar c where c.workunit = :workunit and c.date >= :startDate and  c.date < :endDate and c.ieorgrade = :ieorgrade  and c.lineName is null and c.isDeleted=0 ")
    List<Calendar> selectByWorkunitDate(@Param("workunit")String workunit, @Param("startDate")LocalDate startDate, @Param("endDate")LocalDate endDate,@Param("ieorgrade")Integer ieorgrade);
    @Query(value = "from Calendar c where c.workunit = :workunit and c.lineName = :lineName and c.ieorgrade = :ieorgrade and c.date >= :startDate and  c.date < :endDate and c.isDeleted=0 ")
    List<Calendar> selectByWorkunitDate(@Param("workunit")String workunit,@Param("lineName")String lineName, @Param("startDate")LocalDate startDate, @Param("endDate")LocalDate endDate,@Param("ieorgrade")Integer ieorgrade);


}
