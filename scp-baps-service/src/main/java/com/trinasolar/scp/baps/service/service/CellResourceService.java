package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellResourceDTO;
import com.trinasolar.scp.baps.domain.entity.CellResource;
import com.trinasolar.scp.baps.domain.query.CellResourceQuery;
import com.trinasolar.scp.baps.domain.save.CellResourceSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池资源表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-08 02:52:37
 */
public interface CellResourceService {
    /**
     * 分页获取电池资源表
     *
     * @param query 查询对象
     * @return 电池资源表分页对象
     */
    Page<CellResourceDTO> queryByPage(CellResourceQuery query);

    /**
     * 根据主键获取电池资源表详情
     *
     * @param id 主键
     * @return 电池资源表详情
     */
        CellResourceDTO queryById(Long id);

    /**
     * 保存或更新电池资源表
     *
     * @param saveDTO 电池资源表保存对象
     * @return 电池资源表对象
     */
    CellResourceDTO save(CellResourceSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池资源表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellResourceQuery query, HttpServletResponse response);

    void makeData(Integer ieorgrade);
}

