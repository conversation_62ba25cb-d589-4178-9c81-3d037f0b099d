package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.excel.CellVersionQopIeExcelDTO;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.convert.CellVersionPlanIeDEConvert;
import com.trinasolar.scp.baps.domain.excel.CellProductionPlanTotalExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellVersionPlanIeExcelDTO;
import com.trinasolar.scp.baps.domain.save.CellVersionPlanIeSaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.repository.CellVersionPlanIeRepository;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindException;
import oshi.jna.platform.unix.solaris.LibKstat;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 计划与ie产能对比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Slf4j
@Service("cellVersionPlanIeService")
@RequiredArgsConstructor
public class CellVersionPlanIeServiceImpl implements CellVersionPlanIeService {
    private static final QCellVersionPlanIe qCellVersionPlanIe = QCellVersionPlanIe.cellVersionPlanIe;
    private final CellVersionPlanIeDEConvert convert;
    private final CellVersionPlanIeRepository repository;
    private final CellBaseCapacityService cellBaseCapacityService;
    private final CellGradeCapacityService cellGradeCapacityService;

    private final CellPlanLineTotalService cellPlanLineTotalService;
    private final CellInstockPlanTotalService instockPlanTotalService;

    private final JPAQueryFactory jpaQueryFactory;
    private final CellConversionFactorService cellConversionFactorService;

    @Override
    public Page<CellVersionPlanIeDTO> queryByPage(CellVersionPlanIeQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellVersionPlanIe> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellVersionPlanIeQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellVersionPlanIe.id.eq(query.getId()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qCellVersionPlanIe.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOverseaName())) {
            booleanBuilder.and(qCellVersionPlanIe.isOverseaName.eq(query.getIsOverseaName()));
        }
        if (query.getCellTypeId() != null) {
            booleanBuilder.and(qCellVersionPlanIe.cellTypeId.eq(query.getCellTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellTypeName())) {
            booleanBuilder.and(qCellVersionPlanIe.cellTypeName.eq(query.getCellTypeName()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellVersionPlanIe.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshopName())) {
            booleanBuilder.and(qCellVersionPlanIe.workshopName.eq(query.getWorkshopName()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellVersionPlanIe.month.eq(query.getMonth()));
        }
        if (query.getRate() != null) {
            booleanBuilder.and(qCellVersionPlanIe.rate.eq(query.getRate()));
        }
        if (query.getPlanCapacity() != null) {
            booleanBuilder.and(qCellVersionPlanIe.planCapacity.eq(query.getPlanCapacity()));
        }
        if (query.getCapacity() != null) {
            booleanBuilder.and(qCellVersionPlanIe.capacity.eq(query.getCapacity()));
        }
        if (query.getGap() != null) {
            booleanBuilder.and(qCellVersionPlanIe.gap.eq(query.getGap()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qCellVersionPlanIe.remark.eq(query.getRemark()));
        }
    }

    @Override
    public CellVersionPlanIeDTO queryById(Long id) {
        CellVersionPlanIe queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellVersionPlanIeDTO save(CellVersionPlanIeSaveDTO saveDTO) {
        CellVersionPlanIe newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellVersionPlanIe());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellVersionPlanIeQuery query, HttpServletResponse response) {
        List<CellVersionPlanIeDTO> cellVersionPlanIeCollect=new ArrayList<>();
        Map<String, Object> mapData = makeReport(query);

        Object detailsObj=  mapData.get("details");
        if (detailsObj!=null){
            cellVersionPlanIeCollect.addAll((List<CellVersionPlanIeDTO>)detailsObj);
        }
        Object ieVersionObj=   mapData.get("ieVersion");
        String ieVersion="";
        if (ieVersionObj!=null){
            ieVersion=ieVersionObj.toString();
        }
        Object planVersionObj=  mapData.get("planVersion");
        String planVersion="";
        if (planVersionObj!=null){
            planVersion=planVersionObj.toString();
        }

        // dto数据转为ExcelData数据
        List<CellVersionPlanIeExcelDTO> datas = convert.toExcelDTO(cellVersionPlanIeCollect);
        ExcelPara excelPara=new ExcelPara();
        excelPara.addColumn("isOverseaName","国内/海外",0, 500.0);
        excelPara.addColumn("workshopName","生产车间",1,200.0);
        excelPara.addColumn("cellTypeName","电池类型",2,200.0);
     //   excelPara.addColumn("month","月份",3,100.0);
        excelPara.addColumn("ratePercent","产能利用率",3,200.0);
        excelPara.addColumn("capacity",StringUtils.isNotEmpty(ieVersion)?"IE产能\n"+ieVersion:"IE产能",4,100.0);
        excelPara.addColumn("planCapacity",StringUtils.isNotEmpty(planVersion)?query.getMonth()+"计划\n"+planVersion:query.getMonth()+"计划",5,100.0);
        excelPara.addColumn("gap","计划与IE差异",6,100.0);
        List<List<Object>> excelData = ExcelUtils.getList(datas, excelPara);
        String fileName="版本对比计划与IE产能";
        String sheetName = fileName;
        fileName = fileName + "_" + DateUtils.formatDate(new Date(), "yyyy-MM-dd+HH:mm:ss");
        ExcelUtils.exportEx(response, fileName, sheetName, excelPara.getSimpleHeader(), excelData);
    }
    private void setZero(CellGradeCapacityDTO dto){
        dto.setD1(BigDecimal.ZERO);
        dto.setD2(BigDecimal.ZERO);
        dto.setD3(BigDecimal.ZERO);
        dto.setD4(BigDecimal.ZERO);
        dto.setD5(BigDecimal.ZERO);
        dto.setD6(BigDecimal.ZERO);
        dto.setD7(BigDecimal.ZERO);
        dto.setD8(BigDecimal.ZERO);
        dto.setD9(BigDecimal.ZERO);
        dto.setD10(BigDecimal.ZERO);
        dto.setD11(BigDecimal.ZERO);
        dto.setD12(BigDecimal.ZERO);
        dto.setD13(BigDecimal.ZERO);
        dto.setD14(BigDecimal.ZERO);
        dto.setD15(BigDecimal.ZERO);
        dto.setD16(BigDecimal.ZERO);
        dto.setD17(BigDecimal.ZERO);
        dto.setD18(BigDecimal.ZERO);
        dto.setD19(BigDecimal.ZERO);
        dto.setD20(BigDecimal.ZERO);
        dto.setD21(BigDecimal.ZERO);
        dto.setD22(BigDecimal.ZERO);
        dto.setD23(BigDecimal.ZERO);
        dto.setD24(BigDecimal.ZERO);
        dto.setD25(BigDecimal.ZERO);
        dto.setD26(BigDecimal.ZERO);
        dto.setD27(BigDecimal.ZERO);
        dto.setD28(BigDecimal.ZERO);
        dto.setD29(BigDecimal.ZERO);
        dto.setD30(BigDecimal.ZERO);
        dto.setD31(BigDecimal.ZERO);
    }
    private void setZero(CellInstockPlanTotalDTO dto){
        dto.setD1(BigDecimal.ZERO);
        dto.setD2(BigDecimal.ZERO);
        dto.setD3(BigDecimal.ZERO);
        dto.setD4(BigDecimal.ZERO);
        dto.setD5(BigDecimal.ZERO);
        dto.setD6(BigDecimal.ZERO);
        dto.setD7(BigDecimal.ZERO);
        dto.setD8(BigDecimal.ZERO);
        dto.setD9(BigDecimal.ZERO);
        dto.setD10(BigDecimal.ZERO);
        dto.setD11(BigDecimal.ZERO);
        dto.setD12(BigDecimal.ZERO);
        dto.setD13(BigDecimal.ZERO);
        dto.setD14(BigDecimal.ZERO);
        dto.setD15(BigDecimal.ZERO);
        dto.setD16(BigDecimal.ZERO);
        dto.setD17(BigDecimal.ZERO);
        dto.setD18(BigDecimal.ZERO);
        dto.setD19(BigDecimal.ZERO);
        dto.setD20(BigDecimal.ZERO);
        dto.setD21(BigDecimal.ZERO);
        dto.setD22(BigDecimal.ZERO);
        dto.setD23(BigDecimal.ZERO);
        dto.setD24(BigDecimal.ZERO);
        dto.setD25(BigDecimal.ZERO);
        dto.setD26(BigDecimal.ZERO);
        dto.setD27(BigDecimal.ZERO);
        dto.setD28(BigDecimal.ZERO);
        dto.setD29(BigDecimal.ZERO);
        dto.setD30(BigDecimal.ZERO);
        dto.setD31(BigDecimal.ZERO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public Map<String, Object> makeReport(CellVersionPlanIeQuery query) {
        String oldLang= MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        LovLineDTO overseaLov = null;
        if (query.getIsOverseaId() == null) {
            //默认读取国内数据
            overseaLov = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, OverseaConstant.INLAND);

        } else {
            overseaLov = LovUtils.get(query.getIsOverseaId());
        }
        query.setIsOverseaId(overseaLov.getLovLineId());
        query.setIsOverseaName(overseaLov.getLovName());
        // 准备电池类型--》电池型号的map集合开始
        //构建电池类型对应的型号map
        //电池类型名--》型号
        Map<String,LovLineDTO> mapTypeToSeries= MapStrutUtil.getMapCelltypeToCellModelLov();
        // 准备电池类型--》电池型号的map集合结束
        Map<String, Object> mapData = new HashMap<>();
        String ieVersion = "V" + query.getMonth() + "01";//IE数据版本
        //获取IE产能数据
        CellBaseCapacityQuery cellBaseCapacityQuery = convert.toCellBaseCapacityQuery(query);
        //电池型号Id
        Long cellModelId=  query.getCellTypeId();
        String cellModelName=null;
        if (cellModelId!=null){
            LovLineDTO lovLineDTO = LovUtils.get(cellModelId);
            if (lovLineDTO!=null){
                cellModelName=lovLineDTO.getLovName();
            }
        }
        cellBaseCapacityQuery.setWorkshopid(query.getWorkshopId());
        cellBaseCapacityQuery.setCellsTypeId(null);
        List<CellVersionPlanIe> cellVersionPlanIes = Lists.newArrayList();
        //一、Ie产能数据准备
        iePrepareData(query, mapTypeToSeries, cellBaseCapacityQuery, cellModelName, cellVersionPlanIes);
        CellGradeCapacityQuery cellGradeCapacityQuery = convert.toCellGradeCapacityQuery(query);
        cellGradeCapacityQuery.setWorkshopid(query.getWorkshopId());
        List<CellVersionPlanIe> cellVersionPlanIesByGrade = Lists.newArrayList();
        //二、爬坡产能数据准备
        gradePrepareData(query, mapTypeToSeries, cellModelName, cellGradeCapacityQuery, cellVersionPlanIesByGrade);
        //三、爬坡数据与IE产能数据整合->合并
        List<CellVersionPlanIe> ieAndGradeCollect = mergeIeAndGrade(cellVersionPlanIes, cellVersionPlanIesByGrade);
        //四、入库计划数据准备
        CellInstockPlanTotalQuery instockPlanTotalQuery = convert.toCellInstockPlanTotalQuery(query);
        instockPlanTotalQuery.setWorkshopId(query.getWorkshopId());
        //依据国内海外、车间、电池类型、月份（必须项）查询计划数据
        String productionVersion = instockPlanTotalService.queryMaxVersion(instockPlanTotalQuery);
        List<CellVersionPlanIe> cellVersionPlanIeListByProduction = preparePlanData(query, mapTypeToSeries, cellModelName, instockPlanTotalQuery);
        //五、对统计后的产能数据和计划数据合并处理构建最终统计数据
        mergeIeGradeAndPlanData(query, oldLang, mapData, ieVersion, ieAndGradeCollect, productionVersion, cellVersionPlanIeListByProduction);
        return mapData;
    }

    /**
     * 对统计后的产能数据和计划数据合并处理构建最终统计数据
     * @param query
     * @param oldLang
     * @param mapData
     * @param ieVersion
     * @param ieAndGradeCollect
     * @param productionVersion
     * @param cellVersionPlanIeListByProduction
     */
    private void mergeIeGradeAndPlanData(CellVersionPlanIeQuery query, String oldLang, Map<String, Object> mapData, String ieVersion, List<CellVersionPlanIe> ieAndGradeCollect, String productionVersion, List<CellVersionPlanIe> cellVersionPlanIeListByProduction) {
        ieAndGradeCollect.stream().forEach(cellVersionPlanIe -> {
            int index = cellVersionPlanIeListByProduction.indexOf(cellVersionPlanIe);
            if (index > -1) {
                cellVersionPlanIe.setPlanCapacity(cellVersionPlanIeListByProduction.get(index).getPlanCapacity());
            }
        });
        List<CellVersionPlanIe> collectByProduction = cellVersionPlanIeListByProduction.stream().filter(cellVersionPlanIe -> {
            return !ieAndGradeCollect.contains(cellVersionPlanIe);
        }).collect(Collectors.toList());
        List<CellVersionPlanIe> cellVersionPlanIeCollect = Stream.concat(ieAndGradeCollect.stream(), collectByProduction.stream()).collect(Collectors.toList());


        //生成报表信息基础数据并落表
        AtomicReference<BigDecimal> capacityAll = new AtomicReference<>();
        AtomicReference<BigDecimal> planCapacityAll = new AtomicReference<>();
        cellVersionPlanIeCollect.stream().forEach(cellVersionPlanIe -> {
            cellVersionPlanIe.setVersion(productionVersion);
            if (cellVersionPlanIe.getCapacity() != null) {
                if (capacityAll.get() == null) {
                    capacityAll.set(BigDecimal.ZERO);
                }
                capacityAll.set(capacityAll.get().add(cellVersionPlanIe.getCapacity()));
            }
            if (cellVersionPlanIe.getPlanCapacity() != null) {
                if (planCapacityAll.get() == null) {
                    planCapacityAll.set(BigDecimal.ZERO);
                }
                planCapacityAll.set(planCapacityAll.get().add(cellVersionPlanIe.getPlanCapacity()));
            }
            if (cellVersionPlanIe.getPlanCapacity() != null   ) {
               if (Objects.nonNull(cellVersionPlanIe.getCapacity()) && BigDecimal.ZERO.compareTo(cellVersionPlanIe.getCapacity())!=0){
                   cellVersionPlanIe.setRate(cellVersionPlanIe.getPlanCapacity().divide(cellVersionPlanIe.getCapacity(), 4, RoundingMode.HALF_UP));
               }

                cellVersionPlanIe.setGap(cellVersionPlanIe.getPlanCapacity().subtract(Optional.ofNullable( cellVersionPlanIe.getCapacity()).orElse(BigDecimal.ZERO)));
            }
        });
        CellVersionPlanIe cellVersionPlanIeAll = new CellVersionPlanIe();
        cellVersionPlanIeAll.setWorkshopName((query.getIsOverseaName() == null ? "" : query.getIsOverseaName()) );
        cellVersionPlanIeAll.setCapacity(capacityAll.get());
        cellVersionPlanIeAll.setPlanCapacity(planCapacityAll.get());
        if (cellVersionPlanIeAll.getPlanCapacity() != null ) {
          if (Objects.nonNull(cellVersionPlanIeAll.getCapacity()) && BigDecimal.ZERO.compareTo(cellVersionPlanIeAll.getCapacity())!=0){
            cellVersionPlanIeAll.setRate(cellVersionPlanIeAll.getPlanCapacity().divide(cellVersionPlanIeAll.getCapacity(), 2, RoundingMode.HALF_UP));
          }
            cellVersionPlanIeAll.setGap(cellVersionPlanIeAll.getPlanCapacity().subtract(Optional.ofNullable(cellVersionPlanIeAll.getCapacity()).orElse(BigDecimal.ZERO)));
        }

        cellVersionPlanIeCollect.stream().forEach(cellVersionPlanIe -> {
            cellVersionPlanIe.setMonth(query.getMonth());
            cellVersionPlanIe.setCapacity(null==cellVersionPlanIe.getCapacity()?BigDecimal.ZERO:cellVersionPlanIe.getCapacity());
            cellVersionPlanIe.setGap(null==cellVersionPlanIe.getGap()?BigDecimal.ZERO:cellVersionPlanIe.getGap());
            cellVersionPlanIe.setPlanCapacity(null==cellVersionPlanIe.getPlanCapacity()?BigDecimal.ZERO:cellVersionPlanIe.getPlanCapacity());
        });

        List<CellVersionPlanIeDTO> details = convert.toDto(cellVersionPlanIeCollect);
        // mapData.put("details",convert.toDto( cellVersionPlanIeCollect));
        CellVersionPlanIeDTO firstRow = convert.toDto(cellVersionPlanIeAll);
        //  mapData.put("firstRow",convert.toDto( cellVersionPlanIeAll));
        //对报表基础信息汇总
        //电池类型汇总
        Map<String, List<CellVersionPlanIe>> collectCellVersionPlanIe = cellVersionPlanIeCollect.stream().collect(Collectors.groupingBy(CellVersionPlanIe::getCellTypeName));
        List<CellVersionPlanIe> datas = new ArrayList<>();
        collectCellVersionPlanIe.entrySet().stream().forEach(cellsTypecellVersionPlanIe -> {
            String celltypeName = cellsTypecellVersionPlanIe.getKey();
            AtomicReference<BigDecimal> capcity = new AtomicReference<>();
            AtomicReference<BigDecimal> planCapcity = new AtomicReference<>();
            cellsTypecellVersionPlanIe.getValue().stream().forEach(cellVersionPlanIe -> {
                if (cellVersionPlanIe.getCapacity() != null) {
                    if (capcity.get() == null) {
                        capcity.set(BigDecimal.ZERO);
                    }
                    capcity.set(capcity.get().add(cellVersionPlanIe.getCapacity()));
                }
                if (cellVersionPlanIe.getPlanCapacity() != null) {
                    if (planCapcity.get() == null) {
                        planCapcity.set(BigDecimal.ZERO);
                    }
                    planCapcity.set(planCapcity.get().add(cellVersionPlanIe.getPlanCapacity()));
                }

            });
            CellVersionPlanIe cellVersionPlanIeByCelltype = new CellVersionPlanIe();
            cellVersionPlanIeByCelltype.setMonth(query.getMonth());
            cellVersionPlanIeByCelltype.setCellTypeName(celltypeName);
            cellVersionPlanIeByCelltype.setPlanCapacity(planCapcity.get());
            if (capcity.get()!=null){
                capcity.set(capcity.get().setScale(0,RoundingMode.HALF_UP));
            }
            cellVersionPlanIeByCelltype.setCapacity(capcity.get());
            if (planCapcity.get() != null) {
                if (capcity.get().compareTo(BigDecimal.ZERO) != 0) {
                    cellVersionPlanIeByCelltype.setRate(planCapcity.get().divide(capcity.get(), 2, RoundingMode.HALF_UP));
                }
                cellVersionPlanIeByCelltype.setGap(planCapcity.get().subtract(Optional.ofNullable(capcity.get()).orElse(BigDecimal.ZERO) ));
            }
            datas.add(cellVersionPlanIeByCelltype);
        });
        Collections.sort(details, new Comparator<CellVersionPlanIeDTO>() {
            @Override
            public int compare(CellVersionPlanIeDTO o1, CellVersionPlanIeDTO o2) {
                if (Objects.equals(o1.getWorkshopName(),o2.getWorkshopName())){
                    return o1.getCellTypeName().compareTo(o2.getCellTypeName());
                }else{
                    return o1.getWorkshopName().compareTo(o2.getWorkshopName());
                }
            }
        });
        List<CellVersionPlanIeDTO> datasByCellType = convert.toDto(datas);
        Collections.sort(datasByCellType, new Comparator<CellVersionPlanIeDTO>() {
            @Override
            public int compare(CellVersionPlanIeDTO o1, CellVersionPlanIeDTO o2) {
                return o1.getCellTypeName().compareTo(o2.getCellTypeName());
            }
        });
        MyThreadLocal.get().setLang(oldLang);
        //翻译第一行
        String workshopName = firstRow.getWorkshopName();
        workshopName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,workshopName);
        firstRow.setWorkshopName( workshopName);
        details.add(0,firstRow);
        details.addAll(datasByCellType);
        //略过第一行，翻译details
        details.stream().skip(1).forEach(cellVersionPlanIeDTO -> {
            String cellTypeName = cellVersionPlanIeDTO.getCellTypeName();
            if (StringUtils.isNotEmpty(cellTypeName)){
                cellTypeName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.AOP_CELL_SERIES,cellTypeName);
                cellVersionPlanIeDTO.setCellTypeName(cellTypeName);
            }
            String myWorkshopName = cellVersionPlanIeDTO.getWorkshopName();
            if (StringUtils.isNotEmpty(myWorkshopName)){
                myWorkshopName= MapStrutUtil.getNameByCnNameLang(LovHeaderCodeConstant.WORK_SHOP,myWorkshopName);
                cellVersionPlanIeDTO.setWorkshopName(myWorkshopName);
            }

        });
        mapData.put("details",details);
        mapData.put("ieVersion", ieVersion);
        mapData.put("planVersion", productionVersion);
    }

    /**
     * 入库计划数据准备
     * @param query
     * @param mapTypeToSeries
     * @param cellModelName
     * @param instockPlanTotalQuery
     * @return
     */
    private List<CellVersionPlanIe> preparePlanData(CellVersionPlanIeQuery query, Map<String, LovLineDTO> mapTypeToSeries, String cellModelName, CellInstockPlanTotalQuery instockPlanTotalQuery) {
        List<CellInstockPlanTotalDTO> instockPlanTotalDTOList = instockPlanTotalService.queryByFirst(instockPlanTotalQuery);
        if (instockPlanTotalDTOList == null) {
            throw new BizException(query.getMonth() + "月还没有入库计划确认数据，无法版本对比");
        }
        else {
            // 入库计划产能/兆瓦系数
            instockPlanTotalDTOList.stream().forEach(grade -> {
                CellConversionFactorDTO cellConversionFactorDTO = cellConversionFactorService.queryByCellsType(grade.getIsOversea(),grade.getCellsType());
                if (cellConversionFactorDTO==null) {
                    setZero(grade);
                } else {
                    if (BigDecimal.ZERO.compareTo( cellConversionFactorDTO.getConversionFactor())!=0){
                       BigDecimal val=    grade.getQtyThousandPc();
                        if (val!=null&&BigDecimal.ZERO.compareTo(val)!=0){
                            grade.setQtyThousandPc(val.divide(cellConversionFactorDTO.getConversionFactor(), 6, RoundingMode.HALF_UP));
                        }
//                                ReflectUtil.invoke(grade,"setD"+i,val.divide(cellConversionFactorDTO.getConversionFactor(), 2, RoundingMode.HALF_UP));
//                            }
//                        for (int i = 1; i <=31 ; i++) {
//                            BigDecimal val=   ReflectUtil.invoke(grade,"getD"+i);
//                            if (val!=null&&BigDecimal.ZERO.compareTo(val)!=0){
//                                ReflectUtil.invoke(grade,"setD"+i,val.divide(cellConversionFactorDTO.getConversionFactor(), 2, RoundingMode.HALF_UP));
//                            }
//                        }

                    }else {
                        setZero(grade);
                    }

                }

            });
            //电池型号准备
            instockPlanTotalDTOList.stream().forEach(item->{
                String cellsType=item.getCellsType();
                LovLineDTO lovLineDTO= mapTypeToSeries.get(cellsType);
                String modelName=lovLineDTO.getLovName();
                Long  modelId=lovLineDTO.getLovLineId();
                item.setCellsType(modelName);
                item.setCellsTypeId(modelId);
            });
            String finalCellModelName = cellModelName;
            if (StringUtils.isNotEmpty(finalCellModelName)){
                instockPlanTotalDTOList=   instockPlanTotalDTOList.stream().filter(item->{
                    return item.getCellsType().equals(finalCellModelName);
                }).collect(Collectors.toList());
            }
        }

        //对查到的计划数据依据国内海外、车间、电池类型分组统计
        Map<String, Map<String, Map<String, Map<String, List<CellInstockPlanTotalDTO>>>>> cellPlanLineDTOCollect =
                instockPlanTotalDTOList.stream().collect(Collectors.groupingBy(CellInstockPlanTotalDTO::getIsOversea,
                        Collectors.groupingBy(CellInstockPlanTotalDTO::getBasePlace,
                                Collectors.groupingBy(CellInstockPlanTotalDTO::getWorkshop,
                                        Collectors.groupingBy(CellInstockPlanTotalDTO::getCellsType)))));

        List<CellVersionPlanIe> cellVersionPlanIeListByProduction = new ArrayList<>();
        cellPlanLineDTOCollect.entrySet().stream().forEach(entryOversea -> {
            String oversea = entryOversea.getKey();
            entryOversea.getValue().entrySet().stream().forEach(entryBaseplace -> {
                String basePlace = entryBaseplace.getKey();
                entryBaseplace.getValue().entrySet().stream().forEach(entryWorkshop -> {
                    String workshop = entryWorkshop.getKey();
                    entryWorkshop.getValue().entrySet().stream().forEach(entryCelltype -> {
                        CellVersionPlanIe cellVersionPlanIe = null;
                        AtomicReference<BigDecimal> planCapacity = new AtomicReference<>();
                        for (CellInstockPlanTotalDTO instockPlanTotalDTO : entryCelltype.getValue()) {
                            if (cellVersionPlanIe == null) {
                                cellVersionPlanIe = convert.toCellVersionPlanIeFromCellInstockPlanTotal(instockPlanTotalDTO);
                            }
                            BigDecimal qtyThousandPc = instockPlanTotalDTO.getQtyThousandPc();
                            if (planCapacity.get() == null) {
                                planCapacity.set(BigDecimal.ZERO);
                            }
                            if (qtyThousandPc != null) {
                                planCapacity.set(planCapacity.get().add(qtyThousandPc));
                            }
                        }
                        if (planCapacity.get()!=null){
                            planCapacity.set(planCapacity.get().setScale(0,RoundingMode.HALF_UP));
                        }
                        cellVersionPlanIe.setPlanCapacity(planCapacity.get());
                        cellVersionPlanIe.setCapacity(BigDecimal.ZERO);
                        cellVersionPlanIeListByProduction.add(cellVersionPlanIe);
                    });
                });
            });
        });
        return cellVersionPlanIeListByProduction;
    }

    /**
     * 爬坡数据与IE产能数据整合->合并
     * @param cellVersionPlanIes
     * @param cellVersionPlanIesByGrade
     * @return
     */
    private List<CellVersionPlanIe> mergeIeAndGrade(List<CellVersionPlanIe> cellVersionPlanIes, List<CellVersionPlanIe> cellVersionPlanIesByGrade) {
        cellVersionPlanIes.stream().forEach(cellVersionPlanIe -> {
            int index = cellVersionPlanIesByGrade.indexOf(cellVersionPlanIe);
            if (index > -1) {
                CellVersionPlanIe cellVersionPlanIeInGrade = cellVersionPlanIesByGrade.get(index);
                if (cellVersionPlanIe.getCapacity() != null) {
                    if (cellVersionPlanIeInGrade.getCapacity() != null) {
                        cellVersionPlanIe.setCapacity(cellVersionPlanIe.getCapacity().add(cellVersionPlanIeInGrade.getCapacity()));
                    }
                } else {
                    if (cellVersionPlanIeInGrade.getCapacity() != null) {
                        cellVersionPlanIe.setCapacity(cellVersionPlanIeInGrade.getCapacity());
                    }
                }

            }
        });
        //考虑爬坡数据在IE产能没有对应的特殊情况（也要合并）
        List<CellVersionPlanIe> cellGradecollect = cellVersionPlanIesByGrade.stream().filter(cellVersionPlanIe -> {
            return !cellVersionPlanIes.contains(cellVersionPlanIe);
        }).collect(Collectors.toList());
        //最终IE和Grade合并后的数据
        List<CellVersionPlanIe> ieAndGradeCollect = Stream.concat(cellVersionPlanIes.stream(), cellGradecollect.stream()).collect(Collectors.toList());
        ieAndGradeCollect.stream().forEach(item->{
            if (item.getCapacity()!=null){
                item.setCapacity(item.getCapacity().setScale(0,RoundingMode.HALF_UP));
            }

        });
        return ieAndGradeCollect;
    }

    /**
     * 爬坡产能数据准备
     * @param query
     * @param mapTypeToSeries
     * @param cellModelName
     * @param cellGradeCapacityQuery
     * @param cellVersionPlanIesByGrade
     */
    private void gradePrepareData(CellVersionPlanIeQuery query, Map<String, LovLineDTO> mapTypeToSeries, String cellModelName, CellGradeCapacityQuery cellGradeCapacityQuery, List<CellVersionPlanIe> cellVersionPlanIesByGrade) {
        Page<CellGradeCapacityDTO> pageCellGradeCapacityDTOS = cellGradeCapacityService.queryByPage(cellGradeCapacityQuery);

        List<CellGradeCapacityDTO> cellGradeCapacityDTOS = Lists.newArrayList();
        if (pageCellGradeCapacityDTOS != null) {
            cellGradeCapacityDTOS =Optional.ofNullable( pageCellGradeCapacityDTOS.getContent()).orElse(Lists.newArrayList());
            // 爬坡产能/兆瓦系数
            cellGradeCapacityDTOS.stream().forEach(grade -> {
                CellConversionFactorDTO cellConversionFactorDTO = cellConversionFactorService.queryByCellsType(grade.getIsOversea(),grade.getCellsType());
                if (cellConversionFactorDTO==null) {
                    setZero(grade);
                } else {
                    if (BigDecimal.ZERO.compareTo( cellConversionFactorDTO.getConversionFactor())!=0){
                        for (int i = 1; i <=31 ; i++) {
                            BigDecimal val=   ReflectUtil.invoke(grade,"getD"+i);
                            if (val!=null&&BigDecimal.ZERO.compareTo(val)!=0){
                                ReflectUtil.invoke(grade,"setD"+i,val.divide(cellConversionFactorDTO.getConversionFactor(), 6, RoundingMode.HALF_UP));
                            }
                        }
                    }else {
                        setZero(grade);
                    }

                }

            });
            //电池型号处理
            cellGradeCapacityDTOS.stream().forEach(item->{
                String cellsType=item.getCellsType();
                item.setOldCellsType(cellsType);
                LovLineDTO lovLineDTO= mapTypeToSeries.get(cellsType);
                String modelName=lovLineDTO.getLovName();
                Long  modelId=lovLineDTO.getLovLineId();
                item.setCellsType(modelName);
                item.setCellsTypeId(modelId);
            });
            if (StringUtils.isNotEmpty(cellModelName)){
                String finalCellModelName1 = cellModelName;
                cellGradeCapacityDTOS=   cellGradeCapacityDTOS.stream().filter(item->{
                    return item.getCellsType().equals(finalCellModelName1);
                }).collect(Collectors.toList());
            }
        }

        Map<String, Map<String, List<CellGradeCapacityDTO>>> collectCellGradeCapacityDTOS = cellGradeCapacityDTOS.stream().collect(
                Collectors.groupingBy(CellGradeCapacityDTO::getWorkshop,
                        Collectors.groupingBy(CellGradeCapacityDTO::getCellsType)));

        collectCellGradeCapacityDTOS.entrySet().stream().forEach(workshopEntry -> {
            String workshop = workshopEntry.getKey();
            workshopEntry.getValue().entrySet().stream().forEach(cellsTypeEntry -> {
                String cellstype = cellsTypeEntry.getKey();
                AtomicReference<BigDecimal> maxCapacity = new AtomicReference<>(BigDecimal.ZERO);
                CellVersionPlanIe cellVersionPlanIe = null;
                Map<String, List<CellGradeCapacityDTO>> oldCellsTypeMap = cellsTypeEntry.getValue().stream().collect(Collectors.groupingBy(CellGradeCapacityDTO::getOldCellsType));
                Set<Map.Entry<String, List<CellGradeCapacityDTO>>> entries = oldCellsTypeMap.entrySet();
                for (Map.Entry<String, List<CellGradeCapacityDTO>> entry : entries) {
                    AtomicReference<BigDecimal> capacity = new AtomicReference<>(BigDecimal.ZERO);
                    for (CellGradeCapacityDTO cellGradeCapacity:entry.getValue()) {
                        if (cellVersionPlanIe==null){
                            cellVersionPlanIe= BeanUtil.copyProperties(cellGradeCapacity,CellVersionPlanIe.class);
                            cellVersionPlanIe.setCellTypeId(cellGradeCapacity.getCellsTypeId());
                            cellVersionPlanIe.setWorkshopId(cellGradeCapacity.getWorkshopid());
                            cellVersionPlanIe.setIsOverseaId(cellGradeCapacity.getIsOverseaId());
                            cellVersionPlanIe.setIsOverseaName(cellGradeCapacity.getIsOversea());
                        }
                        for (int day = 1; day <= 31; day++) {
                            Method method = ReflectUtil.getMethod(CellGradeCapacityDTO.class, "getD" + day);
                            BigDecimal dayCapacity = ReflectUtil.invoke(cellGradeCapacity, method);
                            if (dayCapacity != null) {
                                if (capacity.get() == null) {
                                    capacity.set(BigDecimal.ZERO);
                                }
                                capacity.set(capacity.get().add(dayCapacity.multiply(cellGradeCapacity.getLineNumber())));
                            }
                        }

                    }
                    cellVersionPlanIe.setMonth(query.getMonth());
                    cellVersionPlanIe.setWorkshopName(workshop);
                    cellVersionPlanIe.setCellTypeName(cellstype);
                    if (capacity.get()!=null){
                        capacity.set(capacity.get().setScale(0,RoundingMode.HALF_UP));
                    }
                    if (capacity.get().compareTo(maxCapacity.get())>=0){
                        maxCapacity.set(capacity.get());
                    }
                }
                cellVersionPlanIe.setCapacity(maxCapacity.get());
                cellVersionPlanIe.setPlanCapacity(BigDecimal.ZERO);
                cellVersionPlanIesByGrade.add(cellVersionPlanIe);
            });
        });
        log.info("grade:" + cellVersionPlanIesByGrade);
    }

    /**
     * IE产能数据准备构建List<CellVersionPlanIe>
     * @param query
     * @param mapTypeToSeries
     * @param cellBaseCapacityQuery
     * @param cellModelName
     * @param cellVersionPlanIes
     */
    private void iePrepareData(CellVersionPlanIeQuery query, Map<String, LovLineDTO> mapTypeToSeries, CellBaseCapacityQuery cellBaseCapacityQuery, String cellModelName, List<CellVersionPlanIe> cellVersionPlanIes) {
        Page<CellBaseCapacityDTO> pageCellBaseCapacityDTOS = cellBaseCapacityService.queryByPage(cellBaseCapacityQuery);
        List<CellBaseCapacityDTO> cellBaseCapacityDTOS = Lists.newArrayList();
        if (pageCellBaseCapacityDTOS != null) {
            cellBaseCapacityDTOS =Optional.ofNullable( pageCellBaseCapacityDTOS.getContent()).orElse(Lists.newArrayList());
            // IE产能/兆瓦系数
            cellBaseCapacityDTOS.stream().forEach(grade -> {
                CellConversionFactorDTO cellConversionFactorDTO = cellConversionFactorService.queryByCellsType(grade.getIsOversea(),grade.getCellsType());
                if (cellConversionFactorDTO==null) {
                    grade.setSingleCapacity(BigDecimal.ZERO);
                } else {
                    if (BigDecimal.ZERO.compareTo( cellConversionFactorDTO.getConversionFactor())!=0){
                        grade.setSingleCapacity(grade.getSingleCapacity().divide(cellConversionFactorDTO.getConversionFactor(), 2, RoundingMode.HALF_UP));
                    }else {
                        grade.setSingleCapacity(BigDecimal.ZERO);
                    }

                }

            });
            //电池型号准备
            cellBaseCapacityDTOS.stream().forEach(item->{
                String cellsType=item.getCellsType();
                item.setOldCellsType(cellsType);
                LovLineDTO lovLineDTO= mapTypeToSeries.get(cellsType);
                String modelName=lovLineDTO.getLovName();
                Long  modelId=lovLineDTO.getLovLineId();
                item.setCellsType(modelName);
                item.setCellsTypeId(modelId);
            });
            String finalCellModelName = cellModelName;
            if (StringUtils.isNotEmpty(finalCellModelName)){
                cellBaseCapacityDTOS=   cellBaseCapacityDTOS.stream().filter(item->{
                    return item.getCellsType().equals(finalCellModelName);
                }).collect(Collectors.toList());
            }
        }
        //依据国内海外、电池类型、车间、月份（必须项）查询IE产能
        //对查到的IE产能数据依据国内海外、车间、电池类型分组统计
        Map<String, Map<String, Map<String, List<CellBaseCapacityDTO>>>> collectCellBaseCapacityDTO = cellBaseCapacityDTOS.stream().collect(
                Collectors.groupingBy(CellBaseCapacityDTO::getIsOversea,
                        Collectors.groupingBy(CellBaseCapacityDTO::getWorkshop,
                                Collectors.groupingBy(CellBaseCapacityDTO::getCellsType))));
        collectCellBaseCapacityDTO.entrySet().stream().forEach(entryIsOversea -> {
            String oversea = entryIsOversea.getKey();
            entryIsOversea.getValue().entrySet().stream().forEach(entryWorkshop -> {
                String workshop = entryWorkshop.getKey();
                entryWorkshop.getValue().entrySet().stream().forEach(entryCellsType -> {
                    String cellsType = entryCellsType.getKey();
                    CellVersionPlanIe cellVersionPlanIe = new CellVersionPlanIe();
                    cellVersionPlanIe.setIsOverseaName(oversea);
                    cellVersionPlanIe.setWorkshopName(workshop);
                    cellVersionPlanIe.setCellTypeName(cellsType);
                    cellVersionPlanIe.setMonth(query.getMonth());
                    AtomicReference<BigDecimal> maxCapacity = new AtomicReference<>(BigDecimal.ZERO);
                    Map<String, List<CellBaseCapacityDTO>> oldCellsTypeMap = entryCellsType.getValue().stream().collect(Collectors.groupingBy(CellBaseCapacityDTO::getOldCellsType));
                    Set<Map.Entry<String, List<CellBaseCapacityDTO>>> entries = oldCellsTypeMap.entrySet();
                    for (Map.Entry<String, List<CellBaseCapacityDTO>> entry : entries) {
                        AtomicReference<BigDecimal> capacity = new AtomicReference<>(BigDecimal.ZERO);
                        entry.getValue().stream().forEach(cellBaseCapacityDTO -> {
                            //当前月的第一天
                            LocalDate firstDay = DateUtil.getLocalDate(query.getMonth(), 1);
                            //当前月的最后一天
                            LocalDate lastDay = firstDay.with(TemporalAdjusters.lastDayOfMonth());
                            //Ie产能的开始时间
                            LocalDate startDate = cellBaseCapacityDTO.getStartTime();
                            //Ie产能的结束时间
                            LocalDate endDate = cellBaseCapacityDTO.getEndTime();
                            if (startDate.isBefore(firstDay)) {
                                startDate = firstDay;
                            }
                            if (endDate.isAfter(lastDay)) {
                                endDate = lastDay;
                            }
                            long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
                            if (capacity.get() == null) {
                                capacity.set(BigDecimal.ZERO);
                            }

                            capacity.set(capacity.get().add(cellBaseCapacityDTO.getSingleCapacity().multiply(BigDecimal.valueOf(days)).multiply(cellBaseCapacityDTO.getUsageLine())));

                        });
                        if (capacity.get().compareTo(maxCapacity.get())>0){
                            maxCapacity.set(capacity.get());
                        }
                    }
                    cellVersionPlanIe.setCapacity(maxCapacity.get());
                    cellVersionPlanIe.setPlanCapacity(BigDecimal.ZERO);
                    cellVersionPlanIes.add(cellVersionPlanIe);
                });
            });
        });
    }


}
