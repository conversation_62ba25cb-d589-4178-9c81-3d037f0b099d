package com.trinasolar.scp.baps.service.feign.fallback;

import com.trinasolar.scp.baps.domain.dto.ScheduledTaskLinesDTO;
import com.trinasolar.scp.baps.domain.dto.SiliconSliceSupplyLinesDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.BmrpSafetyStockDaysDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.SiliconSlicePurchasePlanDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.TjOnHandDTO;
import com.trinasolar.scp.baps.domain.query.SiliconSliceSupplyLinesQuery;
import com.trinasolar.scp.baps.domain.query.bmrp.BmrpSafetyStockDaysQuery;
import com.trinasolar.scp.baps.domain.query.bmrp.SiliconSlicePurchasePlanQuery;
import com.trinasolar.scp.baps.domain.query.bmrp.TjOnHandQuery;
import com.trinasolar.scp.baps.service.feign.BmrpFeign;
import com.trinasolar.scp.common.api.util.Results;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: BmrpFeignFallbackFactory
 * @date 2024/6/6 09:27
 */
@Slf4j
@Component
public class BmrpFeignFallbackFactory implements FallbackFactory<BmrpFeign> {
    @Override
    public BmrpFeign create(Throwable cause) {
        return new BmrpFeign() {
            @Override
            public ResponseEntity<Results<ScheduledTaskLinesDTO>> initTask() {
                log.warn("【BmrpFeign-initTask】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<String>>> addLogSave(ScheduledTaskLinesDTO taskLinesDTO) {
                log.warn("【BmrpFeign-addLogSave】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<TjOnHandDTO>>> queryInventory(TjOnHandQuery query) {
                log.warn("【BmrpFeign-queryInventory】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<SiliconSliceSupplyLinesDTO>>> findByLastVersionList(SiliconSliceSupplyLinesQuery query) {
                log.warn("【BmrpFeign-findByLastVersionList】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<SiliconSlicePurchasePlanDTO>>> findByCondition(SiliconSlicePurchasePlanQuery query) {
                log.warn("【BmrpFeign-findByCondition】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<BmrpSafetyStockDaysDTO>>> safetyStockList(BmrpSafetyStockDaysQuery query) {
                log.warn("【BmrpFeign-safetyStockList】发生异常：{}", cause);
                return Results.createFailRes();
            }

        };
    }
}
