package com.trinasolar.scp.baps.service.service;

import java.util.concurrent.Callable;

/**
 * 手动处理事务接口
 *
 * <AUTHOR>
 * @date 2022年9月27日14:44:55
 */
public interface TransactionService {
    /**
     * 开启事务
     */
    void begin();

    /**
     * 提交事务
     */
    void commit();

    /**
     * 回滚事务
     */
    void rollback();

    /**
     * 在事务中执行回调函数
     *
     * @param callable 回调函数
     */
    <V> V execute(Callable<V> callable);
}
