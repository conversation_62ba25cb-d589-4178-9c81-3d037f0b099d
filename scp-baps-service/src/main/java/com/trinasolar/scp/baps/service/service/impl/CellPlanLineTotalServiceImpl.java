package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.*;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.dto.aps.ModuleBasePlaceDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.BatterySiliconWaferDTO;
import com.trinasolar.scp.baps.domain.dto.message.EmailAddress;
import com.trinasolar.scp.baps.domain.dto.system.CarbonCertHeaderDTO;
import com.trinasolar.scp.baps.domain.dto.system.CarbonOriginAddress;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.enums.PlanChangeStatusEnum;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineTotalExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineTotalHExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.query.*;
import com.trinasolar.scp.baps.domain.query.aps.ModuleBasePlaceQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineTotalSaveDTO;
import com.trinasolar.scp.baps.domain.save.CellPlanLineVersionSaveDTO;
import com.trinasolar.scp.baps.domain.utils.*;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.feign.BbomFeign;
import com.trinasolar.scp.baps.service.feign.PageFeign;
import com.trinasolar.scp.baps.service.repository.*;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.baps.service.service.log.LogService;
import com.trinasolar.scp.baps.service.service.mail.MailService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.common.api.util.exStrategy.CellStyleModel;
import com.trinasolar.scp.common.api.util.exStrategy.CustomCellStyleHandler;
import jodd.exception.ExceptionUtil;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 投产计划汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Slf4j
@Service("cellPlanLineTotalService")
@RequiredArgsConstructor
public class CellPlanLineTotalServiceImpl implements CellPlanLineTotalService {
    private static final QCellPlanLineTotal qCellPlanLineTotal = QCellPlanLineTotal.cellPlanLineTotal;
    private final CellPlanLineTotalDEConvert convert;
    private final CellConversionFactorService cellConversionFactorService;
    private final CellPlanLineTotalRepository repository;
    private final CellPlanLineService cellPlanLineService;
    private final CellPlanLineRepository cellPlanLineRepository;
    private final CellPlanLineDEConvert cellPlanLineDEConvert;
    private final CellFineService cellFineService;
    private final CellProductionLeadTimeService cellProductionLeadTimeService;
    private final CellInstockPlanRepository cellInstockPlanRepository;
    private final CellInstockPlanDEConvert cellInstockPlanDEConvert;
    private final MailService mailService;
    private final CellPlanLineVersionService cellPlanLineVersionService;
    private final CellPlanLineVersionDEConvert cellPlanLineVersionDEConvert;
    private final CellInstockPlanVersionDEConvert cellInstockPlanVersionDEConvert;
    private final CellPlanLineVersionRepository cellPlanLineVersionRepository;
    private final CellInstockPlanVersionRepository cellInstockPlanVersionRepository;
    private final LogService logService;
    private final CellInstockPlanTotalRepository cellInstockPlanTotalRepository;
    private final CellPlanLineTotalDEConvert cellPlanLineTotalDEConvert;
    @Autowired
    private JPAQueryFactory jpaQueryFactory;
    private final RedissonClient redissonClient;
    private final TransactionService transactionService;
    private final CellPlanQtyFluctuationCoefficientService fluctuationCoefficientService;
    private final CellPlanLineTotalHDEConvert cellPlanLineTotalHDEConvert;
    private final CellPlanLineTotalHRepository cellPlanLineTotalHRepository;
    private final CellPlanLineHRepository cellPlanLineHRepository;
    private final CellPlanLineHDEConvert cellPlanLineHDEConvert;
    private final CellInstockPlanService cellInstockPlanService;
    private final BbomFeign bbomFeign;
    private final ApsFeign apsFeign;
    private final CarbonService carbonService;

    @Autowired
    @Qualifier("dataSyncThreadPool")
    ExecutorService threadPoolExecutor;

    @Lazy
    @Autowired
    private CellPlanLineTotalService selfService;

    /**
     * 投产计划汇总查询(最新版数据)
     *
     * @param query 查询对象
     * @return
     */
    @Override
    public Page<CellPlanLineTotalDTO> queryByPage(CellPlanLineTotalQuery query, Pair<String, String> versions) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        CellPlanLineQuery cellPlanLineQuery = convert.toCellPlanLineQuery(query);
        //读取投产数据后汇总统计
        Page<CellPlanLineTotalDTO> result = calcCellPlanLineTotalByQuery(cellPlanLineQuery, versions, oldLang);
        return result;
    }

    @Override
    public Pair<String, String> getSendedEmailOrLastVersion(CellPlanLineTotalQuery query, Boolean sendedEmailVersionFlag) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (!sendedEmailVersionFlag) {
            if (StringUtils.isNotEmpty(query.getIsOversea())) {
                String version = getPlanLineLastVersion(month, query.getIsOversea());
                if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                    return new ImmutablePair<>(version, null);
                } else {
                    return new ImmutablePair<>(null, version);
                }
            } else {
                String isOversea = OverseaConstant.INLAND;
                String InVersion = getPlanLineLastVersion(month, isOversea);
                isOversea = OverseaConstant.OVERSEA;
                String outVersion = getPlanLineLastVersion(month, isOversea);
                return new ImmutablePair<>(InVersion, outVersion);
            }
        } else {
            if (StringUtils.isNotEmpty(query.getIsOversea())) {
                Long isOverseaId = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()).getLovLineId();
                String version = cellPlanLineVersionService.findMaxVersionSendedEmail(isOverseaId, month);
                if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                    return new ImmutablePair<>(version, null);
                } else {
                    return new ImmutablePair<>(null, version);
                }
            } else {
                String InVersion = cellPlanLineVersionService.findMaxVersionSendedEmail(OverseaConstant.INLAND_ID, month);
                String outVersion = cellPlanLineVersionService.findMaxVersionSendedEmail(OverseaConstant.OVERSEA_ID, month);
                return new ImmutablePair<>(InVersion, outVersion);
            }
        }
    }

    @Override
    public Page<CellPlanLineTotalDTO> queryTwnMonthByPage(CellPlanLineTotalQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        CellPlanLineQuery cellPlanLineQuery = convert.toCellPlanLineQuery(query);
        //获取最新版本号
        Pair<String, String> versions = getSendedEmailOrLastVersion(query, false);
        Page<CellPlanLineTotalDTO> result = calcCellPlanLineTotalTwoMonthByQuery(cellPlanLineQuery, versions, oldLang);

        return result;
    }

    /**
     * 提供给mrp的投产查询接口
     *
     * @param query
     * @return
     */
    @Override
    public Page<CellPlanLineTotalDTO> queryByPageForMrp(CellPlanLineTotalQuery query) {
        //要求的数据:oldMonth在本月并且month在本月的数据+oldMonth在下月并且month在本月的数据（oldMonth代表入库月，month代表投产月）
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        CellPlanLineQuery cellPlanLineQuery = convert.toCellPlanLineQuery(query);
        //1、获取确认的当月最新版本号
        Pair<String, String> versions = getPlanLineLastConfirmVersion(cellPlanLineQuery);
        String month = query.getMonth();
        String nextMonth = DateUtil.getMonth(DateUtil.getLocalDate(month, 1).plusMonths(1));
        CellPlanLineQuery nextCellPlanLineQuery = BeanUtil.copyProperties(cellPlanLineQuery, CellPlanLineQuery.class);
        nextCellPlanLineQuery.setMonth(nextMonth);
        //2、获取确认的次月最新版本号
        Pair<String, String> nextVersions = getPlanLineLastConfirmVersion(nextCellPlanLineQuery);
        //3、查询计算
        return calcCellPlanLineTotalByQueryForMrp(cellPlanLineQuery, versions, nextCellPlanLineQuery, nextVersions, oldLang);
    }

    /**
     * 获取投产汇总数据给MRP
     *
     * @param query        本月查询条件
     * @param versions     本月确认版本号
     * @param nextQuery    次月查询条件
     * @param nextVersions 次月确认版本号
     * @param oldLang      语言
     * @return
     */
    private Page<CellPlanLineTotalDTO> calcCellPlanLineTotalByQueryForMrp(CellPlanLineQuery query, Pair<String, String> versions, CellPlanLineQuery nextQuery, Pair<String, String> nextVersions, String oldLang) {
        //1、获取兆瓦系数用于兆瓦转化计算
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        List<CellPlanLineTotalDTO> cellPlanLineTotals = new ArrayList<>();
        //2、获取数据oldMonth在本月并且month在本月的数据+oldMonth在下月并且month在本月的数据（oldMonth代表入库月，month代表投产月）
        List<CellPlanLineDTO> datas = cellPlanLineService.queryForMrp(query, versions, nextQuery, nextVersions);
        //3、根据key分组
        Map<String, List<CellPlanLineDTO>> collect = datas.stream().collect(
                Collectors.groupingBy(item -> {
                    return StringTools.joinWith(",", item.getIsOversea(), item.getBasePlace(), item.getWorkshop(), item.getCellsType(), item.getMonth(), item.getHTrace(), item.getAesthetics(), item.getTransparentDoubleGlass(), item.getCellSource(), item.getRegionalCountry(), item.getItemCode());
                })
        );
        //依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        Collections.sort(keys);
        //4、分页参数准备
        Integer total = keys.size();
        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer endPos = Math.min(startPos + pageSize, total);
        //5、统计当前页数据
        for (int i = startPos; i < endPos; i++) {
            String key = keys.get(i);
            CellConversionFactorDTO cellConversionFactorDTO = null;
            CellPlanLineTotalDTO cellPlanLineTotal = null;
            List<CellPlanLineDTO> dtos = collect.get(key);
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            for (CellPlanLineDTO dto : dtos) {
                if (cellConversionFactorDTO == null) {
                    cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", dto.getIsOversea(), dto.getCellsType()));
                }
                if (Objects.isNull(cellPlanLineTotal)) {
                    cellPlanLineTotal = convert.toCellPlanLineTotalDTO(dto);
                    cellPlanLineTotal.setFromVersion(dto.getVersion());
                }
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellPlanLineTotal, "getD" + day);
                BigDecimal value = dto.getQtyPc().setScale(6, RoundingMode.HALF_UP);
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, addVal);
                }
                if (Objects.nonNull(dto.getQtyPc())) {
                    qtyCount = qtyCount.add(value);
                }
            }
            // 对日期进行四舍五入
            for (int day = 1; day <= 31; day++) {
                Object val = ReflectUtil.invoke(cellPlanLineTotal, "getD" + day);
                if (Objects.nonNull(val)) {
                    BigDecimal value = (BigDecimal) val;
                    ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, value.setScale(2, RoundingMode.HALF_UP));
                }
            }
            //依据瓦片折算系数计算MV
            if (Objects.nonNull(cellConversionFactorDTO)) {
                if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                    BigDecimal mv = qtyCount.divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                    cellPlanLineTotal.setCellMv(mv);
                }

            } else {
                log.warn("没有《" + cellPlanLineTotal.getCellsType() + "》电池类型折算系数数据，不能进行对应的mv计算");
            }
            qtyCount = qtyCount.setScale(2, RoundingMode.HALF_UP);
            cellPlanLineTotal.setQtyThousandPc(qtyCount);
            cellPlanLineTotals.add(cellPlanLineTotal);

        }
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //6、翻译转换
        MyThreadLocal.get().setLang(oldLang);
        cellPlanLineTotals = convert.toCellPlanLineTotalNameByCnNameDTO(cellPlanLineTotals);
        return new PageImpl(cellPlanLineTotals, pageable, total);
    }

    /**
     * 投产计划汇总查询(最新确认版数据)
     *
     * @param query 查询对象
     * @return
     */

    private Page<CellPlanLineTotalDTO> queryConfirmByPage(CellPlanLineTotalQuery query, Pair<String, String> versions) {
        CellPlanLineQuery cellPlanLineQuery = convert.toCellPlanLineQuery(query);
        return calcCellPlanLineTotalConfirmByQuery(cellPlanLineQuery, versions);
    }

    /**
     * 投产计划汇总查询(上一确认版本数据)
     *
     * @param query
     * @return
     */
    private Page<CellPlanLineTotalDTO> queryConfirmPreByPage(CellPlanLineTotalQuery query) {
        CellPlanLineQuery cellPlanLineQuery = convert.toCellPlanLineQuery(query);
        //获取上一版本的版本号
        Pair<String, String> versions = getPlanLinePreConfirmVersion(cellPlanLineQuery);
        return calcCellPlanLineTotalConfirmByQuery(cellPlanLineQuery, versions);
    }

    /**
     * 查询某月国内或海外某版本的数据(给版本对比使用)
     *
     * @param query
     * @return
     */
    @Override
    public List<CellPlanLineTotalDTO> query(CellPlanLineTotalQuery query, String month, Long isOverseaId, String version) {
        QCellPlanLineTotal qCellPlanLineTotal = QCellPlanLineTotal.cellPlanLineTotal;
        JPAQuery<CellPlanLineTotal> where = jpaQueryFactory.select(qCellPlanLineTotal).from(qCellPlanLineTotal).where(
                qCellPlanLineTotal.oldMonth.eq(query.getMonth())
        );
        where.where(
                qCellPlanLineTotal.version.eq(version)
        );
        where.where(
                qCellPlanLineTotal.isOverseaId.eq(isOverseaId)
        );
        if (query.getWorkshopId() != null) {
            where.where(
                    qCellPlanLineTotal.workshopId.eq(query.getWorkshopId())
            );
        }
        if (query.getCellsTypeId() != null) {
            where.where(
                    qCellPlanLineTotal.cellsTypeId.eq(query.getCellsTypeId())
            );
        }
        List<CellPlanLineTotal> fetch = where.fetch();
        return convert.toDto(fetch);
    }


    /**
     * 统计汇总最新版本数据(从汇总表直接读)
     *
     * @param query
     * @return
     */
    private Page<CellPlanLineTotalDTO> getCellPlanLineTotalByQuery(CellPlanLineTotalQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.ASC, "isOversea");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellPlanLineTotal> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private List<String> buildMonthAllLocalDateTimes(String month, List<CellPlanLineDTO> datas) {
        // tilte天数组装，查询月份对应天数进行填充
        List<LocalDateTime> allLocalDateList= com.google.common.collect.Lists.newLinkedList();
        int days = DateUtil.getDaysForMonth(month);
        YearMonth yearMonth = YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyyMM"));
        for (int i = 1; i <= days; i++) {
            allLocalDateList.add(LocalDateTime.of(yearMonth.getYear(), yearMonth.getMonthValue(), i, 8, 0));
        }
        List<LocalDateTime> otherLocalDateList = datas.stream().map(CellPlanLineDTO::getStartTime).filter(Objects::nonNull)
                .filter(ele -> !Objects.equals(DateTimeFormatter.ofPattern("yyyyMM").format(ele), month)).distinct().collect(toList());
        allLocalDateList.addAll(otherLocalDateList);
        return allLocalDateList.stream().map(ele -> DateTimeFormatter.ofPattern("yyyy-MM-dd").format(ele)).distinct()
                .sorted(Comparator.naturalOrder()).collect(toList());
    }

    private Map<String, List<CellPlanLineDTO>> calcCellPlanLineTotalByQueryHandle(List<CellPlanLineDTO> datas) {
        return datas.stream().peek(
                item -> {
                    if (StringUtils.isBlank(item.getHChangeFlag())) {
                        item.setHChangeFlag("N");
                    }
                }
        ).collect(Collectors.groupingBy(CellPlanLineDTO::group));
    }

    /**
     * 统计汇总最新版本数据(从投产计划表读，读后计算)
     *
     * @param query    查询条件
     * @param versions 版本
     * @param oldLang  语言
     * @return
     */
    private Page<CellPlanLineTotalDTO> calcCellPlanLineTotalByQuery(CellPlanLineQuery query,
                                                                    Pair<String, String> versions,
                                                                    String oldLang) {
        //1、对投产数据进行数据转化（处理投产时间，良率转化处理）
        selfService.handleCellPlanLine(query);
        //2、获取兆瓦系数map集合
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        List<CellPlanLineTotalDTO> cellPlanLineTotals = new ArrayList<>();
        //3、获取投产数据
        List<CellPlanLineDTO> datas = cellPlanLineService.query(query, versions);
        List<String> allLocalDateTimes = this.buildMonthAllLocalDateTimes(query.getMonth(), datas);
        //4、依据各维度分组
        Map<String, List<CellPlanLineDTO>> collect = this.calcCellPlanLineTotalByQueryHandle(datas);

        //依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        Collections.sort(keys);
        //准备分页参数
        Integer total = keys.size();
        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer endPos = Math.min(startPos + pageSize, total);
        //5、分页统计
        for (int i = startPos; i < endPos; i++) {
            String key = keys.get(i);
            CellConversionFactorDTO cellConversionFactorDTO = null;
            CellPlanLineTotalDTO cellPlanLineTotal = null;
            List<CellPlanLineDTO> dtos = collect.get(key);
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            TreeMap<String, String> dataStructures = allLocalDateTimes.stream()
                    .collect(TreeMap::new, (map, s) -> map.put(s, ""), Map::putAll);
            //对每组数据统计，每组最终对应一个CellPlanLineTotalDTO对象
            for (CellPlanLineDTO dto : dtos) {
                if (cellConversionFactorDTO == null) {
                    cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", dto.getIsOversea(), dto.getCellsType()));
                }
                if (Objects.isNull(cellPlanLineTotal)) {

                    cellPlanLineTotal = convert.toCellPlanLineTotalDTO(dto);
                    cellPlanLineTotal.setFromVersion(dto.getVersion());
                }
                BigDecimal value = dto.getQtyPc();
                String dateKey = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(dto.getStartTime());
                String dateValue = dataStructures.get(dateKey);
                if (StringUtils.isEmpty(dateValue)) {
                    dataStructures.put(dateKey, String.valueOf(value));
                } else {
                    dataStructures.put(dateKey, String.valueOf(new BigDecimal(dateValue).add(value)));
                }
//
//                int day = dto.getStartTime().getDayOfMonth();
//                Object val = ReflectUtil.invoke(cellPlanLineTotal, "getD" + day);
//                if (Objects.isNull(val)) {
//                    ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, value);
//                } else {
//                    BigDecimal addVal = ((BigDecimal) val).add(value);
//                    ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, addVal);
//                }
                if (Objects.nonNull(dto.getQtyPc())) {
                    qtyCount = qtyCount.add(value);
                }

            }
            dataStructures.forEach((k, v) -> {
                if (StringUtils.isNotEmpty(v)) {
                    dataStructures.put(k, v);
                }
            });
            cellPlanLineTotal.setDataStructures(dataStructures);
            //依据瓦片折算系数计算MV
            if (Objects.nonNull(cellConversionFactorDTO)) {
                if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                    BigDecimal mv = qtyCount.divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                    cellPlanLineTotal.setCellMv(mv);
                }

            } else {
                log.warn("没有《" + cellPlanLineTotal.getCellsType() + "》电池类型折算系数数据，不能进行对应的mv计算");
            }
            qtyCount = qtyCount.setScale(2, RoundingMode.HALF_UP);
            cellPlanLineTotal.setQtyThousandPc(qtyCount);
            cellPlanLineTotal.setMonth(query.getMonth());
            cellPlanLineTotal.setChangeStatusDesc(PlanChangeStatusEnum.UNCHANGE.getDesc());
            cellPlanLineTotals.add(cellPlanLineTotal);
//            setScale(cellPlanLineTotal);

        }
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //6、对返回结果进行翻译
        MyThreadLocal.get().setLang(oldLang);
        cellPlanLineTotals = convert.toCellPlanLineTotalNameByCnNameDTO(cellPlanLineTotals);
        return new PageImpl(cellPlanLineTotals, pageable, total);
    }

    private Page<CellPlanLineTotalDTO> calcCellPlanLineTotalTwoMonthByQuery(CellPlanLineQuery query, Pair<String, String> versions, String oldLang) {
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        handleCellPlanLine(query);//处理投产时间，良率转化处理
        List<CellPlanLineTotalDTO> cellPlanLineTotals = new ArrayList<>();
        List<CellPlanLineDTO> datas = cellPlanLineService.query(query, versions);
        //投产在本月的数据筛选
        List<CellPlanLineDTO> curMonthData = datas.stream().filter(item -> {
            return Objects.equals(item.getMonth(), query.getMonth());
        }).collect(toList());
        //投产在上一个月的数据筛选
        String preMonth = DateUtil.getMonth(DateUtil.getLocalDate(query.getMonth(), 1).plusMonths(-1));
        List<CellPlanLineDTO> preMonthData = datas.stream().filter(item -> {
            return Objects.equals(item.getMonth(), preMonth);
        }).collect(toList());

        //投产在本月的数据统计
        Map<String, List<CellPlanLineDTO>> collect = curMonthData.stream().collect(
                Collectors.groupingBy(item -> {
                    return StringTools.joinWith(",", item.getIsOversea(), item.getBasePlace(), item.getWorkshop(), item.getCellsType(), item.getHTrace(), item.getAesthetics(), item.getTransparentDoubleGlass(), item.getCellSource(), item.getRegionalCountry(), item.getItemCode());
                })
        );
        //投产在上一个月的数据统计
        Map<String, List<CellPlanLineDTO>> preCollect = preMonthData.stream().collect(
                Collectors.groupingBy(item -> {
                    return StringTools.joinWith(",", item.getIsOversea(), item.getBasePlace(), item.getWorkshop(), item.getCellsType(), item.getHTrace(), item.getAesthetics(), item.getTransparentDoubleGlass(), item.getCellSource(), item.getRegionalCountry(), item.getItemCode());
                })
        );
        /*
        collect和preCollect构建新的Map，新的Map结构：
        {
          ”原map的key“：{
                      "collect":{
                      }
                      "preCollect":{
                      }
          }
        }

         */
        Map<String, Map<String, List<CellPlanLineDTO>>> mapAll = new HashMap<>();
        for (Map.Entry<String, List<CellPlanLineDTO>> entry : collect.entrySet()) {
            String key = entry.getKey();
            Map<String, List<CellPlanLineDTO>> mapValue = new HashMap<>();
            mapValue.put("collect", collect.get(key));
            //判断preCollect的key是否存在
            if (preCollect.containsKey(key)) {
                mapValue.put("preCollect", preCollect.get(key));
                preCollect.remove(key);
            } else {
                mapValue.put("preCollect", new ArrayList<>());
            }
            mapAll.put(key, mapValue);
        }
        //考虑preCollect还有没有被合并的数据
        preCollect.forEach((key, list) ->{
            Map<String, List<CellPlanLineDTO>> mapValue = new HashMap<>();
            mapValue.put("collect", new ArrayList<>());
            mapValue.put("preCollect", list);
            mapAll.put(key, mapValue);
        });
        //依据key排序
        List<String> keys = new ArrayList<>(mapAll.keySet());
        Collections.sort(keys);
        Integer total = keys.size();
        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer endPos = Math.min(startPos + pageSize, total);
        int minDay = 0;//记录上个月的数据是从哪天开始的
        for (int i = startPos; i < endPos; i++) {
            String key = keys.get(i);
            CellConversionFactorDTO cellConversionFactorDTO = null;
            CellPlanLineTotalDTO cellPlanLineTotal = null;
            List<CellPlanLineDTO> dtos = mapAll.get(key).get("collect");
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            //本月统计
            for (CellPlanLineDTO dto : dtos) {
                if (cellConversionFactorDTO == null) {
                    cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", dto.getIsOversea(), dto.getCellsType()));
                }
                if (Objects.isNull(cellPlanLineTotal)) {
                    cellPlanLineTotal = convert.toCellPlanLineTotalDTO(dto);
                    cellPlanLineTotal.setFromVersion(dto.getVersion());
                }
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellPlanLineTotal, "getD" + day);
                BigDecimal value = dto.getQtyPc().setScale(2, RoundingMode.HALF_UP);
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, addVal);
                }
                if (Objects.nonNull(dto.getQtyPc())) {
                    qtyCount = qtyCount.add(value);
                }

            }
            dtos = mapAll.get(key).get("preCollect");
            //上个月统计
            for (CellPlanLineDTO dto : dtos) {
                if (cellConversionFactorDTO == null) {
                    cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", dto.getIsOversea(), dto.getCellsType()));
                }
                if (Objects.isNull(cellPlanLineTotal)) {
                    cellPlanLineTotal = convert.toCellPlanLineTotalDTO(dto);
                    cellPlanLineTotal.setFromVersion(dto.getVersion());
                }
                int day = dto.getStartTime().getDayOfMonth();
                if (minDay == 0) {
                    minDay = day;
                } else {
                    if (day < minDay) {
                        minDay = day;
                    }
                }
                Object val = ReflectUtil.invoke(cellPlanLineTotal, "getD" + (day + 31));
                BigDecimal value = dto.getQtyPc().setScale(2, RoundingMode.HALF_UP);
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellPlanLineTotal, "setD" + (day + 31), value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellPlanLineTotal, "setD" + (day + 31), addVal);
                }
                if (Objects.nonNull(dto.getQtyPc())) {
                    qtyCount = qtyCount.add(value);
                }

            }
            //依据瓦片折算系数计算MV
            if (Objects.nonNull(cellConversionFactorDTO)) {
                if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                    BigDecimal mv = qtyCount.divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                    cellPlanLineTotal.setCellMv(mv);
                }

            } else {
                log.warn("没有《" + cellPlanLineTotal.getCellsType() + "》电池类型折算系数数据，不能进行对应的mv计算");
            }
            qtyCount = qtyCount.setScale(2, RoundingMode.HALF_UP);
            cellPlanLineTotal.setQtyThousandPc(qtyCount);
            cellPlanLineTotals.add(cellPlanLineTotal);

        }


        //构建动态列头treeMap，
        TreeMap<String, String> treeMap = Maps.newTreeMap();
        //上个月的minDay到上个月底（最后一天不用月份不一样）的日期字符串 值为null
        //preMonth的最后一天的日值
        String preMonthLastDay = DateUtil.getMonthLastDay(preMonth);
        //获得preMonthLastDay的日的整数值
        int preMonthLastDayInt = Integer.parseInt(preMonthLastDay.substring(preMonthLastDay.length() - 2));
        //上个月的minDay日到上个月的preMonthLastDayInt日的日期字符串 值为null，存入treeMap中
        for (int i = minDay; minDay > 0 && i <= preMonthLastDayInt; i++) {
            String date = DateUtil.formatLocalDateToString(DateUtil.getLocalDate(preMonth, i), "yyyy-MM-dd");
            treeMap.put(date, "d" + (i + 31));
        }
        //本月的1日到本月的最后一天日期字符串 值为null，存入treeMap中
        String month = query.getMonth();
        for (int i = 1; i <= DateUtil.getDaysByYearMonth(Integer.parseInt(month.substring(0, 4)), Integer.parseInt(month.substring(4))); i++) {
            String date = DateUtil.formatLocalDateToString(DateUtil.getLocalDate(month, i), "yyyy-MM-dd");
            treeMap.put(date, "d" + (i));
        }
        if (CollectionUtils.isNotEmpty(cellPlanLineTotals)) {
            cellPlanLineTotals.get(0).setDataStructures(treeMap);
        }
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //翻译
        MyThreadLocal.get().setLang(oldLang);
        cellPlanLineTotals = convert.toCellPlanLineTotalNameByCnNameDTO(cellPlanLineTotals);
        return new PageImpl(cellPlanLineTotals, pageable, total);
    }

    /**
     * 统计汇总最新确认版本数据(从投产计划表读，读后计算)
     *
     * @param query
     */
    private Page<CellPlanLineTotalDTO> calcCellPlanLineTotalConfirmByQuery(CellPlanLineQuery query, Pair<String, String> versions) {
        Map<String, CellConversionFactorDTO> mapCellConversionFactorDTO = cellConversionFactorService.createMapByCelltypeAndIsOversea();
        // handleCellPlanLine(query);//处理投产时间，良率转化处理
        List<CellPlanLineTotalDTO> cellPlanLineTotals = new ArrayList<>();
        List<CellPlanLineDTO> datas = cellPlanLineService.queryConfirm(query, versions);
        Map<String, Map<String, List<CellPlanLineDTO>>> collect = datas.stream().collect(
                Collectors.groupingBy(CellPlanLineDTO::getCellsType, Collectors.groupingBy(item -> {
                            return StringTools.joinWith(",", item.getIsOversea(), item.getBasePlace(), item.getWorkshop(), item.getHTrace(), item.getSupplyMethod(), item.getHChangeFlag(), item.getAesthetics(), item.getTransparentDoubleGlass(), item.getCellSource(), item.getRegionalCountry(), item.getItemCode(), item.getMainGridSpace(),item.getSourceType());
                        })
                ));
        List<String> allLocalDateTimes = this.buildMonthAllLocalDateTimes(query.getMonth(), datas);
        Integer pageNumber = query.getPageNumber();
        Integer pageSize = query.getPageSize();
        Integer startPos = (pageNumber - 1) * pageSize;
        Integer index = 0;
        Integer endPos = startPos + pageSize;
        AtomicReference<Integer> total = new AtomicReference<>(0);
        collect.entrySet().stream().forEach(cellTypeEntry -> {
            total.set(total.get() + cellTypeEntry.getValue().size());
        });
        Set<Map.Entry<String, Map<String, List<CellPlanLineDTO>>>> entriesCellTypeSet = collect.entrySet();
        boolean isOver = false;
        for (Map.Entry<String, Map<String, List<CellPlanLineDTO>>> entryCellsType : entriesCellTypeSet) {
            //电池类型
            String cellsType = entryCellsType.getKey();
            Set<Map.Entry<String, List<CellPlanLineDTO>>> entries = entryCellsType.getValue().entrySet();
            if (index + entries.size() < startPos) {
                index = index + entries.size();
                continue;
            }
            //依据电池类型获取对象折算系数
            CellConversionFactorDTO cellConversionFactorDTO = null;
            for (Map.Entry<String, List<CellPlanLineDTO>> entry : entries) {
                if (index < startPos) {
                    index++;
                    continue;
                }
                CellPlanLineTotalDTO cellPlanLineTotal = null;
                List<CellPlanLineDTO> dtos = entry.getValue();
                //统计数据
                BigDecimal qtyCount = BigDecimal.ZERO;//万片数
                TreeMap<String, String> dataStructures = allLocalDateTimes.stream()
                        .collect(TreeMap::new, (map, s) -> map.put(s, ""), Map::putAll);
                for (CellPlanLineDTO dto : dtos) {
                    if (cellConversionFactorDTO == null) {
                        cellConversionFactorDTO = mapCellConversionFactorDTO.get(StringTools.joinWith(",", dto.getIsOversea(), dto.getCellsType()));
                    }
                    if (Objects.isNull(cellPlanLineTotal)) {
                        cellPlanLineTotal = convert.toCellPlanLineTotalDTO(dto);
                        cellPlanLineTotal.setFromVersion(dto.getVersion());
                    }
                    BigDecimal value = dto.getQtyPc();
                    String dateKey = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(dto.getStartTime());
                    String dateValue = dataStructures.get(dateKey);
                    if (StringUtils.isEmpty(dateValue)) {
                        dataStructures.put(dateKey, String.valueOf(value));
                    } else {
                        dataStructures.put(dateKey, String.valueOf(new BigDecimal(dateValue).add(value)));
                    }
//                    int day = dto.getStartTime().getDayOfMonth();
//                    Object val = ReflectUtil.invoke(cellPlanLineTotal, "getD" + day);
//                    BigDecimal value = dto.getQtyPc().setScale(2, RoundingMode.HALF_UP);
//                    if (Objects.isNull(val)) {
//                        ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, value);
//                    } else {
//                        BigDecimal addVal = ((BigDecimal) val).add(value);
//                        ReflectUtil.invoke(cellPlanLineTotal, "setD" + day, addVal);
//                    }
                    if (Objects.nonNull(dto.getQtyPc())) {
                        qtyCount = qtyCount.add(value);
                    }

                }
                dataStructures.forEach((k, v) -> {
                    if (StringUtils.isNotEmpty(v)) {
                        dataStructures.put(k, v);
                    }
                });
                cellPlanLineTotal.setDataStructures(dataStructures);
                //依据瓦片折算系数计算MV
                if (Objects.nonNull(cellConversionFactorDTO)) {
                    if (BigDecimal.ZERO.compareTo(cellConversionFactorDTO.getConversionFactor()) != 0) {
                        BigDecimal mv = qtyCount.divide(cellConversionFactorDTO.getConversionFactor(), 0, RoundingMode.HALF_UP);
                        cellPlanLineTotal.setCellMv(mv);
                    }

                } else {
                    log.warn("没有《" + cellPlanLineTotal.getCellsType() + "》电池类型折算系数数据，不能进行对应的mv计算");
                }
                qtyCount = qtyCount.setScale(2, RoundingMode.HALF_UP);
                cellPlanLineTotal.setMonth(query.getMonth());
                cellPlanLineTotal.setQtyThousandPc(qtyCount);
                cellPlanLineTotals.add(cellPlanLineTotal);
                index++;
                if (index >= endPos) {
                    isOver = true;
                    break;
                }
            }
            if (isOver) {
                break;
            }
        }
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        return new PageImpl(cellPlanLineTotals, pageable, total.get());
    }

    /**
     * 邮件发送
     *
     * @param query
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void email(CellPlanLineTotalQuery query) {
        if (StringUtils.isEmpty(query.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Joiner joiner = Joiner.on("-");
        // 加分布式锁
        String lockKey = joiner.join(RedisLockKey.BAPS_PREFIX, query.getIsOversea(), query.getMonth(), "cellplan", "email");
        RLock rLock = redissonClient.getLock(lockKey);
        if (rLock.tryLock()) {
            try {
                doEmail(query, oldLang);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }


    }

    /**
     * 邮件发送
     *
     * @param query
     * @param oldLang 语言
     */
    private void doEmail(CellPlanLineTotalQuery query, String oldLang) {
        query.setPageSize(GlobalConstant.max_page_size);
        query.setPageNumber(1);
        //1、获取本次版本所有数据
        Pair<String, String> versions = this.getSendedEmailOrLastVersion(query, false);
        List<CellPlanLineTotalDTO> cellPlanLineTotals = queryConfirmByPage(query, versions).getContent();

        Pair<String, String> sendedEmailVersions = this.getSendedEmailOrLastVersion(query, true);
        List<CellPlanLineTotalDTO> sendedEmailDTOS = queryConfirmByPage(query, sendedEmailVersions).getContent();

        if (CollectionUtils.isEmpty(cellPlanLineTotals) && CollectionUtils.isEmpty(sendedEmailDTOS)) {
            throw new BizException("暂无数据可发送");
        }
        //当前版本依据生产基地分组后的数据（以方便邮件发送）
        Map<String, List<CellPlanLineTotalDTO>> collect = cellPlanLineTotals.stream().collect(Collectors.groupingBy(CellPlanLineTotalDTO::getBasePlace));
        Map<String, List<CellPlanLineTotalDTO>> sendedEmailMap = sendedEmailDTOS.stream().collect(Collectors.groupingBy(CellPlanLineTotalDTO::getBasePlace));

        //2、准备邮件列表
        Map<EmailAddress, List<String>> mapEmailList = new HashMap<>();//key：用户，value：基地
        List<EmailAddress> allEmailList = new ArrayList<>(); //接受所有基地数据的用户
        Map<String,List<EmailAddress>> emailByBasePlace = new HashMap<>(); //接受所有基地数据的用户
        prepareEmailMap(query.getIsOversea(), mapEmailList, allEmailList,emailByBasePlace);
        //版本管理记录对象定义
        CellPlanLineVersion cellPlanLineVersionInland = null;
        CellPlanLineVersion cellPlanLineVersionOversea = null;
        List<String> fileList = Lists.newArrayList();

        for (Map.Entry<String, List<EmailAddress>> e : emailByBasePlace.entrySet()) {
            String key = e.getKey();
            List<EmailAddress> value = e.getValue();
            List<CellPlanLineTotalDTO> curVersionDatas = collect.get(key);
            List<CellPlanLineTotalDTO> lastVersionDatas = sendedEmailMap.getOrDefault(key,new ArrayList<>());
            if (CollectionUtils.isEmpty(curVersionDatas)) {
                continue;
            }
            String isOversea = curVersionDatas.get(0).getIsOversea();
            String month = curVersionDatas.get(0).getOldMonth();
            String version = curVersionDatas.get(0).getFromVersion();
            //3.4数据排序
            curVersionDatas = curVersionDatas.stream().sorted(Comparator.comparing(CellPlanLineTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(CellPlanLineTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(CellPlanLineTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder())).
                    thenComparing(
                            CellPlanLineTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder())
                    )
            ).collect(toList());
            lastVersionDatas = lastVersionDatas.stream().sorted(Comparator.comparing(CellPlanLineTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(CellPlanLineTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(CellPlanLineTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder())).
                    thenComparing(
                            CellPlanLineTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder())
                    )
            ).collect(toList());
            //3.5翻译转化
            MyThreadLocal.get().setLang(oldLang);
            curVersionDatas = convert.toCellPlanLineTotalNameByCnNameDTO(curVersionDatas);
            lastVersionDatas = convert.toCellPlanLineTotalNameByCnNameDTO(lastVersionDatas);
            MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);

            ExcelPara excelPara = CellPlanLineTotalExcelDTO.buildExcelPara(CollectionUtils.isEmpty(mailService.convertExcelDTOForPlanLine(cellPlanLineTotals)) ? null : mailService.convertExcelDTOForPlanLine(cellPlanLineTotals).get(0).getDataStructuresMap());
            List<DataColumn> columns = excelPara.getColumns();
            List<CellStyleModel> models = differentialJudgment(columns, curVersionDatas, lastVersionDatas);
            query.setModels(models);
            //3.6数据发送
            mailService.sendCellPlanlineTotal(query, curVersionDatas, lastVersionDatas, value, fileList);
            //版本管理记录开始
            if (cellPlanLineVersionInland == null) {
                if (isOversea.equals(OverseaConstant.INLAND)) {
                    cellPlanLineVersionInland = cellPlanLineVersionRepository.selectByVersion(isOversea, month, version);
                    cellPlanLineVersionInland.setIsSendEmail(1);
                    cellPlanLineVersionRepository.save(cellPlanLineVersionInland);
                }
            }
            if (cellPlanLineVersionOversea == null) {
                if (isOversea.equals(OverseaConstant.OVERSEA)) {
                    cellPlanLineVersionOversea = cellPlanLineVersionRepository.selectByVersion(isOversea, month, version);
                    cellPlanLineVersionOversea.setIsSendEmail(1);
                    cellPlanLineVersionRepository.save(cellPlanLineVersionOversea);
                }
            }
        }

/*         //3、循环mapEmailList发邮件（一个用户可能多个基地）
        for (Map.Entry<EmailAddress, List<String>> entry : mapEmailList.entrySet()) {
            //3.1获取邮件发送地址
            EmailAddress emailAddress = entry.getKey();
            //3.2获取基地列表
            List<String> basePlaceList = entry.getValue();
            //3.3准备邮件发送内容（组装多基地数据），用于生成excel文档附件
            List<CellPlanLineTotalDTO> curVersionDatas = new ArrayList<>();
            List<CellPlanLineTotalDTO> lastVersionDatas = new ArrayList<>();
            for (String basePlace : basePlaceList) {
                List<CellPlanLineTotalDTO> cellPlanLineTotals1 = collect.get(basePlace);
                if (CollectionUtils.isNotEmpty(cellPlanLineTotals1)) {
                    curVersionDatas.addAll(cellPlanLineTotals1);
                }
                List<CellPlanLineTotalDTO> cellPlanLineTotals2 = sendedEmailMap.get(basePlace);
                if (CollectionUtils.isNotEmpty(cellPlanLineTotals2)) {
                    lastVersionDatas.addAll(cellPlanLineTotals2);
                }
            }

            if (CollectionUtils.isEmpty(curVersionDatas)) {
                continue;
            }
            String isOversea = curVersionDatas.get(0).getIsOversea();
            String month = curVersionDatas.get(0).getOldMonth();
            String version = curVersionDatas.get(0).getFromVersion();
            List<EmailAddress> emailList = new ArrayList<>();
            emailList.add(emailAddress);
            //3.4数据排序
            curVersionDatas = curVersionDatas.stream().sorted(Comparator.comparing(CellPlanLineTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(CellPlanLineTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(CellPlanLineTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder())).
                    thenComparing(
                            CellPlanLineTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder())
                    )
            ).collect(Collectors.toList());
            lastVersionDatas = lastVersionDatas.stream().sorted(Comparator.comparing(CellPlanLineTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(CellPlanLineTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(CellPlanLineTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder())).
                    thenComparing(
                            CellPlanLineTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CellPlanLineTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder())
                    )
            ).collect(Collectors.toList());
            //3.5翻译转化
            MyThreadLocal.get().setLang(oldLang);
            curVersionDatas = convert.toCellPlanLineTotalNameByCnNameDTO(curVersionDatas);
            lastVersionDatas = convert.toCellPlanLineTotalNameByCnNameDTO(lastVersionDatas);
            MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);

            ExcelPara excelPara = CellPlanLineTotalExcelDTO.buildExcelPara(CollectionUtils.isEmpty(mailService.convertExcelDTOForPlanLine(cellPlanLineTotals)) ? null : mailService.convertExcelDTOForPlanLine(cellPlanLineTotals).get(0).getDataStructuresMap());
            List<DataColumn> columns = excelPara.getColumns();
            List<CellStyleModel> models = differentialJudgment(columns,curVersionDatas,lastVersionDatas);
            query.setModels(models);
            //3.6数据发送
            mailService.sendCellPlanlineTotal(query, curVersionDatas, lastVersionDatas, emailList, fileList);
            //版本管理记录开始
            if (cellPlanLineVersionInland == null) {
                if (isOversea.equals(OverseaConstant.INLAND)) {
                    cellPlanLineVersionInland = cellPlanLineVersionRepository.selectByVersion(isOversea, month, version);
                    cellPlanLineVersionInland.setIsSendEmail(1);
                    cellPlanLineVersionRepository.save(cellPlanLineVersionInland);
                }
            }
            if (cellPlanLineVersionOversea == null) {
                if (isOversea.equals(OverseaConstant.OVERSEA)) {
                    cellPlanLineVersionOversea = cellPlanLineVersionRepository.selectByVersion(isOversea, month, version);
                    cellPlanLineVersionOversea.setIsSendEmail(1);
                    cellPlanLineVersionRepository.save(cellPlanLineVersionOversea);
                }
            }
            //版本管理记录结束

        } */


        //4、发所有数据邮件给allEmailList
        //4.1排序
        cellPlanLineTotals = cellPlanLineTotals.stream().sorted(
                Comparator.comparing(CellPlanLineTotalDTO::getBasePlace, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellPlanLineTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellPlanLineTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellPlanLineTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder())).
                        thenComparing(
                                CellPlanLineTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder())
                        ).thenComparing(
                                CellPlanLineTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder())
                        ).thenComparing(
                                CellPlanLineTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder())
                        ).thenComparing(
                                CellPlanLineTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder())
                        ).thenComparing(
                                CellPlanLineTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder())
                        )
        ).collect(Collectors.toList());
        sendedEmailDTOS = sendedEmailDTOS.stream().sorted(
                Comparator.comparing(CellPlanLineTotalDTO::getBasePlace, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellPlanLineTotalDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellPlanLineTotalDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CellPlanLineTotalDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder())).
                        thenComparing(
                                CellPlanLineTotalDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder())
                        ).thenComparing(
                                CellPlanLineTotalDTO::getRegionalCountry, Comparator.nullsLast(Comparator.naturalOrder())
                        ).thenComparing(
                                CellPlanLineTotalDTO::getAesthetics, Comparator.nullsLast(Comparator.naturalOrder())
                        ).thenComparing(
                                CellPlanLineTotalDTO::getItemCode, Comparator.nullsLast(Comparator.naturalOrder())
                        ).thenComparing(
                                CellPlanLineTotalDTO::getTransparentDoubleGlass, Comparator.nullsLast(Comparator.naturalOrder())
                        )
        ).collect(Collectors.toList());
        //4.2翻译转化
        MyThreadLocal.get().setLang(oldLang);
        cellPlanLineTotals = convert.toCellPlanLineTotalNameByCnNameDTO(cellPlanLineTotals);
        sendedEmailDTOS = convert.toCellPlanLineTotalNameByCnNameDTO(sendedEmailDTOS);
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);

        ExcelPara excelParaTotal = CellPlanLineTotalExcelDTO.buildExcelPara(CollectionUtils.isEmpty(mailService.convertExcelDTOForPlanLine(cellPlanLineTotals)) ? null : mailService.convertExcelDTOForPlanLine(cellPlanLineTotals).get(0).getDataStructuresMap());
        List<DataColumn> columnsTotal = excelParaTotal.getColumns();
        List<CellStyleModel> modelsTotal = differentialJudgment(columnsTotal,cellPlanLineTotals,sendedEmailDTOS);

        //4.3邮件发送
        mailService.sendCellPlanlineTotalToAll(query, cellPlanLineTotals, sendedEmailDTOS, emailByBasePlace.get("-1"), fileList,modelsTotal);
        //4.4 刪除文件
        mailService.deleteEmailFile(fileList);
    }


    /**
     * 准备邮件列表
     *
     * @param mapEmailList 以基地Name构建为key，构建基地的邮件列表
     * @param allEmailList 发送给所有基地的邮件列表
     */
    private void prepareEmailMap(String isOversea, Map<EmailAddress, List<String>> mapEmailList, List<EmailAddress> allEmailList, Map<String, List<EmailAddress>> emailMap) {
        //准备邮箱数据
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BAPS_SCHEDULE_USER_EMAIL);
        //是否发送投产计划为是的lov
        List<LovLineDTO> lovLineDTOS = new ArrayList<>();
        Set<Long> set = new HashSet<>();
        for (LovLineDTO lovLineDTO : allByHeaderCode.values()) {
            if (!set.contains(lovLineDTO.getLovLineId())) {
                set.add(lovLineDTO.getLovLineId());
                if (LovHeaderCodeConstant.BAPS_CELLPLANLINEINSTOCK_YES.equals(lovLineDTO.getAttribute2())) {
                    lovLineDTOS.add(lovLineDTO);
                }
            }
        }


        //以基地Name构建为key，构建基地的邮件列表
        //发送给所有基地的邮件列表
        for (LovLineDTO lov : lovLineDTOS) {
            List<EmailAddress> emails = new ArrayList<>();
            String basePlaceIds = lov.getAttribute3();
            EmailAddress emailAddress = new EmailAddress(lov.getLovName(), lov.getLovValue());
            emails.add(emailAddress);
            if (StringUtils.isEmpty(basePlaceIds)) {
                //判断是否发送国内所有
                if (OverseaConstant.INLAND.equals(isOversea)) {
                    if (LovHeaderCodeConstant.BAPS_CELLPLANLINEINSTOCK_YES.equals(lov.getAttribute4())) {
                        if(emailMap.isEmpty() || Objects.isNull(emailMap.get("-1"))){
                            emailMap.put("-1", emails);
                        } else{
                            emailMap.get("-1").add(emailAddress);
                        }
                        //allEmailList.add(emailAddress);
                    }
                } else {
                    if (LovHeaderCodeConstant.BAPS_CELLPLANLINEINSTOCK_YES.equals(lov.getAttribute5())) {
                        //allEmailList.add(emailAddress);
                        if(emailMap.isEmpty() || Objects.isNull(emailMap.get("-1"))){
                            emailMap.put("-1", emails);
                        } else{
                            emailMap.get("-1").add(emailAddress);
                        }
                    }
                }
                continue;
            }
            //basePlaceId依据逗号拆分
            String[] basePlaceIdsArray = basePlaceIds.split(",");
            /* mapEmailList.put(emailAddress, new ArrayList<>());
            for (String basePlaceId : basePlaceIdsArray) {
                String basePlaceName = LovUtils.get(Long.parseLong(basePlaceId)).getLovName();
                mapEmailList.get(emailAddress).add(basePlaceName);

            } */
            for (String basePlaceId : basePlaceIdsArray) {
                String basePlaceName = LovUtils.get(Long.parseLong(basePlaceId)).getLovName();
                if (emailMap.isEmpty() || Objects.isNull(emailMap.get(basePlaceName))) {
                    emailMap.put(basePlaceName, emails);
                } else {
                    emailMap.get(basePlaceName).add(emailAddress);
                }
            }

        }
    }

    /**
     * 考虑数据全量操作，清楚查询条件
     *
     * @param query
     */
    private void setCellPlanLineQueryNull(CellPlanLineQuery query) {
        if (query != null) {
            query.setCellsType(null);
            query.setCellsTypeId(null);
            query.setBasePlace(null);
            query.setBasePlaceId(null);
            query.setWorkshop(null);
            query.setWorkshopId(null);
        }
    }

    /**
     * 依据投产提前期-良率处理投产数据
     *
     * @param query
     */
    @CacheEvict(cacheNames = "CellPlanLineService_queryByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void handleCellPlanLine(CellPlanLineQuery query) {
        query = BeanUtil.copyProperties(query, CellPlanLineQuery.class);
        setCellPlanLineQueryNull(query);
        //判断是否处理过
        Pair<Boolean, Boolean> booleanBooleanPair = cellPlanLineService.checkHandle(query);
        Boolean left = booleanBooleanPair.getLeft();
        Boolean right = booleanBooleanPair.getRight();
        if (!(Boolean.FALSE.equals(left) || Boolean.FALSE.equals(right))) {
            //已经处理过了，不用再处理
            return;
        }
        // 加分布式锁
        Joiner joiner = Joiner.on("_");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, "cellplan", "make");
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //进行数据转化处理
                doHandleCellPlanLine(query, left, right);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }


    }

    /**
     * 对投产数据进行初始数据转化处理
     *
     * @param query
     * @param left
     * @param right
     */
    private void doHandleCellPlanLine(CellPlanLineQuery query, Boolean left, Boolean right) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_CREATE_CELL_PLAN);
            //-------------------------数据准备开始------------------------
            //1、读取投产数据
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月排产的数据", query.getMonth()));
            List<CellPlanLineDTO> cellPlanLines = cellPlanLineService.queryManyMonth(query);
            //实际数覆盖数据与排产数据合并
            mergeData(cellPlanLines);
            //1.1获取cellPlanLine中isOversea为国内的数据，去重的oldMonth
            List<String> oldMonthsByInland = cellPlanLines.stream().filter(cellPlanLineDTO -> OverseaConstant.INLAND.equals(cellPlanLineDTO.getIsOversea())).map(CellPlanLineDTO::getOldMonth).distinct().collect(Collectors.toList());
            //1.2获取cellPlanLine中isOversea为海外的数据，去重的oldMonth
            List<String> oldMonthsByOversea = cellPlanLines.stream().filter(cellPlanLineDTO -> OverseaConstant.OVERSEA.equals(cellPlanLineDTO.getIsOversea())).map(CellPlanLineDTO::getOldMonth).distinct().collect(Collectors.toList());
            //1.3获取cellPlanLine中isOversea为国内的version
            List<String> versionsByInland = cellPlanLines.stream().filter(cellPlanLineDTO -> OverseaConstant.INLAND.equals(cellPlanLineDTO.getIsOversea())).map(CellPlanLineDTO::getVersion).distinct().collect(Collectors.toList());
            //1.4获取cellPlanLine中isOversea为海外的version
            List<String> versionsByOversea = cellPlanLines.stream().filter(cellPlanLineDTO -> OverseaConstant.OVERSEA.equals(cellPlanLineDTO.getIsOversea())).map(CellPlanLineDTO::getVersion).distinct().collect(Collectors.toList());
            //2、综合投产提前期和良率进行数据处理
            //2.1获取投产提前期数据
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段2：获取投产提前期数据"));
            CellProductionLeadTimeQuery cellProductionLeadTimeQuery = new CellProductionLeadTimeQuery();
            cellProductionLeadTimeQuery.setPageNumber(1);
            cellProductionLeadTimeQuery.setPageSize(GlobalConstant.max_page_size);
            Page<CellProductionLeadTimeDTO> cellProductionLeadTimeDTOS = cellProductionLeadTimeService.queryByPage(cellProductionLeadTimeQuery);
            List<CellProductionLeadTimeDTO> content = cellProductionLeadTimeDTOS.getContent();
            //2.2投产提前期map
            Map<String, CellProductionLeadTimeDTO> cellProductionLeadTimeDTOMap = content.stream().collect(
                    Collectors.toMap(
                            dto -> StringTools.joinWith(",", dto.getBasePlace(), dto.getWorkshop(), dto.getWorkunit()),
                            dto -> dto)
            );
            //考虑有些排产数据没有单元的情况（2024-06-15 新增）
            Map<String, CellProductionLeadTimeDTO> cellProductionLeadTimeDTOMapByWorkShop = content.stream().collect(
                    Collectors.toMap(
                            dto -> StringTools.joinWith(",", dto.getBasePlace(), dto.getWorkshop()),
                            dto -> dto, (k1, k2) -> {
                                if (StringUtils.isBlank(k2.getWorkunit())) {
                                    return k2;
                                } else {
                                    return k1;
                                }
                            })
            );
            //2.3准备良率比例数据（读取两年的良率）
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段3：获取良率比例数据"));
            CellFineQuery cellFineQuery = new CellFineQuery();
            cellFineQuery.setPageNumber(1);
            cellFineQuery.setPageSize(GlobalConstant.max_page_size);
            Integer year = DateUtil.getYear(query.getMonth());
            Integer lastYear = year - 1;
            cellFineQuery.setYear(year);
            Page<CellFineDTO> page = cellFineService.queryByPage(cellFineQuery);
            List<CellFineDTO> allCellFines = new ArrayList<>();
            if (page != null) {
                if (CollectionUtils.isNotEmpty(page.getContent())) {
                    allCellFines.addAll(page.getContent());
                }
            }
            cellFineQuery.setYear(lastYear);
            page = cellFineService.queryByPage(cellFineQuery);
            if (page != null) {
                if (CollectionUtils.isNotEmpty(page.getContent())) {
                    allCellFines.addAll(page.getContent());
                }
            }
            //2.4构建良率map
            LinkedHashMap<String, CellFineDTO> cellFineDTOMap = allCellFines.stream().collect(Collectors.toMap(
                    fine -> {
                        return StringTools.joinWith(",", fine.getYear() + "", fine.getBasePlace(), fine.getWorkshop(), fine.getCellsType());
                    },
                    Function.identity(),
                    (existing, replacement) -> replacement,
                    LinkedHashMap::new
            ));
            //-------------------------------数据准备结束------------------------
            //3.数据处理
            //用于最终数据保存
            List<CellPlanLineDTO> datas = new ArrayList<>();
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段4：处理数据，进行计算"));
            //收集没有投产提前期的数据
            Set<String> leadTimeSet = new LinkedHashSet<>();
            //收集没有良率的数据
            Set<String> cellFineSet = new LinkedHashSet<>();
            //数据处理
            transformData(cellPlanLines, cellProductionLeadTimeDTOMap, cellProductionLeadTimeDTOMapByWorkShop, cellFineDTOMap, datas, leadTimeSet, cellFineSet);
            //收集提前期日志
            if (CollectionUtils.isNotEmpty(leadTimeSet)) {
                for (String item : leadTimeSet
                ) {
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, item);
                }
            }
            //收集良率日志
            if (CollectionUtils.isNotEmpty(cellFineSet)) {
                for (String item : cellFineSet
                ) {
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN, item);
                }
            }
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段5：进行数据存储"));
            //4.数据存储
            cellPlanLineRepository.saveAll(cellPlanLineDEConvert.toEntity(datas));
            //5.版本记录
            //版本管理记录开始
            saveVersion(left, right, oldMonthsByInland, oldMonthsByOversea, versionsByInland, versionsByOversea);
            //版本管理记录结束
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");
        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    private void mergeData(List<CellPlanLineDTO> cellPlanLines) {
        if (CollectionUtils.isEmpty(cellPlanLines)) {
            return;
        }
        List<BatchNoToVersion> batchNoToVersion = cellPlanLines.stream().filter(
                item -> StringUtils.isNotBlank(item.getBatchNo())
        ).map(item -> {
            return BatchNoToVersion.builder().batchNo(item.getBatchNo()).version(item.getVersion()).build();
        }).distinct().collect(toList());

        if (CollectionUtils.isEmpty(batchNoToVersion)) {
            return;
        }
        Map<String, String> batchNoToVersionMap = batchNoToVersion.stream().collect(Collectors.toMap(BatchNoToVersion::getBatchNo, BatchNoToVersion::getVersion, (k1, k2) -> k1));
        //读取指定批次号的数据
        List<String> banchNoList = batchNoToVersionMap.keySet().stream().collect(toList());
        List<ActualInstockPlan> actualInstockPlan = getActualInstockPlan(banchNoList);
        List<CellPlanLineDTO> cellPlanlinesDtoFromActual = actualInstockPlan.stream().map(actualInstockPlan1 -> {
            CellPlanLineDTO cellPlanLineDTO = new CellPlanLineDTO();
            BeanUtils.copyProperties(actualInstockPlan1, cellPlanLineDTO, "id");
            cellPlanLineDTO.setDataType(1);
            cellPlanLineDTO.setId(null);
            String month = DateUtil.getMonth(actualInstockPlan1.getStartTime());
            cellPlanLineDTO.setEndTime(actualInstockPlan1.getStartTime());
            cellPlanLineDTO.setMonth(month);
            cellPlanLineDTO.setOldMonth(month);
            BigDecimal qtyPc = Optional.ofNullable(actualInstockPlan1.getQtyPc()).orElse(BigDecimal.ZERO);
            cellPlanLineDTO.setQtyPc(qtyPc);
            cellPlanLineDTO.setOldQtyPc(qtyPc);
            cellPlanLineDTO.setOldEndTime(null);
            cellPlanLineDTO.setOldStartTime(null);
            cellPlanLineDTO.setVersion(batchNoToVersionMap.get(actualInstockPlan1.getBatchNo()));
            return cellPlanLineDTO;
        }).collect(toList());
        cellPlanLines.addAll(cellPlanlinesDtoFromActual);
    }

    /**
     * 获取指定批次号的实际入库覆盖数据
     *
     * @param batchNos
     */
    private List<ActualInstockPlan> getActualInstockPlan(List<String> batchNos) {
        QActualInstockPlan qActualInstockPlan = QActualInstockPlan.actualInstockPlan;
        List<ActualInstockPlan> fetch = jpaQueryFactory.select(qActualInstockPlan).from(qActualInstockPlan).where(qActualInstockPlan.batchNo.in(batchNos)).fetch();
        return Optional.ofNullable(fetch).orElse(Lists.newArrayList());
    }

    /**
     * 版本管理记录保存
     *
     * @param left
     * @param right
     * @param oldMonthsByInland
     * @param oldMonthsByOversea
     * @param versionsByInland
     * @param versionsByOversea
     */
    private void saveVersion(Boolean left, Boolean right, List<String> oldMonthsByInland, List<String> oldMonthsByOversea, List<String> versionsByInland, List<String> versionsByOversea) {
        //国内版本记录开始
        if (Boolean.FALSE.equals(left) && CollectionUtils.isNotEmpty(oldMonthsByInland)) {
            for (String month : oldMonthsByInland) {
                CellPlanLineVersionSaveDTO cellPlanLineVersionDTOInland = new CellPlanLineVersionSaveDTO();
                if (CollectionUtils.isNotEmpty(versionsByInland)) {
                    cellPlanLineVersionDTOInland.setVersion(versionsByInland.get(0));
                }
                cellPlanLineVersionDTOInland.setMonth(month);
                cellPlanLineVersionDTOInland.setIsOversea(OverseaConstant.INLAND);
                cellPlanLineVersionDTOInland.setIsTransform(1);
                cellPlanLineVersionDTOInland.setIsWaferGrade(0);
                cellPlanLineVersionDTOInland.setIsSiMfrs(0);
                cellPlanLineVersionDTOInland.setIsProcessCategory(0);
                cellPlanLineVersionDTOInland.setIsConfirmPlan(0);
                cellPlanLineVersionDTOInland.setIsSendEmail(0);
                cellPlanLineVersionDTOInland.setIsCreateInstockPlan(0);
                cellPlanLineVersionService.save(cellPlanLineVersionDTOInland);
            }
        }
        //国内版本记录结束
        //海外版本记录开始
        if (Boolean.FALSE.equals(right) && CollectionUtils.isNotEmpty(oldMonthsByOversea)) {
            for (String month : oldMonthsByOversea) {
                CellPlanLineVersionSaveDTO cellPlanLineVersionDTOOutland = new CellPlanLineVersionSaveDTO();
                if (CollectionUtils.isNotEmpty(versionsByOversea)) {
                    cellPlanLineVersionDTOOutland.setVersion(versionsByOversea.get(0));
                }
                cellPlanLineVersionDTOOutland.setMonth(month);
                cellPlanLineVersionDTOOutland.setIsOversea(OverseaConstant.OVERSEA);
                cellPlanLineVersionDTOOutland.setIsTransform(1);
                cellPlanLineVersionDTOOutland.setIsWaferGrade(0);
                cellPlanLineVersionDTOOutland.setIsSiMfrs(0);
                cellPlanLineVersionDTOOutland.setIsProcessCategory(0);
                cellPlanLineVersionDTOOutland.setIsConfirmPlan(0);
                cellPlanLineVersionDTOOutland.setIsSendEmail(0);
                cellPlanLineVersionDTOOutland.setIsCreateInstockPlan(0);
                cellPlanLineVersionService.save(cellPlanLineVersionDTOOutland);

            }
        }
        //海外版本记录结束
    }

    /**
     * 投产数据处理
     *
     * @param cellPlanLines                要处理的数据
     * @param cellProductionLeadTimeDTOMap 投产提前期map
     * @param cellFineDTOMap               良率map
     * @param datas                        待存储数据
     * @param leadTimeSet                  没有提前琪数据集合
     * @param cellFineSet                  没有良率数据集合
     */
    private void transformData(List<CellPlanLineDTO> cellPlanLines, Map<String, CellProductionLeadTimeDTO> cellProductionLeadTimeDTOMap, Map<String, CellProductionLeadTimeDTO> cellProductionLeadTimeDTOMapByWorkShop, LinkedHashMap<String, CellFineDTO> cellFineDTOMap, List<CellPlanLineDTO> datas, Set<String> leadTimeSet, Set<String> cellFineSet) {
        // 目标良率获取方式。用生产车间、电池类型、月份匹配《目标良率表》中的目标良率。数量=数量/良率
        // 投产计划开始时间、结束时间计算。开始时间=原开始时间-投产提前期；结束时间=原结束时间-产提前期。
        for (CellPlanLineDTO cellPlanLine : cellPlanLines) {
            if (Objects.nonNull(cellPlanLine.getOldEndTime())) {
                //已经处理过就不处理
                continue;
            }
            //获取对应的提前期
            //投产提前期获取方式。用生产单元、生产车间、生产基地匹配《投产提前期表》中的投产提前期，投产提前期中有生产单元则用生产单元匹配，没有生产单元则用生产车间匹配。
            String key = StringTools.joinWith(",", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getWorkunit());
            CellProductionLeadTimeDTO cellProductionLeadTimeDTO = cellProductionLeadTimeDTOMap.get(key);
            LocalDateTime startTime = cellPlanLine.getStartTime();
            LocalDateTime endTime = cellPlanLine.getEndTime();
            cellPlanLine.setOldStartTime(startTime);//入库开始时间保存
            cellPlanLine.setOldEndTime(endTime);//入库结束时间保存
            cellPlanLine.setOldQtyPc(cellPlanLine.getQtyPc());
            cellPlanLine.setOldMonth(cellPlanLine.getMonth()); //入库月份保存
            if (cellProductionLeadTimeDTO == null) {
                key = StringTools.joinWith(",", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), null);
                cellProductionLeadTimeDTO = cellProductionLeadTimeDTOMap.get(key);
            }
            //考虑维护到了单元的提前期数据，但是从实际入库转到投产表的数据是没有单元的情况
            if (StringUtils.isBlank(cellPlanLine.getWorkunit())) {
                String k = StringTools.joinWith(",", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop());
                cellProductionLeadTimeDTO = cellProductionLeadTimeDTOMapByWorkShop.get(k);
            }
            if (cellProductionLeadTimeDTO != null) {
                //提前期时间处理
                BigDecimal leadTime = cellProductionLeadTimeDTO.getLeadTime();
                //天转秒
                leadTime = leadTime.multiply(new BigDecimal(24 * 60 * 60)).setScale(0, RoundingMode.HALF_UP);
                Integer sencods = leadTime.intValue();
                int monthValue = startTime.getMonthValue();
                startTime = startTime.plusSeconds(sencods * (-1));
                endTime = endTime.plusSeconds(sencods * (-1));
                cellPlanLine.setStartTime(startTime);
                cellPlanLine.setEndTime(endTime);
                //考虑可能月份变化
                int newMonthValue = startTime.getMonthValue();
                if (newMonthValue != monthValue) {
                    String newMonth = DateUtil.getMonth(startTime);
                    cellPlanLine.setMonth(newMonth);
                }
            } else {
                leadTimeSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), "没有对应的提前期数据!"));
            }
            //获取对应良率信息
            String fineKey = StringTools.joinWith(",", startTime.getYear() + "", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType());
            CellFineDTO cellFineDTO = cellFineDTOMap.get(fineKey);
            int month = startTime.getMonthValue();
            if (cellFineDTO != null) {
                String fineValueResult = ReflectUtil.invoke(cellFineDTO, "getM" + month);

                if (StringUtils.isNotEmpty(fineValueResult)) {

                    BigDecimal fineValue = MapStrutUtil.removePercentage(fineValueResult, 4);
                    //转投产，是除以良率
                    if (Objects.nonNull(fineValue)) {
                        if (fineValue.compareTo(BigDecimal.ZERO) != 0) {
                            cellPlanLine.setQtyPc(cellPlanLine.getQtyPc().divide(fineValue, 6, RoundingMode.HALF_UP));
                        }
                    }

                } else {
                    cellFineSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType(), "没有对应的良率数据,请找AOP管理维护自制良率数据"));
                    // logService.addLog(task,ScheduleTaskStatusEnum.WARN,String.format("%s没有对应的良率数据",cellPlanLine));
                }
            } else {
                cellFineSet.add(StringTools.joinWith("-", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType(), "没有对应的良率数据,请找AOP管理维护自制良率数据"));
            }
            //考虑片源级投产良率（2024-05-22）
            if (Objects.nonNull(cellPlanLine.getWaferYieldRatio()) && cellPlanLine.getWaferYieldRatio().compareTo(BigDecimal.ZERO) != 0) {
                cellPlanLine.setQtyPc(cellPlanLine.getQtyPc().divide(cellPlanLine.getWaferYieldRatio(), 6, RoundingMode.HALF_UP));

            }
            datas.add(cellPlanLine);
        }
    }

    /**
     * 匹配5A料号
     *
     * @param query
     */
//    @Override
//    public void mate5A(CellPlanLineTotalQuery query) {
//        //获取要同步给bom的数据
//        List<CellPlanLineDTO> cellPlanLines = cellPlanLineService.queryNo5AData(convert.toCellPlanLineQuery(query));
//        cellPlanLines.stream().forEach(item -> {
//            item.setFinalVersion(item.getVersion());
//        });
//        //同步数据给bom
//        cellPlanLineService.summaryGroupByCellPlanLine(cellPlanLines, query.getMonth());
//    }

    /**
     * 投产确认
     *
     * @param query
     */
    @Override
    @CacheEvict(cacheNames = "CellPlanLineService_queryByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)

    public void confirm(CellPlanLineTotalQuery query) {
        if (StringUtils.isEmpty(query.getIsOversea())) {
            throw new BizException("请选择月份");
        }
        if (StringUtils.isEmpty(query.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, query.getIsOversea(), query.getMonth(), "cellplan", "confirm");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                doConfirm(query);
                //发送邮件通知
                sendEmail(query);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }
    }

    public void matchItemCallBack(CellPlanLineTotalQuery query) {
        try {
            //数据跑入料号匹配后,触发一下批量匹配
            MaterielMatchHeaderQuery materielMatchHeaderQuery = new MaterielMatchHeaderQuery();
            materielMatchHeaderQuery.setMonth(query.getMonth());
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 这里等待7分钟，避免出现计划确认的时候调用BOM接口落库计划还未落库，此时调用料号匹配还无待匹配的数据
                try {
                    Thread.sleep(1000 * 60 * 7);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                ResponseEntity<Results<Object>> response = bbomFeign.allMatchItem(materielMatchHeaderQuery);
                if(!response.getBody().isSuccess()){
                    log.error("批量料号匹配失败:{}", response.toString());
                }
            }, threadPoolExecutor);
        } catch (Exception ex) {
            log.info("投产计划 matchItemCallBack {}", JSON.toJSONString(ex));
            throw ex;
        }
    }

    /**
     * 发送邮件通知
     *
     * @param query
     */
    private void sendEmail(CellPlanLineTotalQuery query) {
        LovLineDTO lovLine = LovUtils.get(LovHeaderCodeConstant.BBOM_EMAIL_REMINDER, "match_header_inform");
        String emailList = lovLine.getAttribute1();
        Map<String, String> content = Maps.newHashMap();
        content.put("month", query.getMonth());
        List<String> toList = Arrays.asList(emailList.split(","));
        mailService.send(toList, EmailConstant.CELL_INSTOCK_EMAIL_CONFIRM, "料号匹配通知", content, null);
    }

    private void doConfirm(CellPlanLineTotalQuery query) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_CELL_PLAN_CONFIRM_PLAN);
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月投产数据", query.getMonth()));
            //1、获取投产数据
            List<CellPlanLineDTO> cellPlanLineDTOS = cellPlanLineService.query(convert.toCellPlanLineQuery(query));
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段2：生成版本号"));
            //2、生成版本号
            Pair<String, String> lastVersion = cellPlanLineService.getLastFinalVersion(convert.toCellPlanLineQuery(query));
            String inlandVersion = null;
            String overseaVersion = null;
            String version = DateUtils.getDate("yyyy-MM-dd");
            String month = query.getMonth();
            if (StringUtils.isEmpty(lastVersion.getLeft())) {
                //本月还没汇总
                version = version + "-INLAND-" + month + "-(v01)";
            } else {
                String oldVersion = lastVersion.getLeft();
                int startIndex = oldVersion.indexOf("v") + 1;
                int endIndex = oldVersion.indexOf(")");
                Integer version_val = Integer.parseInt(oldVersion.substring(startIndex, endIndex));
                version_val = version_val +1;
                if (version_val < 10) {
                    version += "-INLAND-" + month + "-(v0" + version_val + ")";
                } else {
                    version += "-INLAND-" + month + "-(v" + version_val + ")";
                }


            }
            inlandVersion = version;
            version = DateUtils.getDate("yyyy-MM-dd");
            if (StringUtils.isEmpty(lastVersion.getRight())) {
                //本月还没汇总
                version = version + "-OVERSEA-" + month + "-(v01)";
            } else {
                String oldVersion = lastVersion.getRight();
                int startIndex = oldVersion.indexOf("v") + 1;
                int endIndex = oldVersion.indexOf(")");
                Integer version_val = Integer.parseInt(oldVersion.substring(startIndex, endIndex));
                version_val = version_val + 1;
                if (version_val < 10) {
                    version += "-OVERSEA-" + month + "-(v0" + version_val + ")";
                } else {
                    version += "-OVERSEA-" + month + "-(v" + version_val + ")";
                }

            }
            overseaVersion = version;
            CellPlanLineVersion cellPlanLineVersionInland = null;
            CellPlanLineVersion cellPlanLineVersionOversea = null;
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段3：保存版本号"));
            //3、保存版本号
            for (CellPlanLineDTO cellPlanLineDTO : cellPlanLineDTOS) {
                //版本管理记录开始
                if (cellPlanLineVersionInland == null) {
                    if (cellPlanLineDTO.getIsOversea().equals(OverseaConstant.INLAND)) {
                        cellPlanLineVersionInland = cellPlanLineVersionRepository.selectByVersion(cellPlanLineDTO.getIsOversea(), cellPlanLineDTO.getOldMonth(), cellPlanLineDTO.getVersion());
                        if(ObjectUtils.isNotEmpty(cellPlanLineVersionInland)){
                            cellPlanLineVersionInland.setIsConfirmPlan(1);
                            cellPlanLineVersionInland.setFinalVersion(inlandVersion);
                            cellPlanLineVersionRepository.save(cellPlanLineVersionInland);
                        }
                    }
                }
                if (cellPlanLineVersionOversea == null) {
                    if (cellPlanLineDTO.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                        cellPlanLineVersionOversea = cellPlanLineVersionRepository.selectByVersion(cellPlanLineDTO.getIsOversea(), cellPlanLineDTO.getOldMonth(), cellPlanLineDTO.getVersion());
                        cellPlanLineVersionOversea.setIsConfirmPlan(1);
                        cellPlanLineVersionOversea.setFinalVersion(overseaVersion);
                        cellPlanLineVersionRepository.save(cellPlanLineVersionOversea);
                    }
                }
                //版本管理记录结束
                cellPlanLineDTO.setConfirmPlan(1);
                if (cellPlanLineDTO.getIsOversea().equals(OverseaConstant.INLAND)) {
                    cellPlanLineDTO.setFinalVersion(inlandVersion);
                } else {
                    cellPlanLineDTO.setFinalVersion(overseaVersion);
                }
            }
            List<CellPlanLine> cellPlanLines = cellPlanLineDEConvert.toEntity(cellPlanLineDTOS);
            // 1.根据电池车间获取对应的产地，再通过产地到投产计划对应的法碳证书的产地信息中获取ECS CODE,2.传递到电池BOM-电池料号匹配界面;
            matchEcsCodeByWorkshop(cellPlanLineDTOS);
            //数据存储
            cellPlanLineRepository.saveAll(cellPlanLines);
            //4、同步数据给bom（bom会进行料号匹配）
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段4：数据同步给bbom进行料号匹配"));
            //修改投产数量：投产数量=原投产数量*（1+计划浮动系数。）
            updatePlanQty(cellPlanLineDTOS);

            cellPlanLineDTOS.forEach(item->{
                item.setPlanType(PlanConstant.PLAN_TYPE);
            });
            setCellSource(cellPlanLineDTOS);
            cellPlanLineService.summaryGroupByCellPlanLine(cellPlanLineDTOS, query.getMonth());
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");


        } catch (Exception exception) {
            exception.printStackTrace();
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    /**
     * 片源种类的拼接逻辑：片源种类（投产计划）+"-"+法碳配比（投产计划）+"-"+硅片ECS CODE(硅片切换维护)+"-"+电池ECS CODE(投产计划电池车间）
     * @param cellPlanLineDTOS
     */
    public void setCellSource(List<CellPlanLineDTO> cellPlanLineDTOS) {
        ResponseEntity<Results<List<BatterySiliconWaferDTO>>> siliconWaferResponse = bbomFeign.queryBatterySiliconWaferList();
        List<BatterySiliconWaferDTO> batterySiliconWaferDTOS = new ArrayList<>();
        if (siliconWaferResponse != null || siliconWaferResponse.getBody() != null || siliconWaferResponse.getBody().getData() != null) {
            batterySiliconWaferDTOS = siliconWaferResponse.getBody().getData().stream().filter(k -> "Y".equals(k.getLowCarbonFlag())).collect(toList());
        }

        for (CellPlanLineDTO cellPlanLineDTO : cellPlanLineDTOS) {
            //片源种类（投产计划）
            String cellSourceCellPlanLine = cellPlanLineDTO.getCellSource();
            if (!"DT".equals(cellSourceCellPlanLine)){
                continue;
            }
            String ratioCode = cellPlanLineDTO.getRatioCode();
            //用电池车间、电池单元、排产日期（StartTime），获取相同车间、相同单元、是否低碳（后台字段low_carbon_flag）为是、
            String waferEcsCode ="";
            if (CollectionUtils.isNotEmpty(batterySiliconWaferDTOS)){
                for (BatterySiliconWaferDTO batterySiliconWaferDTO : batterySiliconWaferDTOS) {
                    // 判断 排产日期在有效日期_起和有效日期_止（如果为空，默认直接匹配）范围内的数据行
                    if (isEffectiveByStartTime(batterySiliconWaferDTO, cellPlanLineDTO.getStartTime().toLocalDate()) &&
                        StringUtils.isNotBlank(batterySiliconWaferDTO.getWaferEcsCode()) &&
                        cellPlanLineDTO.getCellsType().equals(batterySiliconWaferDTO.getBatteryName())){
                        waferEcsCode = batterySiliconWaferDTO.getWaferEcsCode();
                        break;
                    }
                }
            }
            String cellSource = StringTools.joinWith("-", cellSourceCellPlanLine + ratioCode, waferEcsCode, cellPlanLineDTO.getEcsCode());
            cellPlanLineDTO.setCellSource(cellSource);
        }
    }

    /**
     * 判断 排产日期在有效日期_起和有效日期_止（如果为空，默认直接匹配）范围内的数据行
     *
     * @param batterySiliconWaferDTO
     * @param
     * @return true 有效，false 无效
     */
    private boolean isEffectiveByStartTime(BatterySiliconWaferDTO batterySiliconWaferDTO, LocalDate startLocalDate) {
        LocalDate effectiveStartDate = batterySiliconWaferDTO.getEffectiveStartDate();
        LocalDate effectiveEndDate = batterySiliconWaferDTO.getEffectiveEndDate();
        int startDateDiffDays = DateUtil.getDiffDays(effectiveStartDate, startLocalDate);
        int endDateDiffDays = -1;
        // effectiveEndDate没有设置时，不用判断结束日期。
        if (effectiveEndDate != null){
            endDateDiffDays = DateUtil.getDiffDays(effectiveEndDate, startLocalDate);
        }
        return startDateDiffDays>= 0 && endDateDiffDays <=0;
    }

    /**
     *  1.根据电池车间获取对应的产地（去aps_module_base_place 表获取 place_info），
     *  2.再通过产地到投产计划对应的法碳证书的产地信息中获取ECS CODE,
     *  3.将法碳里的origin_ecs_code 设置到 CellPlanLineDTO电池里的ecsCode;
     *
     * @param cellPlanLineDTOS
     */
    public void matchEcsCodeByWorkshop(List<CellPlanLineDTO> cellPlanLineDTOS) {
        //查询全部车间 CELL 电池车间  MODULE指定组件车间
        ModuleBasePlaceQuery moduleBasePlaceQuery = new ModuleBasePlaceQuery();
        moduleBasePlaceQuery.setPageSize(Integer.MAX_VALUE);
        moduleBasePlaceQuery.setPageNumber(1);
        List<String> workshopList =  cellPlanLineDTOS.stream().filter(k -> StringUtils.isNotBlank(k.getWorkshop())).map(CellPlanLineDTO::getWorkshop).distinct().collect(Collectors.toList());
        moduleBasePlaceQuery.setWorkshopList(workshopList);
        List<ModuleBasePlaceDTO> basePlaceDTOList = apsFeign.findModuleBasePlaceList(moduleBasePlaceQuery).getBody().getData().getContent();
        if (CollectionUtils.isEmpty(basePlaceDTOList)){
            return;
        }
        //电池车间
        List<String> workshopByBasePlaceList = basePlaceDTOList.stream().filter(e -> "电池".equals(e.getProductType()))
                .map(e -> e.getWorkshop()).distinct()
                .collect(Collectors.toList());

        List<String> certCodeList = cellPlanLineDTOS.stream().map(CellPlanLineDTO::getCertCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CarbonCertHeaderDTO> carbonCertHeaderList = carbonService.findList(certCodeList);
        if (CollectionUtils.isEmpty(carbonCertHeaderList)){
            return;
        }

        // Workshop相同
        Map<String, List<CellPlanLineDTO>> cellPlanLineByWorkshopMap = cellPlanLineDTOS.stream().filter(k ->StringUtils.isNotBlank(k.getCertCode())).collect(Collectors.groupingBy(CellPlanLineDTO::getWorkshop));
        for (String workshopByBasePlace : workshopByBasePlaceList) {
            List<CellPlanLineDTO> cellPlanLineDTOSByWorkshop = cellPlanLineByWorkshopMap.get(workshopByBasePlace);
            if(CollectionUtils.isEmpty(cellPlanLineDTOSByWorkshop)){
                continue;
            }
            for (CellPlanLineDTO cellPlanLineDTO : cellPlanLineDTOSByWorkshop) {
                // 产地信息
                List<String> placeInfos = basePlaceDTOList.stream().filter(ele -> Objects.equals(cellPlanLineDTO.getWorkshop(), ele.getWorkshop())).map(ModuleBasePlaceDTO::getPlaceInfo).distinct().collect(toList());
                for (CarbonCertHeaderDTO carbonCertHeaderDTO : carbonCertHeaderList) {
                    //入库计划表集合和法碳信息 CertCode相等时 ，来取ecsCode
                    String certCode = StringUtils.isNotEmpty(carbonCertHeaderDTO.getCertCode()) ? carbonCertHeaderDTO.getCertCode() : carbonCertHeaderDTO.getTempCertCode();
                    if (certCode.equals(cellPlanLineDTO.getCertCode())){
                        // p2_3190 【电池料号匹配】法碳ECS CODE获取错误 增加产地维度过滤
                        List<CarbonOriginAddress> carbonOriginAddressList = carbonCertHeaderDTO.getCarbonOriginAddressList().stream().filter(k ->
                                "Cell".equalsIgnoreCase(k.getMtlType()) && placeInfos.contains(k.getOrigin())).collect(toList());
                        if(CollectionUtils.isEmpty(carbonOriginAddressList)){
                           continue;
                        }
                        // 正常情况只有一个 ecsCode，取get(0),(业务确认了，只会与1个，所以取get(0))
                        String  ecsCode = carbonOriginAddressList.get(0).getOriginEcsCode();
                        cellPlanLineDTO.setEcsCode(ecsCode);
                    }
                }
            }
        }

    }

    private void updatePlanQty(List<CellPlanLineDTO> cellPlanLineDTOS) {
        //获取计划浮动系数
        CellPlanQtyFluctuationCoefficientQuery query = new CellPlanQtyFluctuationCoefficientQuery();
        query.setPageSize(GlobalConstant.max_page_size);
        query.setPageNumber(1);
        Page<CellPlanQtyFluctuationCoefficientDTO> page = fluctuationCoefficientService.queryByPage(query);
        List<CellPlanQtyFluctuationCoefficientDTO> fluctuationCoefficientDTOS = Lists.newArrayList();
        if (page != null) {
            fluctuationCoefficientDTOS = Optional.ofNullable(page.getContent()).orElse(Lists.newArrayList());
        }
        if (CollectionUtils.isEmpty(fluctuationCoefficientDTOS)) {
            return;
        }
        Map<String, List<CellPlanQtyFluctuationCoefficientDTO>> qtyFluctuationMap = fluctuationCoefficientDTOS.stream().collect(Collectors.groupingBy(CellPlanQtyFluctuationCoefficientDTO::getWorkshopName));
        for (CellPlanLineDTO cellPlanLineDTO : cellPlanLineDTOS) {
            String key = cellPlanLineDTO.getWorkshop();
            if (qtyFluctuationMap.containsKey(key)) {
                List<CellPlanQtyFluctuationCoefficientDTO> coefficientDTOS = qtyFluctuationMap.get(key);
                for (CellPlanQtyFluctuationCoefficientDTO coefficientDTO : coefficientDTOS) {
                    LocalDate startTime = coefficientDTO.getStartTime();
                    LocalDate endtime = coefficientDTO.getEndTime();
                    LocalDate cellPlanLineDTOStartTime = cellPlanLineDTO.getStartTime().toLocalDate();
                    if (cellPlanLineDTOStartTime.isAfter(startTime) && cellPlanLineDTOStartTime.isBefore(endtime)
                            || cellPlanLineDTOStartTime.isEqual(startTime)
                            || cellPlanLineDTOStartTime.isEqual(endtime)) {
                        if (cellPlanLineDTO.getQtyPc() != null) {
                            cellPlanLineDTO.setQtyPc(cellPlanLineDTO.getQtyPc().multiply(BigDecimal.ONE.add(coefficientDTO.getFluctuationCoefficient())));
                        }
                        if (cellPlanLineDTO.getOldQtyPc() != null) {
                            cellPlanLineDTO.setOldQtyPc(cellPlanLineDTO.getOldQtyPc().multiply(BigDecimal.ONE.add(coefficientDTO.getFluctuationCoefficient())));
                        }
                        break;
                    }

                }
            }

        }

    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellPlanLineTotalQuery query) {
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellPlanLineTotal.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellPlanLineTotal.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellPlanLineTotal.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellPlanLineTotal.oldMonth.eq(query.getMonth()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellPlanLineTotal.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getLastVersion(query.getMonth(), query.getIsOversea());
            if (StringUtils.isNotEmpty(version)) {
                booleanBuilder.and(qCellPlanLineTotal.version.eq(version));
            }
        } else {
            Pair<String, String> versions = getLastVersion(query);
            if (StringUtils.isNotEmpty(versions.getLeft()) && StringUtils.isNotEmpty(versions.getRight())) {
                booleanBuilder.and(qCellPlanLineTotal.version.in(versions.getLeft(), versions.getRight()));

            } else if (StringUtils.isNotEmpty(versions.getLeft())) {
                booleanBuilder.and(qCellPlanLineTotal.version.eq(versions.getLeft()));
            } else if (StringUtils.isNotEmpty(versions.getRight())) {
                booleanBuilder.and(qCellPlanLineTotal.version.eq(versions.getRight()));
            }

        }
    }

    @Override
    public CellPlanLineTotalDTO queryById(Long id) {
        CellPlanLineTotal queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellPlanLineTotalDTO save(CellPlanLineTotalSaveDTO saveDTO) {
        CellPlanLineTotal newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellPlanLineTotal());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(CellPlanLineTotalQuery query, HttpServletResponse response) {
        query.setPageSize(GlobalConstant.max_page_size);
        query.setPageNumber(1);
        query.setBasePlace(null);
        query.setWorkshop(null);
        query.setCellsType(null);
        //获取数据
        Pair<String, String> versions = this.getSendedEmailOrLastVersion(query, false);
        List<CellPlanLineTotalDTO> dtos = queryByPage(query, versions).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("暂无数据");
        }
        Pair<String, String> sendedEmailVersions = this.getSendedEmailOrLastVersion(query, true);
        Boolean sameVersionFlag = (Objects.isNull(sendedEmailVersions.getLeft()) && Objects.isNull(sendedEmailVersions.getRight()))
                || ((Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getLeft()) && Objects.equals(versions.getLeft(), sendedEmailVersions.getLeft()))
                && (Objects.nonNull(versions.getRight()) && Objects.nonNull(versions.getRight()) && Objects.equals(versions.getRight(), sendedEmailVersions.getRight())));

        List<CellPlanLineTotalDTO> sendedEmailDTOS = null;
        Map<String, CellPlanLineTotalDTO> sendedEmailDTOMap;
        if (!sameVersionFlag) {
            sendedEmailDTOS = this.queryByPage(query, sendedEmailVersions).getContent();
            sendedEmailDTOMap = sendedEmailDTOS.stream().collect(Collectors.toMap(CellPlanLineTotalDTO::group, Function.identity(), (v1, v2) -> v1));
        } else {
            sendedEmailDTOMap = null;
        }

        dtos.forEach(ele -> {
            if (MapUtils.isNotEmpty(ele.getDataStructures())) {
                TreeMap<String, String> dataStructures = ele.getDataStructures();
                dataStructures.forEach((k, v) -> {
                    dataStructures.put(k, !StringUtils.isEmpty(v) ? String.valueOf(new BigDecimal(v).setScale(2, RoundingMode.HALF_UP)) : "");
                });
                ele.setDataStructures(dataStructures);
            }
            if (sameVersionFlag) {
                ele.setChangeStatusDesc(PlanChangeStatusEnum.UNCHANGE.getDesc());
            } else {
                ele.setChangeStatusDesc(PlanChangeStatusEnum.getDescForCompare(ele, sendedEmailDTOMap.get(ele.group())));
            }
        });

        List<String> dtosList = dtos.stream().map(CellPlanLineTotalDTO::group).collect(toList());
        if (CollectionUtils.isNotEmpty(sendedEmailDTOS)) {
            sendedEmailDTOS.forEach(ele -> {
                if (MapUtils.isNotEmpty(ele.getDataStructures())) {
                    TreeMap<String, String> dataStructures = ele.getDataStructures();
                    dataStructures.forEach((k, v) -> {
                        dataStructures.put(k, !StringUtils.isEmpty(v) ? String.valueOf(new BigDecimal(v).setScale(2, RoundingMode.HALF_UP)) : "");
                    });
                    ele.setDataStructures(dataStructures);
                }
                ele.setChangeStatusDesc(dtosList.contains(ele.group()) ? "" : PlanChangeStatusEnum.DELETE.getDesc());
            });
        }
        //需要先刷新一下数据的变更状态,excel赋值后才能获取头部模板,不然会有问题
        differentialJudgment(Lists.newArrayList(),dtos,sendedEmailDTOS);

        // dto数据转为ExcelData数据
        List<CellPlanLineTotalExcelDTO> datas = convert.toExcelDTO(dtos);
        if (CollectionUtils.isNotEmpty(datas)) {
            for (int i = 0; i < dtos.size(); i++) {
                TreeMap<String, BigDecimal> dataStructuresMap = Maps.newTreeMap();
                dtos.get(i).getDataStructures().forEach((k, v) -> {
                    dataStructuresMap.put(k, StringUtils.isEmpty(v) ? null : new BigDecimal(v));
                });
                datas.get(i).setDataStructuresMap(dataStructuresMap);
            }
        }

        List<CellPlanLineTotalExcelDTO> sendedEmaildatas = sameVersionFlag ? null : convert.toExcelDTO(sendedEmailDTOS);
        if (!sameVersionFlag && CollectionUtils.isNotEmpty(sendedEmaildatas)) {
            for (int i = 0; i < sendedEmaildatas.size(); i++) {
                TreeMap<String, BigDecimal> dataStructuresMap = Maps.newTreeMap();
                sendedEmailDTOS.get(i).getDataStructures().forEach((k, v) -> {
                    dataStructuresMap.put(k, StringUtils.isEmpty(v) ? null : new BigDecimal(v));
                });
                sendedEmaildatas.get(i).setDataStructuresMap(dataStructuresMap);
            }
        }

        ExcelPara excelPara = CellPlanLineTotalExcelDTO.buildExcelPara(CollectionUtils.isEmpty(datas) ? null : datas.get(0).getDataStructuresMap());
        //判断每日数据和上版本变动，并标记蓝色
        List<DataColumn> columns = excelPara.getColumns();
        List<CellStyleModel> models = differentialJudgment(columns,dtos,sendedEmailDTOS);

        List<List<Object>> objList = ExcelUtils.getList(datas, excelPara);
        List<List<Object>> resultList = Lists.newArrayList();
        objList.forEach(ele -> {
            List<Object> itemResultList = Lists.newArrayList();
            ele.forEach(item -> {
                if (Objects.nonNull(item) && item.getClass() == TreeMap.class) {
                    ((TreeMap)item).values().forEach(itemResultList::add);
                } else {
                    itemResultList.add(item);
                }
            });
            resultList.add(itemResultList);
        });

        List<List<Object>> sheet2ResultList = Lists.newArrayList();
        if (!sameVersionFlag) {
            List<List<Object>> sheet2ObjList = ExcelUtils.getList(sendedEmaildatas, excelPara);
            sheet2ObjList.forEach(ele -> {
                List<Object> itemResultList = Lists.newArrayList();
                ele.forEach(item -> {
                    if (Objects.nonNull(item) && item.getClass() == TreeMap.class) {
                        ((TreeMap)item).values().forEach(itemResultList::add);
                    } else {
                        itemResultList.add(item);
                    }
                });
                sheet2ResultList.add(itemResultList);
            });
        }

        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        Iterator<List<String>> iterator = simpleHeader.iterator();
        while (iterator.hasNext()) {
            if (iterator.next().contains("dataStructuresMap")) {
                iterator.remove();
            }
        }


        try {
            String fileName = "投产计划汇总表" + "_" + DateUtils.formatDate(new Date(), new Object[]{"yyyy-MM-dd HH:mm:ss"});

            try {
                ExcelUtils.setExportResponseHeader(response, fileName);
                ExcelWriterBuilder excelWriterBuilder = (ExcelWriterBuilder)((ExcelWriterBuilder)((ExcelWriterBuilder) EasyExcelFactory.write(response.getOutputStream()).registerConverter(new LocalDateConverter())).registerConverter(new LocalDateTimeConverter())).registerConverter(new LongStringConverter()).registerConverter(new MapConverter()).registerWriteHandler(new CustomCellStyleHandler(models));
                ExcelWriter writer = excelWriterBuilder.build();
                WriteSheet sheet1 = new WriteSheet();
                sheet1.setSheetName("当前版本");
                sheet1.setSheetNo(0);
                WriteTable table = new WriteTable();
                table.setTableNo(1);
                table.setHead(simpleHeader);
                writer.write(resultList, sheet1, table);

                WriteSheet sheet2 = new WriteSheet();
                sheet2.setSheetName("上一版本");
                sheet2.setSheetNo(1);
                WriteTable table2 = new WriteTable();
                table2.setTableNo(1);
                table2.setHead(simpleHeader);
                writer.write(sameVersionFlag ? resultList : sheet2ResultList, sheet2, table2);

                WriteSheet sheet3 = new WriteSheet();
                sheet3.setSheetName("范围");
                sheet3.setSheetNo(2);
                WriteTable table3 = new WriteTable();
                table3.setTableNo(1);
                List<List<String>> list = new ArrayList();
                list.add(com.google.common.collect.Lists.newArrayList(new String[]{"查询条件"}));
                table3.setHead(list);
                writer.write(Collections.singletonList(JSON.toJSONString(query)), sheet3, table3);

                writer.finish();
            } finally {
                response.getOutputStream().close();
            }

        } catch (Throwable var17) {
            throw var17;
        }
        // 导出调用excelUtils
//        ExcelUtils.excelExportByQueryFilter(CellPlanLineTotalExcelDTO.class, datas, JSON.toJSONString(query), "投产计划汇总表", response);

    }

    public List<CellStyleModel> differentialJudgment(List<DataColumn> columns
            ,List<CellPlanLineTotalDTO> dtos
            ,List<CellPlanLineTotalDTO> sendedEmailDTOS) {
        List<CellStyleModel> models = Lists.newArrayList();

        int lineCount = 0;

        //赋值上个邮件的变更状态，下面会赋值当前状态
        if(CollectionUtils.isNotEmpty(sendedEmailDTOS)){
            List<String> curVersionDatasGroup = dtos.stream().map(CellPlanLineTotalDTO::group).collect(toList());
            sendedEmailDTOS.forEach(item->{
                item.setChangeStatusDesc(curVersionDatasGroup.contains(item.group()) ? "" : PlanChangeStatusEnum.DELETE.getDesc());
            });
        }

        if(CollectionUtils.isEmpty(sendedEmailDTOS)){
            return models;
        }
        Map<String, CellPlanLineTotalDTO> sendedEmailDTOMap = sendedEmailDTOS.stream().collect(Collectors.toMap(CellPlanLineTotalDTO::group, Function.identity(), (v1, v2) -> v1));

        //循环本次数据，并循环字段，用来获取字段下标
        for (CellPlanLineTotalDTO dto : dtos) {
            CellPlanLineTotalDTO email = sendedEmailDTOMap.get(dto.group());
            dto.setChangeStatusDesc(PlanChangeStatusEnum.getDescForCompare(dto, email));
            if(ObjectUtils.isEmpty(email)){
                continue;
            }
            //通用方法获取改变需要标记颜色的数据
            List<CellPlanLineContrastDTO> changeList = Lists.newArrayList();
            try{
                changeList.addAll(this.changeContrast(email,dto));
            }catch (Exception e){
                log.error(ExceptionUtil.exceptionChainToString(e));
                continue;
            }
            if(CollectionUtils.isEmpty(changeList)){
                continue;
            }
            for (DataColumn col : columns) {
                String name = col.getTitle();

                for(CellPlanLineContrastDTO changeItem : changeList){
                    if(name.equals(changeItem.getName()) && 1==changeItem.getChangeFlag()){
                        //进入此判断说明有改动
                        CellStyleModel model = CellStyleModel.createBackgroundColorCellStyleModel("当前版本", lineCount, col.getIndex()-1, 0,245,255);
                        models.add(model);
                    }
                }
            }
            lineCount++;
        }
        return models;
    }

    @Override
    public List<CellPlanLineTotalDTO> getMaxVersion() {
        List<CellPlanLineTotalDTO> dataList = new ArrayList<>();
        List<Map<String,String>> list = cellInstockPlanRepository.getMaxVersion();
        for(Map<String,String> map : list){
            CellPlanLineTotalDTO cellPlanLineTotalDTO = new CellPlanLineTotalDTO();
            cellPlanLineTotalDTO.setVersion(map.get("version"));
            cellPlanLineTotalDTO.setIsOversea(map.get("isOversea"));
            cellPlanLineTotalDTO.setMonth(map.get("month"));
            dataList.add(cellPlanLineTotalDTO);
        }
        return dataList;
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void exportH(CellPlanLineTotalQuery query, HttpServletResponse response) {
        if (StringUtils.isBlank(query.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        query.setPageSize(GlobalConstant.max_page_size);
        query.setPageNumber(1);
        query.setBasePlace(null);
        query.setWorkshop(null);
        query.setCellsType(null);
        //获取数据
        List<CellPlanLineTotalHDTO> dtos = queryByPageForH(query);
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("暂无数据");
        } else {
            //记录下版本（便于导入识别）
            query.setVersion(dtos.get(0).getVersion());
        }
        saveHData(dtos, query);
        // dto数据转为ExcelData数据
        List<CellPlanLineTotalHExcelDTO> datas = convert.toHExcelDTO(dtos);
        stripZeros(datas, null, null);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellPlanLineTotalHExcelDTO.class, datas, JSON.toJSONString(query, SerializerFeature.DisableCircularReferenceDetect), "投产计划H兼容修改", response);

    }

    private void saveHData(List<CellPlanLineTotalHDTO> dtos, CellPlanLineTotalQuery query) {
        QCellPlanLineH qCellPlanLineH = QCellPlanLineH.cellPlanLineH;
        long count = jpaQueryFactory.select(qCellPlanLineH).from(qCellPlanLineH).where(
                qCellPlanLineH.version.eq(query.getVersion())
                        .and(qCellPlanLineH.isOversea.eq(query.getIsOversea()))
                        .and(qCellPlanLineH.oldMonth.eq(query.getMonth()))
        ).fetchCount();
        if (count == 0) {
            List<CellPlanLineDTO> allHDetails = dtos.stream().filter(item -> {
                if (StringUtils.isBlank(item.getHTrace())) {
                    return false;
                }
                if (StringUtils.equals(item.getHTrace(), "无")) {
                    return false;
                }
                return true;
            }).map(item -> {
                return item.getDetails();
            }).flatMap(list -> list.stream()).collect(toList());
            if (CollectionUtils.isNotEmpty(allHDetails)) {
                List<CellPlanLineH> cellPlanLineHS = cellPlanLineHDEConvert.toEntityFromCellPlanLineDto(allHDetails);
                cellPlanLineHRepository.saveAll(cellPlanLineHS);
            }
        }


    }

    @Override
    public Page<CellPlanLineTotalHDTO> queryHByPage(CellPlanLineTotalQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isBlank(query.getIsOversea())) {
            query.setIsOversea(OverseaConstant.INLAND);
        }
        //获取数据
        List<CellPlanLineTotalHDTO> datas = queryByPageForH(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        MyThreadLocal.get().setLang(oldLang);
        datas = cellPlanLineTotalDEConvert.toCellPlanLineTotalHDTONameFromCNName(datas);
        return new PageImpl(datas.subList((query.getPageNumber() - 1) * query.getPageSize(), Math.min(query.getPageNumber() * query.getPageSize(), datas.size())), pageable, datas.size());
    }

    /**
     * 获取用于H兼容修改的汇总数据
     *
     * @param query
     * @return
     */
    private List<CellPlanLineTotalHDTO> queryByPageForH(CellPlanLineTotalQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        CellPlanLineQuery cellPlanLineQuery = convert.toCellPlanLineQuery(query);
        //获取最新版本号
        Pair<String, String> versions = getSendedEmailOrLastVersion(query, false);
        //读取投产数据后汇总统计
        List<CellPlanLineTotalHDTO> result = calcCellPlanLineHTotalByQuery(cellPlanLineQuery, versions, oldLang);

        return result;
    }

    private List<CellPlanLineTotalHDTO> calcCellPlanLineHTotalByQuery(CellPlanLineQuery query, Pair<String, String> versions, String oldLang) {
        List<CellPlanLineTotalHDTO> cellPlanLineTotalHDTOs = Lists.newArrayList();
        //1、获取投产数据
        List<CellPlanLineDTO> datas = cellPlanLineService.query(query, versions);
        if (CollectionUtils.isNotEmpty(datas)) {
            if (Objects.isNull(datas.get(0).getIsOverseaId())) {
                throw new BizException("排产数据还没有进行初始转化，请先点击投产查询进行转化！");
            }
        } else {
            throw new BizException("暂无数据");
        }
        //2、依据各维度分组
        //2.1 具有可靠性验证数据统计
//        List<CellPlanLineDTO> datasHaveVerificationMark = datas.stream().filter(item -> StringUtils.isNotBlank(item.getVerificationMark())).collect(toList());
//        if (CollectionUtils.isNotEmpty(datasHaveVerificationMark)) {
//            groupData(cellPlanLineTotalHDTOs, datasHaveVerificationMark);
//        }
        //2.2 没有可靠性验证数据统计
        // List<CellPlanLineDTO> datasNoVerificationMark = datas.stream().filter(item -> StringUtils.isBlank(item.getVerificationMark())).collect(toList());
        if (CollectionUtils.isNotEmpty(datas)) {
            groupDataNoVerificationMark(cellPlanLineTotalHDTOs, datas);
        }
        //3、排序
        cellPlanLineTotalHDTOs = cellPlanLineTotalHDTOs.stream().sorted(Comparator.comparing(CellPlanLineTotalHDTO::getBasePlace, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(CellPlanLineTotalHDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder())).

                thenComparing(
                        CellPlanLineTotalHDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getIsSpecialRequirement, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getSiMfrs, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getMonth, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder())
                )
        ).collect(Collectors.toList());
        return cellPlanLineTotalHDTOs;
    }

    private void groupDataNoVerificationMark(List<CellPlanLineTotalHDTO> cellPlanLineTotalHDTOs, List<CellPlanLineDTO> datas) {
        Map<String, List<CellPlanLineDTO>> collect = datas.stream().collect(
                Collectors.groupingBy(item -> {
                    return Joiner.on("♠").useForNull("null").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                            item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                            item.getSiMfrs(),
                            item.getMonth(), item.getHTrace());
                })
        );
        //依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        //3、统计
        for (String key : keys) {
            CellPlanLineTotalHDTO cellPlanLineTotalHDTO = null;
            List<CellPlanLineDTO> dtos = collect.get(key);
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            //对每组数据统计，每组最终对应一个CellPlanLineTotalDTO对象
            for (CellPlanLineDTO dto : dtos) {
                if (Objects.isNull(cellPlanLineTotalHDTO)) {
                    cellPlanLineTotalHDTO = convert.toCellPlanLineTotaHlDTO(dto);
                    cellPlanLineTotalHDTO.getDetails().addAll(dtos);
                }
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellPlanLineTotalHDTO, "getD" + day);
                BigDecimal value = dto.getQtyPc();
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, addVal);
                }
            }
            setScale(cellPlanLineTotalHDTO);
            cellPlanLineTotalHDTOs.add(cellPlanLineTotalHDTO);
        }
    }

    private void groupDataNoVerificationMarkFromH(List<CellPlanLineTotalHDTO> cellPlanLineTotalHDTOs, List<CellPlanLineHDTO> datas) {
        Map<String, List<CellPlanLineHDTO>> collect = datas.stream().collect(
                Collectors.groupingBy(item -> {
                    return Joiner.on("♠").useForNull("null").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                            item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                            item.getSiMfrs(),
                            item.getMonth(), item.getHTrace());
                })
        );
        //依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        //3、统计
        for (String key : keys) {
            CellPlanLineTotalHDTO cellPlanLineTotalHDTO = null;
            List<CellPlanLineHDTO> dtos = collect.get(key);
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            //对每组数据统计，每组最终对应一个CellPlanLineTotalDTO对象
            for (CellPlanLineHDTO dto : dtos) {
                if (Objects.isNull(cellPlanLineTotalHDTO)) {
                    cellPlanLineTotalHDTO = convert.toCellPlanLineTotaHlDTO(dto);
                    cellPlanLineTotalHDTO.setWorkunit(null);
                    cellPlanLineTotalHDTO.setLineName(null);

                }
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellPlanLineTotalHDTO, "getD" + day);
                BigDecimal value = dto.getQtyPc();
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, addVal);
                }
            }
            setScale(cellPlanLineTotalHDTO);
            cellPlanLineTotalHDTOs.add(cellPlanLineTotalHDTO);
        }
    }

    private void groupData(List<CellPlanLineTotalHDTO> cellPlanLineTotalHDTOs, List<CellPlanLineDTO> datas) {
        Map<String, List<CellPlanLineDTO>> collect = datas.stream().collect(
                Collectors.groupingBy(item -> {
                    return Joiner.on("♠").useForNull("null").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                            item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                            item.getSiMfrs()
                            , item.getMonth(), item.getHTrace());
                })
        );
        //依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        //3、统计
        for (String key : keys) {
            CellPlanLineTotalHDTO cellPlanLineTotalHDTO = null;
            List<CellPlanLineDTO> dtos = collect.get(key);
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            //对每组数据统计，每组最终对应一个CellPlanLineTotalDTO对象
            for (CellPlanLineDTO dto : dtos) {
                if (Objects.isNull(cellPlanLineTotalHDTO)) {
                    cellPlanLineTotalHDTO = convert.toCellPlanLineTotaHlDTO(dto);
                    cellPlanLineTotalHDTO.getDetails().addAll(dtos);
                }
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellPlanLineTotalHDTO, "getD" + day);
                BigDecimal value = dto.getQtyPc();
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, addVal);
                }
            }
            setScale(cellPlanLineTotalHDTO);
            cellPlanLineTotalHDTOs.add(cellPlanLineTotalHDTO);
        }
    }

    private void groupDataFromH(List<CellPlanLineTotalHDTO> cellPlanLineTotalHDTOs, List<CellPlanLineHDTO> datas) {
        Map<String, List<CellPlanLineHDTO>> collect = datas.stream().collect(
                Collectors.groupingBy(item -> {
                    return Joiner.on("♠").useForNull("null").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                            item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                            item.getSiMfrs()
                            , item.getMonth(), item.getHTrace());

                })
        );
        //依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        //3、统计
        for (String key : keys) {
            CellPlanLineTotalHDTO cellPlanLineTotalHDTO = null;
            List<CellPlanLineHDTO> dtos = collect.get(key);
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            //对每组数据统计，每组最终对应一个CellPlanLineTotalDTO对象
            for (CellPlanLineHDTO dto : dtos) {
                if (Objects.isNull(cellPlanLineTotalHDTO)) {
                    cellPlanLineTotalHDTO = convert.toCellPlanLineTotaHlDTO(dto);

                }
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellPlanLineTotalHDTO, "getD" + day);
                BigDecimal value = dto.getQtyPc();
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, addVal);
                }
            }
            setScale(cellPlanLineTotalHDTO);
            cellPlanLineTotalHDTOs.add(cellPlanLineTotalHDTO);
        }
    }

    private void setScale(CellPlanLineTotalHDTO cellPlanLineTotalHDTO) {
        for (int i = 1; i <= 31; i++) {
            BigDecimal val = ReflectUtil.invoke(cellPlanLineTotalHDTO, "getD" + i);
            if (Objects.nonNull(val)) {
                ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + i, val.setScale(2, RoundingMode.HALF_UP));
            }
        }
    }

    private void setScale(CellPlanLineTotalHExcelDTO cellPlanLineTotalHDTO) {
        for (int i = 1; i <= 31; i++) {
            BigDecimal val = ReflectUtil.invoke(cellPlanLineTotalHDTO, "getD" + i);
            if (Objects.nonNull(val)) {
                ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + i, val.setScale(2, RoundingMode.HALF_UP));
            }
        }
    }

    @Override
    @SneakyThrows
    public void exportForMrp(CellPlanLineTotalQuery query, HttpServletResponse response) {
        query.setPageSize(GlobalConstant.max_page_size);
        query.setPageNumber(1);
        //  query.setBasePlace(null);
        // query.setWorkshop(null);
        //  query.setCellsType(null);
        //获取数据
        List<CellPlanLineTotalDTO> dtos = queryByPageForMrp(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("暂无数据");
        }
        // dto数据转为ExcelData数据
        List<CellPlanLineTotalExcelDTO> datas = convert.toExcelDTO(dtos);
        // 导出调用excelUtils
        ExcelUtils.excelExportByQueryFilter(CellPlanLineTotalExcelDTO.class, datas, JSON.toJSONString(query), "投产计划汇总表", response);
    }

    @Override
    public void twoMonthExport(CellPlanLineTotalQuery query, HttpServletResponse response) {
        List<CellPlanLineTotalDTO> dtos = queryTwnMonthByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BizException("暂无数据");
        }
        ExcelPara excelPara = query.getExcelPara();
        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        List<List<Object>> objList = ExcelUtils.getList(dtos, excelPara);
        String fileName = "投产计划汇总";

        ExcelUtils.exportEx(response, fileName, fileName, simpleHeader, objList);

    }


    /**
     * 获取投产计划汇总表fromVersion版本号（依据国内外）
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getLastFromVersion(String month, String isOversea) {
        QCellPlanLineTotal qCellPlanLineTotal = QCellPlanLineTotal.cellPlanLineTotal;
        String version = jpaQueryFactory.select(qCellPlanLineTotal.fromVersion.max()).from(qCellPlanLineTotal).where(
                qCellPlanLineTotal.oldMonth.eq(month)
        ).where(
                qCellPlanLineTotal.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    /**
     * 获取投产汇总的version
     *
     * @param query
     * @return
     */
    private Pair<String, String> getLastVersion(CellPlanLineTotalQuery query) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getLastVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }

        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getLastVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getLastVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }
    }

    /**
     * 获取投产汇总的version
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getLastVersion(String month, String isOversea) {
        QCellPlanLineTotal qCellPlanLineTotal = QCellPlanLineTotal.cellPlanLineTotal;
        String version = jpaQueryFactory.select(qCellPlanLineTotal.version.max()).from(qCellPlanLineTotal).where(
                qCellPlanLineTotal.oldMonth.eq(month)
        ).where(
                qCellPlanLineTotal.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    /**
     * 获取投产计划表已经确认的版本号
     *
     * @param query
     * @return
     */
    private Pair<String, String> getPlanLineLastConfirmVersion(CellPlanLineQuery query) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getPlanLineLastConfirmVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }
        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getPlanLineLastConfirmVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getPlanLineLastConfirmVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }

    }

    /**
     * 获取投产计划表版本号（依据国内外）
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getPlanLineLastVersion(String month, String isOversea) {

        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;
        String version = jpaQueryFactory.select(qCellPlanLine.version.max()).from(qCellPlanLine).where(
                qCellPlanLine.oldMonth.eq(month)
        ).where(
                qCellPlanLine.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    /**
     * 获取投产计划表版本号（依据国内外）
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getPlanLineLastConfirmVersion(String month, String isOversea) {
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;
        String version = jpaQueryFactory.select(qCellPlanLine.finalVersion.max()).from(qCellPlanLine).where(
                qCellPlanLine.oldMonth.eq(month)
        ).where(
                qCellPlanLine.isOversea.eq(isOversea)
        ).fetchFirst();
        return version;
    }

    /**
     * 获取投产计划表确认的版本号（上一版本）
     *
     * @param query
     * @return
     */
    private Pair<String, String> getPlanLinePreConfirmVersion(CellPlanLineQuery query) {
        String month = query.getMonth();
        if (StringUtils.isEmpty(month)) {
            month = DateUtil.getMonth(LocalDate.now());
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            String version = getPlanLinePreConfirmVersion(month, query.getIsOversea());
            if (query.getIsOversea().trim().equals(OverseaConstant.INLAND)) {
                return new ImmutablePair<>(version, null);
            } else {
                return new ImmutablePair<>(null, version);
            }
        } else {
            String isOversea = OverseaConstant.INLAND;
            String InVersion = getPlanLinePreConfirmVersion(month, isOversea);
            isOversea = OverseaConstant.OVERSEA;
            String outVersion = getPlanLinePreConfirmVersion(month, isOversea);
            return new ImmutablePair<>(InVersion, outVersion);
        }

    }

    /**
     * 获取投产计划表版本号（依据国内外），上一版本的版本号
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getPlanLinePreVersion(String month, String isOversea) {
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;
        List<String> versions = jpaQueryFactory.selectDistinct(qCellPlanLine.version).from(qCellPlanLine).where(
                qCellPlanLine.oldMonth.eq(month)
        ).where(
                qCellPlanLine.isOversea.eq(isOversea)
        ).orderBy(qCellPlanLine.version.desc()).limit(2).fetch();
        if (CollectionUtils.isNotEmpty(versions)) {
            if (versions.size() > 1) {
                return versions.get(1);
            }
        }
        return null;
    }

    /**
     * 获取投产计划表版本号（依据国内外），上一版本确认的版本号
     *
     * @param month
     * @param isOversea
     * @return
     */
    private String getPlanLinePreConfirmVersion(String month, String isOversea) {
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;
        List<String> versions = jpaQueryFactory.selectDistinct(qCellPlanLine.finalVersion).from(qCellPlanLine).where(
                qCellPlanLine.oldMonth.eq(month)
        ).where(
                qCellPlanLine.isOversea.eq(isOversea)
        ).where(
                qCellPlanLine.finalVersion.isNotNull()
        ).orderBy(qCellPlanLine.finalVersion.desc()).limit(2).fetch();
        if (CollectionUtils.isNotEmpty(versions)) {
            if (versions.size() > 1) {
                return versions.get(1);
            }
        }
        return null;
    }

    @Override
    /**
     * 生成入库计划
     */
    @CacheEvict(cacheNames = "CellInstockPlanService_queryCacheByVersion", allEntries = true)
    @Transactional(rollbackFor = Exception.class)

    public void makeCellInstockPlan(CellPlanLineTotalQuery query) {
        if (StringUtils.isEmpty(query.getIsOversea())) {
            throw new BizException(ErrorConstant.OVERSEA_INLAND_SELECT_ERROR_MESSAGE);
        }
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, query.getIsOversea(), query.getMonth(), "instock", "make");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //执行生成入库计划
                doMakeCellInstockPlan(query);
            } catch (Exception e) {
                throw e;
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }

    }

    /**
     * 生成入库计划
     *
     * @param query
     */
    private void doMakeCellInstockPlan(CellPlanLineTotalQuery query) {
        ScheduledTaskLinesDTO task = null;
        try {
            task = logService.createLogTask(LovHeaderCodeConstant.BAPS_CELL_PLAN_CREATE_INSTOCK_PLAN);
            logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段1：获取%s月确认的投产数据", query.getMonth()));
            List<CellPlanLineDTO> datas = cellPlanLineService.queryConfirmDatas(convert.toCellPlanLineQuery(query));
            makeDataToCellInstockPlan(datas, task);
            logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");

        } catch (Exception exception) {
            logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
            throw new BizException(exception.getMessage());
        } finally {
            logService.saveTaskLog(task);
        }
    }

    /**
     * 生成入库数据（投产转入库）
     *
     * @param cellPlanLineDTOS 投产数据
     * @param task             日志对象
     */
    private void makeDataToCellInstockPlan(List<CellPlanLineDTO> cellPlanLineDTOS, ScheduledTaskLinesDTO task) {
        CellPlanLineVersion cellPlanLineVersionInland = null;
        CellPlanLineVersion cellPlanLineVersionOversea = null;
        CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto = null;
        CellInstockPlanVersionDTO cellInstockPlanVersionOverseaDto = null;
        List<CellInstockPlanDTO> cellInstockPlanDTOS = new ArrayList<>();
        String inland = null;
        String oversea = null;
        //1、根据投产生成入库数据
        for (CellPlanLineDTO item : cellPlanLineDTOS) {
            if (inland == null) {
                if (item.getIsOversea().equals(OverseaConstant.INLAND)) {
                    inland = OverseaConstant.INLAND;
                    //1.1删除原来国内数据
                    cellInstockPlanRepository.deleteByVersion(inland, item.getOldMonth(), item.getFinalVersion());
                    cellInstockPlanTotalRepository.deleteByVersion(inland, item.getOldMonth(), item.getFinalVersion());
                }
            }
            if (oversea == null) {
                if (item.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                    oversea = OverseaConstant.OVERSEA;
                    //1.1 删除原来海外数据
                    cellInstockPlanRepository.deleteByVersion(oversea, item.getOldMonth(), item.getFinalVersion());
                    cellInstockPlanTotalRepository.deleteByVersion(oversea, item.getOldMonth(), item.getFinalVersion());
                }
            }
            //数量不为0才同步
            if (!MathUtils.checkIsZero(item.getQtyPc())) {
                //1，2 数据转化（投产转入库）
                CellInstockPlanDTO dto = new CellInstockPlanDTO();
                BeanUtil.copyProperties(item, dto, "id", "finalVersion");
                dto.setId(null);
                dto.setStartTime(item.getOldStartTime());
                dto.setEndTime(item.getOldEndTime());
                dto.setQtyPc(item.getOldQtyPc());
                dto.setOldStartTime(item.getStartTime());
                dto.setOldEndTime(item.getEndTime());
                dto.setPlanLineFromId(item.getId());
                dto.setSchedulingFromId(item.getFromId());
                dto.setHChangeFlag(item.getHChangeFlag());
                dto.setVersion(item.getFinalVersion());
                dto.setFinalVersion(null);
                String month = item.getMonth();
                dto.setMonth(item.getOldMonth());
                dto.setOldMonth(month);
                //供应方式
                dto.setSupplyMethod(item.getSupplyMethod());
                dto.setSupplyMethodId(item.getSupplyMethodId());
                //背面细栅+正面细栅+硅片厚度+硅片尺寸
                dto.setBackFineGrid(item.getBackFineGrid());
                dto.setFrontFineGrid(item.getFrontFineGrid());
                dto.setSiliconWaferThickness(item.getSiliconWaferThickness());
                dto.setSiliconWaferSize(item.getSiliconWaferSize());
                cellInstockPlanDTOS.add(dto);
            }

            //1.3投产版本管理记录开始（记录投产已经生成入库）
            if (cellPlanLineVersionInland == null) {
                if (item.getIsOversea().equals(OverseaConstant.INLAND)) {
                    cellPlanLineVersionInland = cellPlanLineVersionRepository.selectByVersion(item.getIsOversea(), item.getOldMonth(), item.getVersion());
                    cellPlanLineVersionInland.setIsCreateInstockPlan(1);
                    cellPlanLineVersionRepository.save(cellPlanLineVersionInland);
                }
            }
            if (cellPlanLineVersionOversea == null) {
                if (item.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                    cellPlanLineVersionOversea = cellPlanLineVersionRepository.selectByVersion(item.getIsOversea(), item.getOldMonth(), item.getVersion());
                    cellPlanLineVersionOversea.setIsCreateInstockPlan(1);
                    cellPlanLineVersionRepository.save(cellPlanLineVersionOversea);
                }
            }
            //投产版本管理记录结束
            //1.4入库版本记录开始（记录入库版本信息）
            if (cellInstockPlanVersionInlandDto == null) {
                if (item.getIsOversea().equals(OverseaConstant.INLAND)) {
                    //判断同版本投产是否已经创建过入库计划
                    cellInstockPlanVersionInlandDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getOldMonth(), item.getFinalVersion()));
                    if (cellInstockPlanVersionInlandDto == null) {
                        cellInstockPlanVersionInlandDto = new CellInstockPlanVersionDTO();
                        cellInstockPlanVersionInlandDto.setFromVersion(item.getFinalVersion());
                        cellInstockPlanVersionInlandDto.setMonth(item.getOldMonth());
                        cellInstockPlanVersionInlandDto.setIsOversea(item.getIsOversea());

                        setZero(cellInstockPlanVersionInlandDto);
                    } else {
                        cellInstockPlanVersionInlandDto.setVersion(null);
                        setZero(cellInstockPlanVersionInlandDto);
                    }
                    cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionInlandDto));

                }
            }
            if (cellInstockPlanVersionOverseaDto == null) {
                if (item.getIsOversea().equals(OverseaConstant.OVERSEA)) {
                    //判断同版本投产是否已经创建过入库计划
                    cellInstockPlanVersionOverseaDto = cellInstockPlanVersionDEConvert.toDto(cellInstockPlanVersionRepository.selectByVersion(item.getIsOversea(), item.getOldMonth(), item.getFinalVersion()));
                    if (cellInstockPlanVersionOverseaDto == null) {
                        cellInstockPlanVersionOverseaDto = new CellInstockPlanVersionDTO();
                        cellInstockPlanVersionOverseaDto.setFromVersion(item.getFinalVersion());
                        cellInstockPlanVersionOverseaDto.setMonth(item.getOldMonth());
                        cellInstockPlanVersionOverseaDto.setIsOversea(item.getIsOversea());
                        setZero(cellInstockPlanVersionOverseaDto);
                    } else {
                        cellInstockPlanVersionOverseaDto.setVersion(null);
                        setZero(cellInstockPlanVersionOverseaDto);
                    }
                    cellInstockPlanVersionRepository.save(cellInstockPlanVersionDEConvert.toEntity(cellInstockPlanVersionOverseaDto));
                }
            }
            //入库版本记录结束
        }
        logService.addLog(task, ScheduleTaskStatusEnum.RUNNING, String.format("阶段2：数据存储到入库表"));
        //2.入库计划数据存储
        cellInstockPlanRepository.saveAll(cellInstockPlanDEConvert.toEntity(cellInstockPlanDTOS));
    }

    private void setZero(CellInstockPlanVersionDTO cellInstockPlanVersionInlandDto) {
        cellInstockPlanVersionInlandDto.setIsConfirmPlan(0);
        cellInstockPlanVersionInlandDto.setIsSendEmail(0);
        cellInstockPlanVersionInlandDto.setIsASplit(0);
        cellInstockPlanVersionInlandDto.setIsTransparentDoubleGlass(0);
        cellInstockPlanVersionInlandDto.setIsGradeRule(0);
    }

    @Override
    public List<CellPlanLineTotal> queryByVersion(String version, String month) {
        return repository.selectByVersion(version, month);

    }

    /**
     * 依据国内外以及月份获取三个最大版本号
     *
     * @param query
     * @return
     */
    @Override
    public List<String> queryThreeMaxVersion(CellPlanLineTotalQuery query) {
        QCellPlanLineTotal cellPlanLineTotal = QCellPlanLineTotal.cellPlanLineTotal;
        //求最大版本号
        JPAQuery<String> where = jpaQueryFactory.selectDistinct(
                cellPlanLineTotal.version
        ).from(cellPlanLineTotal).where(
                cellPlanLineTotal.month.eq(query.getMonth())
        ).where(
                cellPlanLineTotal.isOverseaId.eq(query.getIsOverseaId())
        );
        where.orderBy(cellPlanLineTotal.version.desc());
        List<String> versions = where.limit(3).fetch();
        return versions;
    }

    @Override
    public String queryMaxVersion(CellPlanLineTotalQuery query) {
        QCellPlanLineTotal cellPlanLineTotal = QCellPlanLineTotal.cellPlanLineTotal;
        //求最大版本号
        JPAQuery<String> where = jpaQueryFactory.select(
                cellPlanLineTotal.version.max()
        ).from(cellPlanLineTotal).where(
                cellPlanLineTotal.month.eq(query.getMonth())
        );
        if (Objects.nonNull(query.getIsOverseaId())) {
            where.where(cellPlanLineTotal.isOverseaId.eq(query.getIsOverseaId()));
        }

        String version = where.fetchFirst();
        return version;
    }

    /**
     * 获取最新版的投产数据
     *
     * @param query
     * @return
     */
    @Override
    public List<CellPlanLineTotalDTO> queryByFirst(CellPlanLineTotalQuery query) {
        QCellPlanLineTotal cellPlanLineTotal = QCellPlanLineTotal.cellPlanLineTotal;
        String version = queryMaxVersion(query);
        log.info("投产版本：" + version);
        if (version == null) {
            return null;
        }
        //查询最大版本号对应的数据
        JPAQuery<CellPlanLineTotal> whereData = jpaQueryFactory.select(
                cellPlanLineTotal
        ).from(cellPlanLineTotal).where(
                cellPlanLineTotal.month.eq(query.getMonth())
        );
        if (Objects.nonNull(query.getIsOverseaId())) {
            whereData.where(cellPlanLineTotal.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            whereData.where(cellPlanLineTotal.isOversea.eq(query.getIsOversea()));
        }
        if (Objects.nonNull(query.getWorkshopId())) {
            whereData.where(cellPlanLineTotal.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            whereData.where(cellPlanLineTotal.workshop.eq(query.getWorkshop()));
        }
        if (Objects.nonNull(query.getCellsTypeId())) {
            whereData.where(cellPlanLineTotal.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            whereData.where(cellPlanLineTotal.cellsType.eq(query.getCellsType()));
        }
        List<CellPlanLineTotal> fetch = whereData.where(cellPlanLineTotal.version.eq(version)).fetch();

        return convert.toDto(fetch);
    }


    private String minusMonths(String originalYearMonthStr, String pattern, Integer num) {
        YearMonth previousYearMonth = YearMonth.parse(originalYearMonthStr, DateTimeFormatter.ofPattern(pattern)).minusMonths(num);
        String resultYearMonthStr = previousYearMonth.format(DateTimeFormatter.ofPattern(pattern));
        return resultYearMonthStr;
    }


    @Override
    @SneakyThrows
    public void importData(MultipartFile multipartFile) {
        //1、获取导入数据
        CellPlanLineTotalHQuery query = new CellPlanLineTotalHQuery();
        List<CellPlanLineTotalHExcelDTO> excelDtos = getImportData(multipartFile, query);
        if (CollectionUtils.isNotEmpty(excelDtos)) {
            excelDtos.stream().forEach(excelDto -> {
                setScale(excelDto);
            });
        }
        String isOversea = query.getIsOversea();
        String version = query.getVersion();
        String month = query.getMonth();
        //验证修改的是最新版本
        checkMaxVersion(isOversea, month, version);
        Joiner joiner = Joiner.on("-");
        String localKey = joiner.join(RedisLockKey.BAPS_PREFIX, isOversea, month, "cellplan", "import");
        // 加分布式锁
        RLock rLock = redissonClient.getLock(localKey);
        if (rLock.tryLock()) {
            try {
                //2、获取导出时原数据
                CellPlanLineQuery cellPlanLineQuery = new CellPlanLineQuery();
                cellPlanLineQuery.setIsOversea(isOversea);
                cellPlanLineQuery.setVersion(version);
                cellPlanLineQuery.setMonth(month);
                List<CellPlanLineTotalHDTO> dtos = queryHDatasByVersion(cellPlanLineQuery);
                //获取原始H值
                List<CellPlanLineTotalHDTO> oldDtos = queryOldHDatas(query);
                //3、数据验证
                checkInput(excelDtos, dtos, oldDtos);
                //4、数据处理
                doHandler(excelDtos, dtos, oldDtos);
            } catch (Exception e) {
                e.printStackTrace();
                throw new BizException(e.getMessage());
            } finally {
                rLock.unlock();
            }
        } else {
            throw new BizException(ErrorConstant.RUNING_ERROR_MESSAGE);
        }


    }

    private void checkMaxVersion(String isOversea, String month, String version) {
        QCellPlanLineVersion qCellPlanLineVersion = QCellPlanLineVersion.cellPlanLineVersion;
        String maxVersion = jpaQueryFactory.select(qCellPlanLineVersion.version.max()).from(qCellPlanLineVersion).where(
                qCellPlanLineVersion.isOversea.eq(isOversea)
        ).where(qCellPlanLineVersion.month.eq(month)).where(qCellPlanLineVersion.isTransform.eq(1)).fetchFirst();
        if (!Objects.equals(maxVersion, version)) {
            throw new BizException("导入的H兼容计划不是最新版投产计划，请重新导出新增版本投产计划进行修改。");
        }
    }

    private List<CellPlanLineTotalHDTO> queryOldHDatas(CellPlanLineTotalHQuery query) {
        //1、获取原始H数据
        QCellPlanLineH qCellPlanLineH = QCellPlanLineH.cellPlanLineH;
        List<CellPlanLineH> cellPlanLineHS = jpaQueryFactory.select(qCellPlanLineH).from(qCellPlanLineH).where(
                qCellPlanLineH.oldMonth.eq(query.getMonth())
        ).where(qCellPlanLineH.isOversea.eq(query.getIsOversea())).where(qCellPlanLineH.version.eq(query.getVersion())).fetch();
        List<CellPlanLineHDTO> datas = cellPlanLineHDEConvert.toDto(cellPlanLineHS);
        //2、依据各维度分组
        //2.1 具有可靠性验证数据统计
        List<CellPlanLineTotalHDTO> cellPlanLineTotalHDTOs = new ArrayList<>();
//        List<CellPlanLineHDTO> datasHaveVerificationMark = datas.stream().filter(item -> StringUtils.isNotBlank(item.getVerificationMark())).collect(toList());
//        if (CollectionUtils.isNotEmpty(datasHaveVerificationMark)) {
//            groupDataFromH(cellPlanLineTotalHDTOs, datasHaveVerificationMark);
//        }
        //2.2 没有可靠性验证数据统计
        //  List<CellPlanLineHDTO> datasNoVerificationMark = datas.stream().filter(item -> StringUtils.isBlank(item.getVerificationMark())).collect(toList());
        if (CollectionUtils.isNotEmpty(datas)) {
            groupDataNoVerificationMarkFromH(cellPlanLineTotalHDTOs, datas);
        }
        //3、排序
        cellPlanLineTotalHDTOs = cellPlanLineTotalHDTOs.stream().sorted(Comparator.comparing(CellPlanLineTotalHDTO::getBasePlace, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(CellPlanLineTotalHDTO::getWorkshop, Comparator.nullsLast(Comparator.naturalOrder())).

                thenComparing(
                        CellPlanLineTotalHDTO::getCellsType, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getCellSource, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getIsSpecialRequirement, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getSiMfrs, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getMonth, Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CellPlanLineTotalHDTO::getHTrace, Comparator.nullsLast(Comparator.naturalOrder())
                )
        ).collect(Collectors.toList());
        return cellPlanLineTotalHDTOs;
    }

    private void stripZeros(List<CellPlanLineTotalHExcelDTO> excelDtos, List<CellPlanLineTotalHDTO> dtos, List<CellPlanLineTotalHDTO> oldDtos) {

        Optional.ofNullable(excelDtos).orElse(Lists.newArrayList()).stream().filter(item -> {
            return item.getWaferGradeRatio() != null || item.getWaferYieldRatio() != null;
        }).forEach(item -> {
            if (item.getWaferGradeRatio() != null) {
                item.setWaferGradeRatio(item.getWaferGradeRatio().stripTrailingZeros());
            }
            if (item.getWaferYieldRatio() != null) {
                item.setWaferYieldRatio(item.getWaferYieldRatio().stripTrailingZeros());

            }
        });
        Optional.ofNullable(dtos).orElse(Lists.newArrayList()).stream().filter(item -> {
            return item.getWaferGradeRatio() != null || item.getWaferYieldRatio() != null;
        }).forEach(item -> {
            if (item.getWaferGradeRatio() != null) {
                item.setWaferGradeRatio(item.getWaferGradeRatio().stripTrailingZeros());
            }
            if (item.getWaferYieldRatio() != null) {
                item.setWaferYieldRatio(item.getWaferYieldRatio().stripTrailingZeros());

            }
        });
        Optional.ofNullable(oldDtos).orElse(Lists.newArrayList()).stream().filter(item -> {
            return item.getWaferGradeRatio() != null || item.getWaferYieldRatio() != null;
        }).forEach(item -> {
            if (item.getWaferGradeRatio() != null) {
                item.setWaferGradeRatio(item.getWaferGradeRatio().stripTrailingZeros());
            }
            if (item.getWaferYieldRatio() != null) {
                item.setWaferYieldRatio(item.getWaferYieldRatio().stripTrailingZeros());
            }
        });
    }

    /**
     * @param excelDtos excel导入数据
     * @param dtos      当前数据
     * @param oldDtos   原始老数据
     */
    private void doHandler(List<CellPlanLineTotalHExcelDTO> excelDtos, List<CellPlanLineTotalHDTO> dtos, List<CellPlanLineTotalHDTO> oldDtos) {
        Map<String, List<CellPlanLineTotalHExcelDTO>> excelDtosMap = excelDtos.stream().collect(Collectors.groupingBy(item -> {
            return Joiner.on(",").useForNull("").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                    item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                    item.getSiMfrs(), item.getMonth());
        }));
        Map<String, List<CellPlanLineTotalHDTO>> dtosMap = dtos.stream().collect(Collectors.groupingBy(item -> {
            return Joiner.on(",").useForNull("").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                    item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                    item.getSiMfrs(),
                    item.getMonth());
        }));
        Map<String, List<CellPlanLineTotalHDTO>> oldDtosMap = oldDtos.stream().collect(Collectors.groupingBy(item -> {
            return Joiner.on(",").useForNull("").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                    item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                    item.getSiMfrs(),
                    item.getMonth());
        }));
        //存储待更新数据
        List<CellPlanLineDTO> details = Lists.newArrayList();
        doHCompatible(excelDtosMap, dtosMap, oldDtosMap, details);
        //数据存储
        if (CollectionUtils.isNotEmpty(details)) {
            details = details.stream().filter(item -> {
                return !Objects.equals(item.getDataType(), 1);
            }).collect(Collectors.toList());
            //依据投产反推原入库排产数据
            calcOldQtyPc(details);
            try {
                transactionService.begin();
                List<CellPlanLine> cellPlanLines = cellPlanLineDEConvert.toCopyEntityFromDto(details);
                cellPlanLineRepository.saveAll(cellPlanLines);
                transactionService.commit();
            } catch (Exception e) {
                transactionService.rollback();
                throw new BizException(e.getMessage());
            }
        }
    }

    private void doHCompatible(Map<String, List<CellPlanLineTotalHExcelDTO>> excelDtosMap, Map<String, List<CellPlanLineTotalHDTO>> dtosMap, Map<String, List<CellPlanLineTotalHDTO>> oldDtosMap, List<CellPlanLineDTO> details) {
        Map<String, Long> LovHtrace = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.H_TRACE).values().stream().collect(Collectors.toMap(
                item -> item.getLovName(),
                item -> item.getLovLineId(),
                (v1, v2) -> v1
        ));
        for (Map.Entry<String, List<CellPlanLineTotalHExcelDTO>> entry : excelDtosMap.entrySet()) {
            String key = entry.getKey();
            //目前数据
            List<CellPlanLineTotalHDTO> dtos = dtosMap.get(key);
            if (CollectionUtils.isEmpty(dtos)) {
                continue;
            }
            //excel新数据
            List<CellPlanLineTotalHExcelDTO> excelDtos = entry.getValue();
            //excel新数据->日期：H类型：值
            Map<Integer, Map<String, BigDecimal>> excelRateMapByDate = Maps.newHashMap();
            for (CellPlanLineTotalHExcelDTO excelDto : excelDtos) {
                String month = excelDto.getMonth();
                int days = DateUtil.getDaysForMonth(month);
                for (int i = 1; i <= days; i++) {
                    BigDecimal value = ReflectUtil.invoke(excelDto, "getD" + i);
                    value = Optional.ofNullable(value).orElse(BigDecimal.ZERO);
                    BigDecimal finalValue = value;
                    excelRateMapByDate.computeIfAbsent(i, k -> Maps.newHashMap()).computeIfAbsent(excelDto.getHTrace(), k -> finalValue);
                }

            }
            //目前数据-》日期：H类型：值
            Map<Integer, Map<String, BigDecimal>> rateMapByDate = Maps.newHashMap();
            dtos.forEach(item -> {
                String month = item.getMonth();
                int days = DateUtil.getDaysForMonth(month);
                for (int i = 1; i <= days; i++) {
                    BigDecimal value = ReflectUtil.invoke(item, "getD" + i);
                    value = Optional.ofNullable(value).orElse(BigDecimal.ZERO);
                    BigDecimal finalValue = value;
                    rateMapByDate.computeIfAbsent(i, k -> Maps.newHashMap()).computeIfAbsent(item.getHTrace(), k -> finalValue);
                }
            });
            //判断某天数据是否需要处理的数据
            for (Map.Entry<Integer, Map<String, BigDecimal>> mapEntry : excelRateMapByDate.entrySet()) {
                Integer day = mapEntry.getKey();
                Map<String, BigDecimal> excelRateMap = mapEntry.getValue();
                Map<String, BigDecimal> rateMap = rateMapByDate.get(day);
                //判断rateMap与oldRateMap是都相等
                Boolean result = false;
                for (Map.Entry<String, BigDecimal> hTraceEntry : excelRateMap.entrySet()) {
                    BigDecimal value = hTraceEntry.getValue();
                    BigDecimal oldValue = rateMap.get(hTraceEntry.getKey());
                    if (Objects.nonNull(value) && Objects.nonNull(oldValue) && value.compareTo(oldValue) == 0) {
                        continue;
                    } else {
                        result = true;
                    }
                }
                if (result) {
                    //变化了，需要处理
                    // calcRateMap(rateMap);
                    prepareData(day, dtos, excelRateMap, rateMap, details, LovHtrace);
                }
            }
        }
    }

    private void calcRateMap(Map<String, BigDecimal> rateMap) {
        BigDecimal sum = rateMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        List<String> keys = rateMap.keySet().stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
        BigDecimal total = BigDecimal.ZERO;
        for (int i = 0; i < keys.size(); i++) {
            if (i == keys.size() - 1) {
                rateMap.put(keys.get(i), BigDecimal.ONE.subtract(total));
            } else {
                BigDecimal val = rateMap.get(keys.get(i)).divide(sum, 8, BigDecimal.ROUND_HALF_UP);
                total = total.add(val);
                rateMap.put(keys.get(i), val);
            }
        }


    }

    /**
     * 处理数据
     *
     * @param day          天
     * @param dtos         目前数据
     * @param excelRateMap 新数据
     * @param details      存储数据集合
     */
    private void prepareData(Integer day, List<CellPlanLineTotalHDTO> dtos, Map<String, BigDecimal> excelRateMap, Map<String, BigDecimal> rateMap, List<CellPlanLineDTO> details, Map<String, Long> lovHtrace) {

        //1、获取当天要处理的数据数据(day)
        List<CellPlanLineDTO> collect = dtos.stream().map(item -> item.getDetails()).flatMap(list -> list.stream()).filter(item -> {
            return Objects.equals(item.getStartTime().getDayOfMonth(), day);
        }).collect(toList());
        collect.forEach(item -> {
            if (StringUtils.isBlank(item.getHTrace())) {
                item.setHTrace("无");
                item.setHTraceId(lovHtrace.get("无"));
            }
            if (StringUtils.isBlank(item.getHChangeFlag())) {
                item.setHChangeFlag("N");
            }
        });

        //2、获取当天h数据(分为原h数据、h兼容数据)
        Map<String, Map<String, List<CellPlanLineDTO>>> hDataMap = collect.stream().filter(item -> {
            return !Objects.equals(item.getHTrace(), "无");
        }).collect(Collectors.groupingBy(
                CellPlanLineDTO::getHTrace,
                Collectors.groupingBy(
                        CellPlanLineDTO::getHChangeFlag
                )
        ));
        //3、获取当天无数据
        List<CellPlanLineDTO> wuDatas = collect.stream().filter(item -> {
            return Objects.equals(item.getHTrace(), "无");
        }).collect(toList());
        //4、获取excel当天h数据类型
        List<String> keysH = excelRateMap.keySet().stream().filter(item -> {
            return !Objects.equals(item, "无");
        }).collect(toList());
        //--------------------
        //有h数据
        if (CollectionUtils.isNotEmpty(keysH)) {
            //分别对h数据处理
            for (String key : keysH) {
                BigDecimal excelValue = excelRateMap.get(key);
                BigDecimal value = Optional.ofNullable(rateMap.get(key)).orElse(BigDecimal.ZERO);
                //获取h兼容数据
                List<CellPlanLineDTO> hDataY = hDataMap.computeIfAbsent(key, k -> Maps.newHashMap()).computeIfAbsent("Y", k -> Lists.newArrayList());
                if (excelValue.compareTo(value) > 0) {
                    //H增加，无减少
                    //差异量
                    BigDecimal total = excelValue.subtract(value);
                    //冲销无的量
                    if (CollectionUtils.isEmpty(wuDatas)) {
                        throw new RuntimeException("没有H追溯为无的数据,无法调整");
                    }

                    for (CellPlanLineDTO wuData : wuDatas) {
                        if (wuData.getQtyPc().compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        if (wuData.getQtyPc().compareTo(total) >= 0) {
                            wuData.setQtyPc(wuData.getQtyPc().subtract(total));
                            details.add(wuData);
                            CellPlanLineDTO hData = new CellPlanLineDTO();
                            BeanUtils.copyProperties(wuData, hData, "id");
                            hData.setId(null);
                            hData.setQtyPc(total);
                            hData.setHChangeFlag("Y");
                            hData.setHTrace(key);
                            hData.setHTraceId(lovHtrace.get(key));
                            total = BigDecimal.ZERO;
                            details.add(hData);
                            break;
                        } else {
                            total = total.subtract(wuData.getQtyPc());
                            CellPlanLineDTO hData = new CellPlanLineDTO();
                            BeanUtils.copyProperties(wuData, hData, "id");
                            hData.setId(null);
                            hData.setQtyPc(wuData.getQtyPc());
                            wuData.setQtyPc(BigDecimal.ZERO);
                            details.add(wuData);
                            hData.setHChangeFlag("Y");
                            hData.setHTrace(key);
                            hData.setHTraceId(lovHtrace.get(key));
                            details.add(hData);
                        }
                    }


                } else if (excelValue.compareTo(value) < 0) {
                    //H减少，无增加
                    if (CollectionUtils.isEmpty(hDataY)) {
                        throw new RuntimeException("没有H追溯为" + key + "的数据量可调整");
                    }
                    BigDecimal total = value.subtract(excelValue);
                    for (CellPlanLineDTO dto : hDataY) {
                        if (dto.getQtyPc().compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        if (dto.getQtyPc().compareTo(total) >= 0) {
                            dto.setQtyPc(dto.getQtyPc().subtract(total));
                            CellPlanLineDTO wuData = new CellPlanLineDTO();
                            BeanUtils.copyProperties(dto, wuData, "id");
                            wuData.setQtyPc(total);
                            wuData.setHChangeFlag("N");
                            wuData.setHTrace("无");
                            wuData.setHTraceId(lovHtrace.get("无"));
                            details.add(wuData);
                            details.add(dto);
                            total = BigDecimal.ZERO;
                            break;
                        } else {
                            total = total.subtract(dto.getQtyPc());
                            CellPlanLineDTO wuData = new CellPlanLineDTO();
                            BeanUtils.copyProperties(dto, wuData, "id");
                            wuData.setId(null);
                            wuData.setQtyPc(dto.getQtyPc());
                            wuData.setHChangeFlag("N");
                            wuData.setHTrace("无");
                            wuData.setHTraceId(lovHtrace.get("无"));
                            details.add(wuData);
                            dto.setQtyPc(BigDecimal.ZERO);
                            details.add(dto);

                        }

                    }
                }


            }
        }

    }

    /**
     * 根据投产数量计算入库排产数量
     *
     * @param dtos
     */
    @Override
    public void calcOldQtyPc(List<CellPlanLineDTO> dtos) {
        CellFineQuery cellFineQuery = new CellFineQuery();
        cellFineQuery.setPageNumber(1);
        cellFineQuery.setPageSize(GlobalConstant.max_page_size);
        Integer year = DateUtil.getYear(dtos.get(0).getOldMonth());
        Integer lastYear = year - 1;
        cellFineQuery.setYear(year);
        Page<CellFineDTO> page = cellFineService.queryByPage(cellFineQuery);
        List<CellFineDTO> allCellFines = new ArrayList<>();
        if (page != null) {
            if (CollectionUtils.isNotEmpty(page.getContent())) {
                allCellFines.addAll(page.getContent());
            }
        }
        cellFineQuery.setYear(lastYear);
        page = cellFineService.queryByPage(cellFineQuery);
        if (page != null) {
            if (CollectionUtils.isNotEmpty(page.getContent())) {
                allCellFines.addAll(page.getContent());
            }
        }
        //构建良率map
        LinkedHashMap<String, CellFineDTO> cellFineDTOMap = allCellFines.stream().collect(Collectors.toMap(
                fine -> {
                    return StringTools.joinWith(",", fine.getYear() + "", fine.getBasePlace(), fine.getWorkshop(), fine.getCellsType());
                },
                Function.identity(),
                (existing, replacement) -> replacement,
                LinkedHashMap::new
        ));
        dtos.forEach(cellPlanLine -> {
            //考虑片源级投产良率（2024-05-22）
            BigDecimal qtyPc = cellPlanLine.getQtyPc();
            BigDecimal oldQtyPc = qtyPc;
            if (Objects.nonNull(cellPlanLine.getWaferYieldRatio()) && cellPlanLine.getWaferYieldRatio().compareTo(BigDecimal.ZERO) != 0) {
                oldQtyPc = oldQtyPc.multiply(cellPlanLine.getWaferYieldRatio());

            }
            LocalDateTime startTime = cellPlanLine.getStartTime();
            //获取对应良率信息
            String fineKey = StringTools.joinWith(",", startTime.getYear() + "", cellPlanLine.getBasePlace(), cellPlanLine.getWorkshop(), cellPlanLine.getCellsType());
            CellFineDTO cellFineDTO = cellFineDTOMap.get(fineKey);
            int month = startTime.getMonthValue();
            if (cellFineDTO != null) {
                String fineValueResult = ReflectUtil.invoke(cellFineDTO, "getM" + month);

                if (StringUtils.isNotEmpty(fineValueResult)) {

                    BigDecimal fineValue = MapStrutUtil.removePercentage(fineValueResult, 4);
                    //转入库排产数据
                    if (Objects.nonNull(fineValue)) {
                        if (fineValue.compareTo(BigDecimal.ZERO) != 0) {
                            oldQtyPc = oldQtyPc.multiply(fineValue);

                        }
                    }

                }
            }
            //进行了硅片等级良率指定
            if (Objects.equals(cellPlanLine.getIsWaferGrade(), 1)) {
                if (Objects.nonNull(cellPlanLine.getCellFine()) && cellPlanLine.getCellFine().compareTo(BigDecimal.ZERO) > 0) {
                    oldQtyPc = oldQtyPc.multiply(cellPlanLine.getCellFine());
                }
            }
            cellPlanLine.setOldQtyPc(oldQtyPc);
        });

    }

    private void checkInput(List<CellPlanLineTotalHExcelDTO> excelDtos, List<CellPlanLineTotalHDTO> hdtos, List<CellPlanLineTotalHDTO> oldDtos) {
        //验证重复
        checkUnique(excelDtos);
        //验证数值（总数）与原值不一致
        checkValue(excelDtos, hdtos);
        //验证H原数据（调整后的H不能小于最初的H值）
        checkOldHDataValue(excelDtos, oldDtos);
    }

    private void checkOldHDataValue(List<CellPlanLineTotalHExcelDTO> excelDtos, List<CellPlanLineTotalHDTO> oldDtos) {
        Map<String, CellPlanLineTotalHExcelDTO> dtosMap = excelDtos.stream().filter(
                item -> {
                    return StringUtils.isNotBlank(item.getHTrace()) && !StringUtils.equals(item.getHTrace(), "无");
                }
        ).collect(Collectors.toMap(
                item -> {
                    return getHkey(item);
                },
                Function.identity(),
                (old, newItem) -> {
                    return old;
                }
        ));
        Map<String, CellPlanLineTotalHDTO> oldDtosMap = oldDtos.stream().filter(
                item -> {
                    return StringUtils.isNotBlank(item.getHTrace()) && !StringUtils.equals(item.getHTrace(), "无");
                }
        ).collect(Collectors.toMap(
                item -> {
                    return getHkey(item);
                },
                Function.identity(),
                (old, newItem) -> {
                    return old;
                }
        ));
        List<String> keys = oldDtosMap.keySet().stream().sorted(Comparator.naturalOrder()).collect(toList());
        for (String key : keys) {
            if (dtosMap.containsKey(key)) {
                CellPlanLineTotalHDTO oldDto = oldDtosMap.get(key);
                CellPlanLineTotalHExcelDTO dto = dtosMap.get(key);
                //判断d1~d31H值是否小于原值
                for (int i = 1; i <= 31; i++) {
                    BigDecimal oldH = ReflectUtil.invoke(oldDto, "getD" + i);
                    oldH = Optional.ofNullable(oldH).orElse(BigDecimal.ZERO);
                    BigDecimal newH = ReflectUtil.invoke(dto, "getD" + i);
                    newH = Optional.ofNullable(newH).orElse(BigDecimal.ZERO);
                    if (newH.compareTo(oldH) < 0) {
                        LocalDate localDate = DateUtil.getLocalDate(oldDto.getMonth(), i);
                        String message = String.format("%s的 %s %s 值不能小于原值%s", getErrorTitle(oldDto), DateUtil.formatLocalDateToString(localDate, "yyyy-MM-dd"), oldDto.getHTrace(), oldH.stripTrailingZeros());
                        throw new BizException(message);
                    }
                }
            }
        }

    }

    private String getErrorTitle(CellPlanLineTotalHDTO item) {
        return Joiner.on(",").skipNulls().join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                item.getSiMfrs(), item.getMonth()
        );
    }

    private String getHkey(CellPlanLineTotalHExcelDTO item) {
        return Joiner.on(",").useForNull("").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                item.getSiMfrs(),
                item.getMonth(), item.getHTrace());
    }

    private String getHkey(CellPlanLineTotalHDTO item) {
        return Joiner.on(",").useForNull("").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                item.getSiMfrs(),

                item.getMonth(), item.getHTrace());
    }

    private void checkValue(List<CellPlanLineTotalHExcelDTO> excelDtos, List<CellPlanLineTotalHDTO> oldExcelDtos) {
        Map<String, List<CellPlanLineTotalHExcelDTO>> dtosMap = excelDtos.stream().collect(Collectors.groupingBy(item -> {
            return Joiner.on(",").useForNull("").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                    item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                    item.getSiMfrs(), item.getMonth());
        }));
        Map<String, List<CellPlanLineTotalHDTO>> oldDtosMap = oldExcelDtos.stream().collect(Collectors.groupingBy(item -> {
            return Joiner.on(",").useForNull("").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                    item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                    item.getSiMfrs(), item.getMonth());
        }));

        for (Map.Entry<String, List<CellPlanLineTotalHExcelDTO>> entry : dtosMap.entrySet()) {
            String key = entry.getKey();
            List<CellPlanLineTotalHDTO> oldDtos = oldDtosMap.get(key);
            if (CollectionUtils.isEmpty(oldDtos)) {
                throw new BizException(key + "在原始数据中不存在！");
            }
            List<CellPlanLineTotalHExcelDTO> dtos = entry.getValue();
            for (int day = 1; day <= 31; day++) {
                int finalDay = day;
                BigDecimal oldValue = oldDtos.stream().map(item ->
                        ReflectUtil.getFieldValue(item, "d" + finalDay)
                ).filter(Objects::nonNull).map(BigDecimal.class::cast).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal value = dtos.stream().map(item ->
                        ReflectUtil.getFieldValue(item, "d" + finalDay)
                ).filter(Objects::nonNull).map(BigDecimal.class::cast).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (oldValue.compareTo(value) != 0) {
                    String message = String.format("%s在 %s 现总量(%s)与原总量(%s)不一致", getErrorTitle(oldDtos.get(0)), DateUtil.formatLocalDateToString(DateUtil.getLocalDate(dtos.get(0).getMonth(), day), "yyyy-MM-dd"), value.stripTrailingZeros(), oldValue.stripTrailingZeros());
                    throw new BizException(message);
                }
            }
        }
    }

    private void checkUnique(List<CellPlanLineTotalHExcelDTO> excelDtos) {
        Set<String> htraceSet = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.H_TRACE).values().stream().map(
                LovLineDTO::getLovName
        ).collect(Collectors.toSet());
        //验证重复
        Map<String, List<Integer>> map = Maps.newHashMap();
        List<String> errors = new ArrayList<>();
        for (int i = 0; i < excelDtos.size(); i++) {
            CellPlanLineTotalHExcelDTO item = excelDtos.get(i);
            if (StringUtils.isNotBlank(item.getHTrace())) {
                if (!htraceSet.contains(item.getHTrace())) {
                    errors.add("第" + (i + 1) + "行录入了" + item.getHTrace() + "不存在的H追溯值");
                }
            }
            String key = Joiner.on(",").useForNull("null").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                    item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                    item.getSiMfrs(), item.getMonth(), item.getHTrace());
            if (map.containsKey(key)) {
                map.get(key).add(i + 1);
            } else {
                List<Integer> list = Lists.newArrayList();
                list.add(i + 1);
                map.put(key, list);
            }
        }

        map.forEach((k, v) -> {
            if (v.size() > 1) {
                errors.add("第" + Joiner.on(",").skipNulls().join(v) + "行数据重复");
            }
        });
        if (errors.size() > 0) {
            throw new BizException(errors.stream().collect(Collectors.joining("\n")));
        }
    }

    private List<CellPlanLineTotalHDTO> queryHDatasByVersion(CellPlanLineQuery query) {
        //1、获取数据
        QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;
        JPAQuery<CellPlanLine> where = jpaQueryFactory.from(qCellPlanLine).select(qCellPlanLine).where(qCellPlanLine.version.eq(query.getVersion()))
                .where(qCellPlanLine.oldMonth.eq(query.getMonth()))
                .where(qCellPlanLine.isOversea.eq(query.getIsOversea()));
        List<CellPlanLineDTO> datas = cellPlanLineDEConvert.toDto(where.fetch());
        //1.1获取有可靠性验证的数据
//        List<CellPlanLineDTO> datasHaveVerificationMark = datas.stream().filter(item -> StringUtils.isNotBlank(item.getVerificationMark())).collect(toList());
        List<CellPlanLineTotalHDTO> hDtos = Lists.newArrayList();
//        groupDataHaveVerificationMarkMap(datasHaveVerificationMark, hDtos);
        //1.2获取没有可靠性验证的数据
        //  List<CellPlanLineDTO> datasHaveNoVerificationMark = datas.stream().filter(item -> StringUtils.isBlank(item.getVerificationMark())).collect(toList());
        groupDataHaveNoVerificationMarkMap(datas, hDtos);
        return hDtos;
    }

    private void groupDataHaveNoVerificationMarkMap(List<CellPlanLineDTO> datas, List<CellPlanLineTotalHDTO> hDtos) {
        Map<String, List<CellPlanLineDTO>> collect = datas.stream().collect(
                Collectors.groupingBy(item -> {
                    return Joiner.on("♠").useForNull("null").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                            item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                            item.getSiMfrs(), item.getMonth(), item.getHTrace());
                })
        );
        //依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        //3、统计
        for (String key : keys) {
            CellPlanLineTotalHDTO cellPlanLineTotalHDTO = null;
            List<CellPlanLineDTO> dtos = collect.get(key);
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            //对每组数据统计，每组最终对应一个CellPlanLineTotalDTO对象
            for (CellPlanLineDTO dto : dtos) {
                if (Objects.isNull(cellPlanLineTotalHDTO)) {
                    cellPlanLineTotalHDTO = convert.toCellPlanLineTotaHlDTO(dto);
                    cellPlanLineTotalHDTO.setWorkunit(null);
                    cellPlanLineTotalHDTO.setLineName(null);
                }
                cellPlanLineTotalHDTO.getDetails().add(dto);
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellPlanLineTotalHDTO, "getD" + day);
                BigDecimal value = dto.getQtyPc();
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, addVal);
                }
            }
            setScale(cellPlanLineTotalHDTO);
            hDtos.add(cellPlanLineTotalHDTO);
        }
    }

    private void groupDataHaveVerificationMarkMap(List<CellPlanLineDTO> datas, List<CellPlanLineTotalHDTO> hDtos) {
        Map<String, List<CellPlanLineDTO>> collect = datas.stream().collect(
                Collectors.groupingBy(item -> {
                    return Joiner.on("♠").useForNull("null").join(item.getIsOversea(), item.getBasePlace(), item.getWorkshop(),
                            item.getCellsType(), item.getCellSource(), item.getIsSpecialRequirement(),
                            item.getSiMfrs(),
                            item.getMonth(), item.getHTrace());
                })
        );
        //依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());
        //3、统计
        for (String key : keys) {
            CellPlanLineTotalHDTO cellPlanLineTotalHDTO = null;
            List<CellPlanLineDTO> dtos = collect.get(key);
            //对每组数据统计，每组最终对应一个CellPlanLineTotalDTO对象
            for (CellPlanLineDTO dto : dtos) {
                if (Objects.isNull(cellPlanLineTotalHDTO)) {
                    cellPlanLineTotalHDTO = convert.toCellPlanLineTotaHlDTO(dto);
                }
                cellPlanLineTotalHDTO.getDetails().add(dto);
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellPlanLineTotalHDTO, "getD" + day);
                BigDecimal value = dto.getQtyPc();
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellPlanLineTotalHDTO, "setD" + day, addVal);
                }
            }
            setScale(cellPlanLineTotalHDTO);
            hDtos.add(cellPlanLineTotalHDTO);
        }
    }

    private List<CellPlanLineTotalHExcelDTO> getImportData(MultipartFile multipartFile, CellPlanLineTotalHQuery query) throws IOException {
        List<CellPlanLineTotalHExcelDTO> excelDtos = new LinkedList<>();
        // 这里为了简单 所以注册了 同样的head 和Listener 自己使用功能必须不同的Listener
        ExcelReaderBuilder readerBuilder = EasyExcel.read(multipartFile.getInputStream(), CellPlanLineTotalHExcelDTO.class, new ReadListener<CellPlanLineTotalHExcelDTO>() {
            @Override
            public void invoke(CellPlanLineTotalHExcelDTO data, AnalysisContext context) {
                excelDtos.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        });
        readerBuilder.registerConverter(new LocalDateConverter());
        readerBuilder.sheet(0).doRead();
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("导入数据为空");
        } else {
            query.setVersion(excelDtos.get(0).getVersion());
        }
        final QuerySheetExcelDTO[] querySheetExcelDTO = {null};
        EasyExcel.read(multipartFile.getInputStream(), QuerySheetExcelDTO.class, new ReadListener<QuerySheetExcelDTO>() {
            @Override
            public void invoke(QuerySheetExcelDTO data, AnalysisContext context) {
                querySheetExcelDTO[0] = data;
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet(1).doRead();
        QuerySheetExcelDTO queryParam = querySheetExcelDTO[0];

        if (Objects.isNull(queryParam)) {
            throw new BizException("导入查询条件导入为空,请检查导入文件是否正确");
        }

        // 查询原始行

        CellPlanLineTotalHQuery cellPlanLineTotalQuery = JSON.parseObject(queryParam.getQueryParam(), CellPlanLineTotalHQuery.class);
        BeanUtils.copyProperties(cellPlanLineTotalQuery, query);
        handlerNull(excelDtos);
        return excelDtos;
    }

    private void handlerNull(List<CellPlanLineTotalHExcelDTO> excelDtos) {
        if (CollectionUtils.isNotEmpty(excelDtos)) {
            excelDtos.forEach(item -> {
                if (StringUtils.isBlank(item.getHTrace())) {
                    item.setHTrace("无");

                }
            });
        }
    }

    public void checkContrast(CellPlanLineTotalQuery query
            ,Pair<String, String> versions
            ,Page<CellPlanLineTotalDTO> cellPlanLineTotalDTOS
                              ) {
        Pair<String, String> sendedEmailVersions = this.getSendedEmailOrLastVersion(query, true);
        Boolean sameVersionFlag = (Objects.isNull(sendedEmailVersions.getLeft()) && Objects.isNull(sendedEmailVersions.getRight()))
                || ((Objects.nonNull(versions.getLeft()) && Objects.nonNull(versions.getLeft()) && Objects.equals(versions.getLeft(), sendedEmailVersions.getLeft()))
                && (Objects.nonNull(versions.getRight()) && Objects.nonNull(versions.getRight()) && Objects.equals(versions.getRight(), sendedEmailVersions.getRight())));
        Page<CellPlanLineTotalDTO> sendedEmailDTOS = null;
        Map<String, CellPlanLineTotalDTO> sendedEmailDTOMap;
        if (!sameVersionFlag) {
            query.setPageNumber(1);
            query.setPageSize(Integer.MAX_VALUE);
            sendedEmailDTOS = this.queryByPage(query, sendedEmailVersions);
            sendedEmailDTOMap = sendedEmailDTOS.getContent().stream().collect(Collectors.toMap(CellPlanLineTotalDTO::group, Function.identity(), (v1, v2) -> v1));
        } else {
            sendedEmailDTOMap = null;
        }

        cellPlanLineTotalDTOS.getContent().forEach(ele -> {
            if (MapUtils.isNotEmpty(ele.getDataStructures())) {
                TreeMap<String, String> dataStructures = ele.getDataStructures();
                dataStructures.forEach((k, v) -> {
                    dataStructures.put(k, !StringUtils.isEmpty(v) ? String.valueOf(new BigDecimal(v).setScale(2, RoundingMode.HALF_UP)) : "");
                });
                ele.setDataStructures(dataStructures);
            }
            CellPlanLineTotalDTO oldEmailData = null==sendedEmailDTOMap?null:sendedEmailDTOMap.get(ele.group());
            CellPlanLineTotalDTO.class.getDeclaredFields();
            if (sameVersionFlag) {
                ele.setChangeStatusDesc(PlanChangeStatusEnum.UNCHANGE.getDesc());
            } else {
                //每天需要对比
                try{
                    changeContrast(oldEmailData,ele);
                }catch (Exception e){
                    log.error(ExceptionUtil.exceptionChainToString(e));
                    throw new BizException("数据比对失败，请联系运维人员处理");
                }
            }
        });
    }

    /**
     * 对比前后的d1-d31的改动
     * @param source 上一次发送邮件的数据
     * @param target
     */
    public List<CellPlanLineContrastDTO> changeContrast(CellPlanLineTotalDTO source,CellPlanLineTotalDTO target) throws NoSuchFieldException, IllegalAccessException {
        List<CellPlanLineContrastDTO> contrasList = Lists.newArrayList();
        target.setChangeStatusDesc(PlanChangeStatusEnum.getDescForCompare(target, source));
        for(String key:target.getDataStructures().keySet()){
            String targetValue = target.getDataStructures().get(key);
            CellPlanLineContrastDTO dto = new CellPlanLineContrastDTO();
            dto.setName(key);
            if(StringUtils.isBlank(targetValue)){
                dto.setValue(BigDecimal.ZERO);
            }else{
                dto.setValue(new BigDecimal(targetValue));
            }
            dto.setChangeFlag(0);

            if(ObjectUtils.isNotEmpty(source)){
                //没有对比数据时不进入逻辑
                String sourceValue = source.getDataStructures().get(key);
                targetValue = StringUtils.isBlank(targetValue)?"0":new BigDecimal(targetValue).setScale(2,RoundingMode.HALF_UP).toString();
                sourceValue = StringUtils.isBlank(sourceValue)?"0":new BigDecimal(sourceValue).setScale(2,RoundingMode.HALF_UP).toString();
                if( (StringUtils.isNotEmpty(sourceValue) && StringUtils.isNotEmpty(targetValue) &&
                        new BigDecimal(targetValue).compareTo(new BigDecimal(sourceValue))!=0)){
                    dto.setChangeFlag(1);
                    target.setChangeStatusDesc(PlanChangeStatusEnum.CHANGEED.getDesc());
                }
            }

            contrasList.add(dto);
        }
        target.setChangeContrastList(contrasList);
        return contrasList;
    }

}

@Data
@Builder
class BatchNoToVersion {
    String batchNo;
    String version;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BatchNoToVersion that = (BatchNoToVersion) o;
        return Objects.equals(batchNo, that.batchNo) && Objects.equals(version, that.version);
    }

    @Override
    public int hashCode() {
        return Objects.hash(batchNo, version);
    }


}
