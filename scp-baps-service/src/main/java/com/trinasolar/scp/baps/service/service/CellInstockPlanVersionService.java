package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellInstockPlanVersionDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanVersionQueryDto;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionDTO;
import com.trinasolar.scp.baps.domain.entity.CellInstockPlanVersion;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanVersionQuery;
import com.trinasolar.scp.baps.domain.query.InstockPlanVersionQuery;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanVersionSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 入库计划版本管理表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
public interface CellInstockPlanVersionService {
    /**
     * 分页获取入库计划版本管理表
     *
     * @param query 查询对象
     * @return 入库计划版本管理表分页对象
     */
    Page<CellInstockPlanVersionDTO> queryByPage(CellInstockPlanVersionQuery query);

    /**
     * 根据主键获取入库计划版本管理表详情
     *
     * @param id 主键
     * @return 入库计划版本管理表详情
     */
    CellInstockPlanVersionDTO queryById(Long id);

    /**
     * 保存或更新入库计划版本管理表
     *
     * @param saveDTO 入库计划版本管理表保存对象
     * @return 入库计划版本管理表对象
     */
    CellInstockPlanVersionDTO save(CellInstockPlanVersionSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除入库计划版本管理表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(CellInstockPlanVersionQuery query, HttpServletResponse response);

    CellInstockPlanVersionDTO query(CellInstockPlanVersionQueryDto query);

    List<String> getAllVersions(InstockPlanVersionQuery query);

    String findMaxVersion(Long isOverseaId, String month);

    boolean checkVersion(Long isOverseaId, String month, String planVersion);

    /**
     * 最大版本号 ()
     *
     * @param isOverseaId
     * @param month
     * @return
     */
    String findMaxVersionSendedEmail(Long isOverseaId, String month);
}

