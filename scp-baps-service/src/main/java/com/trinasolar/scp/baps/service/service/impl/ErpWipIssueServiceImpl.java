package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.ErpWipIssueDTO;
import com.trinasolar.scp.baps.domain.convert.ErpWipIssueDEConvert;
import com.trinasolar.scp.baps.domain.entity.ErpWipIssue;
import com.trinasolar.scp.baps.domain.entity.QErpWipIssue;
import com.trinasolar.scp.baps.domain.excel.ErpWipIssueExcelDTO;
import com.trinasolar.scp.baps.domain.query.ErpWipIssueQuery;
import com.trinasolar.scp.baps.domain.save.ErpWipIssueSaveDTO;
import com.trinasolar.scp.baps.service.repository.ErpWipIssueRepository;
import com.trinasolar.scp.baps.service.service.ErpWipIssueService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.LocalDateConverter;
import com.trinasolar.scp.common.api.util.LocalDateTimeConverter;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Erp实际入库来源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-04 09:23:58
 */
@Slf4j
@Service("erpWipIssueService")
@RequiredArgsConstructor
public class ErpWipIssueServiceImpl implements ErpWipIssueService {
    private static final QErpWipIssue qErpWipIssue = QErpWipIssue.erpWipIssue;

    private final ErpWipIssueDEConvert convert;

    private final ErpWipIssueRepository repository;

    @Override
    public Page<ErpWipIssueDTO> queryByPage(ErpWipIssueQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ErpWipIssue> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ErpWipIssueQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qErpWipIssue.id.eq(query.getId()));
        }
        if (query.getTransactionQuantity() != null) {
            booleanBuilder.and(qErpWipIssue.transactionQuantity.eq(query.getTransactionQuantity()));
        }
        if(StringUtils.isNotEmpty(query.getItemNumber())){
            booleanBuilder.and(qErpWipIssue.itemNumber.eq(query.getItemNumber()));
        }
        if (query.getTransferOrganizationId() != null) {
            booleanBuilder.and(qErpWipIssue.transferOrganizationId.eq(query.getTransferOrganizationId()));
        }
        if(StringUtils.isNotEmpty(query.getTransactionUomCode())){
            booleanBuilder.and(qErpWipIssue.transactionUomCode.eq(query.getTransactionUomCode()));
        }
        if (query.getOrganizationId() != null) {
            booleanBuilder.and(qErpWipIssue.organizationId.eq(query.getOrganizationId()));
        }
        if(StringUtils.isNotEmpty(query.getItemDescription())){
            booleanBuilder.and(qErpWipIssue.itemDescription.eq(query.getItemDescription()));
        }
        if(StringUtils.isNotEmpty(query.getLocatorCode())){
            booleanBuilder.and(qErpWipIssue.locatorCode.eq(query.getLocatorCode()));
        }
        if(StringUtils.isNotEmpty(query.getTransactionSourceTypeName())){
            booleanBuilder.and(qErpWipIssue.transactionSourceTypeName.eq(query.getTransactionSourceTypeName()));
        }
        if(StringUtils.isNotEmpty(query.getWorkOrder())){
            booleanBuilder.and(qErpWipIssue.workOrder.eq(query.getWorkOrder()));
        }
        if(StringUtils.isNotEmpty(query.getOrganizationName())){
            booleanBuilder.and(qErpWipIssue.organizationName.eq(query.getOrganizationName()));
        }
        if (query.getTransactionTypeId() != null) {
            booleanBuilder.and(qErpWipIssue.transactionTypeId.eq(query.getTransactionTypeId()));
        }
        if (query.getLotPrimaryQuantity() != null) {
            booleanBuilder.and(qErpWipIssue.lotPrimaryQuantity.eq(query.getLotPrimaryQuantity()));
        }
        if(StringUtils.isNotEmpty(query.getSubinventoryCode())){
            booleanBuilder.and(qErpWipIssue.subinventoryCode.eq(query.getSubinventoryCode()));
        }
        if(StringUtils.isNotEmpty(query.getLotNumber())){
            booleanBuilder.and(qErpWipIssue.lotNumber.eq(query.getLotNumber()));
        }
        if (query.getTransactionDate() != null) {
            booleanBuilder.and(qErpWipIssue.transactionDate.eq(query.getTransactionDate()));
        }
        if (query.getTransactionSourceId() != null) {
            booleanBuilder.and(qErpWipIssue.transactionSourceId.eq(query.getTransactionSourceId()));
        }
        if(StringUtils.isNotEmpty(query.getTransactionTypeName())){
            booleanBuilder.and(qErpWipIssue.transactionTypeName.eq(query.getTransactionTypeName()));
        }
        if (query.getTransactionId() != null) {
            booleanBuilder.and(qErpWipIssue.transactionId.eq(query.getTransactionId()));
        }
        if (query.getTransactionSourceTypeId() != null) {
            booleanBuilder.and(qErpWipIssue.transactionSourceTypeId.eq(query.getTransactionSourceTypeId()));
        }
        if (query.getInventoryItemId() != null) {
            booleanBuilder.and(qErpWipIssue.inventoryItemId.eq(query.getInventoryItemId()));
        }
        if(StringUtils.isNotEmpty(query.getOrganizationCode())){
            booleanBuilder.and(qErpWipIssue.organizationCode.eq(query.getOrganizationCode()));
        }
        if (query.getLocatorId() != null) {
            booleanBuilder.and(qErpWipIssue.locatorId.eq(query.getLocatorId()));
        }
        if (query.getPrimaryQuantity() != null) {
            booleanBuilder.and(qErpWipIssue.primaryQuantity.eq(query.getPrimaryQuantity()));
        }
        if (query.getLotTransactionQuantity() != null) {
            booleanBuilder.and(qErpWipIssue.lotTransactionQuantity.eq(query.getLotTransactionQuantity()));
        }
        if (query.getCreationDate() != null) {
            booleanBuilder.and(qErpWipIssue.creationDate.eq(query.getCreationDate()));
        }
        if(StringUtils.isNotEmpty(query.getRemark())){
            booleanBuilder.and(qErpWipIssue.remark.eq(query.getRemark()));
        }
    }

    @Override
    public ErpWipIssueDTO queryById(Long id) {
        ErpWipIssue queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public ErpWipIssueDTO save(ErpWipIssueSaveDTO saveDTO) {
        ErpWipIssue newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new ErpWipIssue());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(ErpWipIssueQuery query, HttpServletResponse response) {
       List<ErpWipIssueDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "Erp实际入库来源表", "Erp实际入库来源表", excelPara.getSimpleHeader(), excelData);
    }
}
