package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ibm.dpf.base.core.util.DateUtils;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.DeliveryHolidayDTO;
import com.trinasolar.scp.baps.domain.convert.DeliveryHolidayDEConvert;
import com.trinasolar.scp.baps.domain.entity.DeliveryHoliday;
import com.trinasolar.scp.baps.domain.entity.QDeliveryHoliday;
import com.trinasolar.scp.baps.domain.excel.DeliveryHolidayExcelDTO;
import com.trinasolar.scp.baps.domain.query.DeliveryHolidayQuery;
import com.trinasolar.scp.baps.domain.save.DeliveryHolidaySaveDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.repository.DeliveryHolidayRepository;
import com.trinasolar.scp.baps.service.service.DeliveryHolidayService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;

/**
 * 物流节假日表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
@Slf4j
@Service("deliveryHolidayService")
@RequiredArgsConstructor
public class DeliveryHolidayServiceImpl implements DeliveryHolidayService {
    private static final QDeliveryHoliday qDeliveryHoliday = QDeliveryHoliday.deliveryHoliday;

    private final DeliveryHolidayDEConvert convert;

    private final DeliveryHolidayRepository repository;

    @Override
    public Page<DeliveryHolidayDTO> queryByPage(DeliveryHolidayQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<DeliveryHoliday> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, DeliveryHolidayQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qDeliveryHoliday.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getIsOversea())){
            booleanBuilder.and(qDeliveryHoliday.isOversea.eq(query.getIsOversea()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlaceFrom())){
            booleanBuilder.and(qDeliveryHoliday.basePlaceFrom.eq(query.getBasePlaceFrom()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlaceTo())){
            booleanBuilder.and(qDeliveryHoliday.basePlaceTo.eq(query.getBasePlaceTo()));
        }
        if(StringUtils.isNotEmpty(query.getMonth())){
            booleanBuilder.and(qDeliveryHoliday.month.eq(query.getMonth()));
        }
        if(StringUtils.isNotEmpty(query.getDateBetween())){
            booleanBuilder.and(qDeliveryHoliday.dateBetween.eq(query.getDateBetween()));
        }
        if(StringUtils.isNotEmpty(query.getRemark())){
            booleanBuilder.and(qDeliveryHoliday.remark.eq(query.getRemark()));
        }
    }

    @Override
    public DeliveryHolidayDTO queryById(Long id) {
        DeliveryHoliday queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public DeliveryHolidayDTO save(DeliveryHolidaySaveDTO saveDTO) {
        DeliveryHoliday newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new DeliveryHoliday());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(DeliveryHolidayQuery query, HttpServletResponse response) {
       List<DeliveryHolidayDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "物流节假日表", "物流节假日表", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAll(List<DeliveryHolidaySaveDTO> saveDTOS) {
        //构建以账套为key，lov为值的map（海外一个账套只对应一个基地）
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BASE_PLACE);
        Map<String,String> mapByBooks=new HashMap<>();
        Collection<LovLineDTO> values = allByHeaderCode.values();

        values.stream().forEach(value->{
            String books= value.getAttribute3();//账套
            String basePlace=value.getLovName();//基地名称
            String oversea_id=value.getAttribute2();
            if (OverseaConstant.OVERSEA_ID.equals(Long.parseLong(oversea_id))){
                if (!mapByBooks.containsKey(books)){
                    mapByBooks.put(books,basePlace);
                }
            }


        });

        List<DeliveryHolidaySaveDTO> filterDtos = saveDTOS.stream().filter(item -> {
            return item.getIsOversea().equals(OverseaConstant.OVERSEA_VALUE);//只要海外数据
        }).collect(Collectors.toList());
        Set<String> months = filterDtos.stream().map(DeliveryHolidaySaveDTO::getMonth).collect(Collectors.toSet());
        months.stream().forEach(month->{
            //删除原来月份数据
            repository.deleteByMonth(month);
        });

        List<DeliveryHoliday> datas=new ArrayList<>();

        filterDtos.stream().forEach(item->{
             item.setId(null);
             //国内海外
            String isOversea=item.getIsOversea().equals(OverseaConstant.INLAND_VALUE)?OverseaConstant.INLAND:OverseaConstant.OVERSEA;
            item.setIsOversea(isOversea);
            //发货基地->依据发货账套获取发货基地
             String booksFrom=  item.getBooksFrom();
             String basePlaceFrom=mapByBooks.get(booksFrom);
             item.setBasePlaceFrom(basePlaceFrom);
            //到货基地
            String booksTo=  item.getBooksTo();
            String basePlaceTo=mapByBooks.get(booksTo);
            item.setBasePlaceTo(basePlaceTo);
            //月份
            String month=item.getMonth();
            //日期区间
            int dayBegin= item.getDayBegin();
            int dayEnd= item.getDayEnd();
            String dateBetween = DateUtil.getDateScope(month, dayBegin, dayEnd);
            item.setDateBetween(dateBetween);
            //备注
            DeliveryHoliday deliveryHoliday=new DeliveryHoliday();
            datas.add(convert.saveDTOtoEntity(item,deliveryHoliday));
        });
         repository.saveAll(datas);
    }
}
