package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellShipmentPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 可发货计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-28 11:52:39
 */
@Repository
public interface CellShipmentPlanRepository extends JpaRepository<CellShipmentPlan, Long>, QuerydslPredicateExecutor<CellShipmentPlan> {
}
