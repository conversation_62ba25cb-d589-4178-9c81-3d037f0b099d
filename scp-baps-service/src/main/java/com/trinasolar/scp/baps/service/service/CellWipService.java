package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellWipDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellWipQuery;
import com.trinasolar.scp.baps.domain.save.CellWipSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工单生成预览 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
public interface CellWipService {
    /**
     * 分页获取工单生成预览
     *
     * @param query 查询对象
     * @return 工单生成预览分页对象
     */
    Page<CellWipDTO> queryByPage(CellWipQuery query);

    /**
     * 根据主键获取工单生成预览详情
     *
     * @param id 主键
     * @return 工单生成预览详情
     */
        CellWipDTO queryById(Long id);

    /**
     * 保存或更新工单生成预览
     *
     * @param saveDTO 工单生成预览保存对象
     * @return 工单生成预览对象
     */
    CellWipDTO save(CellWipSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除工单生成预览
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellWipQuery query, HttpServletResponse response);

    /**
     * 获取最新版本的数据
     * @param query
     * @return
     */
    List<CellWipDTO> ticketPreview(CellPlanLineQuery query);

    /**
     * 工单生成
     */
    List<CellWipDTO> cellWipCreate(List<CellWipDTO> cellWipDTOs);
    /**
     * 工单生成
     */
    List<CellWipDTO> saveContinueCreateWip(List<CellWipDTO> cellWipDTO);
}

