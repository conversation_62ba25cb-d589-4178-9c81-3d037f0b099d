package com.trinasolar.scp.baps.service.service.aps;

import com.trinasolar.scp.baps.domain.dto.aps.CellShippingDaysDTO;
import com.trinasolar.scp.baps.domain.dto.aps.PowerSupplyAopDTO;
import com.trinasolar.scp.baps.domain.dto.aps.RecordTransitionDTO;

import java.util.List;

public interface ApsService {

    List<RecordTransitionDTO> queryCellMateRules(String... cellsType);

    /**
     * 查找aps运输天数
     *
     * @return
     */
    List<CellShippingDaysDTO> findCellShippingDays();

    List<PowerSupplyAopDTO> getPowerSupplyAopDTOS(String month, String isOverseaName);
}
