package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.convert.CalendarDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellBomManufacturingDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellGradeCapacityDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellBomManufacturingDTO;
import com.trinasolar.scp.baps.domain.dto.CellGradeCapacityDTO;
import com.trinasolar.scp.baps.domain.entity.Calendar;
import com.trinasolar.scp.baps.domain.entity.CellBomManufacturing;
import com.trinasolar.scp.baps.domain.entity.CellGradeCapacity;
import com.trinasolar.scp.baps.domain.entity.QCellGradeCapacity;
import com.trinasolar.scp.baps.domain.excel.CellBaseCapacityDiscountsExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellBaseCapacityExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellGradeCapacityExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellGradeCapacityQuery;
import com.trinasolar.scp.baps.domain.save.CellGradeCapacitySaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CalendarRepository;
import com.trinasolar.scp.baps.service.repository.CellBomManufacturingRepository;
import com.trinasolar.scp.baps.service.repository.CellGradeCapacityRepository;
import com.trinasolar.scp.baps.service.service.CalendarService;
import com.trinasolar.scp.baps.service.service.CellBomManufacturingService;
import com.trinasolar.scp.baps.service.service.CellGradeCapacityService;
import com.trinasolar.scp.baps.service.service.CellResourceService;
import com.trinasolar.scp.baps.service.service.system.SystemService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.criteria.Join;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 爬坡产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Slf4j
@Service("cellGradeCapacityService")
@RequiredArgsConstructor
public class CellGradeCapacityServiceImpl implements CellGradeCapacityService {
    private static final QCellGradeCapacity qCellGradeCapacity = QCellGradeCapacity.cellGradeCapacity;

    private final SystemService systemService;

    private final CellGradeCapacityDEConvert convert;

    private final CellBomManufacturingDEConvert cellBomManufacturingDEConvert;

    private final CellGradeCapacityRepository repository;

    private final CellBomManufacturingRepository cellBomManufacturingRepository;
    private final CellBomManufacturingService cellBomManufacturingService;
    private final CalendarRepository calendarRepository;

    private final CalendarService calendarService;

    private final CalendarDEConvert calendarDEConvert;
    private final CellResourceService cellResourceService;

    @Override
    public Page<CellGradeCapacityDTO> queryByPage(CellGradeCapacityQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.ASC, "isOversea", "basePlace", "workshop", "workunit", "lineName", "cellsType", "month");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellGradeCapacity> page = repository.findAll(booleanBuilder, pageable);
        List<CellGradeCapacityDTO> dtos = convert.toDto(page.getContent());
        dtos = convert.copyDtoNameFromDto(dtos);
        return new PageImpl(dtos, page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<CellGradeCapacityDTO> queryForDm(CellGradeCapacityQuery query) {

        List<CellGradeCapacityDTO> dtos = new ArrayList<>();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        List<String> monthList = query.getMonthList();
        if (CollectionUtils.isNotEmpty(monthList)) {
            for (String month : monthList
            ) {
                query.setMonth(month);
                BooleanBuilder booleanBuilder = new BooleanBuilder();
                buildWhere(booleanBuilder, query);
                Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
                Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
                Page<CellGradeCapacity> page = repository.findAll(booleanBuilder, pageable);
                List<CellGradeCapacityDTO> content = convert.toDto(page.getContent());
                if (CollectionUtils.isNotEmpty(content)) {
                    dtos.addAll(content);
                }
            }

        }
        dtos = convert.copyDtoNameFromDto(dtos);
        return dtos;
    }

    @Override
    public BigDecimal findByCondition(Long isOverseaId, String workshop, LocalDate startDate, LocalDate endDate, String productCategory, String crystalType) {
        Map<String, List<LocalDate>> monthMap = DateUtil.getMonthDate(startDate, endDate);
        //月份集合
        List<String> monthList = DateUtil.getMonthList(startDate, endDate);
        //电池类型值集
        Map<Long, LovLineDTO> lovMap = systemService.findByLovCode(LovHeaderCodeConstant.BATTERY_TYPE, LovLineDTO::getLovLineId, Function.identity());
        String productCategoryId = LovUtils.get(LovHeaderCodeConstant.PRODUCT_CATEGORY, productCategory).getLovLineId().toString();
        String crystalTypeId = LovUtils.get(LovHeaderCodeConstant.CRYSTAL_TYPE, crystalType).getLovLineId().toString();
        //获取对应品类、型号下的电池类型
        List<Long> cellTypeIdList = lovMap.values().stream().filter(l -> StringUtils.isNoneBlank(l.getAttribute2(), l.getAttribute3()) && StringUtils.equals(l.getAttribute2(), crystalTypeId) && StringUtils.equals(l.getAttribute3(), productCategoryId)).map(LovLineDTO::getLovLineId).collect(Collectors.toList());
        //拼接查询条件
        CellGradeCapacityQuery query = new CellGradeCapacityQuery();
        query.setIsOverseaId(isOverseaId);
        query.setMonthList(monthList);
        query.setWorkshop(workshop);
        query.setCellsTypeIdList(cellTypeIdList);
        BooleanBuilder builder = new BooleanBuilder();
        buildWhere(builder, query);
        List<CellGradeCapacity> dataList = IterableUtils.toList(repository.findAll(builder));
        List<CellGradeCapacityDTO> cellGradeCapacityDTOS = convert.toDto(dataList);
        if (CollectionUtils.isNotEmpty(cellGradeCapacityDTOS)) {
            return cellGradeCapacityDTOS.stream().map(data -> {
                String month = data.getMonth();
                if (monthMap.containsKey(month)) {
                    List<LocalDate> dateList = monthMap.get(month);
                    return dateList.stream().map(day -> Optional.ofNullable((BigDecimal) ReflectUtil.getFieldValue(data, String.format("d%s", day))).orElse(BigDecimal.ZERO).multiply(data.getLineNumber())).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                return BigDecimal.ZERO;
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return BigDecimal.ZERO;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellGradeCapacityQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellGradeCapacity.id.eq(query.getId()));
        }
        if (Objects.nonNull(query.getCellsTypeId())) {
            booleanBuilder.and(qCellGradeCapacity.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (Objects.nonNull(query.getIsOverseaId())) {
            booleanBuilder.and(qCellGradeCapacity.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (Objects.nonNull(query.getBasePlaceId())) {
            booleanBuilder.and(qCellGradeCapacity.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (Objects.nonNull(query.getWorkunitid())) {
            booleanBuilder.and(qCellGradeCapacity.workunitid.eq(query.getWorkunitid()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellGradeCapacity.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellGradeCapacity.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellGradeCapacity.basePlace.eq(query.getBasePlace()));
        }
        if (Objects.nonNull(query.getWorkshopid())) {

            booleanBuilder.and(qCellGradeCapacity.workshopid.eq(query.getWorkshopid()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellGradeCapacity.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qCellGradeCapacity.workunit.eq(query.getWorkunit()));
        }
        if (query.getLineNumber() != null) {
            booleanBuilder.and(qCellGradeCapacity.lineNumber.eq(query.getLineNumber()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qCellGradeCapacity.month.eq(query.getMonth()));
        }
        if (query.getCapacityQuantity() != null) {
            booleanBuilder.and(qCellGradeCapacity.capacityQuantity.eq(query.getCapacityQuantity()));
        }
        if (StringUtils.isNotEmpty(query.getUnit())) {
            booleanBuilder.and(qCellGradeCapacity.unit.eq(query.getUnit()));
        }
        if (query.getD1() != null) {
            booleanBuilder.and(qCellGradeCapacity.d1.eq(query.getD1()));
        }
        if (query.getD2() != null) {
            booleanBuilder.and(qCellGradeCapacity.d2.eq(query.getD2()));
        }
        if (query.getD3() != null) {
            booleanBuilder.and(qCellGradeCapacity.d3.eq(query.getD3()));
        }
        if (query.getD4() != null) {
            booleanBuilder.and(qCellGradeCapacity.d4.eq(query.getD4()));
        }
        if (query.getD5() != null) {
            booleanBuilder.and(qCellGradeCapacity.d5.eq(query.getD5()));
        }
        if (query.getD6() != null) {
            booleanBuilder.and(qCellGradeCapacity.d6.eq(query.getD6()));
        }
        if (query.getD7() != null) {
            booleanBuilder.and(qCellGradeCapacity.d7.eq(query.getD7()));
        }
        if (query.getD8() != null) {
            booleanBuilder.and(qCellGradeCapacity.d8.eq(query.getD8()));
        }
        if (query.getD9() != null) {
            booleanBuilder.and(qCellGradeCapacity.d9.eq(query.getD9()));
        }
        if (query.getD10() != null) {
            booleanBuilder.and(qCellGradeCapacity.d10.eq(query.getD10()));
        }
        if (query.getD11() != null) {
            booleanBuilder.and(qCellGradeCapacity.d11.eq(query.getD11()));
        }
        if (query.getD12() != null) {
            booleanBuilder.and(qCellGradeCapacity.d12.eq(query.getD12()));
        }
        if (query.getD13() != null) {
            booleanBuilder.and(qCellGradeCapacity.d13.eq(query.getD13()));
        }
        if (query.getD14() != null) {
            booleanBuilder.and(qCellGradeCapacity.d14.eq(query.getD14()));
        }
        if (query.getD15() != null) {
            booleanBuilder.and(qCellGradeCapacity.d15.eq(query.getD15()));
        }
        if (query.getD16() != null) {
            booleanBuilder.and(qCellGradeCapacity.d16.eq(query.getD16()));
        }
        if (query.getD17() != null) {
            booleanBuilder.and(qCellGradeCapacity.d17.eq(query.getD17()));
        }
        if (query.getD18() != null) {
            booleanBuilder.and(qCellGradeCapacity.d18.eq(query.getD18()));
        }
        if (query.getD19() != null) {
            booleanBuilder.and(qCellGradeCapacity.d19.eq(query.getD19()));
        }
        if (query.getD20() != null) {
            booleanBuilder.and(qCellGradeCapacity.d20.eq(query.getD20()));
        }
        if (query.getD21() != null) {
            booleanBuilder.and(qCellGradeCapacity.d21.eq(query.getD21()));
        }
        if (query.getD22() != null) {
            booleanBuilder.and(qCellGradeCapacity.d22.eq(query.getD22()));
        }
        if (query.getD23() != null) {
            booleanBuilder.and(qCellGradeCapacity.d23.eq(query.getD23()));
        }
        if (query.getD24() != null) {
            booleanBuilder.and(qCellGradeCapacity.d24.eq(query.getD24()));
        }
        if (query.getD25() != null) {
            booleanBuilder.and(qCellGradeCapacity.d25.eq(query.getD25()));
        }
        if (query.getD26() != null) {
            booleanBuilder.and(qCellGradeCapacity.d26.eq(query.getD26()));
        }
        if (query.getD27() != null) {
            booleanBuilder.and(qCellGradeCapacity.d27.eq(query.getD27()));
        }
        if (query.getD28() != null) {
            booleanBuilder.and(qCellGradeCapacity.d28.eq(query.getD28()));
        }
        if (query.getD29() != null) {
            booleanBuilder.and(qCellGradeCapacity.d29.eq(query.getD29()));
        }
        if (query.getD30() != null) {
            booleanBuilder.and(qCellGradeCapacity.d30.eq(query.getD30()));
        }
        if (query.getD31() != null) {
            booleanBuilder.and(qCellGradeCapacity.d31.eq(query.getD31()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qCellGradeCapacity.remark.eq(query.getRemark()));
        }
        if (StringUtils.isNotEmpty(query.getLineName())) {
            booleanBuilder.and(qCellGradeCapacity.lineName.eq(query.getLineName()));
        }
        if (StringUtils.isNotEmpty(query.getReliabilityCheck())) {
            booleanBuilder.and(qCellGradeCapacity.reliabilityCheck.eq(query.getReliabilityCheck()));
        }

    }

    @Override
    public CellGradeCapacityDTO queryById(Long id) {
        CellGradeCapacity queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellGradeCapacityDTO save(CellGradeCapacitySaveDTO saveDTO) {
        CellGradeCapacity newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new CellGradeCapacity());
        BeanUtils.copyProperties(saveDTO, newObj);

        repository.save(newObj);
        CellGradeCapacityDTO result = this.queryById(newObj.getId());
//同步bom
        if (result != null) {
            //添加对应新的bom数据
            List<CellBomManufacturing> cellBomManufacturings = cellBomManufacturingDEConvert.fromCellGradeCapacityList(convert.toEntity(result));

            cellBomManufacturings.stream().forEach(cellBomManufacturing -> {
                cellBomManufacturing.setId(null);
                //设置默认值
                CellBomManufacturingRepository.setDefault(cellBomManufacturing);
                cellBomManufacturingRepository.save(cellBomManufacturing);
                //同步对应生产日历
                Calendar calendar = calendarDEConvert.fromCellBomManufacturingToCalendar(cellBomManufacturing);
                calendar.setId(null);
                List<Calendar> calendars = new ArrayList<>();
                calendars.add(calendar);
                //同一生产单元同一日期有多条重复数据。需在写入表前按生产单元、日期归集数据，归集时标准产线数和资源量取最大值
                //设置默认值
                List<Calendar> deduplications = calendarService.deduplication(cellBomManufacturing, calendars);
                //删除重复数据
                calendarRepository.deleteInBatch(deduplications);
                calendarRepository.save(calendar);

            });

        }
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> {
            repository.deleteById(id);
            //同步删除bom数据
            cellBomManufacturingRepository.deleteByFromId(id);
            //同步删除生产日历数据
            calendarRepository.deleteByFromId(id);
        });
        cellBomManufacturingService.bomDataMake(1);
        cellResourceService.makeData(1);
    }


    @Override
    @SneakyThrows
    public void export(CellGradeCapacityQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellGradeCapacityDTO> dtos = queryByPage(query).getContent();
        // dto数据转为ExcelData数据
        List<CellGradeCapacityExcelDTO> datas = convert.toExcelDTO(dtos);
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> list = ExcelUtils.getList(datas, excelPara);
        String fileName=BapsMessgeHelper.getMessage("export.cellgradecapacity.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName, fileName, excelPara.getSimpleHeader(), list);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellGradeCapacityExcelDTO> excelDtos = ExcelUtils.readExcel(multipartFile.getInputStream(), null, CellGradeCapacityExcelDTO.class, excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        setScale(excelDtos, 2);
        //数据验证
        checkInput(excelDtos);
        List<CellGradeCapacitySaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        saveDTOS = convert.toCellGradeCapacitySaveCnNameDTOById(saveDTOS);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        cellBomManufacturingRepository.deleteByIeOrGrade(1);
        calendarRepository.deleteByIeOrGrade(1);
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        saveAll(saveDTOS);
        cellBomManufacturingService.bomDataMake(1);
        cellResourceService.makeData(1);
    }

    private void setScale(List<CellGradeCapacityExcelDTO> dtos, int scale) {
        dtos.stream().forEach(dto -> {
            for (int i = 1; i <= 31; i++) {
                BigDecimal val = ReflectUtil.invoke(dto, "getD" + i);
                if (Objects.nonNull(val)) {
                    ReflectUtil.invoke(dto, "setD" + i, val.setScale(scale, RoundingMode.HALF_UP));
                }
            }
        });

    }

    private void saveAll(List<CellGradeCapacitySaveDTO> saveDTOS) {
        //保存爬坡产能
        List<CellGradeCapacity> cellGradeCapacities = convert.toEntityFromSaveDTO(saveDTOS);
        repository.saveAll(cellGradeCapacities);
        List<CellGradeCapacityDTO> cellGradeCapacityDTOs = convert.toDto(cellGradeCapacities);
        //生成bom数据
        List<CellBomManufacturing> allBomManufacturingList = new ArrayList<>();
        for (CellGradeCapacityDTO dto : cellGradeCapacityDTOs) {
            List<CellBomManufacturing> cellBomManufacturings = cellBomManufacturingDEConvert.fromCellGradeCapacityList(convert.toEntity(dto));
            cellBomManufacturings.stream().forEach(cellBomManufacturing -> {
                cellBomManufacturing.setId(null);
                //设置默认值
                CellBomManufacturingRepository.setDefault(cellBomManufacturing);
            });
            allBomManufacturingList.addAll(cellBomManufacturings);

        }
        //bom存储
        cellBomManufacturingRepository.saveAll(allBomManufacturingList);
        //生成生产日历
        List<Calendar> allCalendars = new ArrayList<>();
        allBomManufacturingList.stream().forEach(cellBomManufacturing -> {
            //同步对应生产日历
            Calendar calendar = calendarDEConvert.fromCellBomManufacturingToCalendar(cellBomManufacturing);
            calendar.setId(null);
            CalendarRepository.setDefault(calendar);//设置默认值
            allCalendars.add(calendar);
        });
        //生产日历去重处理
        //对allCalendars分组，生产单元相同，时间相同的为一组
        Map<String, List<Calendar>> allMap = allCalendars.stream().collect(Collectors.groupingBy(
                item -> Joiner.on(",").useForNull("null").join(item.getWorkunit(), item.getDate().toString(), item.getLineName())
        ));
        List<Calendar> saveCalendars = new ArrayList<>();
        allMap.forEach((k, v) -> {

            //同一生产单元、同产线同一日期有多条重复数据。需在写入表前按生产单元、日期归集数据，归集时标准产线数和资源量取最大值
            //获取v中Defaultqty的最大值
            BigDecimal maxDefaultqty = v.stream().map(Calendar::getDefaultqty).max(BigDecimal::compareTo).get();
            //获取v中numLines的最大值
            BigDecimal maxNumLines = v.stream().map(Calendar::getNumLines).max(BigDecimal::compareTo).get();
            Calendar calendar = v.get(0);
            calendar.setDefaultqty(maxDefaultqty);
            calendar.setNumLines(maxNumLines);
            CalendarRepository.setDefault(calendar);//设置默认值
            saveCalendars.add(calendar);
        });
        //保存生产日历
        calendarRepository.saveAll(saveCalendars);
    }

    public void checkInput(List<CellGradeCapacityExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors = new ArrayList<>();
        Map<String, LovLineDTO> allYesOrNoMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.YES_OR_NO);
        excelDTOS.stream().forEach(excelDTO -> {
            checkNullField(allYesOrNoMap, excelDTO, i[0]);
            //验证电池类型
            LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType());
            if (lovLineDTO == null) {
                String message = BapsMessgeHelper.getMessage("the.row.cellstype.not.exists", new Object[]{i[0], excelDTO.getCellsType()});
                errors.add(message);
            }
            //验证国内海外
            lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOversea());
            if (lovLineDTO == null) {
                String message = BapsMessgeHelper.getMessage("the.row.isoversea.not.exists", new Object[]{i[0], excelDTO.getIsOversea()});
                errors.add(message);
            }
            //验证生产基地
            LovLineDTO lovLineDTOBasePlace = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace());
            if (lovLineDTOBasePlace == null) {
                String message = BapsMessgeHelper.getMessage("the.row.baseplace.not.exists", new Object[]{i[0], excelDTO.getBasePlace()});
                errors.add(message);
            }
            //验证基地与国内海外的关系
            if (lovLineDTO != null && lovLineDTOBasePlace != null) {
                if (!(lovLineDTOBasePlace.getAttribute2() != null && lovLineDTOBasePlace.getAttribute2().equals(lovLineDTO.getLovLineId().toString()))) {
                    String message = BapsMessgeHelper.getMessage("the.row.baseplace.not.in.isoversea", new Object[]{i[0], excelDTO.getBasePlace(), excelDTO.getIsOversea()});
                    errors.add(message);
                }
            }
            //验证生产车间
            LovLineDTO lovLineDTOWorkShop = null;
            lovLineDTOWorkShop = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop());
            if (lovLineDTOWorkShop == null) {
                String message = BapsMessgeHelper.getMessage("the.row.workshop.not.exists", new Object[]{i[0], excelDTO.getWorkshop()});
                errors.add(message);
            }
            //验证车间与基地的关系
            if (lovLineDTOWorkShop != null && lovLineDTOBasePlace != null) {

                if (!(lovLineDTOWorkShop.getAttribute1() != null && lovLineDTOWorkShop.getAttribute1().equals(lovLineDTOBasePlace.getLovLineId().toString()))) {
                    String message = BapsMessgeHelper.getMessage("the.row.workshop.not.in.baseplace", new Object[]{i[0], excelDTO.getWorkshop(), excelDTO.getBasePlace()});
                    errors.add(message);
                }
            }
            //验证生产单元
            LovLineDTO lovLineDTOWorkUnit = null;
            lovLineDTOWorkUnit = LovUtils.getByName(LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit());
            if (lovLineDTOWorkUnit == null) {
                String message = BapsMessgeHelper.getMessage("the.row.workunit.not.exists", new Object[]{i[0], excelDTO.getWorkunit()});
                errors.add(message);
            }
            if (lovLineDTOWorkShop != null && lovLineDTOWorkUnit != null) {
                //验证单元与车间的关系
                if (!(lovLineDTOWorkUnit.getAttribute2() != null && lovLineDTOWorkUnit.getAttribute2().equals(lovLineDTOWorkShop.getLovLineId().toString()))) {
                    String message = BapsMessgeHelper.getMessage("the.row.workunit.not.in.workshop", new Object[]{i[0], excelDTO.getWorkunit(), excelDTO.getWorkshop()});
                    errors.add(message);
                }
            }
            i[0]++;
        });
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }

    }

    private void checkNullField(Map<String, LovLineDTO> allYesOrNoMap, CellGradeCapacityExcelDTO excelDTO, int i) {
        if (StringUtils.isBlank(excelDTO.getCellsType()) || StringUtils.isBlank(excelDTO.getIsOversea()) ||
                StringUtils.isBlank(excelDTO.getBasePlace()) || StringUtils.isBlank(excelDTO.getWorkshop()) || StringUtils.isBlank(excelDTO.getWorkunit())
                || StringUtils.isBlank(excelDTO.getIsSingleGlass()) || StringUtils.isBlank(excelDTO.getIsDt()) || StringUtils.isBlank(excelDTO.getIsRegionalCountry()) || StringUtils.isBlank(excelDTO.getIsHChangeFlag()) || StringUtils.isBlank(excelDTO.getIsHTrace())
                || StringUtils.isBlank(excelDTO.getReliabilityCheck())) {
            String message = BapsMessgeHelper.getMessage("grade.capacity.import.the.row.is.not.empty", new Object[]{i});
            throw new BizException(message);
        }
        if (!(StringUtils.equalsAny(excelDTO.getIsSingleGlass(), "Y", "N")
                && StringUtils.equalsAny(excelDTO.getIsDt(), "Y", "N")
                && StringUtils.equalsAny(excelDTO.getIsRegionalCountry(), "Y", "N")
                && StringUtils.equalsAny(excelDTO.getIsHChangeFlag(), "Y", "N")
                && StringUtils.equalsAny(excelDTO.getIsHTrace(), "Y", "N")
                && StringUtils.equalsAny(excelDTO.getReliabilityCheck(), "Y", "N"))
        ) {
            String message = BapsMessgeHelper.getMessage("grade.capacity.import.the.row.must.y.or.n", new Object[]{i});
            throw new BizException(message);
        }

    }
}
