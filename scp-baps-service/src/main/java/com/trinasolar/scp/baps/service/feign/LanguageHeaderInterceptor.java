package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import feign.RequestInterceptor;
import feign.RequestTemplate;

public class LanguageHeaderInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        // 根据实际业务逻辑设置所需的语言信息
        String language = LovHeaderCodeConstant.LANGUAGE_CN; // 替换为实际要设置的语言值
        // 修改请求头中的Accept-Language字段
        template.header("Lang", language);
    }
}