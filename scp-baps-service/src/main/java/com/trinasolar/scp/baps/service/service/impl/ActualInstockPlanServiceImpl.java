package com.trinasolar.scp.baps.service.service.impl;

import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.ActualInstockPlanDTO;
import com.trinasolar.scp.baps.domain.convert.ActualInstockPlanDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanFinishDTO;
import com.trinasolar.scp.baps.domain.entity.ActualInstockPlan;
import com.trinasolar.scp.baps.domain.entity.QActualInstockPlan;
import com.trinasolar.scp.baps.domain.query.ActualInstockPlanQuery;
import com.trinasolar.scp.baps.domain.save.ActualInstockPlanSaveDTO;
import com.trinasolar.scp.baps.service.repository.ActualInstockPlanRepository;
import com.trinasolar.scp.baps.service.service.ActualInstockPlanService;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

/**
 * 入库数据（ERP实际入库、入库计划）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 02:47:50
 */
@Slf4j
@Service("actualInstockPlanService")
@RequiredArgsConstructor
public class ActualInstockPlanServiceImpl implements ActualInstockPlanService {
    private static final QActualInstockPlan qActualInstockPlan = QActualInstockPlan.actualInstockPlan;

    private final ActualInstockPlanDEConvert convert;

    private final ActualInstockPlanRepository repository;

    @Override
    public Page<ActualInstockPlanDTO> queryByPage(ActualInstockPlanQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ActualInstockPlan> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ActualInstockPlanQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qActualInstockPlan.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatchNo())) {
            booleanBuilder.and(qActualInstockPlan.batchNo.eq(query.getBatchNo()));
        }
        if (StringUtils.isNotEmpty(query.getDataType())) {
            booleanBuilder.and(qActualInstockPlan.dataType.eq(query.getDataType()));
        }
        if (StringUtils.isNotEmpty(query.getOrderCode())) {
            booleanBuilder.and(qActualInstockPlan.orderCode.eq(query.getOrderCode()));
        }
        if (StringUtils.isNotEmpty(query.getSourceType())) {
            booleanBuilder.and(qActualInstockPlan.sourceType.eq(query.getSourceType()));
        }
        if (query.getSourceTypeId() != null) {
            booleanBuilder.and(qActualInstockPlan.sourceTypeId.eq(query.getSourceTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getDemandVersion())) {
            booleanBuilder.and(qActualInstockPlan.demandVersion.eq(query.getDemandVersion()));
        }
        if (query.getIsOverseaId() != null) {
            booleanBuilder.and(qActualInstockPlan.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qActualInstockPlan.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qActualInstockPlan.basePlace.eq(query.getBasePlace()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qActualInstockPlan.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qActualInstockPlan.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qActualInstockPlan.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qActualInstockPlan.workunit.eq(query.getWorkunit()));
        }
        if (query.getWorkunitId() != null) {
            booleanBuilder.and(qActualInstockPlan.workunitId.eq(query.getWorkunitId()));
        }
        if (StringUtils.isNotEmpty(query.getLineName())) {
            booleanBuilder.and(qActualInstockPlan.lineName.eq(query.getLineName()));
        }
        if (query.getNumberLine() != null) {
            booleanBuilder.and(qActualInstockPlan.numberLine.eq(query.getNumberLine()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qActualInstockPlan.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qActualInstockPlan.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qActualInstockPlan.hTrace.eq(query.getHTrace()));
        }
        if (query.getHTraceId() != null) {
            booleanBuilder.and(qActualInstockPlan.hTraceId.eq(query.getHTraceId()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qActualInstockPlan.aesthetics.eq(query.getAesthetics()));
        }
        if (query.getAestheticsId() != null) {
            booleanBuilder.and(qActualInstockPlan.aestheticsId.eq(query.getAestheticsId()));
        }
        if (query.getIsTransparentDoubleGlass() != null) {
            booleanBuilder.and(qActualInstockPlan.isTransparentDoubleGlass.eq(query.getIsTransparentDoubleGlass()));
        }
        if (query.getTransparentDoubleGlassId() != null) {
            booleanBuilder.and(qActualInstockPlan.transparentDoubleGlassId.eq(query.getTransparentDoubleGlassId()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qActualInstockPlan.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            booleanBuilder.and(qActualInstockPlan.cellSource.eq(query.getCellSource()));
        }
        if (query.getCellSourceId() != null) {
            booleanBuilder.and(qActualInstockPlan.cellSourceId.eq(query.getCellSourceId()));
        }
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qActualInstockPlan.productionGrade.eq(query.getProductionGrade()));
        }
        if (query.getProductionGradeId() != null) {
            booleanBuilder.and(qActualInstockPlan.productionGradeId.eq(query.getProductionGradeId()));
        }
        if (StringUtils.isNotEmpty(query.getRegionalCountry())) {
            booleanBuilder.and(qActualInstockPlan.regionalCountry.eq(query.getRegionalCountry()));
        }
        if (query.getRegionalCountryId() != null) {
            booleanBuilder.and(qActualInstockPlan.regionalCountryId.eq(query.getRegionalCountryId()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qActualInstockPlan.itemCode.eq(query.getItemCode()));
        }
        if (query.getDemandBasePlaceId() != null) {
            booleanBuilder.and(qActualInstockPlan.demandBasePlaceId.eq(query.getDemandBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getDemandBasePlace())) {
            booleanBuilder.and(qActualInstockPlan.demandBasePlace.eq(query.getDemandBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getIsSpecialRequirement())) {
            booleanBuilder.and(qActualInstockPlan.isSpecialRequirement.eq(query.getIsSpecialRequirement()));
        }
        if (StringUtils.isNotEmpty(query.getLowResistance())) {
            booleanBuilder.and(qActualInstockPlan.lowResistance.eq(query.getLowResistance()));
        }
        if (query.getCellMfrsId() != null) {
            booleanBuilder.and(qActualInstockPlan.cellMfrsId.eq(query.getCellMfrsId()));
        }
        if (StringUtils.isNotEmpty(query.getCellMfrs())) {
            booleanBuilder.and(qActualInstockPlan.cellMfrs.eq(query.getCellMfrs()));
        }
        if (query.getSilverPulpMfrsId() != null) {
            booleanBuilder.and(qActualInstockPlan.silverPulpMfrsId.eq(query.getSilverPulpMfrsId()));
        }
        if (StringUtils.isNotEmpty(query.getSilverPulpMfrs())) {
            booleanBuilder.and(qActualInstockPlan.silverPulpMfrs.eq(query.getSilverPulpMfrs()));
        }
        if (query.getDemandQty() != null) {
            booleanBuilder.and(qActualInstockPlan.demandQty.eq(query.getDemandQty()));
        }
        if (query.getEndTime() != null) {
            booleanBuilder.and(qActualInstockPlan.endTime.eq(query.getEndTime()));
        }
        if (query.getStartTime() != null) {
            booleanBuilder.and(qActualInstockPlan.startTime.eq(query.getStartTime()));
        }
        if (StringUtils.isNotEmpty(query.getMonth())) {
            booleanBuilder.and(qActualInstockPlan.month.eq(query.getMonth()));
        }
        if (query.getCellMv() != null) {
            booleanBuilder.and(qActualInstockPlan.cellMv.eq(query.getCellMv()));
        }
        if (StringUtils.isNotEmpty(query.getFinalVersion())) {
            booleanBuilder.and(qActualInstockPlan.finalVersion.eq(query.getFinalVersion()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qActualInstockPlan.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qActualInstockPlan.remark.eq(query.getRemark()));
        }
        if (query.getOldQtyPc() != null) {
            booleanBuilder.and(qActualInstockPlan.oldQtyPc.eq(query.getOldQtyPc()));
        }
        if (query.getQtyPc() != null) {
            booleanBuilder.and(qActualInstockPlan.qtyPc.eq(query.getQtyPc()));
        }
        if (query.getDemandSummaryLinesId() != null) {
            booleanBuilder.and(qActualInstockPlan.demandSummaryLinesId.eq(query.getDemandSummaryLinesId()));
        }
        if (query.getSiMfrsId() != null) {
            booleanBuilder.and(qActualInstockPlan.siMfrsId.eq(query.getSiMfrsId()));
        }
        if (StringUtils.isNotEmpty(query.getSiMfrs())) {
            booleanBuilder.and(qActualInstockPlan.siMfrs.eq(query.getSiMfrs()));
        }
        if (query.getIsSiMfrs() != null) {
            booleanBuilder.and(qActualInstockPlan.isSiMfrs.eq(query.getIsSiMfrs()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconMaterialManufacturer())) {
            booleanBuilder.and(qActualInstockPlan.siliconMaterialManufacturer.eq(query.getSiliconMaterialManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getScreenPlateMfrs())) {
            booleanBuilder.and(qActualInstockPlan.screenPlateMfrs.eq(query.getScreenPlateMfrs()));
        }
        if (query.getStartEfficiency() != null) {
            booleanBuilder.and(qActualInstockPlan.startEfficiency.eq(query.getStartEfficiency()));
        }
        if (query.getMaxEfficiency() != null) {
            booleanBuilder.and(qActualInstockPlan.maxEfficiency.eq(query.getMaxEfficiency()));
        }
        if (StringUtils.isNotEmpty(query.getSpecialOrderNo())) {
            booleanBuilder.and(qActualInstockPlan.specialOrderNo.eq(query.getSpecialOrderNo()));
        }
        if (query.getDemandDate() != null) {
            booleanBuilder.and(qActualInstockPlan.demandDate.eq(query.getDemandDate()));
        }
        if (query.getIsWaferGrade() != null) {
            booleanBuilder.and(qActualInstockPlan.isWaferGrade.eq(query.getIsWaferGrade()));
        }
        if (query.getWaferGradeId() != null) {
            booleanBuilder.and(qActualInstockPlan.waferGradeId.eq(query.getWaferGradeId()));
        }
        if (StringUtils.isNotEmpty(query.getWaferGrade())) {
            booleanBuilder.and(qActualInstockPlan.waferGrade.eq(query.getWaferGrade()));
        }
        if (query.getIsASplit() != null) {
            booleanBuilder.and(qActualInstockPlan.isASplit.eq(query.getIsASplit()));
        }
        if (query.getProcessCategoryPriority() != null) {
            booleanBuilder.and(qActualInstockPlan.processCategoryPriority.eq(query.getProcessCategoryPriority()));
        }
        if (query.getProcessCategoryId() != null) {
            booleanBuilder.and(qActualInstockPlan.processCategoryId.eq(query.getProcessCategoryId()));
        }
        if (StringUtils.isNotEmpty(query.getProcessCategory())) {
            booleanBuilder.and(qActualInstockPlan.processCategory.eq(query.getProcessCategory()));
        }
        if (query.getIsProcessCategory() != null) {
            booleanBuilder.and(qActualInstockPlan.isProcessCategory.eq(query.getIsProcessCategory()));
        }
        if (query.getIsHandProcessCategory() != null) {
            booleanBuilder.and(qActualInstockPlan.isHandProcessCategory.eq(query.getIsHandProcessCategory()));
        }
        if (query.getGap() != null) {
            booleanBuilder.and(qActualInstockPlan.gap.eq(query.getGap()));
        }
        if (StringUtils.isNotEmpty(query.getDemandRemark())) {
            booleanBuilder.and(qActualInstockPlan.demandRemark.eq(query.getDemandRemark()));
        }
        if (StringUtils.isNotEmpty(query.getGradeRule())) {
            booleanBuilder.and(qActualInstockPlan.gradeRule.eq(query.getGradeRule()));
        }
        if (StringUtils.isNotEmpty(query.getVerificationMark())) {
            booleanBuilder.and(qActualInstockPlan.verificationMark.eq(query.getVerificationMark()));
        }
        if (StringUtils.isNotEmpty(query.getDemandSource())) {
            booleanBuilder.and(qActualInstockPlan.demandSource.eq(query.getDemandSource()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryMaterialCode())) {
            booleanBuilder.and(qActualInstockPlan.batteryMaterialCode.eq(query.getBatteryMaterialCode()));
        }
        if (query.getOldStartTime() != null) {
            booleanBuilder.and(qActualInstockPlan.oldStartTime.eq(query.getOldStartTime()));
        }
        if (query.getOldEndTime() != null) {
            booleanBuilder.and(qActualInstockPlan.oldEndTime.eq(query.getOldEndTime()));
        }
        if (query.getFromId() != null) {
            booleanBuilder.and(qActualInstockPlan.fromId.eq(query.getFromId()));
        }
        if (query.getParentId() != null) {
            booleanBuilder.and(qActualInstockPlan.parentId.eq(query.getParentId()));
        }
        if (query.getBbomId() != null) {
            booleanBuilder.and(qActualInstockPlan.bbomId.eq(query.getBbomId()));
        }
        if (query.getConfirmPlan() != null) {
            booleanBuilder.and(qActualInstockPlan.confirmPlan.eq(query.getConfirmPlan()));
        }
        if (StringUtils.isNotEmpty(query.getOldMonth())) {
            booleanBuilder.and(qActualInstockPlan.oldMonth.eq(query.getOldMonth()));
        }
        if (StringUtils.isNotEmpty(query.getScheduleMonth())) {
            booleanBuilder.and(qActualInstockPlan.scheduleMonth.eq(query.getScheduleMonth()));
        }
        if (query.getWaferYieldRatio() != null) {
            booleanBuilder.and(qActualInstockPlan.waferYieldRatio.eq(query.getWaferYieldRatio()));
        }
        if (query.getWaferGradeRatio() != null) {
            booleanBuilder.and(qActualInstockPlan.waferGradeRatio.eq(query.getWaferGradeRatio()));
        }
        if (StringUtils.isNotEmpty(query.getMainGridSpace())) {
            booleanBuilder.and(qActualInstockPlan.mainGridSpace.eq(query.getMainGridSpace()));
        }
        if (StringUtils.isNotEmpty(query.getHChangeFlag())) {
            booleanBuilder.and(qActualInstockPlan.hChangeFlag.eq(query.getHChangeFlag()));
        }
        if (query.getCellFine() != null) {
            booleanBuilder.and(qActualInstockPlan.cellFine.eq(query.getCellFine()));
        }
    }

    @Override
    public ActualInstockPlanDTO queryById(Long id) {
        ActualInstockPlan queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public ActualInstockPlanDTO save(ActualInstockPlanSaveDTO saveDTO) {
        ActualInstockPlan newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new ActualInstockPlan());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(ActualInstockPlanQuery query, HttpServletResponse response) {
        List<ActualInstockPlanDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "入库数据（ERP实际入库、入库计划）", "入库数据（ERP实际入库、入库计划）", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ActualInstockPlanDTO> integrateData(String batchNo, String planVersion, List<CellInstockPlanFinishDTO> erpData, List<CellInstockPlanDTO> planData) {
        List<ActualInstockPlan> saveList = Lists.newArrayList();
        //按批次号删除数据
        repository.deleteByBatchNo(batchNo);
        //erp数据转换，合并数据
        Map<ActualInstockPlan, BigDecimal> erpMap = erpData.stream().collect(Collectors.groupingBy(CellInstockPlanFinishDTO::build, Collectors.reducing(BigDecimal.ZERO, CellInstockPlanFinishDTO::getFinishedQty, BigDecimal::add)));
        erpMap.forEach((data, quantity) -> {
            data.setQtyPc(quantity);
            data.setVersion(planVersion);
            saveList.add(data);
        });
        //入库计划数据转换
        planData.stream().map(CellInstockPlanDTO::build).forEach(saveList::add);
        //设置批次号
        saveList.forEach(save -> save.setBatchNo(batchNo));
        repository.saveAll(saveList);
        return convert.toDto(saveList);
    }
}
