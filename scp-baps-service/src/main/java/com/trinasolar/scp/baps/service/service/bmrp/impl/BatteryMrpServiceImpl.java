package com.trinasolar.scp.baps.service.service.bmrp.impl;

import com.trinasolar.scp.baps.domain.dto.SiliconSliceSupplyLinesDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.BmrpSafetyStockDaysDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.SiliconSlicePurchasePlanDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.TjOnHandDTO;
import com.trinasolar.scp.baps.domain.query.SiliconSliceSupplyLinesQuery;
import com.trinasolar.scp.baps.domain.query.bmrp.BmrpSafetyStockDaysQuery;
import com.trinasolar.scp.baps.domain.query.bmrp.SiliconSlicePurchasePlanQuery;
import com.trinasolar.scp.baps.domain.query.bmrp.TjOnHandQuery;
import com.trinasolar.scp.baps.service.feign.BmrpFeign;
import com.trinasolar.scp.baps.service.service.bmrp.BatteryMrpService;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.Results;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: BatteryMrpServiceImpl
 * @date 2024/6/6 09:21
 */
@Service("batteryMrpService")
@AllArgsConstructor
public class BatteryMrpServiceImpl implements BatteryMrpService {
    private final BmrpFeign bmrpFeign;

    @Override
    public List<TjOnHandDTO> findInventory(List<String> subInventoryCodeList, LocalDate inventoryDate) {
        Assert.notEmpty(subInventoryCodeList);
        Assert.notNull(inventoryDate);
        subInventoryCodeList = subInventoryCodeList.stream().distinct().collect(Collectors.toList());
        TjOnHandQuery query = new TjOnHandQuery();
        query.setItemType("4A");
        query.setSubInventoryCodeList(subInventoryCodeList);
        query.setLocalDate(inventoryDate);
        Results<List<TjOnHandDTO>> response = bmrpFeign.queryInventory(query).getBody();
        if(!response.isSuccess() || response.getData() == null){
            throw new BizException("baps_call_mrp_inventory_interface_failed");
        }
        return response.getData();
    }

    @Override
    public List<SiliconSliceSupplyLinesDTO> findSupplyLines(List<String> monthList) {
        if(CollectionUtils.isNotEmpty(monthList)){
            monthList = monthList.stream().distinct().collect(Collectors.toList());
            SiliconSliceSupplyLinesQuery query = new SiliconSliceSupplyLinesQuery();
            query.setMonthList(monthList);
            Results<List<SiliconSliceSupplyLinesDTO>> response = bmrpFeign.findByLastVersionList(query).getBody();
            if(!response.isSuccess() || response.getData() == null){
                throw new BizException("baps_call_mrp_silicon_wafer_interface_failed");
            }
            return response.getData();
        }
        return Lists.newArrayList();
    }

    @Override
    public List<SiliconSlicePurchasePlanDTO> findSiliconSlicePurchasePlan(List<String> monthList) {
        if(CollectionUtils.isNotEmpty(monthList)){
            monthList = monthList.stream().distinct().collect(Collectors.toList());
            SiliconSlicePurchasePlanQuery query = new SiliconSlicePurchasePlanQuery();
            query.setMonthList(monthList);
            Results<List<SiliconSlicePurchasePlanDTO>> response = bmrpFeign.findByCondition(query).getBody();
            if(!response.isSuccess() || response.getData() == null){
                throw new BizException("baps_call_mrp_silicon_wafer_purchase_interface_failed");
            }
            return response.getData();
        }
        return Lists.newArrayList();
    }

    @Override
    public List<BmrpSafetyStockDaysDTO> safetyStockList(String itemCategory, List<String> basePlaceList) {
        if(CollectionUtils.isNotEmpty(basePlaceList)){
            basePlaceList = basePlaceList.stream().distinct().collect(Collectors.toList());
            BmrpSafetyStockDaysQuery query = new BmrpSafetyStockDaysQuery();
            query.setItemCategory(itemCategory);
            query.setBasePlaceList(basePlaceList);
            Results<List<BmrpSafetyStockDaysDTO>> response = bmrpFeign.safetyStockList(query).getBody();
            if(!response.isSuccess() || response.getData() == null){
                throw new BizException("baps_call_mrp_safety_stock_interface_failed");
            }
            return response.getData();
        }
        return Lists.newArrayList();
    }
}
