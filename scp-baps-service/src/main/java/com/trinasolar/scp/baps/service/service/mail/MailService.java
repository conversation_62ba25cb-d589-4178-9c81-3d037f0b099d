package com.trinasolar.scp.baps.service.service.mail;

import com.trinasolar.scp.baps.domain.dto.CellInStockPlanRemarkDTO;
import com.trinasolar.scp.baps.domain.dto.CellInStockPlanRemarkGroupDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalDTO;
import com.trinasolar.scp.baps.domain.dto.message.EmailAddress;
import com.trinasolar.scp.baps.domain.excel.CellInstockPlanTotalExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineTotalExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanTotalQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalQuery;
import com.trinasolar.scp.common.api.util.exStrategy.CellStyleModel;
import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.Version;

import java.util.List;
import java.util.Locale;

public interface MailService {
    static final String FREEMARKER_VERSION = "2.3.29";
    static final String TEMPLATE_PATH = "/templates";
    String EMAIL_FILE_LOCAL_DIR_NAME = "./email_file";

    default Template getTemplate(String templateName) throws Exception {
        Configuration configuration = new Configuration(new Version(FREEMARKER_VERSION));
        configuration.setTemplateLoader(new ClassTemplateLoader(this.getClass(), TEMPLATE_PATH));
        configuration.setEncoding(Locale.getDefault(), "UTF-8");
        configuration.setDateFormat("yyyy-MM-dd HH:mm:ss");
        Template template = configuration.getTemplate(templateName);
        return template;
    }

    boolean send(List<String> toList, String mailTemplate, String subject, Object content,String fileJson) ;

    void sendCellPlanlineTotal(CellPlanLineTotalQuery query, List<CellPlanLineTotalDTO> curVersionDatas, List<CellPlanLineTotalDTO> preVersionDatas, List<EmailAddress> emailList,List<String> fileList);

    void sendInstockPlanlineTotal(CellInstockPlanTotalQuery query, List<CellInstockPlanTotalDTO> curVersionDatas, List<CellInstockPlanTotalDTO> preVersionDatas, List<EmailAddress> emailList,List<String> fileList,  List<CellInStockPlanRemarkDTO> remarkDTOList);

    void sendCellPlanlineTotalToAll(CellPlanLineTotalQuery query, List<CellPlanLineTotalDTO> curVersionDatas,  List<CellPlanLineTotalDTO> preVersionDatas, List<EmailAddress> allEmailList,List<String> fileList,List<CellStyleModel> models);

    void sendInstockPlanlineTotalToAll(CellInstockPlanTotalQuery query, List<CellInstockPlanTotalDTO> cellInstockPlanTotalDTOS, List<CellInstockPlanTotalDTO> preVersionDatas, List<EmailAddress> allEmailList, List<String> fileList,List<CellStyleModel> models,List<CellInStockPlanRemarkGroupDTO> remarkDTOList);

    void deleteEmailFile(List<String> fileList);

    List<CellPlanLineTotalExcelDTO> convertExcelDTOForPlanLine(List<CellPlanLineTotalDTO> dtos);

    List<CellInstockPlanTotalExcelDTO> convertExcelDTO(List<CellInstockPlanTotalDTO> dtos);
}
