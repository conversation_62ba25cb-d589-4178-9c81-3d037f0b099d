package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellConversionFactor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 万片与兆瓦折算系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-31 10:33:29
 */
@Repository
public interface CellConversionFactorRepository extends JpaRepository<CellConversionFactor, Long>, QuerydslPredicateExecutor<CellConversionFactor> {
}
