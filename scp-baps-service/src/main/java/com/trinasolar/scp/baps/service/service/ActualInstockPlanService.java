package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.ActualInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanDTO;
import com.trinasolar.scp.baps.domain.dto.CellInstockPlanFinishDTO;
import com.trinasolar.scp.baps.domain.entity.ActualInstockPlan;
import com.trinasolar.scp.baps.domain.query.ActualInstockPlanQuery;
import com.trinasolar.scp.baps.domain.save.ActualInstockPlanSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 入库数据（ERP实际入库、入库计划） 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 02:47:50
 */
public interface ActualInstockPlanService {
    /**
     * 分页获取入库数据（ERP实际入库、入库计划）
     *
     * @param query 查询对象
     * @return 入库数据（ERP实际入库、入库计划）分页对象
     */
    Page<ActualInstockPlanDTO> queryByPage(ActualInstockPlanQuery query);

    /**
     * 根据主键获取入库数据（ERP实际入库、入库计划）详情
     *
     * @param id 主键
     * @return 入库数据（ERP实际入库、入库计划）详情
     */
    ActualInstockPlanDTO queryById(Long id);

    /**
     * 保存或更新入库数据（ERP实际入库、入库计划）
     *
     * @param saveDTO 入库数据（ERP实际入库、入库计划）保存对象
     * @return 入库数据（ERP实际入库、入库计划）对象
     */
    ActualInstockPlanDTO save(ActualInstockPlanSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除入库数据（ERP实际入库、入库计划）
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(ActualInstockPlanQuery query, HttpServletResponse response);

    /**
     * 整合数据
     *
     * @param batchNo
     * @param planVersion
     * @param erpData
     * @param planData
     * @return
     */
    List<ActualInstockPlanDTO> integrateData(String batchNo, String planVersion, List<CellInstockPlanFinishDTO> erpData, List<CellInstockPlanDTO> planData);
}

