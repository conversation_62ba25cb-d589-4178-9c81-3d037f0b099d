package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellInstockPlanVersion;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 入库计划版本管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Repository
public interface CellInstockPlanVersionRepository extends JpaRepository<CellInstockPlanVersion, Long>, QuerydslPredicateExecutor<CellInstockPlanVersion> {
    @Query("from   CellInstockPlanVersion c where c.isOversea = :isOversea and  c.month = :month and c.fromVersion = :version")
    public  CellInstockPlanVersion selectByVersion(@Param("isOversea") String isOversea, @Param("month") String month, @Param("version") String version);
}
