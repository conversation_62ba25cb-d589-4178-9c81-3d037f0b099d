package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellPlanShippableDTO;
import com.trinasolar.scp.baps.domain.dto.aps.CellPlanShippableSaveListDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanShippableQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanShippableSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 可发货计划表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-09 10:05:37
 */
public interface CellPlanShippableService {
    /**
     * 分页获取可发货计划表
     *
     * @param query 查询对象
     * @return 可发货计划表分页对象
     */
    Page<CellPlanShippableDTO> queryByPage(CellPlanShippableQuery query);

    /**
     * 分页获取可发货计划表
     *
     * @return 可发货计划表分页对象
     */
    void syncDataInstockPlan(CellPlanShippableQuery query);

    /**
     * 根据主键获取可发货计划表详情
     *
     * @param id 主键
     * @return 可发货计划表详情
     */
    CellPlanShippableDTO queryById(Long id);

    /**
     * 保存或更新可发货计划表
     *
     * @param saveDTO 可发货计划表保存对象
     * @return 可发货计划表对象
     */
    CellPlanShippableDTO save(CellPlanShippableSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除可发货计划表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(CellPlanShippableQuery query, HttpServletResponse response);

}

