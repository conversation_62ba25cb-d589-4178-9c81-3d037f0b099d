package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.PriorityLogisticsLineDTO;
import com.trinasolar.scp.baps.domain.convert.PriorityLogisticsLineDEConvert;
import com.trinasolar.scp.baps.domain.dto.aps.CellShippingDaysDTO;
import com.trinasolar.scp.baps.domain.entity.PriorityLogisticsLine;
import com.trinasolar.scp.baps.domain.entity.QPriorityLogisticsLine;
import com.trinasolar.scp.baps.domain.excel.PriorityLogisticsLineExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.query.PriorityLogisticsLineQuery;
import com.trinasolar.scp.baps.domain.save.PriorityLogisticsLineSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.PriorityLogisticsLineRepository;
import com.trinasolar.scp.baps.service.service.PriorityLogisticsLineService;
import com.trinasolar.scp.baps.service.service.aps.ApsService;
import com.trinasolar.scp.baps.service.service.system.SystemService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.component.SystemFeign;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物流线路优先级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:51:00
 */
@Slf4j
@Service("priorityLogisticsLineService")
@RequiredArgsConstructor
public class PriorityLogisticsLineServiceImpl implements PriorityLogisticsLineService {
    private static final QPriorityLogisticsLine qPriorityLogisticsLine = QPriorityLogisticsLine.priorityLogisticsLine;

    private final ApsService apsService;

    private final SystemService systemService;

    private final PriorityLogisticsLineDEConvert convert;

    private final PriorityLogisticsLineRepository repository;

    @Override
    public Page<PriorityLogisticsLineDTO> queryByPage(PriorityLogisticsLineQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query=convert.toPriorityLogisticsLineQueryCNName(query,MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<PriorityLogisticsLine> page = repository.findAll(booleanBuilder, pageable);
        List<PriorityLogisticsLineDTO> dataList = convert.toDto(page.getContent());
        //排序
        dataList.sort(Comparator.comparing(PriorityLogisticsLineDTO::getDemandBasePlace).thenComparing(PriorityLogisticsLineDTO::getBasePlace).thenComparing(PriorityLogisticsLineDTO::getSeries));
        return new PageImpl(dataList, page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, PriorityLogisticsLineQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qPriorityLogisticsLine.id.eq(query.getId()));
        }
        if (query.getBasePlaceId() != null) {
            booleanBuilder.and(qPriorityLogisticsLine.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qPriorityLogisticsLine.basePlace.eq(query.getBasePlace()));
        }
        if (query.getDemandBasePlaceId() != null) {
            booleanBuilder.and(qPriorityLogisticsLine.demandBasePlaceId.eq(query.getDemandBasePlaceId()));
        }
        if (StringUtils.isNotEmpty(query.getDemandBasePlace())) {
            booleanBuilder.and(qPriorityLogisticsLine.demandBasePlace.eq(query.getDemandBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getSeries())) {
            booleanBuilder.and(qPriorityLogisticsLine.series.eq(query.getSeries()));
        }
        if (query.getPriority() != null) {
            booleanBuilder.and(qPriorityLogisticsLine.priority.eq(query.getPriority()));
        }
    }

    @Override
    public PriorityLogisticsLineDTO queryById(Long id) {
        PriorityLogisticsLine queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public PriorityLogisticsLineDTO save(PriorityLogisticsLineSaveDTO saveDTO) {
        PriorityLogisticsLine newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new PriorityLogisticsLine());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(PriorityLogisticsLineQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<PriorityLogisticsLineDTO> dtos = queryByPage(query).getContent();
        // dto数据转为ExcelData数据
        List<PriorityLogisticsLineExcelDTO> excelDtos = convert.toExcelDTO(dtos);
        ExcelPara excelPara =  query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(excelDtos, excelPara);
        // 导出调用excelUtils
        String fileName= BapsMessgeHelper.getMessage("export.prioritylogisticsline.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshData() {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        //获取aps运输天数数据
        List<CellShippingDaysDTO> cellShippingDays = apsService.findCellShippingDays();
        //查询 生产基地 值集
        Map<Long, String> basePlaceMap = reversedBasePlace();
        //查询 生产车间 值集
        Map<String, Long> workshopMap = workshopMap();
        //获取生产基地
        List<PriorityLogisticsLineSaveDTO> dataList = cellShippingDays.stream().map(item -> {
            Integer days = item.getDays();
            Integer rate = item.getRate();
            String demandWorkshop = item.getDemandWorkshop();
            String originWorkshop = item.getOriginWorkshop();
            Long demandBasePlaceId = workshopMap.get(demandWorkshop);
            String demandBasePlace = basePlaceMap.get(demandBasePlaceId);
            Long originBasePlaceId = workshopMap.get(originWorkshop);
            String originBasePlace = basePlaceMap.get(originBasePlaceId);
            return new PriorityLogisticsLineSaveDTO(originBasePlaceId, originBasePlace, demandBasePlaceId, demandBasePlace, rate, days);
        }).filter(PriorityLogisticsLineSaveDTO::nonNull).filter(Objects::nonNull).collect(Collectors.toList());
        //按基地分组并获取最大优先级数据
        Map<String, PriorityLogisticsLineSaveDTO> dataMap = dataList.stream().collect(Collectors.groupingBy(k -> StringUtils.join(k.getBasePlaceId(), k.getBasePlace(), k.getDemandBasePlaceId(), k.getDemandBasePlace()), Collectors.collectingAndThen(Collectors.reducing((a, b) -> a.getPriority() > b.getPriority() ? a : b), Optional::get)));
        List<PriorityLogisticsLine> saveList = convert.saveDTOListtoEntity(IterableUtils.toList(dataMap.values()));
        repository.deleteAllInBatch();
        repository.saveAll(saveList);
    }

    /**
     * 获取生产车间值集
     * 优先获取 【生产基地（物流优先级）】 其次获取 【基地关联】
     *
     * @return
     */
    private Map<String, Long> workshopMap() {
        Map<String, Long> basePlaceMap = basePlace();
        Map<String, LovLineDTO> lovMap = systemService.findByLovCode(LovHeaderCodeConstant.WORK_SHOP, LovLineDTO::getLovValue, Function.identity());
        return lovMap.values().stream().filter(k -> StringUtils.isNotBlank(k.getAttribute1()) || StringUtils.isNotBlank(k.getAttribute11()))
                .collect(Collectors.toMap(LovLineDTO::getLovValue, item -> {
                    if (StringUtils.isNotBlank(item.getAttribute11())) {
                        return basePlaceMap.get(item.getAttribute11());
                    }
                    return Long.valueOf(item.getAttribute1());
                }));
    }

    /**
     * 获取生产基地值集
     *
     * @return
     */
    private Map<String, Long> basePlace() {
        return systemService.findByLovCode(LovHeaderCodeConstant.BASE_PLACE, LovLineDTO::getLovValue, LovLineDTO::getLovLineId);
    }

    /**
     * 获取生产基地值集
     *
     * @return
     */
    private Map<Long, String> reversedBasePlace() {
        return systemService.findByLovCode(LovHeaderCodeConstant.BASE_PLACE, LovLineDTO::getLovLineId, LovLineDTO::getLovValue);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile) {
        List<PriorityLogisticsLineExcelDTO> excelDtos = new LinkedList<>();
        // 这里为了简单 所以注册了 同样的head 和Listener 自己使用功能必须不同的Listener
        ExcelReaderBuilder readerBuilder = EasyExcel.read(multipartFile.getInputStream(), PriorityLogisticsLineExcelDTO.class, new ReadListener<PriorityLogisticsLineExcelDTO>() {
            @Override
            public void invoke(PriorityLogisticsLineExcelDTO data, AnalysisContext context) {
                excelDtos.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        });
        readerBuilder.registerConverter(new LocalDateConverter());
        readerBuilder.sheet(0).doRead();
        final QuerySheetExcelDTO[] querySheetExcelDTO = {null};
        EasyExcel.read(multipartFile.getInputStream(), QuerySheetExcelDTO.class, new ReadListener<QuerySheetExcelDTO>() {
            @Override
            public void invoke(QuerySheetExcelDTO data, AnalysisContext context) {
                querySheetExcelDTO[0] = data;
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet(1).doRead();
        QuerySheetExcelDTO queryParam = querySheetExcelDTO[0];

        if (Objects.isNull(queryParam)) {
            throw new BizException("导入查询条件导入为空,请检查导入文件是否正确");
        }

        // 查询原始行

        PriorityLogisticsLineQuery page = JSON.parseObject(queryParam.getQueryParam(), PriorityLogisticsLineQuery.class);

        List<PriorityLogisticsLineDTO> oriDTOS = this.queryByPage(page).getContent();


        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("导入数据为空");
        }

        List<PriorityLogisticsLineSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);

        // 先删除之前的数据,再保存信息的数据
        oriDTOS.forEach(i -> repository.deleteById(i.getId()));
        saveDTOS.stream().forEach(this::save);
    }

}
