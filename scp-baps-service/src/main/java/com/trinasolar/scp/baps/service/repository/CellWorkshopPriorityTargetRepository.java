package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellWorkshopPriorityTarget;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 车间优先度效率目标值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Repository
public interface CellWorkshopPriorityTargetRepository extends JpaRepository<CellWorkshopPriorityTarget, Long>, QuerydslPredicateExecutor<CellWorkshopPriorityTarget> {
}
