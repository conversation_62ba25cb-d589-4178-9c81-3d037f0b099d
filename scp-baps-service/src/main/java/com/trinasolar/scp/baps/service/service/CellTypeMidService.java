package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellTypeMidDTO;
import com.trinasolar.scp.baps.domain.query.CellTypeMidQuery;
import com.trinasolar.scp.baps.domain.save.CellTypeMidSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池类型转换表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
public interface CellTypeMidService {
    /**
     * 分页获取电池类型转换表
     *
     * @param query 查询对象
     * @return 电池类型转换表分页对象
     */
    Page<CellTypeMidDTO> queryByPage(CellTypeMidQuery query);

    /**
     * 根据主键获取电池类型转换表详情
     *
     * @param id 主键
     * @return 电池类型转换表详情
     */
        CellTypeMidDTO queryById(Long id);

    /**
     * 保存或更新电池类型转换表
     *
     * @param saveDTO 电池类型转换表保存对象
     * @return 电池类型转换表对象
     */
    CellTypeMidDTO save(CellTypeMidSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池类型转换表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellTypeMidQuery query, HttpServletResponse response);
}

