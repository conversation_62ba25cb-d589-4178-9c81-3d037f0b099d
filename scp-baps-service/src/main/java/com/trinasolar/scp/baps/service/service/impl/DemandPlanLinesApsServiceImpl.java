package com.trinasolar.scp.baps.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.DemandPlanLinesApsDTO;
import com.trinasolar.scp.baps.domain.convert.DemandPlanLinesApsDEConvert;
import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.baps.domain.entity.DemandPlanLinesAps;
import com.trinasolar.scp.baps.domain.entity.QDemandPlanLinesAps;
import com.trinasolar.scp.baps.domain.query.DemandPlanLinesApsQuery;
import com.trinasolar.scp.baps.domain.save.DemandPlanLinesApsSaveDTO;
import com.trinasolar.scp.baps.service.repository.DemandPlanLinesApsRepository;
import com.trinasolar.scp.baps.service.service.DemandPlanLinesApsService;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

/**
 * 需求计划明细（APS）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 08:30:30
 */
@Slf4j
@Service("demandPlanLinesApsService")
@RequiredArgsConstructor
public class DemandPlanLinesApsServiceImpl implements DemandPlanLinesApsService {
    private static final QDemandPlanLinesAps qDemandPlanLinesAps = QDemandPlanLinesAps.demandPlanLinesAps;

    private final DemandPlanLinesApsDEConvert convert;

    private final DemandPlanLinesApsRepository repository;

    @Override
    public Page<DemandPlanLinesApsDTO> queryByPage(DemandPlanLinesApsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<DemandPlanLinesAps> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, DemandPlanLinesApsQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qDemandPlanLinesAps.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatchNo())) {
            booleanBuilder.and(qDemandPlanLinesAps.batchNo.eq(query.getBatchNo()));
        }
        if (query.getActualCoverageDate() != null) {
            booleanBuilder.and(qDemandPlanLinesAps.actualCoverageDate.eq(query.getActualCoverageDate()));
        }
        if (query.getPlanLockDate() != null) {
            booleanBuilder.and(qDemandPlanLinesAps.planLockDate.eq(query.getPlanLockDate()));
        }
        if (StringUtils.isNotEmpty(query.getDpId())) {
            booleanBuilder.and(qDemandPlanLinesAps.dpId.eq(query.getDpId()));
        }
        if (StringUtils.isNotEmpty(query.getDemandPlanCode())) {
            booleanBuilder.and(qDemandPlanLinesAps.demandPlanCode.eq(query.getDemandPlanCode()));
        }
        if (StringUtils.isNotEmpty(query.getSourceType())) {
            booleanBuilder.and(qDemandPlanLinesAps.sourceType.eq(query.getSourceType()));
        }
        if (StringUtils.isNotEmpty(query.getDomesticOverseaName())) {
            booleanBuilder.and(qDemandPlanLinesAps.domesticOverseaName.eq(query.getDomesticOverseaName()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qDemandPlanLinesAps.batteryName.eq(query.getBatteryName()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryMaterialCode())) {
            booleanBuilder.and(qDemandPlanLinesAps.batteryMaterialCode.eq(query.getBatteryMaterialCode()));
        }
        if (StringUtils.isNotEmpty(query.getHTraceName())) {
            booleanBuilder.and(qDemandPlanLinesAps.hTraceName.eq(query.getHTraceName()));
        }
        if (StringUtils.isNotEmpty(query.getPcsSourceTypeName())) {
            booleanBuilder.and(qDemandPlanLinesAps.pcsSourceTypeName.eq(query.getPcsSourceTypeName()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlassName())) {
            booleanBuilder.and(qDemandPlanLinesAps.transparentDoubleGlassName.eq(query.getTransparentDoubleGlassName()));
        }
        if (StringUtils.isNotEmpty(query.getAestheticsName())) {
            booleanBuilder.and(qDemandPlanLinesAps.aestheticsName.eq(query.getAestheticsName()));
        }
        if (StringUtils.isNotEmpty(query.getRegionalCountryName())) {
            booleanBuilder.and(qDemandPlanLinesAps.regionalCountryName.eq(query.getRegionalCountryName()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlaceName())) {
            booleanBuilder.and(qDemandPlanLinesAps.basePlaceName.eq(query.getBasePlaceName()));
        }
        if (StringUtils.isNotEmpty(query.getCellMfrs())) {
            booleanBuilder.and(qDemandPlanLinesAps.cellMfrs.eq(query.getCellMfrs()));
        }
        if (StringUtils.isNotEmpty(query.getSilverPulpMfrs())) {
            booleanBuilder.and(qDemandPlanLinesAps.silverPulpMfrs.eq(query.getSilverPulpMfrs()));
        }
        if (StringUtils.isNotEmpty(query.getSiMfrs())) {
            booleanBuilder.and(qDemandPlanLinesAps.siMfrs.eq(query.getSiMfrs()));
        }
        if (StringUtils.isNotEmpty(query.getScreenPlateMfrs())) {
            booleanBuilder.and(qDemandPlanLinesAps.screenPlateMfrs.eq(query.getScreenPlateMfrs()));
        }
        if (query.getStartEfficiency() != null) {
            booleanBuilder.and(qDemandPlanLinesAps.startEfficiency.eq(query.getStartEfficiency()));
        }
        if (StringUtils.isNotEmpty(query.getSpecialOrderNo())) {
            booleanBuilder.and(qDemandPlanLinesAps.specialOrderNo.eq(query.getSpecialOrderNo()));
        }
        if (query.getDemandDate() != null) {
            booleanBuilder.and(qDemandPlanLinesAps.demandDate.eq(query.getDemandDate()));
        }
        if (query.getPassPercent() != null) {
            booleanBuilder.and(qDemandPlanLinesAps.passPercent.eq(query.getPassPercent()));
        }
        if (StringUtils.isNotEmpty(query.getScheduleWorkshop())) {
            booleanBuilder.and(qDemandPlanLinesAps.scheduleWorkshop.eq(query.getScheduleWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getProcessCategoryName())) {
            booleanBuilder.and(qDemandPlanLinesAps.processCategoryName.eq(query.getProcessCategoryName()));
        }
        if (query.getDemandQty() != null) {
            booleanBuilder.and(qDemandPlanLinesAps.demandQty.eq(query.getDemandQty()));
        }
        if (StringUtils.isNotEmpty(query.getExteriorDemandId())) {
            booleanBuilder.and(qDemandPlanLinesAps.exteriorDemandId.eq(query.getExteriorDemandId()));
        }
        if (StringUtils.isNotEmpty(query.getHChangeFlag())) {
            booleanBuilder.and(qDemandPlanLinesAps.hChangeFlag.eq(query.getHChangeFlag()));
        }
        if (StringUtils.isNotEmpty(query.getPcsSourceDtChangeFlag())) {
            booleanBuilder.and(qDemandPlanLinesAps.pcsSourceDtChangeFlag.eq(query.getPcsSourceDtChangeFlag()));
        }
        if (StringUtils.isNotEmpty(query.getMainGridSpace())) {
            booleanBuilder.and(qDemandPlanLinesAps.mainGridSpace.eq(query.getMainGridSpace()));
        }
    }

    @Override
    public DemandPlanLinesApsDTO queryById(Long id) {
        DemandPlanLinesAps queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public DemandPlanLinesApsDTO save(DemandPlanLinesApsSaveDTO saveDTO) {
        DemandPlanLinesAps newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new DemandPlanLinesAps());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(DemandPlanLinesApsQuery query, HttpServletResponse response) {
        List<DemandPlanLinesApsDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "需求计划明细（APS）", "需求计划明细（APS）", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDemandPlan(String batchNo, Long isOverseaId, LocalDate actualCoverageDate, LocalDate planLockDate, List<BatteryDemandPlanLinesDTO> saveList) {
        //逻辑删除
        repository.deleteByDomesticOverseaName(LovUtils.getName(isOverseaId));
        if(CollectionUtils.isNotEmpty(saveList)){
            //入库
            List<DemandPlanLinesAps> entityList = new ArrayList<>();
            saveList.forEach(p->{
                DemandPlanLinesAps demandPlanLinesAps = convert.batteryDemandDTOToEntity(p);
                demandPlanLinesAps.setSupplyMethod(p.getSupplyMethod());
                demandPlanLinesAps.setBatchNo(batchNo);
                demandPlanLinesAps.setActualCoverageDate(actualCoverageDate);
                demandPlanLinesAps.setPlanLockDate(planLockDate);
                demandPlanLinesAps.setUpdatedTime(LocalDateTime.now());
                entityList.add(demandPlanLinesAps);
            });
            repository.saveAll(entityList);
        }
    }
}
