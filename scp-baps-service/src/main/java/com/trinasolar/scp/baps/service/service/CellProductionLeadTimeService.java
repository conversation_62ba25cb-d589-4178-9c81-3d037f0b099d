package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellProductionLeadTimeDTO;
import com.trinasolar.scp.baps.domain.query.CellProductionLeadTimeQuery;
import com.trinasolar.scp.baps.domain.save.CellProductionLeadTimeSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 投产提前期 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
public interface CellProductionLeadTimeService   {
    /**
     * 分页获取投产提前期
     *
     * @param query 查询对象
     * @return 投产提前期分页对象
     */
    Page<CellProductionLeadTimeDTO> queryByPage(CellProductionLeadTimeQuery query);

    /**
     * 根据主键获取投产提前期详情
     *
     * @param id 主键
     * @return 投产提前期详情
     */
        CellProductionLeadTimeDTO queryById(Long id);

    /**
     * 保存或更新投产提前期
     *
     * @param saveDTO 投产提前期保存对象
     * @return 投产提前期对象
     */
    CellProductionLeadTimeDTO save(CellProductionLeadTimeSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除投产提前期
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellProductionLeadTimeQuery query, HttpServletResponse response);

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    void importData(MultipartFile multipartFile, ExcelPara excelPara);
}

