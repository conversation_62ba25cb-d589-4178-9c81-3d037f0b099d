package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.DemandPlanLinesAps;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 需求计划明细（APS）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 08:30:30
 */
@Repository
public interface DemandPlanLinesApsRepository extends JpaRepository<DemandPlanLinesAps, Long>, QuerydslPredicateExecutor<DemandPlanLinesAps> {
    @Query("update DemandPlanLinesAps a set a.isDeleted = 1 where a.batchNo = ?1")
    @Modifying
    void deleteByBatchNo(String batchNo);

    @Query("update DemandPlanLinesAps a set a.isDeleted = 1 where a.domesticOverseaName = ?1")
    @Modifying
    void deleteByDomesticOverseaName(String name);
}
