package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.DemandPlanLinesApsDTO;
import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.baps.domain.query.DemandPlanLinesApsQuery;
import com.trinasolar.scp.baps.domain.save.DemandPlanLinesApsSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

/**
 * 需求计划明细（APS） 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-15 08:30:30
 */
public interface DemandPlanLinesApsService {
    /**
     * 分页获取需求计划明细（APS）
     *
     * @param query 查询对象
     * @return 需求计划明细（APS）分页对象
     */
    Page<DemandPlanLinesApsDTO> queryByPage(DemandPlanLinesApsQuery query);

    /**
     * 根据主键获取需求计划明细（APS）详情
     *
     * @param id 主键
     * @return 需求计划明细（APS）详情
     */
        DemandPlanLinesApsDTO queryById(Long id);

    /**
     * 保存或更新需求计划明细（APS）
     *
     * @param saveDTO 需求计划明细（APS）保存对象
     * @return 需求计划明细（APS）对象
     */
    DemandPlanLinesApsDTO save(DemandPlanLinesApsSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除需求计划明细（APS）
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(DemandPlanLinesApsQuery query, HttpServletResponse response);

    void saveDemandPlan(String batchNo, Long isOverseaId, LocalDate actualCoverageDate, LocalDate planLockDate, List<BatteryDemandPlanLinesDTO> saveList);
}

