package com.trinasolar.scp.baps.service.service.impl;
import cn.hutool.Hutool;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellPlanQtyFluctuationCoefficientDTO;
import com.trinasolar.scp.baps.domain.convert.CellPlanQtyFluctuationCoefficientDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellPlanQtyFluctuationCoefficient;
import com.trinasolar.scp.baps.domain.entity.QCellPlanQtyFluctuationCoefficient;
import com.trinasolar.scp.baps.domain.excel.CellPlanQtyFluctuationCoefficientExcelDTO;
import com.trinasolar.scp.baps.domain.excel.SiliconSplitRuleExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellPlanQtyFluctuationCoefficientQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanQtyFluctuationCoefficientSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.domain.utils.MapStrutUtil;
import com.trinasolar.scp.baps.service.repository.CellPlanQtyFluctuationCoefficientRepository;
import com.trinasolar.scp.baps.service.service.CellPlanQtyFluctuationCoefficientService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-30 01:49:53
 */
@Slf4j
@Service("cellPlanQtyFluctuationCoefficientService")
@RequiredArgsConstructor
public class CellPlanQtyFluctuationCoefficientServiceImpl implements CellPlanQtyFluctuationCoefficientService {
    private static final QCellPlanQtyFluctuationCoefficient qCellPlanQtyFluctuationCoefficient = QCellPlanQtyFluctuationCoefficient.cellPlanQtyFluctuationCoefficient;

    private final CellPlanQtyFluctuationCoefficientDEConvert convert;

    private final CellPlanQtyFluctuationCoefficientRepository repository;

    @Override
    public Page<CellPlanQtyFluctuationCoefficientDTO> queryByPage(CellPlanQtyFluctuationCoefficientQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellPlanQtyFluctuationCoefficient> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellPlanQtyFluctuationCoefficientQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellPlanQtyFluctuationCoefficient.id.eq(query.getId()));
        }
        if (query.getStartTime() != null) {
            booleanBuilder.and(qCellPlanQtyFluctuationCoefficient.startTime.eq(query.getStartTime()));
        }
        if (query.getEndTime() != null) {
            booleanBuilder.and(qCellPlanQtyFluctuationCoefficient.endTime.eq(query.getEndTime()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qCellPlanQtyFluctuationCoefficient.workshop.eq(query.getWorkshop()));
        }
        if (query.getFluctuationCoefficient() != null) {
            booleanBuilder.and(qCellPlanQtyFluctuationCoefficient.fluctuationCoefficient.eq(query.getFluctuationCoefficient()));
        }
    }

    @Override
    public CellPlanQtyFluctuationCoefficientDTO queryById(Long id) {
        CellPlanQtyFluctuationCoefficient queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellPlanQtyFluctuationCoefficientDTO save(CellPlanQtyFluctuationCoefficientSaveDTO saveDTO) {
        CellPlanQtyFluctuationCoefficient newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new CellPlanQtyFluctuationCoefficient());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellPlanQtyFluctuationCoefficientQuery query, HttpServletResponse response) {
       List<CellPlanQtyFluctuationCoefficientDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);
       String fileName = BapsMessgeHelper.getMessage("export.cellplanqtyfluctuationcoefficient.table.name");
       ExcelUtils.exportExWithLocalDate(response, fileName, fileName, excelPara.getSimpleHeader(), excelData);
    }
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellPlanQtyFluctuationCoefficientExcelDTO> excelDtos = ExcelUtils.readExcel(multipartFile.getInputStream(), null, CellPlanQtyFluctuationCoefficientExcelDTO.class, excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        //验证数据
        checkInput(excelDtos);
        List<CellPlanQtyFluctuationCoefficientSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        saveDTOS.stream().forEach(saveDTO -> {
            save(saveDTO);
        });

    }
    private void checkInput(List<CellPlanQtyFluctuationCoefficientExcelDTO> excelDTOS) {
        checkNull(excelDTOS);
        checkUique(excelDTOS);

    }
    private void checkNull(List<CellPlanQtyFluctuationCoefficientExcelDTO> excelDTOS) {
        Set<String> workshopSet = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP).values().stream().map(LovLineDTO::getLovName)
                .collect(Collectors.toSet());
        for (int i = 0; i <excelDTOS.size() ; i++) {
            CellPlanQtyFluctuationCoefficientExcelDTO dto = excelDTOS.get(i);
            if (dto.getStartTime()==null || dto.getEndTime()==null || StringUtils.isBlank(dto.getFluctuationCoefficientPercent()) || StringUtils.isBlank(dto.getWorkshopName())) {
                String message = BapsMessgeHelper.getMessage("cellplanqtyfluctuationcoefficient.import.row.not.null",new Object[]{i+1});
                throw new BizException(message);
            }
            if (dto.getStartTime().isAfter(dto.getEndTime())){
                String message = BapsMessgeHelper.getMessage("the.row.starttime.must.not.after.endtime",new Object[]{i+1});
                throw new BizException(message);
            }
            if (!workshopSet.contains(dto.getWorkshopName())){
                String message = BapsMessgeHelper.getMessage("the.row.workshop.not.exists",new Object[]{i+1});
                throw new BizException (message);
            }
            //验证浮动系数是百分数格式
            checkPercentage(dto);
            if ( !isValidPercentageFormat(dto.getFluctuationCoefficientPercent())) {
                String message = BapsMessgeHelper.getMessage("cellplanqtyfluctuationcoefficient.import.coefficientPercent.format.error",new Object[]{i+1});
                throw new BizException (message);
            }
        }
    }

    private void checkPercentage(CellPlanQtyFluctuationCoefficientExcelDTO dto) {
        String str = dto.getFluctuationCoefficientPercent();
        if (StringUtils.isBlank(str)){
            return;
        }
        //正则表达式，匹配如10.85的小数格式
        String regex = "^[0-9]+\\.[0-9]+$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        if (matcher.matches()) {
            //小数格式字符串转成BigDecimal
            BigDecimal bigDecimal = new BigDecimal(str);
            String val = MapStrutUtil.addPercentage(bigDecimal, 2);
            dto.setFluctuationCoefficientPercent(val);
        }
    }


    private   boolean isValidPercentageFormat(String str) {
        if (StringUtils.isBlank(str)){
            return false;
        }
        // 正则表达式，匹配如10.85%的格式
        String regex = "^[0-9]+(?:\\.[0-9]+)?%$";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);

        return matcher.matches();
    }

    private void checkUique(List<CellPlanQtyFluctuationCoefficientExcelDTO> excelDTOS) {
        Map<String, List<CellPlanQtyFluctuationCoefficientExcelDTO>> collect = excelDTOS.stream().collect(Collectors.groupingBy(CellPlanQtyFluctuationCoefficientExcelDTO::getWorkshopName));
        List<String> errors = new ArrayList<>();
        collect.entrySet().forEach(item->{
            String key=item.getKey();
            List<CellPlanQtyFluctuationCoefficientExcelDTO> values = item.getValue();
            List<TimeRange> ranges = values.stream().map(v -> {
                return new TimeRange(v.getStartTime(), v.getEndTime());
            }).collect(Collectors.toList());
            Boolean  isOverlap= hasOverlap(ranges);
            if (isOverlap) {
                String message = BapsMessgeHelper.getMessage("the.row.time.repeat",new Object[]{key});
                errors.add(message);
            }
        });
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining("\n"));
            throw new BizException(errorString);
        }
    }

    public   boolean hasOverlap(List<TimeRange> timeRanges) {
        for (int i = 0; i < timeRanges.size(); i++) {
            TimeRange range1 = timeRanges.get(i);
            for (int j = i + 1; j < timeRanges.size(); j++) {
                TimeRange range2 = timeRanges.get(j);
                if (range1.overlaps(range2)) {
                    return true; // 有重叠部分，返回true
                }
            }
        }
        return false; // 没有重叠部分，返回false
    }
}
