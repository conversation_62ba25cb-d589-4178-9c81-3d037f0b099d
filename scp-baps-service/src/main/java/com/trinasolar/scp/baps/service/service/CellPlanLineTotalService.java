package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellPlanLineContrastDTO;
import com.trinasolar.scp.common.api.util.DataColumn;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.common.api.util.exStrategy.CellStyleModel;
import org.apache.commons.lang3.tuple.Pair;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalHDTO;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineTotal;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineTotalSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 入库计划汇总表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
public interface CellPlanLineTotalService extends IExcelImportData {
    /**
     * 分页获取入库计划汇总表
     *
     * @param query 查询对象
     * @return 入库计划汇总表分页对象
     */
    Page<CellPlanLineTotalDTO> queryByPage(CellPlanLineTotalQuery query, Pair<String, String> versions);

    /**
     * 获取版本号
     * @param query
     * @param sendedEmailVersionFlag flase = 获取当前最新版本  true = 获取已发送邮件的最新版本
     * @return
     */
    Pair<String, String> getSendedEmailOrLastVersion(CellPlanLineTotalQuery query, Boolean sendedEmailVersionFlag);

    public List<CellPlanLineTotalDTO> query(CellPlanLineTotalQuery query,String month,Long isOversea,String version);

    /**
     * 根据主键获取入库计划汇总表详情
     *
     * @param id 主键
     * @return 入库计划汇总表详情
     */
        CellPlanLineTotalDTO queryById(Long id);

    /**
     * 保存或更新入库计划汇总表
     *
     * @param saveDTO 入库计划汇总表保存对象
     * @return 入库计划汇总表对象
     */
    CellPlanLineTotalDTO save(CellPlanLineTotalSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除入库计划汇总表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellPlanLineTotalQuery query, HttpServletResponse response);

    public void email(CellPlanLineTotalQuery query);

    void confirm(CellPlanLineTotalQuery query);
//    void mate5A(CellPlanLineTotalQuery query);

    void matchItemCallBack(CellPlanLineTotalQuery query);

    void makeCellInstockPlan(CellPlanLineTotalQuery query);

    List<CellPlanLineTotal> queryByVersion(String version,String month);

    /**
     * 获取投产计划最新3个版本
     * @param query
     * @return
     */
    List<String> queryThreeMaxVersion(CellPlanLineTotalQuery query);

    /**
     * 获取投产计划最新版本
     * @param query
     * @return
     */
    String queryMaxVersion(CellPlanLineTotalQuery query);

    /**
     * 获取最新版本的数据
     * @param query
     * @return
     */
    List<CellPlanLineTotalDTO> queryByFirst(CellPlanLineTotalQuery query);

    Page<CellPlanLineTotalDTO> queryTwnMonthByPage(CellPlanLineTotalQuery query);

    Page<CellPlanLineTotalDTO> queryByPageForMrp(CellPlanLineTotalQuery query);

    void exportForMrp(CellPlanLineTotalQuery query, HttpServletResponse response);

    void twoMonthExport(CellPlanLineTotalQuery query, HttpServletResponse response);

    void exportH(CellPlanLineTotalQuery query, HttpServletResponse response);

    Page<CellPlanLineTotalHDTO> queryHByPage(CellPlanLineTotalQuery query);

    void handleCellPlanLine(CellPlanLineQuery query);

    void calcOldQtyPc(List<CellPlanLineDTO> dtos);

    void checkContrast(CellPlanLineTotalQuery query,Pair<String, String> versions,Page<CellPlanLineTotalDTO> cellPlanLineTotalDTOS);

    List<CellPlanLineContrastDTO> changeContrast(CellPlanLineTotalDTO source, CellPlanLineTotalDTO target) throws NoSuchFieldException, IllegalAccessException;

    List<CellStyleModel> differentialJudgment(List<DataColumn> columns, List<CellPlanLineTotalDTO> dtos, List<CellPlanLineTotalDTO> sendedEmailDTOS);

    List<CellPlanLineTotalDTO> getMaxVersion();

    void setCellSource(List<CellPlanLineDTO> cellPlanLineDTOS);

    void matchEcsCodeByWorkshop(List<CellPlanLineDTO> cellPlanLineDTOS);
}

