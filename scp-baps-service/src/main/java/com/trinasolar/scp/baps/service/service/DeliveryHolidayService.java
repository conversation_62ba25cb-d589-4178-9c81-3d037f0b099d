package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.DeliveryHolidayDTO;
import com.trinasolar.scp.baps.domain.entity.DeliveryHoliday;
import com.trinasolar.scp.baps.domain.query.DeliveryHolidayQuery;
import com.trinasolar.scp.baps.domain.save.DeliveryHolidaySaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 物流节假日表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-11 11:09:00
 */
public interface DeliveryHolidayService {
    /**
     * 分页获取物流节假日表
     *
     * @param query 查询对象
     * @return 物流节假日表分页对象
     */
    Page<DeliveryHolidayDTO> queryByPage(DeliveryHolidayQuery query);

    /**
     * 根据主键获取物流节假日表详情
     *
     * @param id 主键
     * @return 物流节假日表详情
     */
        DeliveryHolidayDTO queryById(Long id);

    /**
     * 保存或更新物流节假日表
     *
     * @param saveDTO 物流节假日表保存对象
     * @return 物流节假日表对象
     */
    DeliveryHolidayDTO save(DeliveryHolidaySaveDTO saveDTO);

    /**
     * 根据主键逻辑删除物流节假日表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(DeliveryHolidayQuery query, HttpServletResponse response);

    void saveAll(List<DeliveryHolidaySaveDTO> saveDTOS);
}

