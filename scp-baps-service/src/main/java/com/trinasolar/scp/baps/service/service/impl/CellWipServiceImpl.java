package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.nacos.shaded.com.google.common.base.Splitter;
import com.netflix.hystrix.contrib.javanica.utils.CommonUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellPlanLineDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellWipTotalDEConvert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.dto.erp.ERPResponse;
import com.trinasolar.scp.baps.domain.dto.erp.ESBResponse;
import com.trinasolar.scp.baps.domain.query.CellPlanLineQuery;
import com.trinasolar.scp.baps.domain.query.erp.ErpAlternateDesignatorQuery;
import com.trinasolar.scp.baps.domain.convert.CellProductionPlanDEConvert;
import com.trinasolar.scp.baps.domain.convert.CellWipDEConvert;
import com.trinasolar.scp.baps.domain.dto.erp.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.baps.domain.dto.erp.WipWorkOrderDTO;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.query.CellProductionPlanQuery;
import com.trinasolar.scp.baps.domain.query.CellWipQuery;
import com.trinasolar.scp.baps.domain.query.ClassCodeQuery;
import com.trinasolar.scp.baps.domain.save.CellWipSaveDTO;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.feign.ERPFeign;
import com.trinasolar.scp.baps.service.repository.*;
import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

/**
 * 工单生成预览
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Slf4j
@Service("cellWipService")
@RequiredArgsConstructor
public class CellWipServiceImpl implements CellWipService {
    private static final QCellWip qCellWip = QCellWip.cellWip;

    private static final QCellPlanLine qCellPlanLine = QCellPlanLine.cellPlanLine;

    private final CellWipDEConvert convert;

    private final CellWipTotalDEConvert cellWipTotalDEConvert;

    private final CellPlanLineDEConvert cellPlanLineDEConvert;

    private final CellWipRepository repository;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    private final CellPlanLineRepository cellPlanLineRepository;

    private final ApsFeign apsFeign;

    private final ERPFeign erpFeign;

    @Autowired
    RedissonClient redissonClient;

    private final TransactionService transactionService;

    private final CellWipTotalRepository cellWipTotalRepository;

    private final CellPlanLineService cellPlanLineService;
    private static final QCellClimbCapacityLead qCellClimbCapacityLead = QCellClimbCapacityLead.cellClimbCapacityLead;


    @Override
    public Page<CellWipDTO> queryByPage(CellWipQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellWip> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellWipQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellWip.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getWipCode())){
            booleanBuilder.and(qCellWip.wipCode.eq(query.getWipCode()));
        }
        if(StringUtils.isNotEmpty(query.getCategory())){
            booleanBuilder.and(qCellWip.category.eq(query.getCategory()));
        }
        if(StringUtils.isNotEmpty(query.getItemCode())){
            booleanBuilder.and(qCellWip.itemCode.eq(query.getItemCode()));
        }
        if(StringUtils.isNotEmpty(query.getState())){
            booleanBuilder.and(qCellWip.state.eq(query.getState()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qCellWip.workshop.eq(query.getWorkshop()));
        }
        if (query.getStartTime() != null) {
            booleanBuilder.and(qCellWip.startTime.eq(query.getStartTime()));
        }
        if (query.getEndTime() != null) {
            booleanBuilder.and(qCellWip.endTime.eq(query.getEndTime()));
        }
        if(StringUtils.isNotEmpty(query.getProjectCode())){
            booleanBuilder.and(qCellWip.projectCode.eq(query.getProjectCode()));
        }
        if(StringUtils.isNotEmpty(query.getAlternateRoutingDesignator())){
            booleanBuilder.and(qCellWip.alternateRoutingDesignator.eq(query.getAlternateRoutingDesignator()));
        }
        if(StringUtils.isNotEmpty(query.getAlternateBomDesignator())){
            booleanBuilder.and(qCellWip.alternateBomDesignator.eq(query.getAlternateBomDesignator()));
        }
        if(StringUtils.isNotEmpty(query.getOrganizationId())){
            booleanBuilder.and(qCellWip.organizationId.eq(query.getOrganizationId()));
        }
        if(StringUtils.isNotEmpty(query.getIsH())){
            booleanBuilder.and(qCellWip.isH.eq(query.getIsH()));
        }
        if (query.getQty() != null) {
            booleanBuilder.and(qCellWip.qty.eq(query.getQty()));
        }
        if(StringUtils.isNotEmpty(query.getBondedForm())){
            booleanBuilder.and(qCellWip.bondedForm.eq(query.getBondedForm()));
        }
        if(StringUtils.isNotEmpty(query.getOutsourcingFactory())){
            booleanBuilder.and(qCellWip.outsourcingFactory.eq(query.getOutsourcingFactory()));
        }
        if(StringUtils.isNotEmpty(query.getIsAdvanceProduction())){
            booleanBuilder.and(qCellWip.isAdvanceProduction.eq(query.getIsAdvanceProduction()));
        }
        if(StringUtils.isNotEmpty(query.getIsSell())){
            booleanBuilder.and(qCellWip.isSell.eq(query.getIsSell()));
        }
        if(StringUtils.isNotEmpty(query.getReliability())){
            booleanBuilder.and(qCellWip.reliability.eq(query.getReliability()));
        }
        if (query.getVerificationDate() != null) {
            booleanBuilder.and(qCellWip.verificationDate.eq(query.getVerificationDate()));
        }
    }

    @Override
    public CellWipDTO queryById(Long id) {
        CellWip queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellWipDTO save(CellWipSaveDTO saveDTO) {
        CellWip newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new CellWip());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellWipQuery query, HttpServletResponse response) {
       List<CellWipDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "工单生成预览", "工单生成预览", excelPara.getSimpleHeader(), excelData);
    }
    /**
     * 前端推送的cellPlanLine数据组装
     *
     * @param query
     * @return
     */
    @Override
    public List<CellWipDTO> ticketPreview(CellPlanLineQuery query) {
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        //1.具有转换功能的对象
        DateTimeFormatter dfs = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startTime="";
        String endTime="";
        //如果日期拼接包含多个 拆分第一个到开始时间 截取最后一个
        if(query.getStrTime().indexOf(",")>0){
            String firstIndexOf = String.valueOf(query.getStrTime().indexOf(","));
             startTime =query.getStrTime().substring(0,Integer.parseInt(firstIndexOf));
             endTime =query.getStrTime().substring(query.getStrTime().lastIndexOf(",")+1);
        }else{
            startTime= query.getStrTime();
        }

        List<CellWipDTO>cellWipDTOList=new ArrayList<>();
        CellWipDTO cellWip=new CellWipDTO();
        ClassCodeQuery classCodeQuery=new ClassCodeQuery();
        //车间
        classCodeQuery.setWorkshop(query.getWorkshop());
        List<ClassCodeDTO>classCodeDTOList=apsFeign.queryClassCode(classCodeQuery).getBody().getData();
        if(CollectionUtils.isNotEmpty(classCodeDTOList)){
            //分类
            cellWip.setCategory(classCodeDTOList.get(0).getClassCode());

        }
        cellWip.setHTrace(query.getHTrace());
        if(query.getHTrace().contains("H")){
            cellWip.setIsH("是");
        }else {
            cellWip.setIsH("否");
        }
        //片 数量
        //判断日期是多选还是单元
        if(query.getStrTime().contains(",")){
            List<String> list = Splitter.on(",").splitToList(query.getStrTime());
            final BigDecimal[] totalQuantity = {BigDecimal.ZERO};
            list.stream().forEach(str->{
                totalQuantity[0] = totalQuantity[0].add(query.getSubMap().get(str));
            });
            cellWip.setQty(totalQuantity[0].multiply(new BigDecimal("10000")));
        }else{
            cellWip.setQty(query.getSubMap().get(query.getStrTime()).multiply(new BigDecimal("10000")));
        }
        //装配件==5A料号
        cellWip.setItemCode(query.getItemCode());
        cellWip.setWorkshop(query.getWorkshop());
        cellWip.setStartTime(LocalDate.parse(startTime, dfs).atStartOfDay());
        if(StringUtils.isEmpty(endTime)){
            cellWip.setEndTime(LocalDate.parse(startTime, dfs).atStartOfDay());
        }else{
            cellWip.setEndTime(LocalDate.parse(endTime, dfs).atStartOfDay());
        }
        //默认
        cellWip.setState("3");
        //x.setStateName("已释放");
        /**********暂定************/
        //是否外售
        if(StringUtils.isNotEmpty(query.getSourceType())&&query.getSourceType().contains("外销需求")){
            cellWip.setIsSell("Y");
        }else{
            cellWip.setIsSell("N");
        }
        //可靠性标识
        if(StringUtils.isNotEmpty(query.getVerificationMark())&&query.getVerificationMark().contains("可靠性")){
            cellWip.setReliability("Y");
            CellClimbCapacityLead climbCapacityLead=jpaQueryFactory
                    .select(qCellClimbCapacityLead)
                    .from(qCellClimbCapacityLead)
                    .where(qCellClimbCapacityLead.isDeleted.eq(0)
                            .and(qCellClimbCapacityLead.cellType.eq(query.getCellsType()))
                            .and(qCellClimbCapacityLead.basePlace.eq(query.getBasePlace()))
                            .and(qCellClimbCapacityLead.workShop.eq(query.getWorkshop()))
                            .and(qCellClimbCapacityLead.workUnit.eq(query.getWorkunit()))
                            .and(qCellClimbCapacityLead.lineName.eq(query.getLineName()))).fetchFirst();
            if(null!=climbCapacityLead){
                cellWip.setVerificationDate(climbCapacityLead.getValidateTime());
            }
            //获取可靠性验证日期
        }else{
            cellWip.setReliability("N");
        }

        //bom替代项
        //根据车间获取inventory_organization 库存组织代码对照关系
        LovLineDTO lovLineDTO = LovUtils.get(LovHeaderCodeConstant.WORK_SHOP, cellWip.getWorkshop());
        if(null!=lovLineDTO){
            LovLineDTO lovLineDTO_Module = LovUtils.get(LovHeaderCodeConstant.INVENTORY_ORGANIZATION, lovLineDTO.getAttribute10());
            if(null!=lovLineDTO_Module){
                cellWip.setOrganizationId(lovLineDTO_Module.getAttribute1());
                //拿到库存erp_org_id在查询
                if(StringUtils.isNotEmpty(lovLineDTO_Module.getAttribute1())){
                    ErpAlternateDesignatorQuery designatorQuery=new ErpAlternateDesignatorQuery();
                    designatorQuery.setDescription(cellWip.getWorkshop());
                    designatorQuery.setOrganizationId(Long.valueOf(lovLineDTO_Module.getAttribute1()));
                    List<ErpAlternateDesignatorDTO>designatorDTOList=apsFeign.getAlternateDesignatorCode(designatorQuery).getBody().getData();
                    if(CollectionUtils.isNotEmpty(designatorDTOList)){
                        cellWip.setAlternateBomDesignator(designatorDTOList.get(0).getAlternateDesignatorCode());
                    }
                }
            }else{
                throw  new BizException(cellWip.getWorkshop()+"没有维护对应的BOM替代项");
            }

        }
        cellWip.setFinalVersion(query.getFinalVersion());

        cellWip.setIsOverseaId(query.getIsOverseaId());
        cellWip.setIsOversea(query.getIsOversea());
        //返工标识 默认为N
        cellWip.setReworkFlag("N");
        cellWipDTOList.add(cellWip);
        return cellWipDTOList;
    }

    /**
     * 工单下发到erp 的先根据装配件==5A料号+车间从CellWipDTO 获取数据汇总数量 对比投产明细的汇总数据(到月)  如果超出则提示
     * 相反走erp接口下发 成功保存开单预览和工单记录
     * @param cellWipDTOs
     * @return
     */
    @Override
    public List<CellWipDTO> cellWipCreate(List<CellWipDTO> cellWipDTOs) {
        CellWipDTO cellWipDTO=cellWipDTOs.stream().filter(willDto->"N".equals(willDto.getReworkFlag())).collect(Collectors.toList()).get(0);
        //根据料号+车间查询工单信息 汇总数量
        BooleanBuilder booleanBuilder = new BooleanBuilder()
                .and(qCellWip.itemCode.in(cellWipDTO.getCellPlanLineDTOList().get(0).getItemCode()))
                .and(qCellWip.workshop.eq(cellWipDTO.getWorkshop()))
                .and(qCellWip.month.eq(cellWipDTO.getMonth()))
                .and(qCellWip.finalVersion.eq(cellWipDTO.getFinalVersion())
                .and(qCellWip.isOverseaId.eq(cellWipDTO.getIsOverseaId()))
                        .and(qCellWip.reworkFlag.eq("N")));
        List<CellWipDTO>cellWipDTOList=convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
        BigDecimal cellWipTotalQuantity=BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(cellWipDTOList)){
            cellWipTotalQuantity=cellWipDTOList.stream().map(wipDTO->Optional.ofNullable(wipDTO.getQty()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            cellWipTotalQuantity=cellWipTotalQuantity.divide(new BigDecimal("10000"));
        }
        List<CellPlanLineDTO>planDTOList=queryCellPlanLineByFinalVersion(cellWipDTO);
        BigDecimal planDtoTotalQuantity=BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(planDTOList)){
            planDtoTotalQuantity=planDTOList.stream().map(planDTO->Optional.ofNullable(planDTO.getQtyPc()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if(cellWipTotalQuantity.compareTo(planDtoTotalQuantity)>0){
            String error="车间:"+cellWipDTO.getWorkshop()+"且料号:"+cellWipDTO.getItemCode()+"在"+cellWipDTO.getMonth()+"月份总量大于投产明细数量";
            throw new BizException(error);
        }
        return cellWipDTOs;
    }
    @Override
    public List<CellWipDTO> saveContinueCreateWip(List<CellWipDTO> cellWipDTO) {
        String redisKey = "CellWipServiceImpl.saveContinueCreateWip";
        RLock lock = redissonClient.getLock(redisKey);
        if (lock.tryLock()) {
            try {
                    callErp(cellWipDTO);
            }catch (Exception e){
                throw new BizException(e.getMessage());
            }finally {
                if (lock != null && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            throw new RuntimeException("其他用户正在操作中，请稍后");
        }
        return cellWipDTO;
    }
    public void callErp(List<CellWipDTO> cellWipDTOs){

        try {
            transactionService.begin();
            for (CellWipDTO cellWipDTO : cellWipDTOs) {
                String sourceId = UUID.randomUUID().toString();
                String sourceCode = "SCP";
                String processType = "CREATE";
                String processStatus = "PENDING";
                /*LocalDateTime localDateTime = LocalDateTimeUtil.parse(cellWipDTO.getStartTime()
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                if (localDateTime == null) {
                    log.warn("[DpScheduleLinesServiceImpl.callErp]数据异常,创建时间为空");
                    throw new BizException("数据异常,起始时间为空");
                }
                Date scheduledCompletionDate = localDateTime.isBefore(LocalDateTime.now()) ?
                        DateTime.of(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 23:59:59",
                                "yyyy-MM-dd HH:mm:ss") :
                        Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());*/
                //根据车间获取inventory_organization 库存组织代码对照关系
                LovLineDTO lovLineDTO = LovUtils.get(LovHeaderCodeConstant.WORK_SHOP, cellWipDTO.getWorkshop());
                if (null != lovLineDTO) {
                    LovLineDTO lovLineDTO_Module = LovUtils.get(LovHeaderCodeConstant.INVENTORY_ORGANIZATION, lovLineDTO.getAttribute10());
                    cellWipDTO.setOrganizationId(lovLineDTO_Module.getAttribute1());
                } else {
                    cellWipDTO.setOrganizationId("0");
                }
                LovLineDTO lineDTO = LovUtils.getByName("5A00100100135", cellWipDTO.getHTrace());
                LocalDateTime   verificationDate =  cellWipDTO.getVerificationDate() ;
                String verificationDateStr = null;
                if (null!=verificationDate){
                    verificationDateStr = verificationDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                }
                WipWorkOrderDTO wipWorkOrderDTO = WipWorkOrderDTO.builder()
                        .wipEntityName(cellWipDTO.getWipCode())
                        .sourceId(sourceId)
                        .sourceCode(sourceCode)
                        .processType(processType)
                        .processStatus(processStatus)
                        .organizationId(Long.parseLong(cellWipDTO.getOrganizationId()))//4146L
                        .attributeCategory(cellWipDTO.getOrganizationId())// 4146L
                        .statusType(3L)
                        .primaryItemNum(cellWipDTO.getCellPlanLineDTOList().get(0).getItemCode())//5A料号  5A002228
                        .jobType(1L)
                        .classCode(cellWipDTO.getCategory())// S_CE2_STD
                        .scheduledCompletionDate(Date.from(cellWipDTO.getStartTime().atZone(ZoneId.systemDefault()).toInstant()))
                        .startQuantity(cellWipDTO.getQty())
                        .attribute7(cellWipDTO.getWorkshop())
                        .alternateRoutingDesignator(cellWipDTO.getAlternateRoutingDesignator())
                        .attribute8(cellWipDTO.getProjectCode())
                        .attribute3(cellWipDTO.getWipCode())
                        .attribute9(null != lineDTO ? lineDTO.getAttribute2() : "")//是否哈密瓜
                        .attribute5(cellWipDTO.getBondedForm())//保税形态
                        .attribute6(cellWipDTO.getIsAdvanceProduction())//是否提前投产
                        .attribute15(cellWipDTO.getOutsourcingFactory())//外协厂商
                        .attribute16(cellWipDTO.getIsSell())//是否外售
                        .attribute17(cellWipDTO.getReliability())//是否可靠性标识
                        .attribute18(verificationDateStr)//可靠性验证日期
                        .alternateBomDesignator(cellWipDTO.getAlternateBomDesignator())
                        .build();
                ESBResponse<ERPResponse> response = erpFeign.wipAdd(wipWorkOrderDTO);
                if (!ESBResponse.SUCCESS.equals(response.getBizcode()) ||
                        !ERPResponse.SUCCESS.equals(Optional.ofNullable(response).map(ESBResponse::getData)
                                .map(ERPResponse::getXReturnCode).orElse(null))) {
                    //System.out.println(Optional.ofNullable(response).map(ESBResponse::getBizmsg).orElse(""));
                    throw new BizException("调用ERP工单下发接口异常:" + Optional.ofNullable(response).map(ESBResponse::getBizmsg).orElse("") +
                            Optional.ofNullable(response).map(ESBResponse::getData).map(ERPResponse::getXReturnMesg).orElse(""));
                }
                cellWipDTO.setIsOverseaId(cellWipDTO.getCellPlanLineDTOList().get(0).getIsOverseaId());
                cellWipDTO.setIsOversea(cellWipDTO.getCellPlanLineDTOList().get(0).getIsOversea());
                //保存CellWipDto、和CellWipTotalDTO
                repository.save(convert.toEntity(cellWipDTO));
                CellWipTotalDTO wipTotalDTO = CellWipTotalDTO.builder()
                        .billingAccount(UserUtil.getUserById(cellWipDTO.getUserId()).getName())
                        .billingDate(LocalDateTime.now())
                        .erpOrderCode(cellWipDTO.getWipCode())
                        .month(cellWipDTO.getMonth())
                        .basePlace(cellWipDTO.getCellPlanLineDTOList().get(0).getBasePlace())
                        .workshop(cellWipDTO.getCellPlanLineDTOList().get(0).getWorkshop())
                        .qty(cellWipDTO.getCellPlanLineDTOList().get(0).getQtyPc().multiply(new BigDecimal("10000")))
                        .qtyBilling(cellWipDTO.getQty())
                        .qtyBillingTotal(cellWipDTO.getQty())
                        .cellType(cellWipDTO.getCellPlanLineDTOList().get(0).getCellsType())
                        .itemFivea(cellWipDTO.getItemCode())
                        .isOversea(cellWipDTO.getCellPlanLineDTOList().get(0).getIsOversea())
                        .startTime(cellWipDTO.getStartTime())
                        .finalVersion(cellWipDTO.getFinalVersion())
                        .isOverseaId(cellWipDTO.getCellPlanLineDTOList().get(0).getIsOverseaId())
                        .build();
                cellWipTotalRepository.save(cellWipTotalDEConvert.toEntity(wipTotalDTO));
                //todo
                //保存完需要更新投产明细数据 根据cellWipDTO.getStartTime()、cellWipDTO.getEndTime()、生产基地、生产车间、月份、电池类型查询明细
                // 更新明细装配件5A料号、车间 为预览的对应数据
                CellPlanLineQuery planQuery = new CellPlanLineQuery();
                planQuery.setStrTime(cellWipDTO.getCellPlanLineDTOList().get(0).getStrTime());
                planQuery.setBasePlace(cellWipDTO.getCellPlanLineDTOList().get(0).getBasePlace());
                planQuery.setWorkshop(cellWipDTO.getCellPlanLineDTOList().get(0).getWorkshop());
                planQuery.setMonth(cellWipDTO.getCellPlanLineDTOList().get(0).getMonth());
                planQuery.setCellsType(cellWipDTO.getCellPlanLineDTOList().get(0).getCellsType());
                planQuery.setFinalVersion(cellWipDTO.getFinalVersion());
                List<CellPlanLineDTO> planDTOList = cellPlanLineService.queryMaxVersionByCellWip(planQuery);
                if (CollectionUtils.isNotEmpty(planDTOList)) {
                    planDTOList.stream().forEach(y -> {
                        y.setItemCode(cellWipDTO.getItemCode());
                        y.setWorkshop(cellWipDTO.getWorkshop());
                    });
                    cellPlanLineRepository.saveAll(cellPlanLineDEConvert.toEntity(planDTOList));
                }
            }
            transactionService.commit();
        } catch (Exception e) {
            transactionService.rollback();
            throw e;
        }

    }

    public List<CellPlanLineDTO> queryCellPlanLineByFinalVersion(CellWipDTO cellWipDTO) {
        JPAQuery<CellPlanLine> whereData = jpaQueryFactory.select(
                qCellPlanLine
        ).from(qCellPlanLine).where(
                qCellPlanLine.finalVersion.eq(cellWipDTO.getFinalVersion())
        );
        whereData.where(qCellPlanLine.month.eq(cellWipDTO.getMonth())
                .and(qCellPlanLine.workshop.eq(cellWipDTO.getWorkshop())
                .and(qCellPlanLine.itemCode.eq(cellWipDTO.getCellPlanLineDTOList().get(0).getItemCode())))
        );
        List<CellPlanLine> fetch = whereData.fetch();
        return cellPlanLineDEConvert.toDto(fetch);
    }
}
