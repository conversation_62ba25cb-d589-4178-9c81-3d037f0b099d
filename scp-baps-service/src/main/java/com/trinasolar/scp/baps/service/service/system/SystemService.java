package com.trinasolar.scp.baps.service.service.system;

import com.trinasolar.scp.common.api.base.LovLineDTO;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: SystemService
 * @date 2024/3/1 08:55
 */
public interface SystemService {

    /**
     * 查找
     *
     * @return
     */
    <R, S> Map<R, S> findByLovCode(String lovCode, Function<LovLineDTO, R> mapper1, Function<LovLineDTO, S> mapper2);

    /**
     * 获取电池车间库存组织
     *
     * @return
     */
    List<Long> findOrganizationIds();
}
