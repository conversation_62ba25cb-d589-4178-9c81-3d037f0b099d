package com.trinasolar.scp.baps.service.service.log.impl;
import com.trinasolar.scp.baps.domain.convert.ScheduledTaskLinesDEConverter;
import com.trinasolar.scp.baps.domain.dto.ScheduleTaskStatusEnum;
import com.trinasolar.scp.baps.domain.dto.ScheduledTaskLinesDTO;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.RedisLockKey;
import com.trinasolar.scp.baps.service.repository.ScheduledTaskLinesRepository;
import com.trinasolar.scp.baps.service.service.log.LogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
@Slf4j
@Service("logService")
@RequiredArgsConstructor
public class LogServiceImpl implements LogService {
    private final ScheduledTaskLinesDEConverter converter;
    private final ScheduledTaskLinesRepository repository;
    @Autowired
    private StringRedisTemplate redisTemplate;
    /**
     * 获取日志任务对象
     * @return
     */
    @Override
    public ScheduledTaskLinesDTO createLogTask(String taskName) {
        LocalDate now = LocalDate.now();
        String format = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String cacheKey = RedisLockKey.BAPS_SERIAL_NO + format;
        if (!redisTemplate.hasKey(cacheKey)) {
            redisTemplate.opsForValue().set(cacheKey, "0", DateUtil.getSeconds(), TimeUnit.SECONDS);
        }
        String value = redisTemplate.opsForValue().get(cacheKey);
        Long serialNo = null;
        if (StringUtils.isNotBlank(value)) {
            serialNo = Long.parseLong(value) + 1;
        } else {
            serialNo = 1L;
        }
        //放入缓存
        redisTemplate.opsForValue().increment(cacheKey, 1);
        //生成运行中状态数据
        ScheduledTaskLinesDTO scheduledTaskLines = ScheduledTaskLinesDTO.init();
        //年月日+4位流水号
        scheduledTaskLines.setTaskNumber(format + String.format("%04d", serialNo));
        scheduledTaskLines.setTaskName(taskName);
        return scheduledTaskLines;
    }

    /**
     * 向任务中添加详细日志
     * @param task
     * @param status
     * @param log
     */
    @Override
    public void addLog(ScheduledTaskLinesDTO task, ScheduleTaskStatusEnum status, String log) {
        if (task==null)return;
        String taskLog = Optional.ofNullable(task.getLog()).orElse("");
        String desc=status.getDesc();
        if (Objects.equals(task.getStatus(),ScheduleTaskStatusEnum.WARN.getDesc())){
            if (Objects.equals(status.getDesc(),ScheduleTaskStatusEnum.SUCCESS.getDesc()) || Objects.equals(status.getDesc(),ScheduleTaskStatusEnum.RUNNING.getDesc())){
                desc=ScheduleTaskStatusEnum.WARN.getDesc();
            }
        }
        task.setStatus(desc);
        if (StringUtils.isNotEmpty(taskLog)){
            taskLog=taskLog+"\n";
        }
        if (Objects.equals(status.getDesc(),ScheduleTaskStatusEnum.WARN.getDesc()) || Objects.equals(status.getDesc(),ScheduleTaskStatusEnum.ERROR.getDesc())){
            task.setLog(taskLog + status.getDesc()+":"+log);
        }else{
            task.setLog(taskLog + log);
        }

        task.setCompleteDate(LocalDateTime.now());
    }
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    /**
     * 保存任务日志
     * @param task
     */
    @Override
    public void saveTaskLog(ScheduledTaskLinesDTO task) {
        if (task!=null){
            repository.save(converter.toEntity(task));
        }

    }

}
