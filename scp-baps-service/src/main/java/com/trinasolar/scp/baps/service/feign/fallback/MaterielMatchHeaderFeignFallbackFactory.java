package com.trinasolar.scp.baps.service.feign.fallback;

import com.trinasolar.scp.baps.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.baps.service.feign.MaterielMatchHeaderFeign;
import com.trinasolar.scp.common.api.util.Results;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
@Component
@Slf4j
public class MaterielMatchHeaderFeignFallbackFactory implements FallbackFactory<MaterielMatchHeaderFeign> {

    @Override
    public MaterielMatchHeaderFeign create(Throwable cause) {
        return new MaterielMatchHeaderFeign() {
            @Override
            public ResponseEntity<Results<Map<Long, List<String>>>> query4AByMatchHeadDto(List<MaterielMatchHeaderDTO> headerDTO) {
                log.warn("【BbomFeign】获取4A料号发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Map<Long, String>>> query5AByMatchHeadDto(List<MaterielMatchHeaderDTO> headerDTOs) {
                log.warn("【BbomFeign】获取5A发生异常：{}", cause);
                return Results.createFailRes();
            }
        };
    }
}
