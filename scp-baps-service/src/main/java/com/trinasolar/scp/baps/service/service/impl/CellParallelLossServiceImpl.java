package com.trinasolar.scp.baps.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellParallelLossDTO;
import com.trinasolar.scp.baps.domain.convert.CellParallelLossDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellParallelLoss;
import com.trinasolar.scp.baps.domain.entity.QCellParallelLoss;
import com.trinasolar.scp.baps.domain.excel.CellLossExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellParallelLossExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellParallelLossQuery;
import com.trinasolar.scp.baps.domain.save.CellParallelLossSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellParallelLossRepository;
import com.trinasolar.scp.baps.service.service.CellParallelLossService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * 产能并行损失表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Slf4j
@Service("cellParallelLossService")
@RequiredArgsConstructor
public class CellParallelLossServiceImpl implements CellParallelLossService {
    private static final QCellParallelLoss qCellParallelLoss = QCellParallelLoss.cellParallelLoss;

    private final CellParallelLossDEConvert convert;

    private final CellParallelLossRepository repository;

    @Override
    public Page<CellParallelLossDTO> queryByPage(CellParallelLossQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query=  convert.toCellParallelLossCNNameQuery(query, MyThreadLocal.get().getLang());
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellParallelLoss> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellParallelLossQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellParallelLoss.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getCellsType())){
            BooleanBuilder builder = new BooleanBuilder();
            builder.or(qCellParallelLoss.cellsType.eq(query.getCellsType()));
            builder.or(qCellParallelLoss.cellsTypeTwo.eq(query.getCellsType()));
            //booleanBuilder.and(qCellParallelLoss.cellsType.eq(query.getCellsType()));
            booleanBuilder.and(builder);
        }
        if(StringUtils.isNotEmpty(query.getParallelType())){
            booleanBuilder.and(qCellParallelLoss.parallelType.eq(query.getParallelType()));
        }
        if(StringUtils.isNotEmpty(query.getBasePlace())){
            booleanBuilder.and(qCellParallelLoss.basePlace.eq(query.getBasePlace()));
        }
        if(StringUtils.isNotEmpty(query.getWorkshop())){
            booleanBuilder.and(qCellParallelLoss.workshop.eq(query.getWorkshop()));
        }
        if(StringUtils.isNotEmpty(query.getWorkunit())){
            booleanBuilder.and(qCellParallelLoss.workunit.eq(query.getWorkunit()));
        }
        if (query.getLineNumber() != null) {
            booleanBuilder.and(qCellParallelLoss.lineNumber.eq(query.getLineNumber()));
        }
        if(StringUtils.isNotEmpty(query.getMonth())){
            booleanBuilder.and(qCellParallelLoss.month.eq(query.getMonth()));
        }
        if (query.getLoss() != null) {
            booleanBuilder.and(qCellParallelLoss.loss.eq(query.getLoss()));
        }
        if(StringUtils.isNotEmpty(query.getUnit())){
            booleanBuilder.and(qCellParallelLoss.unit.eq(query.getUnit()));
        }
    }

    @Override
    public CellParallelLossDTO queryById(Long id) {
        CellParallelLoss queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellParallelLossDTO save(CellParallelLossSaveDTO saveDTO) {
        CellParallelLoss newObj = Optional.ofNullable(saveDTO.getId())
            .map(id -> repository.getOne(saveDTO.getId()))
            .orElse(new CellParallelLoss());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellParallelLossQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellParallelLossDTO> dtos = queryByPage(query).getContent();
        // dto数据转为ExcelData数据
        List<CellParallelLossExcelDTO> excelDtos = convert.toExcelDTO(dtos);
        ExcelPara excelPara =  query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(excelDtos, excelPara);
        // 导出调用excelUtils
        String fileName= BapsMessgeHelper.getMessage("export.parallel.loss.table.name");
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);

    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellParallelLossExcelDTO> excelDtos = Lists.newArrayList();
        excelDtos=  ExcelUtils.readExcel(multipartFile.getInputStream(),null, CellParallelLossExcelDTO.class,excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        checkInput(excelDtos);
        List<CellParallelLossSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        saveDTOS=convert.toCnNameSaveDtoById(saveDTOS);
        // 先删除之前的数据,再保存信息的数据
         repository.deleteAll();
        saveDTOS.stream().parallel().forEach(this::save);
    }
    public void checkInput(List<CellParallelLossExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors=new ArrayList<>();
        excelDTOS.stream().forEach(excelDTO -> {
            //验证数据类型
            if(StringUtils.isNotEmpty(excelDTO.getParallelType())){
                LovLineDTO   lovLineDTO_ParallelType = LovUtils.getByName(LovHeaderCodeConstant.BAPS_PARALLET_LOSS_TYPE, excelDTO.getParallelType());
                if (lovLineDTO_ParallelType == null) {
                    String message = BapsMessgeHelper.getMessage("parallelLoss.import.parallelyype.not.exists", new Object[]{i[0],excelDTO.getParallelType()});
                    errors.add(message);
                }
            }else{
                String message = BapsMessgeHelper.getMessage("parallelLoss.import.parallelyype.not.null", new Object[]{i[0]});
                errors.add(message);
            }
            //验证电池类型1
            if(StringUtils.isNotEmpty(excelDTO.getCellsType())){
                LovLineDTO   lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsType());
                if (lovLineDTO == null) {
                    String message = BapsMessgeHelper.getMessage("parallelLoss.import.cellstype.not.exists", new Object[]{i[0],excelDTO.getCellsType()});
                    errors.add(message);
                }
            }else{
                String message = BapsMessgeHelper.getMessage("parallelLoss.import.cellstype.not.null", new Object[]{i[0]});
                errors.add(message);
            }


            //验证电池类型2
            if(StringUtils.isNotEmpty(excelDTO.getCellsTypeTwo())){
                LovLineDTO  lovLineDTO_cellsTypeTwo = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, excelDTO.getCellsTypeTwo());
                if (lovLineDTO_cellsTypeTwo == null) {
                    String message = BapsMessgeHelper.getMessage("parallelLoss.import.cellstypetwo.not.exists", new Object[]{i[0],excelDTO.getCellsTypeTwo()});
                    errors.add(message);
                }
            }else{
                String message = BapsMessgeHelper.getMessage("parallelLoss.import.cellstypetwo.not.null", new Object[]{i[0]});
                errors.add(message);
            }

            //验证生产基地
            LovLineDTO   lovLineDTO_BasePlace = null;
            if(StringUtils.isNotEmpty(excelDTO.getBasePlace())){
                lovLineDTO_BasePlace = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace());
                if (lovLineDTO_BasePlace == null) {
                    String message = BapsMessgeHelper.getMessage("the.row.baseplace.not.exists", new Object[]{i[0],excelDTO.getBasePlace()});
                    errors.add(message);
                }
            }else{
                String message = BapsMessgeHelper.getMessage("the.row.baseplace.not.null", new Object[]{i[0]});
                errors.add(message);
            }

            //验证生产车间
            LovLineDTO   lovLineDTO_WorkShop = null;
            if(StringUtils.isNotEmpty(excelDTO.getWorkshop())){
                lovLineDTO_WorkShop=  LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop());
                if (lovLineDTO_WorkShop == null) {
                    String message = BapsMessgeHelper.getMessage("the.row.workshop.not.exists", new Object[]{i[0],excelDTO.getWorkshop()});
                    errors.add(message);
                }

            }else{
                String message = BapsMessgeHelper.getMessage("the.row.workshop.not.null", new Object[]{i[0]});
                errors.add(message);
            }

            //验证车间与基地的关系
            if (lovLineDTO_WorkShop!=null && lovLineDTO_BasePlace!=null){
                if (!(lovLineDTO_WorkShop.getAttribute1()!=null&&lovLineDTO_WorkShop.getAttribute1().equals(lovLineDTO_BasePlace.getLovLineId().toString()))){
                    String message=BapsMessgeHelper.getMessage("the.row.workshop.not.in.baseplace", new Object[]{i[0],excelDTO.getWorkshop(),excelDTO.getBasePlace()});
                    errors.add(message);
                }
            }



            //验证生产单元
            LovLineDTO   lovLineDTO_WorkUnit = null;
            if (StringUtils.isNotEmpty(excelDTO.getWorkunit())){
                lovLineDTO_WorkUnit = LovUtils.getByName(LovHeaderCodeConstant.WORK_UNIT, excelDTO.getWorkunit());
                if (lovLineDTO_WorkUnit == null) {
                    String message = BapsMessgeHelper.getMessage("the.row.workunit.not.exists", new Object[]{i[0],excelDTO.getWorkunit()});
                    errors.add( message);
                }
            }else{
                String message = BapsMessgeHelper.getMessage("the.row.workunit.not.null", new Object[]{i[0]});
                errors.add( message);
            }

            if (lovLineDTO_WorkShop!=null && lovLineDTO_WorkUnit!=null){
                //验证单元与车间的关系
                if (!(lovLineDTO_WorkUnit.getAttribute2()!=null&&lovLineDTO_WorkUnit.getAttribute2().equals(lovLineDTO_WorkShop.getLovLineId().toString()))){
                    String message=BapsMessgeHelper.getMessage("the.row.workunit.not.in.workshop", new Object[]{i[0],excelDTO.getWorkunit(),excelDTO.getWorkshop()});
                    errors.add(message);
                }
            }

            i[0]++;
        });
        if (errors.size()>0){
            String errorString = errors.stream()
                    .collect(Collectors.joining(","));
            throw new BizException(errorString);
        }

    }
}
