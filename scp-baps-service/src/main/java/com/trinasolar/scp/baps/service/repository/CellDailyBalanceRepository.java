package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellDailyBalance;
import com.trinasolar.scp.baps.domain.utils.CellDailyBalanceConstant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;

/**
 * 每日结存报表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-12 06:19:24
 */
@Repository
public interface CellDailyBalanceRepository extends JpaRepository<CellDailyBalance, Long>, QuerydslPredicateExecutor<CellDailyBalance> {
    @Transactional(rollbackFor = Exception.class)
   @Modifying
   @Query("delete  from CellDailyBalance c  where c.isOversea= :isOversea and c.project='"+ CellDailyBalanceConstant.OUTSOURCING +"' and c.month= :month ")
    public void deleteOverseaPurchaseByMonth(@Param("isOversea") String isOversea,@Param("month") String month);
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("delete from CellDailyBalance c  where c.isOversea= :isOversea and  c.project='"+CellDailyBalanceConstant.CELL_DEMAND+"' and c.month= :month ")
    public void deleteDemandPlanByMonth(@Param("isOversea") String isOversea,@Param("month") String month);
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    @Query("delete from CellDailyBalance c  where c.isOversea= :isOversea and  c.project='"+CellDailyBalanceConstant.MADE_BY_ONESELF+"' and c.month= :month ")
    void deleteMadeByOneSelf(@Param("isOversea") String isOversea,@Param("month") String month);
}
