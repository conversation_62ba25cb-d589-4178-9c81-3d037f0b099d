package com.trinasolar.scp.baps.service.service.bdm.impl;

import com.trinasolar.scp.baps.domain.dto.bdm.BatteryDemandPlanLinesDTO;
import com.trinasolar.scp.baps.domain.dto.bdm.DemandPlanLinesApsQuery;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.service.feign.BatteryDemandFeign;
import com.trinasolar.scp.baps.service.service.bdm.BatteryDemandService;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: BatteryDemandServiceImpl
 * @date 2024/6/15 10:25
 */
@Slf4j
@Service("batteryDemandService")
@RequiredArgsConstructor
public class BatteryDemandServiceImpl implements BatteryDemandService {
    private final BatteryDemandFeign batteryDemandFeign;

    @Override
    public List<BatteryDemandPlanLinesDTO> findDemandPlan(Long isOverseaId, String month) {
        DemandPlanLinesApsQuery query = new DemandPlanLinesApsQuery();
        query.setDomesticOverseaName(LovUtils.getName(isOverseaId));
        query.setStartDate(DateUtil.getLocalDate(month, 1));
        ResponseEntity<Results<List<BatteryDemandPlanLinesDTO>>> responseEntity = batteryDemandFeign.queryByConditionForBaps(query);
        if (responseEntity == null || responseEntity.getBody() == null || !responseEntity.getBody().isSuccess()) {
            log.error("调用bdm接口失败");
            throw new BizException("baps_call_mrp_inventory_interface_failed");
        }
        return responseEntity.getBody().getData();
    }
}
