package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellLocatorWorkshopRelationDTO;
import com.trinasolar.scp.baps.domain.entity.CellLocatorWorkshopRelation;
import com.trinasolar.scp.baps.domain.query.CellLocatorWorkshopRelationQuery;
import com.trinasolar.scp.baps.domain.save.CellLocatorWorkshopRelationSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货位对应车间关系表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 02:57:01
 */
public interface CellLocatorWorkshopRelationService {
    /**
     * 分页获取货位对应车间关系表
     *
     * @param query 查询对象
     * @return 货位对应车间关系表分页对象
     */
    Page<CellLocatorWorkshopRelationDTO> queryByPage(CellLocatorWorkshopRelationQuery query);

    /**
     * 根据主键获取货位对应车间关系表详情
     *
     * @param id 主键
     * @return 货位对应车间关系表详情
     */
        CellLocatorWorkshopRelationDTO queryById(Long id);

    /**
     * 保存或更新货位对应车间关系表
     *
     * @param saveDTO 货位对应车间关系表保存对象
     * @return 货位对应车间关系表对象
     */
    CellLocatorWorkshopRelationDTO save(CellLocatorWorkshopRelationSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除货位对应车间关系表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellLocatorWorkshopRelationQuery query, HttpServletResponse response);

    void importData(MultipartFile multipartFile, ExcelPara excelPara);
    public List<CellLocatorWorkshopRelation> getAll();
}

