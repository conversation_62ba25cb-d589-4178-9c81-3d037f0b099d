package com.trinasolar.scp.baps.service.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellBaseCapacityDiscountsDTO;
import com.trinasolar.scp.baps.domain.convert.CellBaseCapacityDiscountsDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellBaseCapacityDiscounts;
import com.trinasolar.scp.baps.domain.entity.QCellBaseCapacityDiscounts;
import com.trinasolar.scp.baps.domain.excel.CellBaseCapacityDiscountsExcelDTO;
import com.trinasolar.scp.baps.domain.excel.CellPlanLineTotalHExcelDTO;
import com.trinasolar.scp.baps.domain.excel.QuerySheetExcelDTO;
import com.trinasolar.scp.baps.domain.query.CellBaseCapacityDiscountsQuery;
import com.trinasolar.scp.baps.domain.save.CellBaseCapacityDiscountsSaveDTO;
import com.trinasolar.scp.baps.domain.utils.BapsMessgeHelper;
import com.trinasolar.scp.baps.domain.utils.DateUtil;
import com.trinasolar.scp.baps.domain.utils.EmailConstant;
import com.trinasolar.scp.baps.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.baps.service.repository.CellBaseCapacityDiscountsRepository;
import com.trinasolar.scp.baps.service.service.CellBaseCapacityDiscountsService;
import com.trinasolar.scp.baps.service.service.mail.MailService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * IE产能打折（人力）表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:28
 */
@Slf4j
@Service("cellBaseCapacityDiscountsService")
@RequiredArgsConstructor
public class CellBaseCapacityDiscountsServiceImpl implements CellBaseCapacityDiscountsService {
    private static final QCellBaseCapacityDiscounts qCellBaseCapacityDiscounts = QCellBaseCapacityDiscounts.cellBaseCapacityDiscounts;
    private final CellBaseCapacityDiscountsDEConvert convert;
    private final CellBaseCapacityDiscountsRepository repository;
    private final MailService mailService;

    @Override
    public Page<CellBaseCapacityDiscountsDTO> queryByPage(CellBaseCapacityDiscountsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<CellBaseCapacityDiscounts> page = repository.findAll(booleanBuilder, pageable);
        List<CellBaseCapacityDiscountsDTO> dtos = convert.toDto(page.getContent());
        dtos = convert.toCellBaseCapacityDiscountsDTOSNameById(dtos);
        return new PageImpl(dtos, page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellBaseCapacityDiscountsQuery query) {
        if (query.getFromId() != null) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.fromId.eq(query.getFromId()));
        }
        if (query.getId() != null) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.workunit.eq(query.getWorkunit()));
        }
        if (query.getRatio() != null) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.ratio.eq(query.getRatio()));
        }
        if (query.getIeConfirm() != null) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.ieConfirm.eq(query.getIeConfirm()));
        }
        if (query.getPlanConfirm() != null) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.planConfirm.eq(query.getPlanConfirm()));
        }
        if (StringUtils.isNotEmpty(query.getImporter())) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.importer.eq(query.getImporter()));
        }
        if (StringUtils.isNotEmpty(query.getDiscountVersion())) {
            booleanBuilder.and(qCellBaseCapacityDiscounts.discountVersion.eq(query.getDiscountVersion()));
        }
        if (query.getStartTime() != null && query.getEndTime() != null) {
            booleanBuilder.and(
                    qCellBaseCapacityDiscounts.startTime.goe(query.getStartTime()).and(qCellBaseCapacityDiscounts.startTime.loe(query.getEndTime()))
                            .or(qCellBaseCapacityDiscounts.endTime.goe(query.getStartTime()).and(qCellBaseCapacityDiscounts.endTime.loe(query.getEndTime()))));
        } else {
            if (query.getEndTime() != null) {
                booleanBuilder.and(qCellBaseCapacityDiscounts.endTime.eq(query.getEndTime()));
            }
            if (query.getStartTime() != null) {
                booleanBuilder.and(qCellBaseCapacityDiscounts.startTime.eq(query.getStartTime()));
            }
        }
    }

    @Override
    public CellBaseCapacityDiscountsDTO queryById(Long id) {
        CellBaseCapacityDiscounts queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellBaseCapacityDiscountsDTO save(CellBaseCapacityDiscountsSaveDTO saveDTO) {
        CellBaseCapacityDiscounts newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new CellBaseCapacityDiscounts());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
        //发邮件提醒
        //1、准备收件人email(读取lov)
        List<String> emails;
        String emailName = "";
        LovLineDTO lovLineDTOEmail = LovUtils.get(LovHeaderCodeConstant.BAPS_EMAIL_REMINDER, LovHeaderCodeConstant.BAPS_CAPICITY_DISCOUNT_MAIL_REMINDER);
        if (Objects.isNull(lovLineDTOEmail)) {
            String messge=BapsMessgeHelper.getMessage("capacity.discount.import.no.email");
            throw new BizException(messge);
        } else {
            emailName = lovLineDTOEmail.getAttribute1();
            String email = lovLineDTOEmail.getAttribute2();
            if (StringUtils.isEmpty(email)) {
                String messge=BapsMessgeHelper.getMessage("capacity.discount.import.no.email");
                throw new BizException(messge);
            }
            String[] emailArray = email.split(";");
            emails = Arrays.asList(emailArray);
        }
        //2、准备发送的内容
        CellBaseCapacityDiscountsQuery query = new CellBaseCapacityDiscountsQuery();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        Page<CellBaseCapacityDiscountsDTO> page = queryByPage(query);
        List<CellBaseCapacityDiscountsDTO> dtos = new ArrayList<>();
        if (page != null) {
            if (page.getContent() != null) {
                dtos = page.getContent();
            }
        }
        Map<String, Object> content = MapUtil.of("contentList", dtos);
        content.put("emailName", emailName);
        content.put("updateTime", DateUtil.getFormatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        String subject = BapsMessgeHelper.getMessage("capacity.discount.import.email.title");
        mailService.send(emails, EmailConstant.CellBaseCapacityDiscountsEmail, subject, content, null);
    }


    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void export(CellBaseCapacityDiscountsQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<CellBaseCapacityDiscountsDTO> dtos = queryByPage(query).getContent();
        // dto数据转为ExcelData数据
        List<CellBaseCapacityDiscountsExcelDTO> datas = convert.toExcelDTO(dtos);
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> exportDatas = ExcelUtils.getList(datas, excelPara);
        // 导出调用excelUtils
        String excelName = BapsMessgeHelper.getMessage("export.cellbasecapacity.discount.table.name");
        ExcelUtils.exportExWithLocalDate(response, excelName, excelName, excelPara.getSimpleHeader(), exportDatas);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void ieConfirm(List<Long> ids, int tag) {
        ids.stream().forEach(id -> {
            repository.updateIeConfirm(id, tag);
        });
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void planConfirm(List<Long> ids, int tag) {
        ids.stream().forEach(id -> {
            repository.updatePlanConfirm(id, tag);
        });
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<CellBaseCapacityDiscountsExcelDTO> excelDtos = ExcelUtils.readExcel(multipartFile.getInputStream(), null, CellBaseCapacityDiscountsExcelDTO.class, excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        //1、验证数据
        checkInput(excelDtos);
        List<CellBaseCapacityDiscountsSaveDTO> saveDTOS = convert.excelDtoToSaveDto(excelDtos);
        //2、先删除之前的数据
        repository.deleteAll();
        List<CellBaseCapacityDiscountsDTO> dtos = new ArrayList<>();
        saveDTOS.stream().forEach(dto -> {
            dto.setImporter(UserUtil.getUser().getName());
            CellBaseCapacityDiscountsDTO cellBaseCapacityDiscountsDTO = save(dto);
            dtos.add(cellBaseCapacityDiscountsDTO);
        });
        //发邮件提醒
        //1、准备收件人email(读取lov)
        List<String> emails;
        LovLineDTO lovLineDTOEmail = LovUtils.get(LovHeaderCodeConstant.BAPS_EMAIL_REMINDER, LovHeaderCodeConstant.BAPS_CAPICITY_DISCOUNT_MAIL_REMINDER);
        if (Objects.isNull(lovLineDTOEmail)) {
            throw new BizException("capacity.discount.import.no.email");
        } else {
            String email = lovLineDTOEmail.getAttribute2();
            if (StringUtils.isEmpty(email)) {
                throw new BizException("capacity.discount.import.no.email");
            }
            String[] emailArray = email.split(";");
            emails = Arrays.asList(emailArray);
        }


        //2、准备发送的内容
        Map<String, Object> content = MapUtil.of("contentList", dtos);
        content.put("updateTime", DateUtil.getFormatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        String subject = BapsMessgeHelper.getMessage("capacity.discount.import.email.title");
        mailService.send(emails, EmailConstant.CellBaseCapacityDiscountsEmail, subject, content, null);

    }

    public void checkInput(List<CellBaseCapacityDiscountsExcelDTO> excelDTOS) {
        final int[] i = {1};
        List<String> errors = new ArrayList<>();
        excelDTOS.stream().forEach(excelDTO -> {
            LocalDate startDate = excelDTO.getStartTime();
            LocalDate endDate = excelDTO.getEndTime();
            if (startDate == null) {
                String message = BapsMessgeHelper.getMessage("the.row.starttime.must.not.null", new Object[]{i[0]});
                errors.add(message);
            }
            if (endDate == null) {
                String message = BapsMessgeHelper.getMessage("the.row.endtime.must.not.null", new Object[]{i[0]});
                errors.add(message);
            }
            if (startDate != null && endDate != null && startDate.isAfter(endDate)) {
                String message = BapsMessgeHelper.getMessage("the.row.endtime.must.not.before.starttime", new Object[]{i[0]});
                errors.add(message);
            }
            //验证国内海外
            LovLineDTO lovLineDTO = null;
            if (Objects.isNull(excelDTO.getIsOversea())) {
                String message = BapsMessgeHelper.getMessage("the.row.isoversea.not.null", new Object[]{i[0]});
                errors.add(message);
            } else {
                lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOversea());
                if (lovLineDTO == null) {
                    String message = BapsMessgeHelper.getMessage("the.row.isoversea.not.exists", new Object[]{i[0], excelDTO.getIsOversea()});
                    errors.add(message);
                }
            }
            //验证生产基地
            LovLineDTO lovLineDTO_BasePlace = null;
            if (Objects.isNull(excelDTO.getBasePlace())) {
                String message = BapsMessgeHelper.getMessage("the.row.baseplace.not.null", new Object[]{i[0]});
                errors.add(message);
            } else {
                lovLineDTO_BasePlace = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, excelDTO.getBasePlace());
                if (lovLineDTO_BasePlace == null) {
                    String message = BapsMessgeHelper.getMessage("the.row.baseplace.not.exists", new Object[]{i[0], excelDTO.getBasePlace()});
                    errors.add(message);
                }
            }
            //验证基地与国内海外的关系
            if (lovLineDTO != null && lovLineDTO_BasePlace != null) {
                if (!(lovLineDTO_BasePlace.getAttribute2() != null && lovLineDTO_BasePlace.getAttribute2().equals(lovLineDTO.getLovLineId().toString()))) {
                    String message = BapsMessgeHelper.getMessage("the.row.baseplace.not.in.isoversea", new Object[]{i[0], excelDTO.getBasePlace(), excelDTO.getIsOversea()});
                    errors.add(message);
                }

            }
            //验证生产车间
            LovLineDTO lovLineDTO_WorkShop = null;
            if (Objects.isNull(excelDTO.getWorkshop())) {
                String message = BapsMessgeHelper.getMessage("the.row.workshop.not.null", new Object[]{i[0]});
                errors.add(message);
            } else {
                lovLineDTO_WorkShop = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, excelDTO.getWorkshop());
                if (lovLineDTO_WorkShop == null) {
                    String message = BapsMessgeHelper.getMessage("the.row.workshop.not.exists", new Object[]{i[0], excelDTO.getWorkshop()});
                    errors.add(message);
                }
            }
            //验证车间与基地的关系
            if (lovLineDTO_WorkShop != null && lovLineDTO_BasePlace != null) {

                if (!(lovLineDTO_WorkShop.getAttribute1() != null && lovLineDTO_WorkShop.getAttribute1().equals(lovLineDTO_BasePlace.getLovLineId().toString()))) {
                    String message = BapsMessgeHelper.getMessage("the.row.workshop.not.in.baseplace", new Object[]{i[0], excelDTO.getWorkshop(), excelDTO.getBasePlace()});
                    errors.add(message);
                }

            }
            i[0]++;
        });
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }

    }
}
