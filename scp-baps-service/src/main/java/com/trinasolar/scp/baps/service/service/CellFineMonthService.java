package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellFineMonthDTO;
import com.trinasolar.scp.baps.domain.query.CellFineMonthQuery;
import com.trinasolar.scp.baps.domain.entity.CellFineMonth;
import com.trinasolar.scp.baps.domain.query.CellFineMonthQuery;
import com.trinasolar.scp.baps.domain.save.CellFineMonthSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池良率月拆分表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-05 05:51:04
 */
public interface CellFineMonthService {
    /**
     * 分页获取电池良率月拆分表
     *
     * @param query 查询对象
     * @return 电池良率月拆分表分页对象
     */
    Page<CellFineMonthDTO> queryByPage(CellFineMonthQuery query);

    /**
     * 根据主键获取电池良率月拆分表详情
     *
     * @param id 主键
     * @return 电池良率月拆分表详情
     */
        CellFineMonthDTO queryById(Long id);

    /**
     * 保存或更新电池良率月拆分表
     *
     * @param saveDTO 电池良率月拆分表保存对象
     * @return 电池良率月拆分表对象
     */
    CellFineMonthDTO save(CellFineMonthSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池良率月拆分表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellFineMonthQuery query, HttpServletResponse response);
}

