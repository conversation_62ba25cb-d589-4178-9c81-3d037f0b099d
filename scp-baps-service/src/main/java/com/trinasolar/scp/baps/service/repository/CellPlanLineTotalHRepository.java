package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellPlanLineTotalH;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 投产计划H汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-31 06:53:01
 */
@Repository
public interface CellPlanLineTotalHRepository extends JpaRepository<CellPlanLineTotalH, Long>, QuerydslPredicateExecutor<CellPlanLineTotalH> {
}
