package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.PriorityLogisticsLineDTO;
import com.trinasolar.scp.baps.domain.entity.PriorityLogisticsLine;
import com.trinasolar.scp.baps.domain.query.PriorityLogisticsLineQuery;
import com.trinasolar.scp.baps.domain.save.PriorityLogisticsLineSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 物流线路优先级 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:51:00
 */
public interface PriorityLogisticsLineService extends IExcelImportData {
    /**
     * 分页获取物流线路优先级
     *
     * @param query 查询对象
     * @return 物流线路优先级分页对象
     */
    Page<PriorityLogisticsLineDTO> queryByPage(PriorityLogisticsLineQuery query);

    /**
     * 根据主键获取物流线路优先级详情
     *
     * @param id 主键
     * @return 物流线路优先级详情
     */
    PriorityLogisticsLineDTO queryById(Long id);

    /**
     * 保存或更新物流线路优先级
     *
     * @param saveDTO 物流线路优先级保存对象
     * @return 物流线路优先级对象
     */
    PriorityLogisticsLineDTO save(PriorityLogisticsLineSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除物流线路优先级
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(PriorityLogisticsLineQuery query, HttpServletResponse response);

    /**
     * 更新数据
     */
    void refreshData();
}

