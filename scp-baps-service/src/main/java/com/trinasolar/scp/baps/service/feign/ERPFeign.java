package com.trinasolar.scp.baps.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.baps.domain.dto.erp.ERPResponse;
import com.trinasolar.scp.baps.domain.dto.erp.ESBResponse;
import com.trinasolar.scp.baps.domain.dto.erp.WipWorkOrderDTO;
import com.trinasolar.scp.baps.domain.query.erp.SyncWipIssueQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ERP对接相关接口
 *
 * @author: darke
 * @create: 2022年8月31日09:10:27
 */

@FeignClient(name = FeignConstant.FEIGN_NAME_ERP, url = "${ipass.url}")
public interface ERPFeign {

    /**
     * 工单下发
     *
     * @param wipWorkOrderDTO
     * @return
     */
    @PostMapping(value = "${erp.wipAdd.url}", headers = {"Content-Type=application/json;charset=UTF-8", "tsl-clientid=${erp.wipAdd.clientid}", "tsl-clientsecret=${erp.wipAdd.clientsecret}"})
    ESBResponse<ERPResponse> wipAdd(@RequestBody WipWorkOrderDTO wipWorkOrderDTO);


}
