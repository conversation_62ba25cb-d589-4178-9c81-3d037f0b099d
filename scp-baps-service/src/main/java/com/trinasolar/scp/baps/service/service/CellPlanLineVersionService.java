package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionDTO;
import com.trinasolar.scp.baps.domain.dto.CellPlanLineVersionQueryDto;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineVersion;
import com.trinasolar.scp.baps.domain.query.CellPlanLineVersionQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineVersionSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 投产计划版本管理表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
public interface CellPlanLineVersionService {
    /**
     * 分页获取投产计划版本管理表
     *
     * @param query 查询对象
     * @return 投产计划版本管理表分页对象
     */
    Page<CellPlanLineVersionDTO> queryByPage(CellPlanLineVersionQuery query);

    /**
     * 根据主键获取投产计划版本管理表详情
     *
     * @param id 主键
     * @return 投产计划版本管理表详情
     */
    CellPlanLineVersionDTO queryById(Long id);

    /**
     * 保存或更新投产计划版本管理表
     *
     * @param saveDTO 投产计划版本管理表保存对象
     * @return 投产计划版本管理表对象
     */
    CellPlanLineVersionDTO save(CellPlanLineVersionSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除投产计划版本管理表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(CellPlanLineVersionQuery query, HttpServletResponse response);

    CellPlanLineVersionDTO query(CellPlanLineVersionQueryDto query);

    /**
     * 最大版本号
     *
     * @param isOverseaId
     * @param monthList
     * @return
     */
    Map<String, String> findMaxVersion(Long isOverseaId, List<String> monthList);

    /**
     * 最大版本号
     *
     * @param isOverseaId
     * @param month
     * @return
     */
    String findMaxVersion(Long isOverseaId, String month);

    /**
     * 最大版本号 ()
     *
     * @param isOverseaId
     * @param month
     * @return
     */
    String findMaxVersionSendedEmail(Long isOverseaId, String month);
}

