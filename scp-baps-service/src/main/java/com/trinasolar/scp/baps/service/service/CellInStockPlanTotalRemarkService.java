package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellInStockPlanTotalRemarkDTO;
import com.trinasolar.scp.baps.domain.query.CellInStockPlanTotalRemarkQuery;

import java.util.List;

public interface CellInStockPlanTotalRemarkService {

    List<CellInStockPlanTotalRemarkDTO> queryBy(CellInStockPlanTotalRemarkQuery query);

    void save(CellInStockPlanTotalRemarkDTO dto);

}
