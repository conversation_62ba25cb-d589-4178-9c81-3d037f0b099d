package com.trinasolar.scp.baps.service.service.impl;

import com.trinasolar.scp.baps.domain.dto.OverseaPurchasePlanDTO;
import com.trinasolar.scp.baps.domain.query.OverseaPurchasePlanQuery;
import com.trinasolar.scp.baps.domain.utils.OverseaConstant;
import com.trinasolar.scp.baps.service.feign.OverseaPurchasePlanFeign;
import com.trinasolar.scp.baps.service.service.OverseaPurchasePlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
@Slf4j
@Service("overseaPurchasePlanService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class OverseaPurchasePlanServiceImpl implements OverseaPurchasePlanService {
    @Autowired
    private OverseaPurchasePlanFeign feign;
    @Override
    public List<OverseaPurchasePlanDTO> list(OverseaPurchasePlanQuery query) {
        return feign.List(query).getBody().getData();
    }

    @Override
    @Cacheable(cacheNames = "OverseaPurchasePlanService_listByMonthAndIsOversea", key = "#p0+'_'+#p1", unless = "#result == null", condition = "#p0!=null")
    public List<OverseaPurchasePlanDTO> listByMonthAndIsOversea(String month, String isOversea) {
        //外购计划没有版本的概念
        OverseaPurchasePlanQuery overseaPurchasePlanQuery = new OverseaPurchasePlanQuery();
        overseaPurchasePlanQuery.setMonth(month);
        if (StringUtils.isNotEmpty(isOversea)) {
            if (isOversea.equals(OverseaConstant.INLAND)) {
                overseaPurchasePlanQuery.setIsOversea(OverseaConstant.INLAND_VALUE);
            }
            if (isOversea.equals(OverseaConstant.OVERSEA)) {
                overseaPurchasePlanQuery.setIsOversea(OverseaConstant.OVERSEA_VALUE);
            }
        } else {
            overseaPurchasePlanQuery.setIsOversea(null);
        }

        //获取外购数据
        return list(overseaPurchasePlanQuery);
    }
}
