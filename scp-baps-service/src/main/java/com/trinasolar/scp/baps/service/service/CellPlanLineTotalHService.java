package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.CellPlanLineTotalHDTO;
import com.trinasolar.scp.baps.domain.entity.CellPlanLineTotalH;
import com.trinasolar.scp.baps.domain.query.CellPlanLineTotalHQuery;
import com.trinasolar.scp.baps.domain.save.CellPlanLineTotalHSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 投产计划H汇总表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-31 06:53:01
 */
public interface CellPlanLineTotalHService {
    /**
     * 分页获取投产计划H汇总表
     *
     * @param query 查询对象
     * @return 投产计划H汇总表分页对象
     */
    Page<CellPlanLineTotalHDTO> queryByPage(CellPlanLineTotalHQuery query);

    /**
     * 根据主键获取投产计划H汇总表详情
     *
     * @param id 主键
     * @return 投产计划H汇总表详情
     */
        CellPlanLineTotalHDTO queryById(Long id);

    /**
     * 保存或更新投产计划H汇总表
     *
     * @param saveDTO 投产计划H汇总表保存对象
     * @return 投产计划H汇总表对象
     */
    CellPlanLineTotalHDTO save(CellPlanLineTotalHSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除投产计划H汇总表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(CellPlanLineTotalHQuery query, HttpServletResponse response);
}

