package com.trinasolar.scp.baps.service.service;

import com.trinasolar.scp.baps.domain.dto.OverseaPurchasePlanDTO;
import com.trinasolar.scp.baps.domain.query.OverseaPurchasePlanQuery;

import java.util.List;

public interface OverseaPurchasePlanService {
    List<OverseaPurchasePlanDTO> list( OverseaPurchasePlanQuery query);

    List<OverseaPurchasePlanDTO> listByMonthAndIsOversea(String month, String isOversea);
}
