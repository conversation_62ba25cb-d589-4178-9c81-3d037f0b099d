package com.trinasolar.scp.baps.service.feign;

import com.trinasolar.scp.baps.domain.dto.ScheduledTaskLinesDTO;
import com.trinasolar.scp.baps.domain.dto.SiliconSliceSupplyLinesDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.BmrpSafetyStockDaysDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.SiliconSlicePurchasePlanDTO;
import com.trinasolar.scp.baps.domain.dto.bmrp.TjOnHandDTO;
import com.trinasolar.scp.baps.domain.query.SiliconSliceSupplyLinesQuery;
import com.trinasolar.scp.baps.domain.query.bmrp.BmrpSafetyStockDaysQuery;
import com.trinasolar.scp.baps.domain.query.bmrp.SiliconSlicePurchasePlanQuery;
import com.trinasolar.scp.baps.domain.query.bmrp.TjOnHandQuery;
import com.trinasolar.scp.baps.domain.utils.FeignConstant;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = FeignConstant.SCP_BATTERY_MRP_API, path = "/scp-battery-mrp-api")
public interface BmrpFeign {

    /**
     * 日志任务初始化
     * @return 查询结果
     */
    @PostMapping("/scheduled-task-lines/initTask")
    @ApiOperation(value = "日志任务初始化", notes = "日志任务初始化")
    ResponseEntity<Results<ScheduledTaskLinesDTO>> initTask();

    /**
     * Bbom新增日志保存接口
     * @return 查询结果
     */
    @PostMapping("/scheduled-task-lines/addLogSave")
    @ApiOperation(value = "Baps新增日志保存接口", notes = "Baps新增日志保存接口")
    ResponseEntity<Results<List<String>>> addLogSave(@RequestBody ScheduledTaskLinesDTO taskLinesDTO);

    /**
     * 根据条件查询库存
     *
     * @return
     */
    @PostMapping("/tj-on-hand/query-inventory")
    @ApiOperation(value = "根据条件查询库存", notes = "查询库存")
    ResponseEntity<Results<List<TjOnHandDTO>>> queryInventory(@RequestBody TjOnHandQuery query);

    @ApiOperation(value = "最新版本供应能力列表", notes = "最新版本供应能力列表")
    @PostMapping("/silicon-slice-supply-lines/findByLastVersionList")
    ResponseEntity<Results<List<SiliconSliceSupplyLinesDTO>>> findByLastVersionList(@RequestBody SiliconSliceSupplyLinesQuery query);

    /**
     * 硅片外购计划
     *
     * @param query
     * @return
     */
    @PostMapping("/silicon-slice-purchase-plan/findByCondition")
    @ApiOperation(value = "根据条件", notes = "根据条件")
    ResponseEntity<Results<List<SiliconSlicePurchasePlanDTO>>> findByCondition(@RequestBody SiliconSlicePurchasePlanQuery query);

    /**
     * 列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/safety-stock-days-h/list")
    @ApiOperation(value = "安全库存天数列表", notes = "安全库存天数列表")
    ResponseEntity<Results<List<BmrpSafetyStockDaysDTO>>> safetyStockList(@RequestBody BmrpSafetyStockDaysQuery query);
}