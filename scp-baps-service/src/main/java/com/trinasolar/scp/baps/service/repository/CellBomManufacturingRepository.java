package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.CellBomManufacturing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 制造BOM表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Repository
public interface CellBomManufacturingRepository extends JpaRepository<CellBomManufacturing, Long>, QuerydslPredicateExecutor<CellBomManufacturing> {
    @Modifying
    @Query("update  CellBomManufacturing  c set c.isDeleted=1 WHERE c.isDeleted = 0 and c.ieorgrade = :ieorgrade")
    void deleteByIeOrGrade(@Param("ieorgrade") int ieorgrade);
    @Modifying
    @Query("update  CellBomManufacturing  c set c.isDeleted=1 WHERE c.isDeleted = 0 and c.fromid = :fromid")
    void deleteByFromId(@Param("fromid") Long fromid);
    @Query("select c from CellBomManufacturing c where c.ieorgrade = :ieorgrade and c.isDeleted=0")
    List<CellBomManufacturing> findByIeOrGrade(@Param("ieorgrade") Integer ieorgrade);


    /**
     * 设置默认值
     * @param cellBomManufacturing
     */
    public static void setDefault(CellBomManufacturing cellBomManufacturing) {
        if (cellBomManufacturing.getProcessCode() == null) {
            cellBomManufacturing.setProcessCode("10");
        }
        if (cellBomManufacturing.getProcessId() == null) {
            cellBomManufacturing.setProcessId(10);
        }
        if (cellBomManufacturing.getInstructionType() == null) {
            cellBomManufacturing.setInstructionType("U");
        }
        if (cellBomManufacturing.getInstructionCode() == null) {
            cellBomManufacturing.setInstructionCode("M");
        }
        if (cellBomManufacturing.getCapacityQuantity() != null) {
            cellBomManufacturing.setManufacturing(cellBomManufacturing.getCapacityQuantity() + "PH");
        }
    }
}
