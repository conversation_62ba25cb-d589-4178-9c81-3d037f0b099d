package com.trinasolar.scp.baps.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.convert.CellInStockPlanRemarkDEConvert;
import com.trinasolar.scp.baps.domain.dto.CellInStockPlanRemarkDTO;
import com.trinasolar.scp.baps.domain.query.CellInStockPlanRemarkQuery;
import com.trinasolar.scp.baps.service.repository.CellInStockPlanRemarkRepository;
import com.trinasolar.scp.baps.service.service.CellInStockPlanRemarkService;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.trinasolar.scp.baps.domain.entity.QCellInStockPlanRemark.cellInStockPlanRemark;

@Service
public class CellInStockPlanRemarkServiceImpl implements CellInStockPlanRemarkService {

    @Resource
    private CellInStockPlanRemarkRepository repository;

    @Resource
    private CellInStockPlanRemarkDEConvert convert;

    @Override
    public List<CellInStockPlanRemarkDTO> queryBy(CellInStockPlanRemarkQuery query) {
       BooleanBuilder booleanBuilder = buildWhere(query);
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    @Override
    public void save(CellInStockPlanRemarkDTO dto) {
        repository.save(convert.toEntity(dto));
    }


    private BooleanBuilder buildWhere(CellInStockPlanRemarkQuery query) {
        BooleanBuilder builder = new BooleanBuilder();
        if (StringUtils.isNotBlank(query.getMonth())) {
            builder.and(cellInStockPlanRemark.month.eq(query.getMonth()));
        }
        if (StringUtils.isNotBlank(query.getBasePlace())) {
            builder.and(cellInStockPlanRemark.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotBlank(query.getIsOversea())) {
            builder.and(cellInStockPlanRemark.isOversea.eq(query.getIsOversea()));
        }
        if (query.getBasePlaceId() != null) {
            builder.and(cellInStockPlanRemark.basePlaceId.eq(query.getBasePlaceId()));
        }
        if (query.getIsOverseaId() != null) {
            builder.and(cellInStockPlanRemark.isOverseaId.eq(query.getIsOverseaId()));
        }
        return builder;
    }
}
