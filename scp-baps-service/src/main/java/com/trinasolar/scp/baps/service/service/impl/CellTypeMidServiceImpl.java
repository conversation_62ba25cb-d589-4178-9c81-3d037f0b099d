package com.trinasolar.scp.baps.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.baps.domain.dto.CellTypeMidDTO;
import com.trinasolar.scp.baps.domain.convert.CellTypeMidDEConvert;
import com.trinasolar.scp.baps.domain.entity.CellTypeMid;
import com.trinasolar.scp.baps.domain.entity.QCellTypeMid;
import com.trinasolar.scp.baps.domain.query.CellTypeMidQuery;
import com.trinasolar.scp.baps.domain.save.CellTypeMidSaveDTO;
import com.trinasolar.scp.baps.service.repository.CellTypeMidRepository;
import com.trinasolar.scp.baps.service.service.CellTypeMidService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.ExcelPara;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import javax.servlet.http.HttpServletResponse;

/**
 * 电池类型转换表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Slf4j
@Service("cellTypeMidService")
@RequiredArgsConstructor
public class CellTypeMidServiceImpl implements CellTypeMidService {
    private static final QCellTypeMid qCellTypeMid = QCellTypeMid.cellTypeMid;

    private final CellTypeMidDEConvert convert;

    private final CellTypeMidRepository repository;

    @Override
    public Page<CellTypeMidDTO> queryByPage(CellTypeMidQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellTypeMid> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellTypeMidQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellTypeMid.id.eq(query.getId()));
        }
        if(StringUtils.isNotEmpty(query.getCellsType())){
            booleanBuilder.and(qCellTypeMid.cellsType.eq(query.getCellsType()));
        }
        if(StringUtils.isNotEmpty(query.getCellsTypeLeft())){
            booleanBuilder.and(qCellTypeMid.cellsTypeLeft.eq(query.getCellsTypeLeft()));
        }
    }

    @Override
    public CellTypeMidDTO queryById(Long id) {
        CellTypeMid queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public CellTypeMidDTO save(CellTypeMidSaveDTO saveDTO) {
        CellTypeMid newObj = Optional.ofNullable(saveDTO.getId())
            .map(id -> repository.getOne(saveDTO.getId()))
            .orElse(new CellTypeMid());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(CellTypeMidQuery query, HttpServletResponse response) {
       List<CellTypeMidDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "电池类型转换表", "电池类型转换表", excelPara.getSimpleHeader(), excelData);
    }
}
