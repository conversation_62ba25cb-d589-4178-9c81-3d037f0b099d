package com.trinasolar.scp.baps.service.service;
import com.trinasolar.scp.baps.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.baps.domain.dto.bbom.ItemsDTO;

import java.util.List;
import java.util.Map;

public interface BatteryBomService {
    /**
     * 获取所有电池类型
     * @return
     */
    public  List<BatteryTypeMainDTO> queryBatteryCodeTypeAll();

    Map<String, ItemsDTO> findItems(List<String> itemCodes);

    Map<String, ItemsDTO> findItemsNew(List<String> itemCodes);

}
