package com.trinasolar.scp.baps.service.service.impl;
import cn.hutool.core.util.ReflectUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanDEConvert;
import com.trinasolar.scp.baps.domain.dto.*;
import com.trinasolar.scp.baps.domain.convert.CellInstockPlanFinishDEConvert;
import com.trinasolar.scp.baps.domain.dto.bbom.ItemsDTO;
import com.trinasolar.scp.baps.domain.entity.*;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanFinishQuery;
import com.trinasolar.scp.baps.domain.query.CellInstockPlanFinishTotalQuery;
import com.trinasolar.scp.baps.domain.query.bbom.ItemsQuery;
import com.trinasolar.scp.baps.domain.save.CellInstockPlanFinishSaveDTO;
import com.trinasolar.scp.baps.domain.utils.*;
import com.trinasolar.scp.baps.service.feign.ApsFeign;
import com.trinasolar.scp.baps.service.feign.BbomFeign;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanFinishRepository;
import com.trinasolar.scp.baps.service.repository.CellInstockPlanNoFinishRepository;
import com.trinasolar.scp.baps.service.service.BatteryBomService;
import com.trinasolar.scp.baps.service.service.CellInstockPlanFinishService;
import com.trinasolar.scp.baps.service.service.CellLocatorWorkshopRelationService;
import com.trinasolar.scp.baps.service.service.log.LogService;
import com.trinasolar.scp.baps.service.service.system.SystemService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import jodd.exception.ExceptionUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.SneakyThrows;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 实际入库表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-06 06:42:07
 */
@Slf4j
@Service("cellInstockPlanFinishService")
@RequiredArgsConstructor
public class CellInstockPlanFinishServiceImpl implements CellInstockPlanFinishService {
    private static final QCellInstockPlanFinish qCellInstockPlanFinish = QCellInstockPlanFinish.cellInstockPlanFinish;
    private final CellInstockPlanFinishDEConvert convert;
    private final CellInstockPlanFinishRepository repository;
    private final CellInstockPlanNoFinishRepository cellInstockPlanNoFinishRepository;
    private final JPAQueryFactory jpaQueryFactory;
    private final BbomFeign bbomFeign;
    private final ApsFeign apsFeign;
    private final SystemService systemService;
    private final CellLocatorWorkshopRelationService cellLocatorWorkshopRelationService;
    private final CellInstockPlanDEConvert cellInstockPlanDEConvert;
    private final LogService logService;
    private final BatteryBomService batteryBomService;
    @Override
    public Page<CellInstockPlanFinishDTO> queryByPage(CellInstockPlanFinishQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellInstockPlanFinish> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, CellInstockPlanFinishQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qCellInstockPlanFinish.workshop.eq(query.getWorkshop()));
        }
        if (query.getWorkshopId() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.workshopId.eq(query.getWorkshopId()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(qCellInstockPlanFinish.cellsType.eq(query.getCellsType()));
        }
        if (query.getCellsTypeId() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.cellsTypeId.eq(query.getCellsTypeId()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qCellInstockPlanFinish.itemCode.eq(query.getItemCode()));
        }
        if (query.getProductionGradeId() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.productionGradeId.eq(query.getProductionGradeId()));
        }
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qCellInstockPlanFinish.productionGrade.eq(query.getProductionGrade()));
        }
        if (query.getCellSourceId() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.cellSourceId.eq(query.getCellSourceId()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            booleanBuilder.and(qCellInstockPlanFinish.cellSource.eq(query.getCellSource()));
        }
        if (query.getTransparentDoubleGlassId() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.transparentDoubleGlassId.eq(query.getTransparentDoubleGlassId()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qCellInstockPlanFinish.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (query.getHTraceId() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.hTraceId.eq(query.getHTraceId()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qCellInstockPlanFinish.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getIsRegionalCountry())) {
            booleanBuilder.and(qCellInstockPlanFinish.isRegionalCountry.eq(query.getIsRegionalCountry()));
        }
        if (query.getRegionalCountryId() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.regionalCountryId.eq(query.getRegionalCountryId()));
        }
        if (StringUtils.isNotEmpty(query.getRegionalCountry())) {
            booleanBuilder.and(qCellInstockPlanFinish.regionalCountry.eq(query.getRegionalCountry()));
        }
        if (query.getFinishedDate() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.finishedDate.eq(query.getFinishedDate()));
        }
        if (query.getFinishedQty() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.finishedQty.eq(query.getFinishedQty()));
        }
        if (query.getCreationDate() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.creationDate.eq(query.getCreationDate()));
        }
        if (query.getOrganizationId() != null) {
            booleanBuilder.and(qCellInstockPlanFinish.organizationId.eq(query.getOrganizationId()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qCellInstockPlanFinish.remark.eq(query.getRemark()));
        }
    }

    @Override
    public CellInstockPlanFinishDTO queryById(Long id) {
        CellInstockPlanFinish queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }
    @Override
    public CellInstockPlanFinishDTO save(CellInstockPlanFinishSaveDTO saveDTO) {
        CellInstockPlanFinish newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new CellInstockPlanFinish());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }
    @Override
    @SneakyThrows
    public void export(CellInstockPlanFinishTotalQuery query, HttpServletResponse response) {
        List<CellInstockPlanFinishTotalDTO> dtos = query(query).getContent();
        if (CollectionUtils.isEmpty(dtos)){
            throw new RuntimeException("导出数据为空");
        }
        ExcelPara excelPara = query.getExcelPara();
        converExcelPara(excelPara);
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportExWithLocalDate(response, "实际入库覆盖排产", "实际入库覆盖排产", excelPara.getSimpleHeader(), excelData);
    }

    private void converExcelPara(ExcelPara excelPara) {
        List<DataColumn> columns = excelPara.getColumns();
        columns=  Optional.ofNullable(columns).orElse(Lists.newArrayList());
        columns.stream().forEach(column -> {
            if ("hTrace".equals(column.getName())){
                column.setName("HTrace");
            }
        });
    }

    private Map<String, ErpWorkShop> prepareWorkhops() {
        Map<String, LovLineDTO> allOverseas = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.DOMESTIC_OVERSEA);
        Map<String, LovLineDTO> allOverseasById = allOverseas.values().stream().collect(Collectors.toMap(item -> {
            return item.getLovLineId() + "";
        }, Function.identity(), (v1, v2) -> v1));
        Map<String, LovLineDTO> allBaseplaces = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BASE_PLACE);
        Map<String, LovLineDTO> allBaseplacesById = allBaseplaces.values().stream().collect(Collectors.toMap(item -> {
            return item.getLovLineId() + "";
        }, Function.identity(), (v1, v2) -> v1));
        Map<String, LovLineDTO> allWorkshops = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP);
        Map<String, LovLineDTO> allWorkshopsByValue = allWorkshops.values().stream().collect(Collectors.toMap(item -> {
            return item.getLovValue();
        }, Function.identity(), (v1, v2) -> v1));
        Map<String, ErpWorkShop> map = new HashMap<>();
        allWorkshopsByValue.forEach((k, v) -> {
            ErpWorkShop erpWorkShop = new ErpWorkShop();
            erpWorkShop.setWorkshopId(v.getLovLineId());
            erpWorkShop.setWorkshopName(v.getLovName());
            String basePlaceId = v.getAttribute1();
            LovLineDTO basePlaceLov = Optional.ofNullable(allBaseplacesById.get(basePlaceId)).orElse(new LovLineDTO());
            erpWorkShop.setBaseplaceId(basePlaceLov.getLovLineId());
            erpWorkShop.setBaeplaceName(basePlaceLov.getLovName());
            String isOvereaId = basePlaceLov.getAttribute2();
            LovLineDTO overseaLov = Optional.ofNullable(allOverseasById.get(isOvereaId)).orElse(new LovLineDTO());
            erpWorkShop.setOverseaId(overseaLov.getLovLineId());
            erpWorkShop.setOverseaName(overseaLov.getLovName());
            map.put(k, erpWorkShop);


        });
        return map;
    }


    /**
     * 从ErpWipIssue同步数据到实际入库表
     *
     * @param startDate
     * @param endDate
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void synDataFromErpWipIssue(LocalDate startDate, LocalDate endDate) {
        Map<String, ErpWorkShop> workShopMap = prepareWorkhops();
        LocalDate beginDate = LocalDate.of(2024, 5, 1);
        LocalDate lastDate = LocalDate.now();
        if (Objects.nonNull(startDate) && startDate.isAfter(beginDate)) {
            beginDate = startDate;
        }
        if (Objects.nonNull(endDate) && endDate.isBefore(lastDate)) {
            lastDate = endDate;
        }
        if (beginDate.isAfter(lastDate)) {
            return;
        }
        List<Long> organizationIds = systemService.findOrganizationIds();
        Map<String, LovLineDTO> productionGradeMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.PRODUCTION_GRADE);
        productionGradeMap = productionGradeMap.values().stream().collect(Collectors.toMap(lovLineDTO -> lovLineDTO.getLovValue(), Function.identity(), (s, s2) -> s2));
        Map<String, LovLineDTO> cellsTypeMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BATTERY_TYPE);
        cellsTypeMap = cellsTypeMap.values().stream().collect(Collectors.toMap(lovLineDTO -> lovLineDTO.getLovValue(), Function.identity(), (s, s2) -> s2));
        Map<String, LovLineDTO> cellSourceMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.CELL_SOURCE);
        cellSourceMap = cellSourceMap.values().stream().collect(Collectors.toMap(lovLineDTO -> lovLineDTO.getLovValue(), Function.identity(), (s, s2) -> s2));
        Map<String, LovLineDTO> hTraceMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.H_TRACE);
        hTraceMap = hTraceMap.values().stream().collect(Collectors.toMap(lovLineDTO -> lovLineDTO.getLovValue(), Function.identity(), (s, s2) -> s2));
        Map<String, LovLineDTO> aestheticsMap= LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AESTHETICS);
        aestheticsMap=aestheticsMap.values().stream().collect(Collectors.toMap(lovLineDTO -> lovLineDTO.getLovValue(), Function.identity(), (s, s2) -> s2));
        //获取待排除的货位信息
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BAPS_ERP_WIP_FINISHED_EXCEPT_LOCATOR);
        List<String> excludeLocators = allByHeaderCode.values().stream().map(LovLineDTO::getLovValue).collect(Collectors.toList());
        List<CellLocatorWorkshopRelation> relationList = cellLocatorWorkshopRelationService.getAll();
        if (CollectionUtils.isEmpty(relationList)) {
            throw new BizException("baps_cell_locator_workshop_relation_not_exist");
        }
        Map<String, String> locatorWorkshopMap = relationList.stream().collect(Collectors.toMap(CellLocatorWorkshopRelation::getLocator, CellLocatorWorkshopRelation::getWorkshop, (s, s2) -> s));
        ScheduledTaskLinesDTO task = null;
     try {

         task = logService.createLogTask(LovHeaderCodeConstant.BAPS_INSTOCK_FINISH_TASK);
         if (task== null){
             log.info("创建任务失败");
         }
         for (Long organizationId : organizationIds) {
             synDataByOrganizationId(organizationId, beginDate, lastDate, productionGradeMap, cellsTypeMap, cellSourceMap, hTraceMap, workShopMap, locatorWorkshopMap,   aestheticsMap,    excludeLocators,task);
         }
         logService.addLog(task, ScheduleTaskStatusEnum.SUCCESS, "执行结束");
     }
     catch (Exception exception){
         logService.addLog(task, ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(exception));
         log.error(ExceptionUtil.exceptionChainToString(exception));
         throw new BizException(exception.getMessage());
     }
     finally {
         logService.saveTaskLog(task);
     }



    }

    @Override
    public Page<CellInstockPlanFinishTotalDTO> query(CellInstockPlanFinishTotalQuery query) {
        String oldLang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        if (StringUtils.isBlank(query.getIsOversea())){
           throw new BizException("baps_isOversea_not_null");
        }
        if (StringUtils.isBlank(query.getMonth())){
           throw new BizException("baps_month_not_null");
        }
       if (StringUtils.isBlank(query.getVersion())){
           throw new BizException("baps_version_not_null");
       }
        //1、获取入库计划数据
       List<CellInstockPlanFinishTotalDTO>  cellInstockPlanTotalDTOS = getCellInstockPlanDatas(query);
        //2、获取实际入库数据
        List<CellInstockPlanFinishTotalDTO>  cellInstockPlanFinishTotalDTOS = getCellInstockPlanFinishDatas(query);
        //3、数据合并
        List<CellInstockPlanFinishTotalDTO>  mergeDataDtos = mergeData(cellInstockPlanTotalDTOS,cellInstockPlanFinishTotalDTOS);
        mergeDataDtos.forEach(item->{
            if(StringUtils.isBlank(item.getMainGridSpace())){
                item.setMainGridSpace("无");
            }
            if(StringUtils.isBlank(item.getSupplyMethod())){
                item.setSupplyMethod("无");
            }
        });
        //4、分页
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Integer total =  mergeDataDtos.size();
        List<CellInstockPlanFinishTotalDTO>  datas=mergeDataDtos.subList((query.getPageNumber() - 1) * query.getPageSize(), Math.min(query.getPageNumber() * query.getPageSize(), mergeDataDtos.size()));
        //5、翻译
        MyThreadLocal.get().setLang(oldLang);
        convert.toCellInstockPlanFinishTotalNameByCnNameDTO(datas);
        return new PageImpl(datas, pageable, total);
    }

    private List<CellInstockPlanFinishTotalDTO> mergeData(List<CellInstockPlanFinishTotalDTO> cellInstockPlanTotalDTOS, List<CellInstockPlanFinishTotalDTO> cellInstockPlanFinishTotalDTOS) {
        Map<String, CellInstockPlanFinishTotalDTO> planMap = cellInstockPlanTotalDTOS.stream().collect(Collectors.toMap(item->{return  getKey(item);}, Function.identity(), (s, s2) -> s2));
        Map<String, CellInstockPlanFinishTotalDTO> finishMap = cellInstockPlanFinishTotalDTOS.stream().collect(Collectors.toMap(item -> {
            return getKey(item);
        }, Function.identity(), (s, s2) -> s2));
        List<String> keys = planMap.keySet().stream().sorted().collect(Collectors.toList());
        List<CellInstockPlanFinishTotalDTO> dtos = Lists.newArrayList();
        for (String key : keys){
            CellInstockPlanFinishTotalDTO planDto = planMap.get(key);
            dtos.add(planDto);
            if (finishMap.containsKey(key)){
                CellInstockPlanFinishTotalDTO finishDto = finishMap.get(key);
                dtos.add(finishDto);
                finishMap.remove(key);
            }else {
                CellInstockPlanFinishTotalDTO finishDto=new CellInstockPlanFinishTotalDTO();
                BeanUtils.copyProperties(planDto,finishDto);
                setZero(finishDto,"实际入库");
                dtos.add(finishDto);
            }
        }
        if (finishMap.size() >0){
            List<String> finishKeys = finishMap.keySet().stream().sorted().collect(Collectors.toList());

            for (String key : finishKeys){
                CellInstockPlanFinishTotalDTO planDto=new CellInstockPlanFinishTotalDTO();
                CellInstockPlanFinishTotalDTO finishDto = finishMap.get(key);
                BeanUtils.copyProperties(finishDto,planDto);
                setZero(planDto,"计划排产");
                dtos.add(planDto);
                dtos.add(finishDto);
            }

        }
        return dtos;
    }

    private void setZero(CellInstockPlanFinishTotalDTO finishDto,String dataType) {
        finishDto.setQtyThousandPc(BigDecimal.ZERO);
        finishDto.setDataType(dataType);
        finishDto.setD1(BigDecimal.ZERO);
        finishDto.setD2(BigDecimal.ZERO);
        finishDto.setD3(BigDecimal.ZERO);
        finishDto.setD4(BigDecimal.ZERO);
        finishDto.setD5(BigDecimal.ZERO);
        finishDto.setD6(BigDecimal.ZERO);
        finishDto.setD7(BigDecimal.ZERO);
        finishDto.setD8(BigDecimal.ZERO);
        finishDto.setD9(BigDecimal.ZERO);
        finishDto.setD10(BigDecimal.ZERO);
        finishDto.setD11(BigDecimal.ZERO);
        finishDto.setD12(BigDecimal.ZERO);
        finishDto.setD13(BigDecimal.ZERO);
        finishDto.setD14(BigDecimal.ZERO);
        finishDto.setD15(BigDecimal.ZERO);
        finishDto.setD16(BigDecimal.ZERO);
        finishDto.setD17(BigDecimal.ZERO);
        finishDto.setD18(BigDecimal.ZERO);
        finishDto.setD19(BigDecimal.ZERO);
        finishDto.setD20(BigDecimal.ZERO);
        finishDto.setD21(BigDecimal.ZERO);
        finishDto.setD22(BigDecimal.ZERO);
        finishDto.setD23(BigDecimal.ZERO);
        finishDto.setD24(BigDecimal.ZERO);
        finishDto.setD25(BigDecimal.ZERO);
        finishDto.setD26(BigDecimal.ZERO);
        finishDto.setD27(BigDecimal.ZERO);
        finishDto.setD28(BigDecimal.ZERO);
        finishDto.setD29(BigDecimal.ZERO);
        finishDto.setD30(BigDecimal.ZERO);
        finishDto.setD31(BigDecimal.ZERO);
    }

    private  String getKey(CellInstockPlanFinishTotalDTO dto){
        return Joiner.on(",").useForNull("无").join(dto.getIsOversea(),dto.getBasePlace(),dto.getWorkshop(),dto.getCellsType(),dto.getHTrace(),dto.getCellSource(),dto.getAesthetics(),dto.getTransparentDoubleGlass(),dto.getIsRegionalCountry(),dto.getProductionGrade(),dto.getMainGridSpace(),StringUtils.isBlank(dto.getSupplyMethod()) ? "无" : dto.getSupplyMethod(),dto.getRatioCode());
    }

    private List<CellInstockPlanFinishTotalDTO> getCellInstockPlanFinishDatas(CellInstockPlanFinishTotalQuery query) {
        String month = query.getMonth();
        Pair<LocalDate, LocalDate> firstDayAndLastDay = DateUtil.getFirstDayAndLastDay(month);
        LocalDateTime firstDay=firstDayAndLastDay.getLeft().atTime(LocalTime.MIN);
        LocalDateTime lastDay=firstDayAndLastDay.getRight().atTime(LocalTime.MAX);
        QCellInstockPlanFinish qCellInstockPlanFinish = QCellInstockPlanFinish.cellInstockPlanFinish;
        List<CellInstockPlanFinish> cellInstockPlanFinishes = jpaQueryFactory.select(qCellInstockPlanFinish).from(qCellInstockPlanFinish).where(qCellInstockPlanFinish.isOversea.eq(query.getIsOversea())).
                where(qCellInstockPlanFinish.finishedDate.goe(firstDay)).where(qCellInstockPlanFinish.finishedDate.loe(lastDay)).fetch();
        List<CellInstockPlanFinishDTO> cellInstockPlanFinishDTOS = convert.toDto(cellInstockPlanFinishes);
        //1、 分组
        Map<String, List<CellInstockPlanFinishDTO>> collect = cellInstockPlanFinishDTOS.stream().collect(
                Collectors.groupingBy(item -> Joiner.on(",").useForNull("无").join( item.getIsOversea(), item.getBasePlace(), item.getWorkshop(), item.getCellsType(),  item.getHTrace(), item.getCellSource(), item.getAesthetics(),item.getTransparentDoubleGlass(), item.getIsRegionalCountry(), item.getProductionGrade(), item.getMainGridSpace(),StringUtils.isBlank(item.getSupplyMethod()) ? "无" : item.getSupplyMethod(),item.getRatioCode()))
        );
        //2、 依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());

        List<CellInstockPlanFinishTotalDTO> totals=Lists.newArrayList();
        //3、计算
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            CellInstockPlanFinishTotalDTO cellInstockPlanFinishTotal =  null;
            List<CellInstockPlanFinishDTO> dtos = collect.get(key);
            //统计数据
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            for (CellInstockPlanFinishDTO dto : dtos) {

                if (Objects.isNull(cellInstockPlanFinishTotal)) {
                    cellInstockPlanFinishTotal =new CellInstockPlanFinishTotalDTO();
                    BeanUtils.copyProperties(dto, cellInstockPlanFinishTotal);
                    cellInstockPlanFinishTotal.setMonth(month);
                    cellInstockPlanFinishTotal.setDataType("实际入库");


                }
                int day = dto.getFinishedDate().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellInstockPlanFinishTotal, "getD" + day);
                BigDecimal value = dto.getFinishedQty();
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellInstockPlanFinishTotal, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellInstockPlanFinishTotal, "setD" + day, addVal);
                }
                if (Objects.nonNull(dto.getFinishedQty())) {
                    qtyCount = qtyCount.add(dto.getFinishedQty());
                }

            }
            cellInstockPlanFinishTotal.setQtyThousandPc(qtyCount);
            setScale(cellInstockPlanFinishTotal,3);
            totals.add(cellInstockPlanFinishTotal);
        }
        return totals;

    }

    private void setScale(CellInstockPlanFinishTotalDTO cellInstockPlanFinishTotal, int scale) {
        for (int i = 1; i <= 31; i++) {
            BigDecimal val = ReflectUtil.invoke(cellInstockPlanFinishTotal, "getD" + i);
            if (Objects.nonNull(val)) {
                ReflectUtil.invoke(cellInstockPlanFinishTotal, "setD" + i, val.setScale(scale, RoundingMode.HALF_UP));
            }
        }
        if (Objects.nonNull(cellInstockPlanFinishTotal.getQtyThousandPc())){
            cellInstockPlanFinishTotal.setQtyThousandPc(cellInstockPlanFinishTotal.getQtyThousandPc().setScale(scale, RoundingMode.HALF_UP));
        }
    }

    private List<CellInstockPlanFinishTotalDTO> getCellInstockPlanDatas(CellInstockPlanFinishTotalQuery query) {
        QCellInstockPlan qCellInstockPlan=QCellInstockPlan.cellInstockPlan;
        List<CellInstockPlan> datas = jpaQueryFactory.select(qCellInstockPlan).from(qCellInstockPlan).where(qCellInstockPlan.isOversea.eq(query.getIsOversea())).
                where(qCellInstockPlan.month.eq(query.getMonth())).where(qCellInstockPlan.version.eq(query.getVersion())).fetch();
        List<CellInstockPlanDTO> cellInstockPlanDTOS = cellInstockPlanDEConvert.toDto(datas);
        cellInstockPlanDTOS.forEach(item->{
            if (StringUtils.equalsAny(item.getRegionalCountry(),"无",null,"")){
                item.setIsRegionalCountry("N");
            }else {
                item.setIsRegionalCountry("Y");
            }
        });
        //1、 分组
        Map<String, List<CellInstockPlanDTO>> collect = cellInstockPlanDTOS.stream().collect(
                Collectors.groupingBy(item -> Joiner.on(",").useForNull("无").join( item.getIsOversea(), item.getBasePlace(), item.getWorkshop(), item.getCellsType(),  item.getHTrace(), item.getCellSource() ,item.getAesthetics(), item.getTransparentDoubleGlass(), item.getIsRegionalCountry(), item.getProductionGrade(),item.getMainGridSpace(),StringUtils.isBlank(item.getSupplyMethod()) ? "无" : item.getSupplyMethod(),item.getRatioCode()))
        );
        //2、 依据key排序
        List<String> keys = new ArrayList<>(collect.keySet());

        List<CellInstockPlanFinishTotalDTO> totals=Lists.newArrayList();
        //3、计算
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);

            CellInstockPlanFinishTotalDTO cellInstockPlanFinishTotal =  null;
            List<CellInstockPlanDTO> dtos = collect.get(key);
            //统计数据
            BigDecimal qtyCount = BigDecimal.ZERO;//万片数
            for (CellInstockPlanDTO dto : dtos) {

                if (Objects.isNull(cellInstockPlanFinishTotal)) {
                    cellInstockPlanFinishTotal =new CellInstockPlanFinishTotalDTO();
                            BeanUtils.copyProperties(dto,cellInstockPlanFinishTotal);
                    cellInstockPlanFinishTotal.setFromVersion(dto.getVersion());
                    cellInstockPlanFinishTotal.setDataType("计划排产");
                }
                int day = dto.getStartTime().getDayOfMonth();
                Object val = ReflectUtil.invoke(cellInstockPlanFinishTotal, "getD" + day);
                BigDecimal value = dto.getQtyPc();
                if (Objects.isNull(val)) {
                    ReflectUtil.invoke(cellInstockPlanFinishTotal, "setD" + day, value);
                } else {
                    BigDecimal addVal = ((BigDecimal) val).add(value);
                    ReflectUtil.invoke(cellInstockPlanFinishTotal, "setD" + day, addVal);
                }
                if (Objects.nonNull(dto.getQtyPc())) {
                    qtyCount = qtyCount.add(dto.getQtyPc());
                }
            }
            cellInstockPlanFinishTotal.setQtyThousandPc(qtyCount);
            setScale(cellInstockPlanFinishTotal,3);
            totals.add(cellInstockPlanFinishTotal);
        }

        return totals;
    }
    private void synDataByOrganizationId(Long organizationId, LocalDate beginDate, LocalDate lastDate, Map<String, LovLineDTO> productionGradeMap, Map<String, LovLineDTO> cellsTypeMap, Map<String, LovLineDTO> cellSourceMap, Map<String, LovLineDTO> hTraceMap, Map<String, ErpWorkShop> workshopMap, Map<String, String> locatorWorkshopMap, Map<String, LovLineDTO> aestheticsMap,  List<String> excludeLocators,ScheduledTaskLinesDTO task) {
        //获取上次更新时间
        CellInstockPlanFinish lastUpdateObj = repository.findLastUpdateObj(organizationId);
        LocalDateTime beginDateTime = beginDate.atTime(LocalTime.MIN);
        LocalDateTime lastDateTime = lastDate.atTime(LocalTime.MAX);
        if (Objects.nonNull(lastUpdateObj)) {
            beginDateTime = lastUpdateObj.getCreationDate().plusSeconds(1);
        }
        if (beginDateTime.isAfter(lastDateTime)) {
            return;
        }
        QErpWipIssue qErpWipIssue = QErpWipIssue.erpWipIssue;
        JPAQuery<ErpWipIssue> where = jpaQueryFactory.select(qErpWipIssue).from(qErpWipIssue)
                .where(qErpWipIssue.organizationId.eq(organizationId));
        if (Objects.nonNull(beginDateTime)) {
            where.where(qErpWipIssue.creationDate.goe(beginDateTime));
        }
        if (Objects.nonNull(lastDateTime)) {
            where.where(qErpWipIssue.creationDate.loe(lastDateTime));
        }
        List<ErpWipIssue> erpWipIssues = where.fetch();
        //获取erpWipIssues的itemNumber和workOrder
        Set<String> itemCodes = Sets.newHashSet();
        Set<String> workOrders = Sets.newHashSet();
        erpWipIssues.forEach(erpWipIssue -> {
            itemCodes.add(erpWipIssue.getItemNumber());
            workOrders.add(erpWipIssue.getWorkOrder());
        });
        //通过bbom获取物料信息
        ItemsQuery query = new ItemsQuery();
        if (CollectionUtils.isEmpty(itemCodes)) {
            return;
        }
        query.setItemCodes(itemCodes.stream().collect(Collectors.toList()));
        query.setProductGrades(Arrays.asList("Q1", "A-","Q1低效"));
        query.setOrganizationId(organizationId);
        ResponseEntity<Results<List<ItemsDTO>>> resultsResponseEntity = bbomFeign.findReworkItemCode(query);
        if (resultsResponseEntity == null || resultsResponseEntity.getBody() == null || !resultsResponseEntity.getBody().isSuccess()) {
            log.error("通过bbom获取物料信息接口失败,库存组织：{},开始时间：{},结束时间:{}", organizationId, beginDateTime, lastDateTime);
            throw new BizException("baps_call_bbom_material_interface_failed");
        }
        List<ItemsDTO> itemsDTOSList = resultsResponseEntity.getBody().getData();

        //workOrder->(classCode工单类型包含OSP和REW)
        Map<String, String> workOrderMap = Maps.newHashMap();
        Map<String, ItemsDTO> itemCodeMap = itemsDTOSList.stream().collect(Collectors.toMap(ItemsDTO::getItemCode, Function.identity(), (k1, k2) -> k1));
        if (CollectionUtils.isNotEmpty(workOrders)) {
            ResponseEntity<Results<Map<String, String>>> responseEntity = apsFeign.getEntityNameToClassCodeMap(workOrders.stream().collect(Collectors.toList()));
            if (responseEntity == null || responseEntity.getBody() == null || !responseEntity.getBody().isSuccess()) {
                log.error("通过aps获取工单类型,库存组织：{},开始时间：{},结束时间:{}", organizationId, beginDateTime, lastDateTime);
                throw new BizException("baps_call_aps_wip_interface_failed");
            }
            workOrderMap = responseEntity.getBody().getData();
        }
        List<CellInstockPlanFinish> cellInstockPlanFinishes = Lists.newArrayList();
        List<CellInstockPlanNoFinish> cellInstockPlanNoFinishes = Lists.newArrayList();
        Set<String> locatorMsgSet = Sets.newHashSet();
        //根据料号匹配bbom_items
        List<String> itemNumberList = erpWipIssues.stream().map(ErpWipIssue::getItemNumber).collect(Collectors.toList());

//        ResponseEntity<Results<List<ItemsDTO>>> resultsResponseEntity = bbomFeign.findReworkItemCode(query);
        Map<String, ItemsDTO> itemMap = batteryBomService.findItemsNew(itemNumberList);
        for (ErpWipIssue erpWipIssue : erpWipIssues) {
            if (excludeLocators.contains(erpWipIssue.getLocatorCode())) {
                continue;
            }
            if (!itemCodeMap.containsKey(erpWipIssue.getItemNumber())) {
                continue;
            }


            if (workOrderMap.containsKey(erpWipIssue.getWorkOrder())) {
                String classCode = workOrderMap.get(erpWipIssue.getWorkOrder());
                if (StringUtils.isBlank(classCode)){
                    continue;
                }
                if (StringUtils.containsAny(classCode, "OSP", "REW")) {
                    continue;
                }
                if (Arrays.asList(35L, 42L).contains(erpWipIssue.getTransactionTypeId()) && StringUtils.containsAny(classCode, "STD")) {
                    continue;
                }
            }else {
                continue;
            }
            ItemsDTO itemsDTO = itemCodeMap.get(erpWipIssue.getItemNumber());
            CellInstockPlanFinish cellInstockPlanFinish = new CellInstockPlanFinish();
            cellInstockPlanFinish.setOrganizationId(organizationId);
            cellInstockPlanFinish.setItemCode(erpWipIssue.getItemNumber());
            //p2_1590 【BP-16】实际值覆盖加上主栅间距维度
            //根据料号匹配bbom_items中的segment13获取主栅间距属性
            ItemsDTO itemsMapDTO = itemMap.get(erpWipIssue.getItemNumber());
            if(ObjectUtils.isNotEmpty(itemsMapDTO)){
                //只需要获取10.8和10.9的属性，获取的其余数据记为"无"写入主栅间距字段
                if(StringUtils.isNotEmpty(itemsMapDTO.getSegment13())
                        && ("10.8".equals(itemsMapDTO.getSegment13()) || "10.9".equals(itemsMapDTO.getSegment13()))){
                    cellInstockPlanFinish.setMainGridSpace(itemsMapDTO.getSegment13());
                }else{
                    cellInstockPlanFinish.setMainGridSpace("无");
                }
            }else{
                cellInstockPlanFinish.setMainGridSpace("无");
            }
            //如果TransactionDate在8：20之前(包括8：20)更改为前一天23:59:59
            if (erpWipIssue.getTransactionDate().isBefore(LocalDateTime.of(erpWipIssue.getTransactionDate().toLocalDate(), LocalTime.of(8, 20, 1)))) {
                cellInstockPlanFinish.setFinishedDate(erpWipIssue.getTransactionDate().minusDays(1).withHour(23).withMinute(59).withSecond(59));
            } else {
                cellInstockPlanFinish.setFinishedDate(erpWipIssue.getTransactionDate());
            }
            cellInstockPlanFinish.setFinishedQty(!MathUtils.checkIsZero(erpWipIssue.getLotTransactionQuantity()) ? erpWipIssue.getLotTransactionQuantity().divide(new BigDecimal("10000"),6,RoundingMode.HALF_UP) : BigDecimal.ZERO);
            cellInstockPlanFinish.setCreationDate(erpWipIssue.getCreationDate());
            String productionGrade = itemsDTO.getSegment2();
            if (StringUtils.isNotBlank(productionGrade) &&  ("A-".equals(productionGrade) || "Q1低效".equals(productionGrade)) )  {
                LovLineDTO productionGradelovLineDTO = productionGradeMap.get(productionGrade);
                cellInstockPlanFinish.setProductionGrade(productionGradelovLineDTO.getLovName());
                cellInstockPlanFinish.setProductionGradeId(productionGradelovLineDTO.getLovLineId());
            }
            if(StringUtils.isNotBlank(itemsDTO.getSegment39())){
                cellInstockPlanFinish.setSupplyMethod(itemsDTO.getSegment39());
            }
            Long cellSourceId = Optional.ofNullable(cellSourceMap.get("无")).map(LovLineDTO::getLovLineId).orElse(null);
            if (StringUtils.isNotBlank(itemsDTO.getSegment38())) {
                if (StringUtils.contains(itemsDTO.getSegment38(), "DT")) {
                    LovLineDTO dtLovLineDTO = cellSourceMap.get("DT");
                    cellInstockPlanFinish.setCellSource(dtLovLineDTO.getLovName());
                    cellInstockPlanFinish.setCellSourceId(dtLovLineDTO.getLovLineId());
                    String dt = Arrays.stream(itemsDTO.getSegment38().replace("DT", "").split("-")).findFirst().get();
                    cellInstockPlanFinish.setRatioCode(dt);
                }else{
                    cellInstockPlanFinish.setCellSourceId(cellSourceId);
                    cellInstockPlanFinish.setCellSource("无");
                }
            }else{
                cellInstockPlanFinish.setCellSourceId(cellSourceId);
                cellInstockPlanFinish.setCellSource("无");
            }
            if (StringUtils.isNotBlank(itemsDTO.getSegment23())) {
                if (StringUtils.equals(itemsDTO.getSegment23(), "透明双玻")) {
                    cellInstockPlanFinish.setTransparentDoubleGlass(Optional.ofNullable(cellSourceMap.get("透明双玻")).map(LovLineDTO::getLovName).orElse(null));
                    cellInstockPlanFinish.setTransparentDoubleGlassId(Optional.ofNullable(cellSourceMap.get("透明双玻")).map(LovLineDTO::getLovLineId).orElse(null));
                }else  if (StringUtils.equals(itemsDTO.getSegment23(), "单玻")) {
                    cellInstockPlanFinish.setTransparentDoubleGlass(Optional.ofNullable(cellSourceMap.get("单玻")).map(LovLineDTO::getLovName).orElse(null));
                    cellInstockPlanFinish.setTransparentDoubleGlassId(Optional.ofNullable(cellSourceMap.get("单玻")).map(LovLineDTO::getLovLineId).orElse(null));
                }else{
                    cellInstockPlanFinish.setTransparentDoubleGlass("无");
                    cellInstockPlanFinish.setTransparentDoubleGlassId(cellSourceId);
                }
            }else{
                cellInstockPlanFinish.setCellSourceId(cellSourceId);
                cellInstockPlanFinish.setCellSource("无");
            }


            if (StringUtils.isNotBlank(itemsDTO.getSegment34())) {
                LovLineDTO lovLineDTO = hTraceMap.get(itemsDTO.getSegment34());
                cellInstockPlanFinish.setHTrace(lovLineDTO.getLovName());
                cellInstockPlanFinish.setHTraceId(lovLineDTO.getLovLineId());
            }else {
                Long hTraceId = Optional.ofNullable(hTraceMap.get("无")).map(LovLineDTO::getLovLineId).orElse(null);
                cellInstockPlanFinish.setHTrace("无");
                cellInstockPlanFinish.setHTraceId(hTraceId);
            }
            if (StringUtils.isNotBlank(itemsDTO.getSegment1()) && StringUtils.isNotBlank(itemsDTO.getSegment16())) {
                if (StringUtils.equals(itemsDTO.getSegment1(), "P型") && StringUtils.equals(itemsDTO.getSegment16(), "双长鱼叉头")) {
                    cellInstockPlanFinish.setIsRegionalCountry("Y");
                } else {
                    cellInstockPlanFinish.setIsRegionalCountry("N");
                }
            }else {
                cellInstockPlanFinish.setIsRegionalCountry("N");
            }
            //美学
            if (StringUtils.isNotBlank(itemsDTO.getSegment37()) && "美学".equals(itemsDTO.getSegment37())){
                LovLineDTO lovLineDTO = aestheticsMap.get(itemsDTO.getSegment37());
                cellInstockPlanFinish.setAesthetics(lovLineDTO.getLovName());
                cellInstockPlanFinish.setAestheticsId(lovLineDTO.getLovLineId());
            }else {
                Long aestheticsId = Optional.ofNullable(aestheticsMap.get("无")).map(LovLineDTO::getLovLineId).orElse(null);
                cellInstockPlanFinish.setAesthetics("无");
                cellInstockPlanFinish.setAestheticsId(aestheticsId);
            }
            //电池类型转化
            //通过物料和BBOM_ITEM获取属性进行组合（通过下划线连接）：
            //1、晶体类型，category_segment3;单晶电池片则单晶，多晶电池片则多晶
            //2、P/N型，segment1
            //3、品类，segment3
            //4、单双面，segment10
            //5、主栅类别，segment12
            //6、分片方式，segment11；包含 二分片 则 二分，包含 三分片 则 三分
            //String cellsTypeName = Joiner.on("_").join(itemsDTO.getCategorySegment3().replace("电池片", ""), itemsDTO.getSegment1(), itemsDTO.getSegment3(), itemsDTO.getSegment10(), itemsDTO.getSegment12(), itemsDTO.getSegment11().replace("片", ""));
            Map<String, LovLineDTO> batteryTypeMap = LovUtils.getAllByHeaderCode("BATTERY_TYPE");
            LovLineDTO lovLineDTO = batteryTypeMap.get(itemsDTO.getSegment60());
            log.info("电池类型：{}", batteryTypeMap.get(itemsDTO.getSegment60()));
            //Long cellsTypeId = MapStrutUtil.getIdByName(LovHeaderCodeConstant.BATTERY_TYPE, cellsTypeName);
            cellInstockPlanFinish.setCellsTypeId(lovLineDTO.getLovLineId());
            cellInstockPlanFinish.setCellsType(lovLineDTO.getLovName());
            //货位与车间转化
            String workshop = locatorWorkshopMap.get(erpWipIssue.getLocatorCode());
            if (StringUtils.isNotBlank(workshop)) {
                ErpWorkShop erpWorkShop = workshopMap.get(workshop);
                cellInstockPlanFinish.setWorkshop(erpWorkShop.getWorkshopName());
                cellInstockPlanFinish.setWorkshopId(erpWorkShop.getWorkshopId());
                cellInstockPlanFinish.setBasePlace(erpWorkShop.getBaeplaceName());
                cellInstockPlanFinish.setBasePlaceId(erpWorkShop.getBaseplaceId());
                cellInstockPlanFinish.setIsOversea(erpWorkShop.getOverseaName());
                cellInstockPlanFinish.setIsOverseaId(erpWorkShop.getOverseaId());
                cellInstockPlanFinish.setLocatorCode(erpWipIssue.getLocatorCode());


            }else {
                String locatorError =   String.format("货位车间转化：%s 没有对应车间，请及时维护货位车间关系数据！",erpWipIssue.getLocatorCode());
                if (locatorMsgSet.add(locatorError)){
                    logService.addLog(task, ScheduleTaskStatusEnum.WARN,  locatorError);
                }

               CellInstockPlanNoFinish cellInstockPlanNoFinish = new CellInstockPlanNoFinish();
               BeanUtils.copyProperties(cellInstockPlanFinish, cellInstockPlanNoFinish);
               cellInstockPlanNoFinish.setLocatorCode(erpWipIssue.getLocatorCode());
               cellInstockPlanNoFinishes.add(cellInstockPlanNoFinish);
                continue;
            }
            //如果finish
            cellInstockPlanFinishes.add(cellInstockPlanFinish);
            //
        }
        if (CollectionUtils.isNotEmpty(cellInstockPlanFinishes)) {
            repository.saveAll(cellInstockPlanFinishes);
        }
        if (CollectionUtils.isNotEmpty(cellInstockPlanNoFinishes)) {
            cellInstockPlanNoFinishRepository.saveAll(cellInstockPlanNoFinishes);
        }
    }


    @Override
    public List<CellInstockPlanFinishDTO> findByCondition(Long isOverseaId, LocalDate actualCoverageDate) {
        LocalDateTime startLocalDate = LocalDateTime.of(actualCoverageDate.withDayOfMonth(1), LocalTime.MIN);
        LocalDateTime endLocalDate = LocalDateTime.of(actualCoverageDate, LocalTime.MAX);
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qCellInstockPlanFinish.isOverseaId.eq(isOverseaId));
        builder.and(qCellInstockPlanFinish.finishedDate.between(startLocalDate, endLocalDate));
        List<CellInstockPlanFinish> data = IterableUtils.toList(repository.findAll(builder));
        return convert.toDto(data);
    }

    @Override
    public List<String> findLocatorCode() {
        QCellInstockPlanNoFinish qCellInstockPlanNoFinish = QCellInstockPlanNoFinish.cellInstockPlanNoFinish;
        List<String> fetch = jpaQueryFactory.select(qCellInstockPlanNoFinish.locatorCode).from(qCellInstockPlanNoFinish).distinct().fetch();
        fetch=Optional.ofNullable(fetch).orElse(Lists.newArrayList());
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BAPS_ERP_WIP_FINISHED_EXCEPT_LOCATOR);
        List<String> collect = allByHeaderCode.values().stream().map(LovLineDTO::getLovValue).distinct().collect(Collectors.toList());
        fetch.removeAll(collect);
        return fetch;
    }


}
@Data
class ErpWorkShop {
    private Long workshopId;
    private String workshopName;
    private Long baseplaceId;
    private String baeplaceName;
    private Long overseaId;
    private String overseaName;
}
