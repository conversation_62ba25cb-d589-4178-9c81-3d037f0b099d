package com.trinasolar.scp.baps.service.repository;

import com.trinasolar.scp.baps.domain.entity.SiliconSplitRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 硅片拆分规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-17 07:44:54
 */
@Repository
public interface SiliconSplitRuleRepository extends JpaRepository<SiliconSplitRule, Long>, QuerydslPredicateExecutor<SiliconSplitRule> {
}
