<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ibm.dpf</groupId>
        <artifactId>dpf-common</artifactId>
        <version>5.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>scp-baps</artifactId>
    <groupId>com.trinasolar.scp.baps</groupId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>scp-baps-domain</module>
        <module>scp-baps-service</module>
        <module>scp-baps-api</module>
        <module>scp-baps-job</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <scp-common-api.version>2.1.2</scp-common-api.version>
    </properties>

    <distributionManagement>
        <repository>
            <id>trina-release</id>
            <name>maven-releases</name>
            <url>http://10.40.1.96:18081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>trina-snapshot</id>
            <name>maven-snapshots</name>
            <url>http://10.40.1.96:18081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>trina-snapshot</id>
            <name>Trina Project Snapshot</name>
            <url>http://10.40.1.96:18081/nexus/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>trina-release</id>
            <name>Trina Project Release</name>
            <url>http://10.40.1.96:18081/nexus/content/repositories/releases/</url>
        </repository>
        <repository>
            <id>aliyun-public</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public/</url>
        </repository>
    </repositories>

    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2.2.8.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2.2.8.RELEASE</version>
        </dependency>


        <!-- 对接天合光能log -->
        <dependency>
            <groupId>com.trinasolar.lapetus.log</groupId>
            <artifactId>log2-spring-boot-starter</artifactId>
            <version>2.2.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.3</version>
            <scope>compile</scope>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>com.trinasolar.scp.common</groupId>
            <artifactId>scp-common-api</artifactId>
            <version>${scp-common-api.version}</version>
        </dependency>
        <!--excel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar.devops.file</groupId>
            <artifactId>file-spring-boot-starter</artifactId>
            <version>1.0.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>2.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.sun.istack</groupId>
                <artifactId>istack-commons-runtime</artifactId>
                <version>3.0.7</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.taglibs</groupId>
                <artifactId>taglibs-standard-impl</artifactId>
                <version>1.2.5</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet.jsp.jstl</groupId>
                <artifactId>jstl-api</artifactId>
                <version>1.2</version>
            </dependency>
            <!--<dependency>-->
            <!--<groupId>javax.mail</groupId>-->
            <!--<artifactId>javax.mail-api</artifactId>-->
            <!--<version>1.6.2</version>-->
            <!--</dependency>-->
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.6</version>
            </dependency>
            <dependency>
                <groupId>com.sun.activation</groupId>
                <artifactId>javax.activation</artifactId>
                <version>1.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-transports-http-jetty</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-frontend-jaxws</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-transports-http</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-jaxb-annotations</artifactId>
                <version>2.9.7</version>
            </dependency>
            <dependency>
                <groupId>com.cre.dmp.osp</groupId>
                <artifactId>osp-common-openapi</artifactId>
                <version>3.4.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.15.6</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
