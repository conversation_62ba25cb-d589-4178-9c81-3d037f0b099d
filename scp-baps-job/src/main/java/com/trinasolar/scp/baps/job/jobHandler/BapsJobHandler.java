package com.trinasolar.scp.baps.job.jobHandler;

import com.trinasolar.scp.baps.service.service.*;
import com.trinasolar.scp.baps.service.service.erp.DataSyncService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Slf4j
@RequiredArgsConstructor
public class BapsJobHandler {

    private final PriorityLogisticsLineService priorityLogisticsLineService;
    private final CellConversionFactorService cellConversionFactorService;
    private final CellWorkshopPriorityTargetService cellWorkshopPriorityTargetService;
    private final MainGridSpacingRuleService mainGridSpacingRuleService;
    private final DataSyncService dataSyncService;
    private final CellInstockPlanFinishService cellInstockPlanFinishService;
    private final CellFineService cellFineService;
    /**
     * 每天中午12点
     *
     * @return
     */
    @XxlJob("cellConversionFactorJobHandler")
    public ReturnT<String> cellConversionFactorJobHandler() {
        XxlJobHelper.log("开始同步一期兆瓦系数cellConversionFactorJobHandler：{}", LocalDateTime.now());
        cellConversionFactorService.updateConversionFactor();
        XxlJobHelper.log("结束同步一期兆瓦系数cellConversionFactorJobHandler：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 每天中午12点
     *
     * @return
     */
    @XxlJob("priorityLogisticsLineJobHandler")
    public ReturnT<String> priorityLogisticsLineJobHandler() {
        XxlJobHelper.log("开始同步一期运输天数priorityLogisticsLineJobHandler：{}", LocalDateTime.now());
        priorityLogisticsLineService.refreshData();
        XxlJobHelper.log("结束同步一期运输天数priorityLogisticsLineJobHandler：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 每天中午12点
     *
     * @return
     */
    @XxlJob("cellWorkshopPriorityTargetJobHandler")
    public ReturnT<String> cellWorkshopPriorityTargetJobHandler() {
        XxlJobHelper.log("开始同步一期车间优先度效率目标值cellWorkshopPriorityTargetJobHandler：{}", LocalDateTime.now());
        cellWorkshopPriorityTargetService.refreshData();
        XxlJobHelper.log("结束同步一期车间优先度效率目标值cellWorkshopPriorityTargetJobHandler：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
    /**
     * 每天8：30读取ERP实际入库数据
     *
     * @return
     */
    @XxlJob("syncWipIssueJobHandler")
    public ReturnT<String> syncWipIssueJobHandler() {
        XxlJobHelper.log("开始同步ERP实际入库数据：{}", LocalDateTime.now());
        dataSyncService.syncWipIssue(null,null);
        XxlJobHelper.log("结束同步ERP实际入库数据：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
    /**
     * 每天8：55读取ERP实际入库数据
     *
     * @return
     */
    @XxlJob("synDataFromErpWipIssueJobHandler")
    public ReturnT<String> synDataFromErpWipIssueJobHandler() {
        XxlJobHelper.log("开始转化ERP实际入库数据：{}", LocalDateTime.now());
        cellInstockPlanFinishService.synDataFromErpWipIssue(null,null);
        XxlJobHelper.log("结束转化ERP实际入库数据：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
    /**
     * 每天9:00读取ERP实际入库数据
     *
     * @return
     */
    @XxlJob("synDataCellFineJobHandler")
    public ReturnT<String> synDataCellFineJobHandler() {
        XxlJobHelper.log("开始同步电池良率数据：{}", LocalDateTime.now());
        cellFineService.importData();
        XxlJobHelper.log("结束同步电池良率数据：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
}
