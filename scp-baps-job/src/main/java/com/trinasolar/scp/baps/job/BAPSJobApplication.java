package com.trinasolar.scp.baps.job;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * @date 2022/4/21
 */
@SpringBootApplication(scanBasePackages = {"com.trinasolar.scp.baps",
        "com.ibm.dpf.common.config",
        "com.ibm.dpf.gateway",
        "com.ibm.dpf.gateway.service",
        "com.trinasolar.scp.common.api.util",
        "com.trinasolar.scp.common.api.component"}, exclude = {CacheAutoConfiguration.class, MongoAutoConfiguration.class,
        MongoRepositoriesAutoConfiguration.class, MongoDataAutoConfiguration.class,
        FreeMarkerAutoConfiguration.class, RabbitAutoConfiguration.class})
@EnableDiscoveryClient
@EnableHystrix
@EnableSwagger2
@RefreshScope
@EnableFeignClients(basePackages = {"com.trinasolar.scp.common.api.component", "com.trinasolar.scp.baps.service.feign"})
@EnableJpaAuditing(auditorAwareRef = "userAuditorAware")
@EnableAsync
@EnableCaching
public class BAPSJobApplication {
    public static void main(String[] args) {
        SpringApplication.run(BAPSJobApplication.class, args);
    }
}
