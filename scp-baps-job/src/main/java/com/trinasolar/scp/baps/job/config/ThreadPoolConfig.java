package com.trinasolar.scp.baps.job.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.*;

/**
 * 配置默认线程池，后期修改
 *
 * <AUTHOR>
 * @date 2022/1/7
 */
@Configuration
public class ThreadPoolConfig {
    @Bean
    @Primary
    public ExecutorService threadPoolExecutor() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() + 1,
                Runtime.getRuntime().availableProcessors() + 1,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(Integer.MAX_VALUE),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }

    @Bean
    @Qualifier("dataSyncThreadPool")
    public ExecutorService dataSyncThreadPool() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 2,
                Runtime.getRuntime().availableProcessors() * 2,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(Integer.MAX_VALUE),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }

    @Bean
    @Qualifier("itemCodeMatchThreadPool")
    public ExecutorService itemCodeMatchThreadPool(ScpThreadPoolProperties config) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(config.getThreadNamePrefix() + "itemCodeMatchThreadPool-%d").build();
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                Integer.max(config.getCorePoolSize(), Runtime.getRuntime().availableProcessors() * 2),
                Integer.max(config.getMaxPoolSize(),Runtime.getRuntime().availableProcessors() * 2),
                config.getKeepAliveSeconds(),
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(config.getQueueCapacity()), // 工作队列
                threadFactory, // 线程工厂，用于创建新线程
                new ThreadPoolExecutor.AbortPolicy() // 在任务太多处理不过来时，新任务将被拒绝并抛出异常
        ));
    }
}
